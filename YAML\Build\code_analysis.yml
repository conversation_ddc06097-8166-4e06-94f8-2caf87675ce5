﻿parameters:
  variableGroupName: crbbro-ps-nonprod
  variableGroupNameShared: crbbro-bkt-nonprod
  microservice: ps
  nightly: false
  appName: 'Placement Store Api'
  enableNotification: false
  compareBranch: trunk
  agentPoolName: Private-CRB-Linux-AKS-D

stages:
- stage: code_analysis
  ${{ if eq(parameters.nightly, 'false') }}:
    dependsOn: start
  jobs:

  - ${{ if and(ne(variables['Build.Reason'],'PullRequest'),ne(variables['Build.SourceBranch'], format('refs/heads/{0}',parameters.compareBranch)),not(startsWith(variables['Build.SourceBranch'], 'refs/heads/release/'))) }}:
    - deployment: approve
      displayName: 'Approve tests'
      pool: server
      environment: y-start-crbbro-${{parameters.microservice}}

  - job: VarJob
    displayName: 'Library variables'
    variables:
    - group: ${{parameters.variableGroupName}}
    - group: ${{parameters.variableGroupNameShared}}
    pool:
      name: ${{parameters.agentPoolName}}
    steps:
    - checkout: none
    - pwsh: |
        Write-Host "sonarEnabled: $(sonarEnabled)"
        Write-Host "##vso[task.setvariable variable=sonarEnabled;isOutput=true]$(sonarEnabled)"
        Write-Host "chkEnabled: $(chkEnabled)"
        Write-Host "##vso[task.setvariable variable=chkEnabled;isOutput=true]$(chkEnabled)"
        Write-Host "nugetEnabled: $(nugetEnabled)"
        Write-Host "##vso[task.setvariable variable=nugetEnabled;isOutput=true]$(nugetEnabled)"
      name: libraryVariables

  - ${{ if or(eq(variables['Build.Reason'],'PullRequest'),eq(variables['Build.SourceBranch'], format('refs/heads/{0}',parameters.compareBranch))) }}:
    # This stage performs static code analysis using CheckMarx - incremental in branches and full in Trunk
    - job: SnykJob
      displayName: 'Snyk Scan'
      ${{ if or(eq(variables['Build.Reason'],'PullRequest'),eq(variables['Build.SourceBranch'], format('refs/heads/{0}',parameters.compareBranch))) }}:
        dependsOn: VarJob
      ${{ else }}:
        dependsOn:
        - approve
        - VarJob
      variables:
      - group: ${{parameters.variableGroupNameShared}}
      - group: ${{parameters.variableGroupNameBkp}}
      - group: ${{parameters.variableGroupNameBpa}}
      condition: eq(dependencies.VarJob.outputs['libraryVariables.snykEnabled'], 'true')
      pool:
        name: ${{parameters.agentPoolName}}
      steps:
      - checkout: self
        fetchDepth: 1    # shallow
        fetchTags: false # dont fetch tags
      - task: NodeTool@0
        displayName: 'Use Node'
        inputs:
          versionSpec: $(nodeVersion)
      - task: SnykSecurityScan@1
        inputs:
          serviceConnectionEndpoint: 'Snyk'
          testType: 'code'
          codeSeverityThreshold: 'low'
          failOnThreshold: 'low'
          failOnIssues: $(snykFailOnIssues)
          organization: 'broking-technologies-miNyNf7Rv4WfQYbQN3o3Wq'
          projectName: 'ConnectedBroking/BrokingPlatform'
          monitorOnBuild: true
          #additionalArguments: '--project-name=ConnectedBroking/BrokingPlatform'

  - job: NuGetVulnerabilitiesJob
    displayName: 'NuGet Vulnerabilities'
    ${{ if and(ne(variables['Build.Reason'],'PullRequest'),ne(variables['Build.SourceBranch'], format('refs/heads/{0}',parameters.compareBranch)),not(startsWith(variables['Build.SourceBranch'], 'refs/heads/release/'))) }}:
      dependsOn:
      - approve
      - VarJob
    ${{ else }}:
      dependsOn: VarJob
    variables:
    - group: ${{parameters.variableGroupName}}
    - group: ${{parameters.variableGroupNameShared}}
    condition: eq(dependencies.VarJob.outputs['libraryVariables.nugetEnabled'], 'true')
    pool:
      name: ${{parameters.agentPoolName}}
    steps:
    - checkout: self
      fetchDepth: 1    # shallow
      fetchTags: false # dont fetch tags
    - template: Build/check-nuget.yml@templates
      parameters:
        appName: ${{parameters.appName}}
        enableNotification: ${{parameters.enableNotification}}
        notificationUrl: $(MSTeamsDevOpsChannel)
        vstsFeed: $(vstsFeed)
        criticalThreshold: 0
        highThreshold: 0
        moderateThreshold: 0
        projects:
        - displayName: 'Web App'
          project: $(Build.SourcesDirectory)/src/PsWeb/PsWeb.csproj
        - displayName: 'Func App'
          project: $(Build.SourcesDirectory)/src/PsFunc/PsFunc.csproj

  - job: SonarCloudJob
    displayName: 'SonarCloud Scan'
    ${{ if and(ne(variables['Build.Reason'],'PullRequest'),ne(variables['Build.SourceBranch'], format('refs/heads/{0}',parameters.compareBranch)),not(startsWith(variables['Build.SourceBranch'], 'refs/heads/release/'))) }}:
      dependsOn:
      - approve
      - VarJob
    ${{ else }}:
      dependsOn: VarJob
    condition: and(and(eq(dependencies.VarJob.outputs['libraryVariables.sonarEnabled'], 'true'),eq('${{parameters.nightly}}','false')),not(startsWith(variables['Build.SourceBranch'], 'refs/heads/release/')))
    variables:
    - group: ${{parameters.variableGroupName}}
    - group: ${{parameters.variableGroupNameShared}}
    pool:
      name: ${{parameters.agentPoolName}}
      demands: AZP_AGENT_SIZE -equals large
    steps:
    - checkout: self
      fetchDepth: 0 # full - this is required for SonarCloud
      persistCredentials: true
    - template: Build/build-sonar.yml@templates
      parameters:
        buildConfiguration: $(buildConfiguration)
        buildVersion: $(Build.BuildNumber)
        nodeVersion: $(nodeVersion)
        dotnetSDKVersion: 8.0.x

        # dotnet core restore
        restoreDNC:
        - displayName: 'Nuget Restore'
          projects: '$(Build.SourcesDirectory)/PlacementStore.sln'
          vstsFeed: $(vstsFeed)
        # dotnet core build
        buildDNC:
        - displayName: 'Build Database'
          projects: '$(Build.SourcesDirectory)/src/PlacementStoreDb.Build/PlacementStoreDb.Build.csproj'
        - displayName: 'Build Web App'
          projects: '$(Build.SourcesDirectory)/src/PsWeb/PsWeb.csproj'
        - displayName: 'Build Function App'
          projects: '$(Build.SourcesDirectory)/src/PsFunc/PsFunc.csproj'

        # static code analysis
        sonarCloud:
          cloud: 'wtw-crb'
          organization: 'wtw-crb'
          projectKey: 'wtwcrb_PlacementStore'
          projectName: 'PlacementStore'
