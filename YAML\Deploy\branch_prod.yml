﻿parameters:
  configOnly: false
  reporting: false
  download: current
  variableGroupName: crbbro-ps-preprod
  variableGroupNameShared: crbbro-bkt-preprod
  microservice: ps
  appId: 002757
  billingCode: 'Placement Store'
  envName: branch_prod
  regionName: em20
  azureServiceConnection: AKS-CRBBRO-UAT
  aksIdentityName: aks-u-em20-identity
  agentPoolName: Private-CRB-Linux-AKS-U
  resourceGroupRoleAssignment:
  - roleName: Reader                                                        # Azure role
    members:
    - memberName: R-AZC-CRBBRO-BRANCH-RGRP-READER                           # Cloud group
      type: Group
  - roleName: 'Monitoring Reader'
    members:
    - memberName: R-AZC-CRBBRO-BRANCH-RGRP-READER                           # Cloud group
      type: Group
  - roleName: Contributor                                                   # Azure role
    members:
    - memberName: R-AZC-CRBBRO-BRANCH-RGRP-CONTRIBUTOR                      # Cloud group
      type: Group
  - roleName: 'Lock Contributor'                                            # Azure role
    members:
    - memberName: R-AZC-CRBBRO-<PERSON><PERSON>CH-RGRP-CONTRIBUTOR                      # Cloud group
      type: Group
  - roleName: 'Monitoring Contributor'                                      # Azure role
    members:
    - memberName: R-AZC-CRBBRO-BRANCH-RGRP-CONTRIBUTOR                      # Cloud group
      type: Group
  - roleName: 'Storage Blob Data Contributor'                               # Storage Blob Data Contributor
    members:
    - memberName: crbbro-ps-##envLetter##-##regionName##-adf                # Data Factory
      type: ServicePrincipal
  storageAccount:
    create: true                                                            # Create Storage Account
    fileShares:
    - name: datasource-cam-analytics
  appServicePlans:
  - name: crbbro-ps-##branchId####envLetter##-##regionName##-asp
    shortName: dedicated
    sku: S1
    kind: app                                                               # app or linux
  keyVault:                                                                 # Create KeyVault
    create: true
    vaultAccess:                                                            # Access list for KeyVault
      - displayName: aks-u-em20-identity
        type: ServicePrincipal
        permissions:                                                        # Permissions to enable on the KeyVault
          keys: 'Get,List,Update,Create,Import,Delete,Recover,Backup,Restore'
          secrets: 'Get,List,Set,Delete,Recover,Backup,Restore'
          certificates: 'Get,List,Update,Create,Import,Delete,Recover,Backup,Restore,ManageContacts,ManageIssuers,GetIssuers,ListIssuers,SetIssuers,DeleteIssuers'
      - displayName: R-AZC-CRBBRO-BRANCH-RGRP-CONTRIBUTOR                    # Cloud Group
        type: Group
        permissions:                                                        # Permissions to enable on the KeyVault
          secrets: 'Get,List'
  webApps:
  - project: PsWeb                                                          # Name of project containing Web application
    dotnetVersion: 'v8.0'                                                   # DotNet Framework Version
    appServicePlan: crbbro-ps-##branchId####envLetter##-##regionName##-asp  # App Service Plan hosting the Web application
    subnetName: crbbro-ps-##branchId####envLetter##-##regionName##-subn1    # Subnet name to connect Web application
    clientAffinityEnabled: false
    use32BitWorkerProcess: false
    autoHealEnabled: true
    artifactName: WebApp                                                    # Name of published artefact from build stage
    healthCheckPath: '/api/HealthCheck'                                     # Path of health check api to call to check health during deployment
    appSettingsName: webAppSettings                                         # Artefact name with common app settings
    overrideAppSettings:                                                    # Environment specific overrides for app.settings
    - name: AzureAd__ClientId
      value: 35ae11d4-0d36-40af-9316-7ef58a1628d0
    - name: AzureAd__Scope
      value: api://35ae11d4-0d36-40af-9316-7ef58a1628d0/.default
    - name: PlacementStore__Mappings__0__ApplicationId
      value: 96e9be59-82c1-4d85-8705-9615e2914e87                           # PSA.Client.DevTest
    - name: PlacementStore__Mappings__0__SharedKeys__0
      value: D1C69217-47C9-490D-A7D2-5C0B6BB7066C                           # 50000
    - name: PlacementStore__Mappings__1__ApplicationId
      value: bcd61622-981e-4f97-8689-10cfcd4896f1                           # PSA.Client.COL-PowerApps
    - name: PlacementStore__Mappings__1__SharedKeys__0
      value: F4560FAD-00BC-431D-97D3-9E0705FB46C6                           # 50003
    - name: PlacementStore__Mappings__2__ApplicationId
      value: b94fd0ce-008f-448a-90fc-c576f5937dec                           # PSA.Client.Eclipse-PowerApps
    - name: PlacementStore__Mappings__2__SharedKeys__0
      value: D1C69217-47C9-490D-A7D2-5C0B6BB7066C                           # 50000
    - name: PlacementStore__Mappings__3__ApplicationId
      value: 3b1cf3b6-5f9f-4fca-931a-2ae26227fe25                           # PSA.Client.Broking-net-PowerApps
    - name: PlacementStore__Mappings__3__SharedKeys__0
      value: 477D97FB-BF43-4F1D-8923-43A6D684F6B2                           # 50358
    - name: PlacementStore__Mappings__4__ApplicationId
      value: c40ce2e4-dbd5-4e77-8db2-5ecf2a3af0d0                           # PSA.Client.Epic-US-PowerApps
    - name: PlacementStore__Mappings__4__SharedKeys__0
      value: 4CD4A178-6C96-4073-BE07-81F2A0855AB1                           # 50001
    - name: PlacementStore__Mappings__5__ApplicationId
      value: 7cca3417-7096-4cb2-b4cb-dcb94b9749aa                           # PSA.Client.CPT
    - name: PlacementStore__Mappings__5__SharedKeys__0
      value: 4CD4A178-6C96-4073-BE07-81F2A0855AB1                           # 50001
    - name: PlacementStore__Mappings__6__ApplicationId
      value: 7575f1d9-9b73-4af8-8793-a2cce1e23504                           # PSA.Client.CGW
    - name: PlacementStore__Mappings__7__ApplicationId
      value: 5e4434ef-0f60-47a7-aea4-5ba57aeed1d7                           # PSA.Client.Eglobal-Australia-PowerApps
    - name: PlacementStore__Mappings__7__SharedKeys__0
      value: 0AFF89AC-C352-48B9-90CA-4283382D3896                           # 50004
    - name: PlacementStore__Mappings__8__ApplicationId
      value: b5fde768-4b50-42a0-8534-45718631ad27                           # PSA.Client.BPA
    - name: PlacementStore__Mappings__9__ApplicationId
      value: 56825227-c263-43e9-b4e1-8624be0f8589                           # PSA.Client.WIBS (Italy)
    - name: PlacementStore__Mappings__9__SharedKeys__0
      value: 1ABF41B3-BE9F-4E5C-B053-D70310D4E982                           # 50045
    - name: PlacementStore__Mappings__10__ApplicationId
      value: eb7d1bb3-2194-485b-814d-73b334c0fccc                           # PSA.Client.ASYS-Germany
    - name: PlacementStore__Mappings__10__SharedKeys__0
      value: 4E1AEF3A-FB97-4CE3-B3E6-87A5FA7FD725                           # 50029
    - name: PlacementStore__Mappings__11__ApplicationId
      value: 03a20d44-0d5e-41fa-aa9a-e0df63f49ec8                           # PSA.Client.Gras-Savoye-EGS (France)
    - name: PlacementStore__Mappings__11__SharedKeys__0
      value: C762D601-6F8A-4AEC-A7FC-DC47617BC7BF                           # 50364
    - name: PlacementStore__Mappings__12__ApplicationId
      value: f7dd3b46-17e2-4059-bfe3-7e8c545b1dcb                           # PSA.Client.VisualSeg-Spain
    - name: PlacementStore__Mappings__12__SharedKeys__0
      value: 879725C5-72C9-4421-9C52-6DE1B7140FDD                           # 50044
    - name: PlacementStore__Mappings__13__ApplicationId
      value: 37fd81fa-a452-48c1-9815-9244f73a59d0                           # PSA.Client.eGlobal-Netherlands
    - name: PlacementStore__Mappings__13__SharedKeys__0
      value: 75F798E7-61A4-4F99-A8CF-B02D77C6367E                           # 50010
    - name: PlacementStore__Mappings__14__ApplicationId
      value: e15d1e81-8315-4e8c-8fb6-2764ee5f9d65                           # PSA.Client.eGlobal-HongKong
    - name: PlacementStore__Mappings__14__SharedKeys__0
      value: E82BD58C-F207-4FE6-AD79-C3544C5CF97E                           # 50007
    - name: PlacementStore__Mappings__15__ApplicationId
      value: c24999e0-cd38-44b6-80bd-5f2ec070cfa4                           # PSA.Client.BisCore
    - name: PlacementStore__Mappings__15__SharedKeys__0
      value: 10A4D7CF-02AA-41EB-B34F-288BA3E342FF                           # 50500
    - name: PlacementStore__Mappings__16__ApplicationId
      value: f7dcb086-0acd-4ebf-b911-a389daad2379                           # PSA.Client.eGlobal-South-Africa
    - name: PlacementStore__Mappings__16__SharedKeys__0
      value: 0BB9DDDD-1892-4792-9C5F-3F0DBC20EAC8                           # 50015
    - name: PlacementStore__Mappings__17__ApplicationId
      value: 87bf6957-206a-4331-bc95-fc2f946d1b2b                           # PSA.Client.Reference-Data
    - name: PlacementStore__Mappings__17__SharedKeys__0
      value: B5F6B099-EA5A-4FD5-9F86-7E45C5BD9514                           # 50355
    - name: PlacementStore__Mappings__18__ApplicationId
      value: ccae9c27-30b1-44dd-a103-3173fe40fe1c                           # PSA.Client.Carrier-Gateway
    - name: PlacementStore__Mappings__19__ApplicationId
      value: 92943c30-4a4c-414c-a90f-e023a08d8602                           # PSA.Client.SegElevia-Portugal
    - name: PlacementStore__Mappings__19__SharedKeys__0
      value: 23FD35A6-8849-4B88-A512-7F37252F71E4                           # 50041
    - name: PlacementStore__Mappings__20__ApplicationId
      value: 8fd4f945-09da-445a-9d71-9b547f16bb18                           # PSA.Client.eGlobal-China
    - name: PlacementStore__Mappings__20__SharedKeys__0
      value: E1696C42-29A6-464F-83A3-F9EF42AC0EFE
  funcApps:
  - project: PsFunc                                                         # Name of project containing Function application
    dotnetVersion: 'v8.0'                                                   # DotNet Framework Version
    artifactName: FuncApp                                                   # Name of published artefact from build stage
    appServicePlan: crbbro-ps-##branchId####envLetter##-##regionName##-asp  # App Service Plan hosting the Web application
    subnetName: crbbro-ps-##branchId####envLetter##-##regionName##-subn1    # Subnet name to connect Web application
    healthCheckPath: '/api/HealthCheck'                                     # Path of Http Trigger Function to call to check health during deployment
    disabledFunctions:                                                      # Functions that should remain disabled
    - name: HealthCheckExt                                                  # Extended healthcheck for manual run only
    - name: PlacementCompleted                                              # CRB EventHub integration not supported in feature branch
    - name: SqlBulkCopyData                                                 # Sql bulk copy not supported in feature branch
    - name: PlacementEvents                                                 # PlacementEvents supported in feature branch
    - name: RefreshAnalysisServer                                           # RefreshAnalysisServer supported in feature branch
    appSettingsName: funcAppSettings                                        # Artefact name with common app settings
  sqlDatabases:
  - project: PlacementStoreDb.Build                                         # Project containing database
    name: crbbro-ps-##branchId####envLetter##-##regionName##-db
    shortName: ps
    useManagedIdentity: true                                                # Using Microsoft.Data.SqlClient instead of System.Data.SqlClient to allow automatic token authentication
    vaultSecretName: ConnectionStrings--Database
    source:
      resourceGroupName: crbbro-dvo-p-##regionName##-rgrp
      sqlServerName: crbbro-dvo-p-##regionName##-sql
      databaseName: crbbro-ps-p-##regionName##-db
    dest:                                                                   # SQL Server / Elastic Pool which will host the database (to manage Azure costs)
      resourceGroupName: crbbro-dvo-u-em20-rgrp
      sqlServerName: crbbro-dvo-u-em20-sql
      elasticPoolName: crbbro-dvo-u-em-epool-ps
    artifactName: PlacementStore.Database                                   # Name of published artefact from build stage
    roleMembers:
    - roleName: db_owner                                                    # Database role
      members:
      - memberName: R-AZC-CRBBRO-BRANCH-SQL-OWNER                           # Cloud group 
        type: Group
    - roleName: ETL_Role                                                    # Database role
      members:
      - memberName: crbbro-ps-##branchId####envLetter##-##regionName##-fa   # PS Func App
        type: ServicePrincipal
      - memberName: crbbro-ps-##branchId####envLetter##-##regionName##-fa/slots/staging # PS Staging Func App
        type: ServicePrincipal
    - roleName: PlacementStoreAPIConsumerRole                                # Database role
      members:
      - memberName: crbbro-ps-##branchId####envLetter##-##regionName##-wa    # PS web App
        type: ServicePrincipal
      - memberName: crbbro-ps-##branchId####envLetter##-##regionName##-wa/slots/staging # PS Staging web App
        type: ServicePrincipal
stages:
- stage: ${{parameters.envName}}
  dependsOn:
  - start
  jobs:
  - deployment: approve
    displayName: 'Approve ${{parameters.envName}}'
    pool: server
    environment: y-deploy-crbbro-${{parameters.microservice}}-${{parameters.envName}}

  - template: deploy_infra.yml
    parameters:
      configOnly: ${{parameters.configOnly}}
      reporting: ${{parameters.reporting}}
      download: ${{parameters.download}}
      variableGroupName: ${{parameters.variableGroupName}}
      variableGroupNameShared: ${{parameters.variableGroupNameShared}}
      appId: ${{parameters.appId}}
      billingCode: ${{parameters.billingCode}}
      microservice: ${{parameters.microservice}}
      envName: ${{parameters.envName}}
      regionName: ${{parameters.regionName}}
      azureServiceConnection: ${{parameters.azureServiceConnection}}
      agentPoolName: ${{parameters.agentPoolName}}
      resourceGroupRoleAssignment: ${{parameters.resourceGroupRoleAssignment}}
      keyVault: ${{parameters.keyVault}}
      sqlDatabases: ${{parameters.sqlDatabases}}
      appServicePlans: ${{parameters.appServicePlans}}
      webApps: ${{parameters.webApps}}
      funcApps: ${{parameters.funcApps}}
      storageAccount: ${{parameters.storageAccount}}

- stage: ${{parameters.envName}}_apps
  ${{ if or(eq(parameters.configOnly,'true'),eq(parameters.reporting,'true')) }}:
    dependsOn:
    - ${{parameters.envName}}
    - start
    - build_db
  ${{ else }}:
    dependsOn:
    - ${{parameters.envName}}
    - start
    - build_db
    - build_apps
  variables:
  - name: virtualNetworkResourceGroupName
    value: $[ stageDependencies.${{parameters.envName}}.${{parameters.envName}}_${{parameters.regionName}}_vnet.outputs['vnet.virtualNetworkResourceGroupName'] ]
  - name: virtualNetworkName
    value: $[ stageDependencies.${{parameters.envName}}.${{parameters.envName}}_${{parameters.regionName}}_vnet.outputs['vnet.virtualNetworkName'] ]
  - name: appSubnetNamesCSV
    value: $[ stageDependencies.${{parameters.envName}}.${{parameters.envName}}_${{parameters.regionName}}_security.outputs['appSubnets.appSubnetNamesCSV'] ]
  - name: appIdentitiesCSV
    value: $[ stageDependencies.${{parameters.envName}}.${{parameters.envName}}_${{parameters.regionName}}_security.outputs['identities.appIdentitiesCSV'] ]
  jobs:
  - template: deploy_apps.yml
    parameters:
      configOnly: ${{parameters.configOnly}}
      reporting: ${{parameters.reporting}}
      download: ${{parameters.download}}
      variableGroupName: ${{parameters.variableGroupName}}
      variableGroupNameShared: ${{parameters.variableGroupNameShared}}
      appId: ${{parameters.appId}}
      billingCode: ${{parameters.billingCode}}
      microservice: ${{parameters.microservice}}
      envName: ${{parameters.envName}}
      regionName: ${{parameters.regionName}}
      azureServiceConnection: ${{parameters.azureServiceConnection}}
      agentPoolName: ${{parameters.agentPoolName}}
      sqlDatabases: ${{parameters.sqlDatabases}}
      webApps: ${{parameters.webApps}}
      funcApps: ${{parameters.funcApps}}
      virtualNetworkResourceGroupName: $(virtualNetworkResourceGroupName)
      virtualNetworkName: $(virtualNetworkName)
      appSubnetNamesCSV: $(appSubnetNamesCSV)
      appIdentitiesCSV: $(appIdentitiesCSV)