# Set TLS 1.2
[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12

# Set encoding depending on whether we are running as Windows PowerShell or PowerShell Core
if ($psversiontable.PSEdition -eq 'Desktop') {
  $PSDefaultParameterValues['*:Encoding'] = 'UTF8'
} else {
  $PSDefaultParameterValues['*:Encoding'] = 'UTF8NoBOM'
}

if (Test-Path .\quotes.json) {
  $authors = (Get-Content .\quotes.json -raw | ConvertFrom-Json).quoteAuthor | Where-Object { $_ -ne '' }
}

$global:gitbin = 'C:\Program Files\Git\usr\bin'
Set-Alias file.exe $gitbin\file.exe
Set-Alias iconv.exe $gitbin\iconv.exe

function Clean-StdOutErr {
  Get-Item "$($env:Temp)\std*.txt" | Remove-Item -ErrorAction SilentlyContinue | Out-Null
}

function Sql-Prompt {
  param(
    $fullName,
    $formatPath
  )

  if (!(Test-Path $fullName) -or !(Test-Path $formatPath)) {
    return $false
  }

  $sqlPromptFormatter = Get-SqlPromptFormatter
  if (-not $sqlPromptFormatter) {
    Write-Host "Warning - SQL Prompt is not installed or not found"
  } 
  $fileName = (Get-Item $fullName).Name

  Write-Host "Formatting: $($fullName.Substring($pwd.Path.Length + 1))"

  $env:DOTNET_USE_POLLING_FILE_WATCHER=1

  $fileDateTime = Get-Date -Format "yyyyMMdd_HHmmss"
  $stdoutName = "$($env:Temp)/stdout-$($fileName)-$($fileDateTime).txt"
  $stderrName = "$($env:Temp)/stderr-$($fileName)-$($fileDateTime).txt"
     
  $proc = Start-Process $sqlPromptFormatter -ArgumentList @('-p',"""$fullName""",'-s',$formatPath,'-is','-ac','-e','WithoutBrackets','-ta','RemoveAs','-ca','EqualsWithoutString','--i-agree-to-the-eula') -PassThru -NoNewWindow -RedirectStandardOutput $stdoutName -RedirectStandardError $stderrName

  $waitsecs = 0
  while (!(Get-Content $stdoutName | Select-String -Pattern "[100%] Formatting $($fileName)... Done" -SimpleMatch) -and $waitsecs -le 60) {
    Start-Sleep -Seconds 1
    $waitsecs+=1
  }
  if ($waitsecs -gt 60) {
    Get-Content $stdoutName
    Get-Content $stderrName
    Write-Host "SqlPrompt taking over 1 mins - aborting"
    Stop-Process -InputObject $proc
    return $false
  } else {
    Get-Content $stdoutName
    Get-Content $stderrName
    Stop-Process -InputObject $proc
    return $true
  }
}

function Load-SqlDom {
  $Microsoft_SqlServer_DacFx = '160.5400.1'
  $csproj = @"
  <Project Sdk="Microsoft.NET.Sdk">
    <PropertyGroup>
      <OutputType>Exe</OutputType>
      <TargetFramework>net60</TargetFramework>
    </PropertyGroup>
    <ItemGroup>
      <PackageReference Include=`"Microsoft.SqlServer.DacFx`" Version=`"$Microsoft_SqlServer_DacFx`" />
    </ItemGroup>
  </Project>
"@

  $nugetPackages = "$env:USERPROFILE/.nuget/packages"
  $Microsoft_SqlServer_TransactSql_ScriptDom = "$nugetPackages/microsoft.sqlserver.dacfx/$Microsoft_SqlServer_DacFx/lib/netstandard2.1/Microsoft.SqlServer.TransactSql.ScriptDom.dll"
  if (!(Test-Path $Microsoft_SqlServer_TransactSql_ScriptDom)) {
    $dbProj = "$($env:Temp)\Database.csproj"
    Set-Content -Path $dbProj -Value $csproj
    Write-Host "Restoring nuget packages.."
    dotnet restore $dbProj
    Remove-Item $dbProj -Recurse -Force | Out-Null
  }
  Add-Type -Path $Microsoft_SqlServer_TransactSql_ScriptDom
}

function Sql-Sort {
  param(
    $fullName
  )

  $parser = New-Object Microsoft.SqlServer.TransactSql.ScriptDom.TSql150Parser($true)
  $parseErrors = New-Object System.Collections.Generic.List[Microsoft.SqlServer.TransactSql.ScriptDom.ParseError]

  Write-Host "Sorting.. $($fullName.Substring($pwd.Path.Length + 1))"
  $script = gc $fullName -Raw
  $stringReader = New-Object System.IO.StringReader($script)
  $parsedObjects = $parser.Parse($stringReader, [ref]$parseErrors)

  if ($parseErrors.Count -eq 0) {
    $insertstatement = [InsertStatementRef]::new()
    $parsedobjects.Accept($insertstatement)
    $script2 = ''
    $offset = 0
    foreach ($tableInfo in $insertstatement.tables.ToArray()) {
      $table = $tableInfo['Table']
      $startOffset = [int]$tableInfo['StartOffset']
      $fragmentLength = [int]$tableInfo['FragmentLength']
      $insertOffset = [int]$tableInfo['InsertOffset']
      $table.DefaultView.Sort = $table.Columns.ColumnName -join ','
      $tableSorted = $table.DefaultView.ToTable()

      $block = $script.Substring($startOffset, $fragmentLength)
      $selectOffset = $block.IndexOf('SELECT')
      $script2 += $script.Substring($offset, $startOffset-$offset)
      $script2 += $block.Substring(0, $selectOffset).TrimEnd() + "`r`n"
      $unionAll = ($block.IndexOf('UNION ALL') -ge 0)

      for ($rowIndex = 0; $rowIndex -lt $tableSorted.Rows.Count; $rowIndex++) {
        $row = $tableSorted.Rows[$rowIndex]
        if ($rowIndex -gt 0) {
          if ($unionAll) {
            $script2 += "`r`nUNION ALL`r`n"
          } else {
            $script2 += "`r`nUNION`r`n"
          }
        }
        $script2 += "SELECT $($row.ItemArray -join ', ')"
      }
      $script2 += ';'
      $offset = $startOffset + $fragmentLength
    }
    $script2 += $script.Substring($offset)
    Set-Content -Path "$($fullName)" -Value $script2 -NoNewline
    return $true
  } else {
    Write-Host "$($parser.GetType().Name): $($parseErrors.Count) parsing error(s): $(($parseErrors | ConvertTo-Json))" -ForegroundColor Red
    return $false
  }
}

function Walk-Json {
  param(
    $prop,
    $path,
    $personalAccessTokens,
    $uiPasswords,
    $sqlConns,
    $storageConns,
    $serviceBusConns,
    $serviceBusFQNConns,
    $appInsightsConns,
    $signalRConns,
    $stripValuePrefix
  )

  if ($path -ne '') {
    $fullpath = "$($path):$($prop.Name)"
  } else {
    $fullpath = $prop.Name
  }

  # Function App localsettings.json file uses a 'Values {}' block that we don't want for user secrets
  if ($stripValuePrefix -and $fullpath.StartsWith('Values:')) {
    $fullpath = $fullpath.Substring(7)
  }

  if ($prop.TypeNameOfValue -eq 'System.String') {

    if ($prop.Value -eq '##PAT##' -and !($personalAccessTokens -contains $fullpath)) {
      $personalAccessTokens += $fullpath
    } elseif ($prop.Value -eq '##intatatestPassword##' -and !($uiPasswords -contains $fullpath)) {
      $uiPasswords += $fullpath
    } elseif ($prop.Value -eq 'UseDevelopmentStorage=true' -and !($storageConns -contains $fullpath)) {
      $storageConns += $fullpath
    } elseif ($($prop.Value).ToString().StartsWith("Endpoint=sb://") -and !($serviceBusConns -contains $fullpath)) {
      $serviceBusConns += $fullpath
    } elseif ($($prop.Value).ToString().EndsWith(".servicebus.windows.net") -and !($serviceBusFQNConns -contains $fullpath)) {
      $serviceBusFQNConns += $fullpath
    } elseif ($($prop.Value).ToString().EndsWith(".service.signalr.net") -and !($signalRConns -contains $fullpath)) {
      $signalRConns += $fullpath
    } elseif ($($prop.Value).ToString().StartsWith("InstrumentationKey=") -and !($appInsightsConns -contains $fullpath)) {
      $appInsightsConns += $fullpath
    } elseif ($($prop.Value).ToString().ToLower() -match 'data source[\s]*=' -or $($prop.Value).ToString().ToLower() -match 'server[\s]*=') {
      $sb = New-Object System.Data.Common.DbConnectionStringBuilder
      try {
        $sb.set_ConnectionString($prop.Value)

        if ($sb.server -eq '.' -or $sb.server -eq 'localhost') {
          if ($sqlConns.Keys -notcontains $fullpath) {
            $sqlConns[$fullpath] = $sb.ConnectionString
          }
        }
      } catch {
        Write-Host "Error parsing SQL connection string: $($prop.Value)"
      }
    }
  } elseif ($prop.TypeNameOfValue -eq 'System.Management.Automation.PSCustomObject') {
    foreach ($propChild in $prop.PSObject.Properties) {
      $personalAccessTokens, $uiPasswords, $sqlConns, $storageConns, $serviceBusConns, $serviceBusFQNConns, $appInsightsConns, $signalRConns = Walk-Json -prop $propChild -path $fullpath -personalAccessTokens $personalAccessTokens -uiPasswords $uiPasswords -sqlConns $sqlConns -storageConns $storageConns -serviceBusConns $serviceBusConns -serviceBusFQNConns $serviceBusFQNConns -appInsightsConns $appInsightsConns -signalRConns $signalRConns -stripValuePrefix $stripValuePrefix
    }
  } elseif ($prop.TypeNameOfValue -eq 'System.Object[]') {
    foreach ($propObj in $prop.Value) {
      foreach ($propChild in $propObj.PSObject.Properties) {
        $personalAccessTokens, $uiPasswords, $sqlConns, $storageConns, $serviceBusConns, $serviceBusFQNConns, $appInsightsConns, $signalRConns = Walk-Json -prop $propChild -path $fullpath -personalAccessTokens $personalAccessTokens -uiPasswords $uiPasswords -sqlConns $sqlConns -storageConns $storageConns -serviceBusConns $serviceBusConns -serviceBusFQNConns $serviceBusFQNConns -appInsightsConns $appInsightsConns -signalRConns $signalRConns -stripValuePrefix $stripValuePrefix
      }
    }
  } elseif ($prop.TypeNameOfValue -eq 'System.Object') {
    foreach ($propChild in $prop.Value.PSObject.Properties) {
      $personalAccessTokens, $uiPasswords, $sqlConns, $storageConns, $serviceBusConns, $serviceBusFQNConns, $appInsightsConns, $signalRConns = Walk-Json -prop $propChild -path $path -personalAccessTokens $personalAccessTokens -uiPasswords $uiPasswords -sqlConns $sqlConns -storageConns $storageConns -serviceBusConns $serviceBusConns -serviceBusFQNConns $serviceBusFQNConns -appInsightsConns $appInsightsConns -signalRConns $signalRConns -stripValuePrefix $stripValuePrefix
    }
  }

  return $personalAccessTokens, $uiPasswords, $sqlConns, $storageConns, $serviceBusConns, $serviceBusFQNConns, $appInsightsConns, $signalRConns
}

function Get-VisualStudioAzureSubscription {
  param(
    $subs
  )

  # Find Visual Studio subscription
  $vsSubs = $subs | Where-Object { $_.name.startswith('Visual Studio') -and $_.state -eq 'Enabled' }
  if ($vsSubs.Count -eq 1) {
    $mySub = $vsSubs[0]
  } elseif ($vsSubs.Count -gt 1) {
    foreach ($sub in $vsSubs) {
      try {
        $roleDefinitionName = az role assignment list --subscription $sub.id --include-classic-administrators true --query "[?principalName=='$($sub.user.name)' && roleDefinitionId=='NA(classic admin role)'].roleDefinitionName" -o tsv
        if ($roleDefinitionName -eq 'ServiceAdministrator;AccountAdministrator') {
          Write-Host "Found MSDN subscription: $($sub.Name)"
          $mySub = $sub
          break
        }
      } catch {
      }
    }
  }
  return $mySub
}

function Get-IdFromToken {
  param(
    $token
  )

  $tokenheader = $token.Split(".")[0].Replace('-', '+').Replace('_', '/')

  # Fix padding as needed, keep adding "=" until string length modulus 4 reaches 0
  while ($tokenheader.Length % 4) { $tokenheader += "=" }

  # Convert from Base64 encoded string to PSObject all at once
  [System.Text.Encoding]::ASCII.GetString([system.convert]::FromBase64String($tokenheader)) | ConvertFrom-Json | fl | Out-Default

  # Payload
  $tokenPayload = $token.Split(".")[1].Replace('-', '+').Replace('_', '/')

  # Fix padding as needed, keep adding "=" until string length modulus 4 reaches 0
  while ($tokenPayload.Length % 4) { $tokenPayload += "=" }

  # Convert to Byte array
  $tokenByteArray = [System.Convert]::FromBase64String($tokenPayload)

  # Convert to string array
  $tokenArray = [System.Text.Encoding]::ASCII.GetString($tokenByteArray)

  # Convert from JSON to PSObject
  $tokobj = $tokenArray | ConvertFrom-Json

  return $tokobj.oid
}

function Gen-Password {
  $word1 = $null
  $word2 = $null

  while ($null -eq $word1 -or $null -eq $word2) {
    $index = Get-Random -Maximum $authors.Count
    $words = $authors[$index] -split ' '

    foreach ($word in $words) {
      $word = $word -replace '[^A-Za-z]',''
      if ($word.Length -gt 5 -and $word.Length -lt 10) {
        if ($null -eq $word1) {
          $word1 = $word
        } elseif ($null -eq $word2) {
          if ($word1 -ne $word) {
            $word2 = $word
          }
        }
      }
    }
  }

  return "$word1-$word2-$index"
}

function Get-Secrets {
  param(
    [string]$secretsPath,
    [switch]$prompt,
    [bool]$uiPassword,
    [bool]$translationSecret
  )

  $secrets = @{}

  if (Test-Path $secretsPath) {
    $secrets = (Get-Content $secretsPath | ConvertFrom-Json -AsHashTable)
  }

  if (!$secrets.SA_PASSWORD) {
    $secrets.SA_PASSWORD = Gen-Password
  }

  if ($uiPassword) {
    if (!$secrets.UI_PASSWORD -or $secrets.UI_PASSWORD -eq '##intatatestPassword##') {
      Write-Host ""
      Write-Host "The password used to run UI tests locally has not been set" -ForegroundColor Yellow
      Write-Host "If you need the ability to run the UI tests locally, the password can be obtained from QA team" -ForegroundColor Yellow
      Write-Host "If you don't need to run the UI tests locally, then you can answer 'n' to the prompt" -ForegroundColor Yellow
      Write-Host ""
      $confirm = Read-Host "Would you like to set the UI regression test password (can be obtained from QA team), y/n"
      if ($confirm -eq 'y') {
        $secrets.UI_PASSWORD = Read-Host "Enter password"
      } else {
        $secrets.UI_PASSWORD = '##intatatestPassword##'
      }
    } else {
      Write-Host ""
      Write-Host "The password used to run UI tests locally is currently set to: $($secrets.UI_PASSWORD)" -ForegroundColor Yellow
      Write-Host ""
      if ($prompt) {
        $confirm = Read-Host "Would you like to update the UI regression test password (latest can be obtained from QA team), y/n"
        if ($confirm -eq 'y') {
          $secrets.UI_PASSWORD = Read-Host "Enter password"
        }
      }
    }
  }

  if ($translationSecret) {
    if (!$secrets.TRANSLATION_TOKEN -or !$secrets.TRANSLATION_TOKEN -eq '##noToken##') {
      Write-Host ""
      Write-Host "A token is required to automatically run translations" -ForegroundColor Yellow
      Write-Host "If you need the ability to perform translations, the token can be obtained from DevOps team" -ForegroundColor Yellow
      Write-Host "If you don't need to perform translations, then you can answer 'n' to the prompt" -ForegroundColor Yellow
      Write-Host ""
      if ($prompt) {
        $confirm = Read-Host "Would you like to set the translations token? (can be obtained from DevOps team), y/n"
        if ($confirm -eq 'y') {
          $secrets.TRANSLATION_TOKEN = Read-Host "Enter token"
        } else {
          $secrets.TRANSLATION_TOKEN = '##noToken##'
        }
      }
    } else {
      Write-Host ""
      Write-Host "The secret token used to generate translations is currently set to: $($secrets.TRANSLATION_TOKEN)" -ForegroundColor Yellow
      Write-Host ""
      if ($prompt) {
        $confirm = Read-Host "Would you like to update the translations token (latest can be obtained from DevOps team), y/n"
        if ($confirm -eq 'y') {
          $secrets.TRANSLATION_TOKEN = Read-Host "Enter token"
        }
      }
    }
  }

  return $secrets
}

function Create-Secrets {

  param(
    [string]$secretsFile
  )

  $SA_PASSWORD = $null
  if (Test-Path $secretsFile) {
    $saMatches = (Get-Content $secretsFile -Raw) | Select-String -Pattern "(?ms)SA_PASSWORD=([^`r`n]+)"
    if ($saMatches) {
      $SA_PASSWORD = $saMatches.Matches.Groups[1].Value
    }
  }

  if (!$SA_PASSWORD) {
    $SA_PASSWORD = Gen-Password
  }

  return $SA_PASSWORD
}

function Create-SqlServer {

  param(
    [string]$resourceGroupName,
    [string]$sqlServerName,
    [string]$sqlStorageName,
    [string]$SA_PASSWORD,
    [string]$ACI_PERS_CPU,
    [string]$ACI_PERS_MEMORYINGB
  )

  $ACI_PERS_LOCATION='westeurope'
  $ACI_PERS_SHARE_NAME='mssql'

  $ymlFile = 'dev-aci-priv.yml'

  $SUBNETIDAVD = '/subscriptions/f3a96a8b-6a8c-45f1-8e43-25768f5427ec/resourceGroups/crbbro-dvo-x-em20-rgrp/providers/Microsoft.Network/virtualNetworks/crbbro-dvo-x-em20-vnet1/subnets/BUS-CRBBRO-DVO-X-EM20-VNET1-SUBN1'
  $SUBNETIDACI = '/subscriptions/f3a96a8b-6a8c-45f1-8e43-25768f5427ec/resourceGroups/crbbro-dvo-x-em20-rgrp/providers/Microsoft.Network/virtualNetworks/CRBBRO-DVO-X-EM20-VNET4/subnets/default'

  $exists = (az storage account list --resource-group $resourceGroupName --query "[?name=='$sqlStorageName'].{name:name,id:id}" --output tsv)
  if (!$exists) {
    Write-Host "Creating SQL Storage Account: $sqlStorageName (this may take a couple of minutes..)"

    az storage account create `
      --resource-group $resourceGroupName `
      --name $sqlStorageName `
      --location $ACI_PERS_LOCATION `
      --sku Premium_LRS `
      --kind FileStorage `
      --min-tls-version TLS1_2 `
      --subnet $SUBNETIDAVD `
      --https-only `
      --default-action deny `
      --allow-blob-public-access false `
      --allow-cross-tenant-replication false `
      --only-show-errors | Out-Null

    Write-Host "Adding access to ACI subnet"
    az storage account network-rule add `
      --resource-group $resourceGroupName `
      --account-name $sqlStorageName `
      --subnet $SUBNETIDACI `
      --only-show-errors | Out-Null

    $STORAGE_KEY=$(az storage account keys list --resource-group $resourceGroupName --account-name $sqlStorageName --query "[0].value" --output tsv --only-show-errors)

    Write-Host "Create the file share: $ACI_PERS_SHARE_NAME for MSSQL databases on $sqlStorageName"
    az storage share create `
      --name $ACI_PERS_SHARE_NAME `
      --account-name $sqlStorageName `
      --account-key $STORAGE_KEY `
      --only-show-errors | Out-Null
  } else {
    $STORAGE_KEY=$(az storage account keys list --resource-group $resourceGroupName --account-name $sqlStorageName --query "[0].value" --output tsv --only-show-errors)
  }

  $yml = Get-Content $ymlFile -Raw
  $yml = $yml.Replace("##SA_PASSWORD##", $SA_PASSWORD)
  $yml = $yml.Replace("##containergroupname##", $sqlServerName)
  $yml = $yml.Replace("##storageAccountName##", $sqlStorageName)
  $yml = $yml.Replace("##storageAccountKey##", $STORAGE_KEY)
  $yml = $yml.Replace("##shareName##", $ACI_PERS_SHARE_NAME)
  $yml = $yml.Replace("##cpu##", $ACI_PERS_CPU)
  $yml = $yml.Replace("##memoryInGb##", $ACI_PERS_MEMORYINGB)
  $yml = $yml.Replace("##SUBNETIDACI##", $SUBNETIDACI)
  Set-Content -Encoding ASCII -Path "$($env:TEMP)\$ymlFile" -Value $yml

  $exists = az container list --resource-group $resourceGroupName --query "[?name=='$sqlServerName'].{name:name,id:id}" --output tsv
  if (!$exists) {

    Write-Host "Creating SQL Server 2022: $sqlServerName (this will take 2-5 minutes..)"
    az container create --resource-group $resourceGroupName --file "$($env:TEMP)\$ymlFile" | Out-Null

  } else {

    $state = az container show --resource-group $resourceGroupName --name $sqlServerName --query "instanceView.state" --output tsv

    $cpu, $memoryInGb = Get-SqlServerResources -resourceGroupName $resourceGroupName -name $name
    if ($cpu -ne $ACI_PERS_CPU -or $memoryInGb -ne $ACI_PERS_MEMORYINGB) {

      Write-Host "SQL Server 2022: $sqlServerName, cpu or memory requirements have changed"
      if ($state -ne 'Stopped') {
        Write-Host "SQL Server 2022: $sqlServerName, stopping..."
        az container stop --resource-group $resourceGroupName --name $sqlServerName | Out-Null
      }

      Write-Host "SQL Server 2022: $sqlServerName, deleting..."
      az container delete --resource-group $resourceGroupName --name $sqlServerName  --yes | Out-Null

      Write-Host "Creating SQL Server 2022: $sqlServerName (this will take 2-5 minutes..)"
      az container create --resource-group $resourceGroupName --file "$($env:TEMP)\$ymlFile" | Out-Null
    } else {
      Write-Host "SQL Server 2022: $sqlServerName, has already been set up"

      if ($state -eq 'Stopped') {
        Write-Host "Starting SQL Server 2022 (this will take 2-5 minutes..)"
        az container start --resource-group $resourceGroupName --name $sqlServerName | Out-Null
        Write-Host "Started"
        Write-Host "Please note, it may be a few more minutes before any existing database have been attached and are available to connect."
      } else {
        Write-Host "SQL Server 2022 is already started"
      }
    }
  }
}

function Start-SqlServer {

  param(
    [string]$resourceGroupName,
    [string]$sqlServerName
  )

  $state = az container show --resource-group $resourceGroupName --name $sqlServerName --query "instanceView.state" --output tsv
  if ($state -eq 'Stopped') {
    Write-Host "Starting SQL Server 2022 (this may take a couple of minutes..)"
    az container start --resource-group $resourceGroupName --name $sqlServerName --no-wait | Out-Null
  }
}

function Get-SqlServerResources {

  param(
    [string]$resourceGroupName,
    [string]$sqlServerName
  )

  $exists = (az container list --resource-group $resourceGroupName --query "[?name=='$sqlServerName'].{name:name,id:id}" --output tsv)
  if ($exists) {
    $cpu = az container show --resource-group $resourceGroupName --name $sqlServerName --query "containers[0].resources.requests.cpu" --output tsv
    $memoryInGb = az container show --resource-group $resourceGroupName --name $sqlServerName --query "containers[0].resources.requests.memoryInGb" --output tsv
  } else {
    $cpu = '2.0'
    $memoryInGb = '4.0'
  }
  return $cpu, $memoryInGb
}

function Get-StorageConnectionString {

  param(
    [string]$resourceGroupName,
    [string]$sqlServerName
  )

  $cred = $(az storage account show-connection-string --name $ACI_PERS_STORAGE_ACCOUNT_NAME --resource-group $resourceGroupName --query "connectionString" --output tsv)
  return $cred
}

function Get-SqlServerIP {

  param(
    [string]$resourceGroupName,
    [string]$sqlServerName
  )

  $ip = $(az container show --resource-group $resourceGroupName --name $sqlServerName --query "ipAddress.ip" --output tsv)
  return $ip
}

function Check-RequiredSoftware {
  param(
    [switch]$requiredNode = $true,
    [string]$angularPath = ''
  )
  $up2date = $true
  $psVersion = Check-PSVersion
  $dnVersion = Check-DotnetVersion
  $acVersion = Check-AzureCLIVersion
  $gtVersion = Check-GitVersion
  if ($requiredNode) {
    $ndVersion = Check-NodeVersion -angularPath $angularPath
  } else {
    $ndVersion = $true
  }
  return $psVersion -and $dnVersion -and $acVersion -and $gtVersion -and $ndVersion
}

# Check we are running in PowerShell core and minimum version is installed
function Check-PSVersion {

  # Check for PowerShell Edition
  if ($PSVersionTable.PSEdition -ne 'Core') {
    Write-Host 'Please run this script from PowerShell 7 - not Windows Powershell or Visual Studio Package Manager' -ForegroundColor Yellow
    Write-Host 'PowerShell 7.4.0 - https://github.com/PowerShell/PowerShell/releases/download/v7.4.0/PowerShell-7.4.0-win-x64.msi'
    return $false
  }

  # Check for PowerShell 7.4.x
  $ver = $PSVersionTable.PsVersion
  if ($ver.Major -lt 7 -or ($ver.Major -eq 7 -and $ver.Minor -lt 4)) {
    Write-Host "Please update your version of PowerShell to at least 7.4.x" -ForegroundColor Yellow
    Write-Host 'PowerShell 7.4.0 - https://github.com/PowerShell/PowerShell/releases/download/v7.4.0/PowerShell-7.4.0-win-x64.msi'
    return $false
  }

  return $true
}

# Check minimum DotNet version is installed
function Check-DotnetVersion {
  $verString = (dotnet --version)
  # If you have a preview version of .NET installed the version will come out in a format like "6.0.400-preview.22330.6"
  # To work around this we strip it back to before the "-preview", e.g. "6.0.400".
  $dashPos = $verString.IndexOf("-");
  if($dashPos -gt 0) {
    $verString = $verString.Substring(0, $dashPos)
    Write-Host "Reformatted .NET version string to remove preview: $verString"
  }
  $ver = [version]($verString)
  if ($ver.Major -lt 8) {
    Write-Host "Please install DotNet 8 SDK or higher"
    Write-Host ".NET 8.0.100 SDK - https://download.visualstudio.microsoft.com/download/pr/93961dfb-d1e0-49c8-9230-abcba1ebab5a/811ed1eb63d7652325727720edda26a8/dotnet-sdk-8.0.100-win-x64.exe"
    return $false
  }
  return $true
}

# Check minimum Azure CLI version is installed
function Check-AzureCLIVersion {
  try {
    $verstring = [string](az --version --only-show-errors)
  } catch {}

  if ($verstring -and $verstring -match "azure\-cli[\s]+([0-9\.]+)") {
    $ver = [version]($Matches[1])
    if ($ver.Major -lt 2 -or ($ver.Major -eq 2 -and $ver.Minor -lt 55)) {
      Write-Host "Found Azure CLI version: $ver" -ForegroundColor Yellow
      Write-Host "Please update your version of Azure CLI to at least 2.55.0" -ForegroundColor Yellow
      Write-Host "Download new:"
      Write-Host "Azure CLI (Latest) - https://aka.ms/installazurecliwindows"
      Write-Host "Upgrade existing:"
      Write-Host "az upgrade"
      Write-Host "After installing Azure CLI open a new PowerShell window, before re-running this script"
      return $false
    } else {
      $aiExtension = az extension list --query "[?name=='application-insights'].name" -o tsv
      if (!$aiExtension) {
        az extension add -n application-insights 2>nul
      }
    }
  } else {
    Write-Host "Please install Azure CLI" -ForegroundColor Yellow
    Write-Host "Azure CLI (Latest) - https://aka.ms/installazurecliwindows"
    Write-Host "After installing Azure CLI open a new PowerShell window, before re-running this script"
    return $false
  }
  return $true
}

# Check minimum Git version is installed
function Check-GitVersion {
  $updategit = $true
  try {
    $gitversion = git --version
    if ($gitversion -match 'git version (\d+\.\d+).*') {
      $ver = [version]$matches[1]
      if ($ver.Major -gt 2 -or ($ver.Major -eq 2 -and $ver.Minor -gt 30)) {
        $updategit = $false
      } else {
        Write-Host "Found Git version: $gitversion" -ForegroundColor Yellow
      }
    }
  }
  catch {}

  if ($updategit) {
    Write-Host "Please update your version of Git to at least 2.31.x" -ForegroundColor Yellow
    Write-Host "You can download latest version of Git for Windows from: https://git-scm.com/download/win"
    Write-Host "After updating Git open a new PowerShell window, before re-running this script"
    return $false
  }

  return $true
}

# Check minimum Node version is installed
function Check-NodeVersion {
  param(
    [string]$angularPath
  )

  $updatenode = $true
  try {
    $nodeVer = node --version
    if ($nodeVer -match 'v(\d+\.\d+.\d+)') {
      $ver = [version]$matches[1]
      if ($ver.Major -eq 18 -and ($ver.Minor -gt 19 -or ($ver.Minor -eq 19 -and $ver.Build -ge 1))) {
        $updatenode = $false
      } else {
        Write-Host "Found Node version: $nodeVer" -ForegroundColor Yellow
        if ($ver.Major -gt 18) {
          Write-Host "Your version of Node is newer than the version required for compatibility" -ForegroundColor Yellow
          Write-Host "with all Broking Tech applications and Azure Virtual Desktop" -ForegroundColor Yellow
          Write-Host "You will need to downgrade your Node version to the latest 18.x release" -ForegroundColor Yellow
        }
      }
    }
  } catch {}

  if ($updatenode) {

    $package = Get-Content "$angularPath\\package.json" | ConvertFrom-Json
    $angularCliVersion = $package.devDependencies."@angular/cli"

    Write-Host ""
    Write-Host "Please install / select the latest Node 18.x release" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "If you have any other version of Node installed please remove it from Add or Remove Programs" -ForegroundColor Red
    Write-Host ""
    Write-Host "Download and install Node Version Manager for Windows:"
    Write-Host ""
    Write-Host "https://github.com/coreybutler/nvm-windows/releases/download/1.1.12/nvm-setup.exe"
    Write-Host ""
    Write-Host "Once installed use nvm to install Node 14 and Node 16 side by side:"
    Write-Host ""
    Write-Host "nvm install 18.20.5"
    Write-Host "nvm use 18.20.5"
    Write-Host ""
    Write-Host "Make sure you have angular/cli@$angularCliVersion installed globally"
    Write-Host ""
    Write-Host "Remove existing versions:"
    Write-Host "npm uninstall -g @angular/cli"
    Write-Host "npm cache verify"
    Write-Host ""
    Write-Host "Install specific version:"
    Write-Host "npm install -g @angular/cli@$angularCliVersion"
    Write-Host ""
    Write-Host "After updating Node open a new PowerShell window, before re-running this script" -ForegroundColor Yellow
    Write-Host ""
    return $false
  }

  return $true
}

# Add wtw.crb.common Nuget source if not already present
function Update-NugetConfig {
  $nugetFolder = "$env:APPDATA\NuGet"
  if (!(Test-Path $nugetFolder)) {
    New-Item -ItemType Directory $nugetFolder | Out-Null
  }
  $nugetPath = "$nugetFolder\NuGet.Config"
  $nugetUpdated = $false
  if (Test-Path $nugetPath) {
    [xml] $nugetXml = Get-Content -Path $nugetPath
    if ($nugetXml.configuration.packageSources.add.key -notcontains 'wtw.crb.common') {

      $newSource = $nugetXml.CreateElement('add')
      $newSource.SetAttribute('key','wtw.crb.common')
      $newSource.SetAttribute('value','https://pkgs.dev.azure.com/wtwcrb/Wtw.Crb.Common/_packaging/wtw.crb.common/nuget/v3/index.json')
      $nugetXml.SelectSingleNode('//packageSources').AppendChild($newSource)
      $nugetXml.Save($nugetPath)
      $nugetUpdated = $true
    }
  } else {
    $content = @"
<?xml version="1.0" encoding="utf-8"?>
<configuration>
<packageSources>
    <add key="nuget.org" value="https://api.nuget.org/v3/index.json" protocolVersion="3" />
    <add key="Microsoft Visual Studio Offline Packages" value="C:\Program Files (x86)\Microsoft SDKs\NuGetPackages\" />
    <add key="wtw.crb.common" value="https://pkgs.dev.azure.com/wtwcrb/Wtw.Crb.Common/_packaging/wtw.crb.common/nuget/v3/index.json" />
</packageSources>
<config>
</config>
<packageRestore>
    <add key="enabled" value="True" />
    <add key="automatic" value="True" />
</packageRestore>
<bindingRedirects>
    <add key="skip" value="False" />
</bindingRedirects>
<packageManagement>
    <add key="format" value="0" />
    <add key="disabled" value="False" />
</packageManagement>
</configuration>
"@
    Set-Content -Path $nugetPath -Value $content
    $nugetUpdated = $true
  }

  if ($nugetUpdated) {
    Write-Host "Your Visual Studio Nuget Package Manager configuration has been updated" -ForegroundColor Yellow
    Read-Host "Please restart any instances of Visual Studio and press return"
  }

  # Credential Provider
  if (!(Test-Path "$env:USERPROFILE\.nuget\plugins\netcore\CredentialProvider.Microsoft")) {
    while (Get-NetAdapter | Where-Object { $_.Status -eq 'Up' -and $_.DriverProvider -eq 'PaloAltoNetworks' }) {
      Read-Host "You are currently connected to GlobalProtect - please disconnect and press <return> - you may reconnect after setup has completed"
    }
    $installcredprovider = 'https://raw.githubusercontent.com/microsoft/artifacts-credprovider/master/helpers/installcredprovider.ps1'
    $credscript = Invoke-WebRequest $installcredprovider
    $ScriptBlock = [Scriptblock]::Create($credscript.Content)
    Invoke-Command -ScriptBlock $ScriptBlock
  }
}

# Compare filesystem to SQL project
function Compare-Folders2SqlProject {
  param(
    [string]$sqlProjFile,
    [switch]$silent = $false
  )

  $sqlPath = (Get-Item $sqlProjFile).Directory.FullName

  do {
    $dirs = gci $sqlPath -directory -recurse | Where { (gci $_.fullName).count -eq 0 } | select -expandproperty FullName
    $dirs | Foreach-Object {
      Write-Host "Removing empty folder: $_"
      Remove-Item $_
    }
  } while ($dirs.count -gt 0)

  # Open .sqlproj file
  [xml] $projXml = Get-Content -Path $sqlProjFile

  $nodiffs = $false
  $hasChanged = $false

  while ($nodiffs -eq $false) {

    # Get all folders
    $folders = @()
    foreach ($folder in gci $sqlPath -Recurse -Directory -Exclude '.*') {
      if (!($folder.FullName -like "$sqlPath\bin*" -or $folder.FullName -like "$sqlPath\obj*"  -or $folder.FullName -like "$sqlPath\Properties*" )) {
        $folders += ($folder.FullName).Substring($sqlPath.Length + 1)
      }
    }

    $sqlFolders=@()

    foreach ($element in $projxml.Project.GetElementsByTagName('Folder')) {
      foreach ($include in $element) {
        if ($include.Include.EndsWith('\')) {
          $sqlFolders += $include.Include.Substring(0, $include.Include.Length - 1)
        } elseif ($include.Include -ne 'Properties') {
          $sqlFolders += $include.Include
        }
      }
    }

    # Do we have duplicate entries in SQL project file?
    $dupFolders = $sqlFolders | Group-Object -NoElement | ?{$_.count -gt 1}

    # Do we have different folders in file system compared to SQL project file?
    $diffFolder = diff $folders $sqlFolders

    if ($dupFolders.Name.Count -gt 0) {
      Write-Host "The following folders are duplicate entries in your SQL project:"
      foreach ($dupFolder in $dupFolders.Name) {
        Write-Host "* $dupFolder"
      }
      Write-Host "These MUST be removed before you can create a new migration"
      if (!$silent) {
        Read-Host "Press <enter> to remove duplicates"
      }
      foreach ($dupFolder in $dupFolders.Name) {
        RemoveFromSQLproj -projXml $projXml -tagName 'Folder' -item "$dupFolder"
      }
      $hasChanged = $true

    } elseif ($diffFolder) {
      Write-Host "The folders in your file system and those in your SQL project do match - these issues MUST be corrected before you can add a new migration"

      $folderCmp = Compare-Object -ReferenceObject $folders -DifferenceObject $sqlFolders

      foreach ($folderDiff in $folderCmp) {
        if ($folderDiff.SideIndicator -eq '<=') {
          Write-Host "**Warning** - folder '$($folderDiff.InputObject)' exists on your file system but is missing from SQL project"

          if ($silent) {
            $confirm = 'y'
          } else {
            $confirm = $null
            while ($confirm -ne 'y' -and $confirm -ne 'n') {
              $confirm = Read-Host "Would you like to add it to the SQL Project? (y/n)"
            }
          }
          if ($confirm -eq 'y') {
            AddToSQLproj -projXml $projXml -tagName 'Folder' -item "$($folderDiff.InputObject)"
            $hasChanged = $true
          }
        } else {
          Write-Host "**Warning** - folder '$($folderDiff.InputObject)' exists in your SQL project but is missing from file system"
          if ($silent) {
            $confirm = 'y'
          } else {
            $confirm = $null
            while ($confirm -ne 'y' -and $confirm -ne 'n') {
              $confirm = Read-Host "**Warning** - folder '$($folderDiff.InputObject)' exists in your SQL project but is missing from file system, would you like to remove it from the SQL Project? (y/n)"
            }
          }
          if ($confirm -eq 'y') {
            RemoveFromSQLproj -projXml $projXml -tagName 'Folder' -item "$($folderDiff.InputObject)"
            $hasChanged = $true
          }
        }
      }
    }

    # Get all files
    $files = @()
    foreach ($file in gci $sqlPath\*.sql -Recurse) {
      if (!($file.DirectoryName -like "$sqlPath\bin*" -or $file.DirectoryName -like "$sqlPath\obj*" )) {
        $files += ($file.FullName).Substring($sqlPath.Length + 1)
      }
    }

    $sqlFiles=@()
    $sqlElements = @()
    $sqlElements += $projxml.Project.GetElementsByTagName('Build')
    $sqlElements += $projxml.Project.GetElementsByTagName('None')
    $sqlElements += $projxml.Project.GetElementsByTagName('PostDeploy')
    $sqlElements += $projxml.Project.GetElementsByTagName('PreDeploy')

    foreach ($element in $sqlElements) {
      foreach ($include in $element) {
        if ($include.Include.EndsWith('.sql')) {
          $sqlFiles += $include.Include
        }
      }
    }

    # Do we have duplicate entries in SQL project file?
    $dupFiles = ($sqlFiles | Group-Object -NoElement | ?{$_.count -gt 1})

    # Do we have different files in file system compared to SQL project file?
    $diffFile = diff $files $sqlFiles

    if ($dupFiles.Name.Count -gt 0) {
      Write-Host "The following files are duplicate entries in your SQL project:"
      foreach ($dupFile in $dupFiles.Name) {
        Write-Host "* $dupFile"
      }
      if (!$silent) {
        Read-Host "Press <enter> to remove duplicates"
      }
      foreach ($dupFile in $dupFiles.Name) {
        RemoveFromSQLproj -projXml $projXml -tagName 'File' -item "$dupFile"
      }
      $hasChanged = $true

    } elseif ($diffFile) {
      Write-Host "The files in your file system and those in your SQL project do match - these issues MUST be corrected before you can add a new migration"

      $fileCmp = Compare-Object -ReferenceObject $files -DifferenceObject $sqlFiles

      foreach ($fileDiff in $fileCmp) {
        if ($fileDiff.SideIndicator -eq '<=') {
          Write-Host "**Warning** - file '$($fileDiff.InputObject)' exists on your file system but is missing from SQL project"
          if ($silent) {
            $confirm = 'n'
          } else {
            $confirm = $null
            while ($confirm -ne 'b' -and $confirm -ne 'n' -and $confirm -ne 'i') {
              $confirm = Read-Host "Would you like to add it the SQL Project with build action 'Build' (b), build action 'None' or ignore 'i'? (b/n/i)"
            }
          }
          if ($confirm -eq 'b') {
            # Add file where build action is 'Build'
            AddToSQLproj -projXml $projXml -tagName 'Build' -item "$($fileDiff.InputObject)"
            $hasChanged = $true
          } elseif ($confirm -eq 'n') {
            # Add file where build action is 'None'
            AddToSQLproj -projXml $projXml -tagName 'None' -item "$($fileDiff.InputObject)"
            $hasChanged = $true
          }
        } else {
          Write-Host "**Warning** - file '$($fileDiff.InputObject)' exists in your SQL project but is missing from file system"
          if ($silent) {
            $confirm = 'y'
          } else {
            $confirm = $null
            while ($confirm -ne 'y' -and $confirm -ne 'n') {
              $confirm = Read-Host "Would you like to remove it from the SQL Project? (y/n)"
            }
          }
          if ($confirm -eq 'y') {
            RemoveFromSQLproj -projXml $projXml -tagName 'File' -item "$($fileDiff.InputObject)"
            $hasChanged = $true
          }
        }
      }
    }

    if ($dupFolders.Name.Count -eq 0 -and
        $dupFiles.Name.Count -eq 0 -and
        !$diffFolder -and
        !$diffFile) {

      if ($hasChanged -eq $true) {
        # Save updated SQL project files (will trigger Visual Studio to prompt to reload if open)
        $projXml.save($sqlProjFile)
      }

      $nodiffs = $true
      Write-Host "Your file system and Sql project match"
    }
  }
}

# Adds elements to SQL project file if not already exists
function AddToSQLproj {
  param(
    [xml]$projXml,
    [string]$tagName,
    [string]$item
  )

  $elements = $projxml.Project.GetElementsByTagName($tagName)
  $lastInclude = $null
  $lastElement = $null

  foreach ($element in $elements) {
    foreach ($include in $element) {
      if ($include.Include -eq $item) {
        return
      }
      $lastInclude = $include
    }
    $lastElement = $element
  }

  if ($lastInclude) {
    $newInclude = $lastInclude.Clone()
    $newInclude.Include = $item
    $lastElement.ParentNode.AppendChild($newInclude) | Out-Null
  }
}

# Removes elements from SQL project file if exists
function RemoveFromSQLproj {
  param(
    [xml]$projXml,
    [string]$tagName,
    [string]$item
  )

  if ($tagName -eq 'File') {
    $elements = $projxml.Project.GetElementsByTagName('Build')
    $elements += $projxml.Project.GetElementsByTagName('None')
    $elements += $projxml.Project.GetElementsByTagName('PreDeploy')
    $elements += $projxml.Project.GetElementsByTagName('PostDeploy')
  } else {
    $elements = $projxml.Project.GetElementsByTagName($tagName)
  }

  foreach ($element in $elements) {
    foreach ($include in $element) {

      # Handle trailing slash
      $name = $include.Include
      if ($name.EndsWith('\')) {
        $name = $name.Substring(0, $name.Length -1)
      }

      if ($name -eq $item) {
        $element.ParentNode.RemoveChild($include) | Out-Null
        return
      }
    }
  }
}

# Rewrite SQL from migration batch to resolve issues with
# the SQL automatically generated by DacFx
function RewriteSQL {
  param(
    [string]$sql,
    $model
  )

  $lines = $sql -split '`r`n'
  $sqlupdate = ''

  foreach ($line in $lines) {
    # Replace DROP statements for unnamed constraints which generate syntax errors
    $r = [regex]::new('ALTER TABLE \[([^\]]+)\]\.\[([^\]]+)\] DROP CONSTRAINT ;')
    $m = $r.Match($line.Trim())
    if ($m.Success -and $m.Groups.Count -eq 3) {
      $line = "EXEC [devops].[DropUnnamedConstraints] @schemaName = '$($m.Groups[1])', @tableName = '$($m.Groups[2])'"
    }

    if ($model) {
      # Replace DROP statements for TABLES with temporal history enabled
      $r = [regex]::new('DROP TABLE \[([^\]]+)\]\.\[([^\]]+)\];')
      $m = $r.Match($line.Trim())
      if ($m.Success -and $m.Groups.Count -eq 3) {
        $schemaName = $m.Groups[1]
        $tableName = $m.Groups[2]
        $oi = New-Object -TypeName Microsoft.SqlServer.Dac.Model.ObjectIdentifier($schemaName, $tableName)
        $table = $model.GetObject([Microsoft.SqlServer.Dac.Model.Table]::TypeClass, $oi, [Microsoft.SqlServer.Dac.Model.DacQueryScopes]::UserDefined)
        if ($table -and ($table.GetReferencedRelationshipInstances().Relationship | Where-Object { $_.Name -eq 'TemporalSystemVersioningHistoryTable' })) {
          $line = "ALTER TABLE [$schemaName].[$tableName] SET ( SYSTEM_VERSIONING = OFF)`r`nGO" + $line
        }
      }
    }

    $sqlupdate += "$line`r`n"
  }

  return $sqlupdate.Trim() + "`r`nGO`r`n"
}

# Generates a unique name for the migration
function Get-MigrationName {

  $branchname = &git branch --show-current
  $name = $branchname.Substring($branchname.LastIndexOf('/') + 1) -Replace '[^a-zA-Z0-9_\-]'

  return $name
}

# Get authentication headers for Azure DevOps REST call
function Get-AuthHeaders {
  param(
    [string]$PAT
  )

  # Generate security headers to connect to Azure DevOps REST API
  $base64AuthInfo = [Convert]::ToBase64String([Text.Encoding]::ASCII.GetBytes(":$PAT"))
  $headers = @{ Authorization="Basic $base64AuthInfo" }

  return $headers
}

# Get Azure DevOps info
function Execute-AzureDevOpsRest {
  param(
    $headers,
    $uri,
    $organisation,
    $method,
    $body = $null,
    $contentType = 'application/json'
  )

  $success = $false
  while (!$success) {
    try {
      $environments = Invoke-RestMethod -Method $method -ContentType $contentType -Uri $uri -Headers $headers -Body $body
      return $environments
    } catch {
      Write-Host $Error[0]
      Write-Host ""
      Write-Host "*** Error: your PAT token has insufficient permissions - an additional permission (Tokens - Read & Manage) is now required ***"
      Write-Host ""
      Write-Host "Please go to https://dev.azure.com/$organisation/_usersSettings/tokens and verify that your PAT token"
      Write-Host "for SqlMigrations has been granted the following permissions:"
      Write-Host "Build (Read)"
      Write-Host "Tokens (Read & Manage)"
      Write-Host "Packaging (Read)"
      Read-Host "Press <enter> to retry"
    }
  }
}

# Get Branch name and parentId (if applicable)
function Get-Branch {

  $branchname = &git branch --show-current
  $branch = $branchname.Substring($branchname.IndexOf('/') + 1) -Replace '[^a-zA-Z0-9_\-]'

  $r = [regex]::new('(.+[^/])/(\d+[^\-])\-.+From[_\-](\d+)$')
  $m = $r.Match($branchname)

  if ($m.Success -and $m.Groups.Count -eq 4) {
    return $branch, $($m.Groups[3].Value)
  }

  return $branch, $null
}

# Show build details
function Show-Build {
  param(
    $build,
    $organisation,
    $project
  )

  # Skip if there is no build information
  if (!$build -or !$build.buildNumber -or !$build.id) {
    return
  }

  Write-Host "Selected build to compare schema against:"
  Write-Host "  Name: $($build.buildNumber)"
  Write-Host "  BuildId: $($build.id)"
  Write-Host "  Url: https://dev.azure.com/$organisation/$project/_build/results?buildId=$($build.id)&view=results"
  Write-Host "  For: $($build.requestedFor.displayName)"
  Write-Host "  Branch: $($build.sourceBranch)"
  Write-Host "  Date: $($build.queueTime)"
}

# Get Build Artefacts
function Get-BuildArtefacts {
  param(
    $builds,
    $headers,
    $organisation,
    $project,
    $artefactName
  )

  $lastBuild = $null
  $lastDeployHash = $null
  $lastBuildArtefacts = $null

  foreach ($build in $builds) {

    Write-Host "Checking: $($build.buildNumber).."
    $uri = "https://dev.azure.com/$organisation/$project/_apis/build/builds/$($build.Id)/artifacts?api-version=6.1-preview.5"
    $buildArtefacts = $null

    while (!$buildArtefacts) {

      $buildArtefacts = Get-AzureDevOpsRest -headers $headers -uri $uri -organisation $organisation

      if (($buildArtefacts.value | Where-Object { $_.name -eq $artefactName }).Count -ne 1) {

        $buildArtefacts = $null

        if ($lastBuild) {
          Write-Host "This build is missing '$artefactName' artefact, skipping"
          break
        } else {
          Show-Build -build $build -organisation $organisation -project $project

          Write-Host "This build is missing either '$artefactName' artefact which indicates the build is either in progress, failed or was cancelled"
          Write-Host "In order to reliably select the correct build to compare with, this build needs to have completed the 'build' stage"
          Write-Host "Please check the 'build' stage of this pipeline, ideally you should wait for this stage to complete or retry it, if cancelled"
          Write-Host ""

          $confirm = Read-Host "Do you want to skip (s) or retry (r)"
          Write-Host ""
          if ($confirm -eq 's') {
            break
          }
        }
      } else {

        # This build has a valid artefact
        $deployDatabaseArtefact = ($buildArtefacts.value | Where-Object { $_.name -eq $artefactName }).resource
        $data = $deployDatabaseArtefact.data

        if (!$lastBuild) {
          # This is the first build with valid artefact we have found
          $lastBuild = $build
          $lastDeployHash = $data
          $lastBuildArtefacts = $buildArtefacts
        } else {
          # Check to see if the artefact has same hash as previous build
          if ($lastDeployHash -eq $data) {
            # Use the older build as the database schema has not changed
            $lastBuild = $build
            $lastBuildArtefacts = $buildArtefacts
          } else {
            # Previous build is different so we have found our build to return
            Show-Build -build $lastBuild -organisation $organisation -project $project
            return $lastBuild, $lastBuildArtefacts
          }
        }
      }
    }
  }

  # Use the last build we found if there are no more with artefacts
  if (!$buildArtefacts) {
    Show-Build -build $lastBuild -organisation $organisation -project $project
    return $lastBuild, $lastBuildArtefacts
  } else {
    Show-Build -build $build -organisation $organisation -project $project
    return $build, $buildArtefacts
  }
}

# Generates an access token for connecting to Azure resources
function Connect-AzureDevicelogin {
  [cmdletbinding()]
  param(
    [Parameter()]
    $ClientID = '1950a258-227b-4e31-a9cf-717495945fc2',

    [Parameter()]
    [switch]$Interactive,

    [Parameter()]
    $TenantID = 'common',

    [Parameter()]
    $Resource = "https://graph.microsoft.com/",

    # Timeout in seconds to wait for user to complete sign in process
    [Parameter(DontShow)]
    $Timeout = 300
  )
  try {
    $DeviceCodeRequestParams = @{
      Method = 'POST'
      Uri  = "https://login.microsoftonline.com/$TenantID/oauth2/devicecode"
      Body   = @{
        resource  = $Resource
        client_id = $ClientId
      }
    }
    $DeviceCodeRequest = Invoke-RestMethod @DeviceCodeRequestParams

    if ($Interactive.IsPresent) {
      Write-Host 'Trying to open a browser with login prompt. Please sign in.'
      Start-Sleep -Second 1
      $PostParameters = @{otc = $DeviceCodeRequest.user_code }
      $InputFields = foreach ($entry in $PostParameters.GetEnumerator()) {
        "<input type=`"hidden`" name=`"$($entry.Name)`" value=`"$($entry.Value)`">"
      }
      $PostUrl = "https://login.microsoftonline.com/common/oauth2/deviceauth"
      $LocalHTML = @"
    <!DOCTYPE html>
<html>
 <head>
  <title>&hellip;</title>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
  <script type="text/javascript">
   function dosubmit() { document.forms[0].submit(); }
  </script>
 </head>
 <body onload="dosubmit();">
  <form action="$PostUrl" method="POST" accept-charset="utf-8">
   $InputFields
  </form>
 </body>
</html>
"@
      $TempPage = New-TemporaryFile
      $TempPage = Rename-Item -Path $TempPage.FullName ($TempPage.FullName -replace '$', '.html') -PassThru
      Out-File -FilePath $TempPage.FullName -InputObject $LocalHTML
      Start-Process $TempPage.FullName
    }
    else {
      Write-Host $DeviceCodeRequest.message
    }

    $TokenRequestParams = @{
      Method = 'POST'
      Uri  = "https://login.microsoftonline.com/$TenantId/oauth2/token"
      Body   = @{
        grant_type = "urn:ietf:params:oauth:grant-type:device_code"
        code     = $DeviceCodeRequest.device_code
        client_id  = $ClientId
      }
    }
    $TimeoutTimer = [System.Diagnostics.Stopwatch]::StartNew()
    while ([string]::IsNullOrEmpty($TokenRequest.access_token)) {
      if ($TimeoutTimer.Elapsed.TotalSeconds -gt $Timeout) {
        throw 'Login timed out, please try again.'
      }
      $TokenRequest = try {
        Invoke-RestMethod @TokenRequestParams -ErrorAction Stop
      }
      catch {
        $Message = $_.ErrorDetails.Message | ConvertFrom-Json
        if ($Message.error -ne "authorization_pending") {
          throw
        }
      }
      Start-Sleep -Seconds 1
    }
    return $TokenRequest.access_token
  }
  finally {
    try {
      Remove-Item -Path $TempPage.FullName -Force -ErrorAction Stop
      $TimeoutTimer.Stop()
    }
    catch {
      # We don't care about errors here
    }
  }
}

function Get-SqlPromptFormatter {
    $sqlPromptBasePath = "C:\Program Files (x86)\Red Gate\"
    $sqlPromptExeName = "SqlPrompt.Format.CommandLine.exe"
    $sqlPromptDirs = Get-ChildItem -Path $sqlPromptBasePath -Directory | Where-Object { $_.Name -like "SQL Prompt *" } | Sort-Object Name -Descending
    foreach ($dir in $sqlPromptDirs) {
        $candidate = Join-Path $dir.FullName $sqlPromptExeName
        if (Test-Path $candidate) {
            return $candidate
        }
    }
    return $null
}