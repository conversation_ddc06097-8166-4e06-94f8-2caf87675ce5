#
# deploy.Merge.ps1
#
# Merges Trunk (or specified branch) into current (or specified) branch
param (
  [string]$branch,
  [string]$sourceBranch = 'trunk',
  [switch]$clean
)

Push-Location
$workingDir = $PSScriptRoot
Set-Location $workingDir

. .\deploy.Functions.ps1

if (Test-Path '.\deploy.Sql-Classes.ps1') {
  Load-SqlDom
  Clean-StdOutErr
  . .\deploy.Sql-Classes.ps1
}

if (!(Check-PSVersion)) {
  Pop-Location
  return
}

if ($psversiontable.PSEdition -eq 'Desktop') {
  Pop-Location
  Write-Host 'Please re-run this using PowerShell 7 not Windows PowerShell'
  return
}

if (!(Test-Path 'C:\Program Files\Git\usr\bin\file.exe')) {
  Pop-Location
  Write-Host 'Git for Windows is not installed to "C:\Program Files\Git", exiting'
  return
}

$config = gc .\deploy.Config.json | ConvertFrom-Json

$env:GIT_REDIRECT_STDERR = '2>&1'

# Checking any Uncommitted local changes present
$changes = git status --porcelain

if ($changes) {
  Write-Host "Below changes are uncommited :"
  $changes

  $confirm = Read-Host "You must first commit these changes, would you like to do this now? (y/n)"
  if ($confirm -ne 'y') {
    Write-Host "Aborting due to uncommitted changes"
    Pop-Location
    return
  }

  # Commit changes
  git add .
  $comments = Read-Host "Commit comments"
  git commit -a -m $comments
  if ($clean -and $branch) {
    # Push before switching branches if we are going to clean branches
    git push
  }
}

git fetch
Write-Host "Fetch latest from origin/$sourceBranch"
git fetch origin "$sourceBranch":"$sourceBranch"

if ($branch) {
  git checkout $branch
} else {
  $branch = &git branch --show-current
}
git pull

if ($clean) {
  $branches2clean = $($(git branch).Trim() | ForEach-Object { if ($_ -notin ($sourceBranch, 'trunk') -and !$_.StartsWith('* ')) { $_ }})
  if ($branches2clean) {
    git branch -D $branches2clean
  }
}

Write-Host "Merge $sourceBranch into $branch"
git merge "origin/$sourceBranch" -m "merging $sourceBranch" --no-edit

Read-Host 'Please verify merge has completed, then press <enter> to continue'

$sqlProject = $config.sqlProject

if ($sqlProject) {
  Write-Host 'Checking SQL project..'
  $sqlProjFile = "$workingDir\$($config.sqlProjectPath)\$($config.sqlProject).sqlproj"
  Compare-Folders2SqlProject -sqlProjFile $sqlProjFile
}

    $sqlPromptFormatter = Get-SqlPromptFormatter
    if (-not $sqlPromptFormatter) {
        Write-Host "Warning - SQL Prompt is not installed or not found"
        $useSqlPrompt = $false
    } else {
        $useSqlPrompt = $true
    }
$sqlPromptSchemaPath = "$workingDir\SqlPrompt-Schema.json"
$sqlPromptScriptsPath = "$workingDir\SqlPrompt-Scripts.json"
$sqlProjectPath = "$workingDir\$($config.sqlProjectPath)"
$useSqlPrompt = $false

if ((Test-Path $sqlPromptFormatter) -and (Test-Path $sqlPromptSchemaPath) -and (Test-Path $sqlPromptScriptsPath)) {
  $useSqlPrompt = $true
  $excludeDirs = $config.sqlPromptExcludeDirs.Split('|') | Foreach-Object { "$workingDir\$($config.sqlProjectPath)\$_" }
  $includeDirs = $config.sqlPromptIncludeDirs.Split('|') | Foreach-Object { "$workingDir\$($config.sqlProjectPath)\$_" }
} elseif ($sqlProject) {
  Write-Host "Warning - you do not have SQL Prompt installed and/or SQL Prompt is not configured in deploy.Config.json"
}

git diff --name-status $sourceBranch > diff.txt
$badFiles = @()
Get-Content .\diff.txt | ForEach-Object {
  if ($_ -match '^(.)\t(.+)') {
    if (($Matches[1] -eq 'M' -or $Matches[1] -eq 'A') -and $Matches[2].EndsWith('.sql')) {
      $file = Get-Item $Matches[2]
      $fullName = $file.FullName
      if ($fullName.StartsWith("$sqlProjectPath\")) {
        Write-Host "Encoding.. $($fullName.Substring($pwd.Path.Length+1))"
        $encodingBefore = file.exe --mime-encoding $fullName --brief

        # Check file encoding
        if ($encodingBefore -notin @('utf-8','us-ascii')) {
          Move-Item $fullName "$($fullName).bak"
          iconv.exe --silent --from-code=$encodingBefore --to-code=utf-8 "$($fullName).bak" > $fullName
          $encodingAfter = file.exe --mime-encoding $fullName --brief
          if ($encodingAfter -notin @('utf-8','us-ascii')) {
            Remove-Item $fullName -Force
            Move-Item "$($fullName).bak" $fullName
            Write-Host "Error converting $encodingBefore : $($fullName.Substring($pwd.Path.Length + 1))"
          } else {
            Remove-Item "$($fullName).bak" -Force
            Write-Host "Converted $encodingBefore to $encodingAfter : $($fullName.Substring($pwd.Path.Length + 1))"
          }
        }

        # SqlPrompt Formatting
        if ($useSqlPrompt -and $true -notin ($excludeDirs | Foreach-Object { $fullName.StartsWith($_) })) {
          if (!(Sql-Prompt -fullName $fullName -formatPath $sqlPromptSchemaPath)) {
            Write-Host "Sql-Prompt failed processing $($fullName.Substring($pwd.Path.Length + 1))" -ForegroundColor Red
            return
          }
        } elseif ($useSqlPrompt -and $true -in ($includeDirs | Foreach-Object { $fullName.StartsWith($_) })) {
          if (!(Sql-Prompt -fullName $fullName -formatPath $sqlPromptScriptsPath)) {
            Write-Host "Sql-Prompt failed processing $($fullName.Substring($pwd.Path.Length + 1))" -ForegroundColor Red
            return
          }
          if (!(Sql-Sort -fullName $fullName)) {
            Write-Host "Sql-Sort failed processing $($fullName.Substring($pwd.Path.Length + 1))" -ForegroundColor Red
            return
          }
        }

        $badEncoding = Get-Content $fullName | Where-Object { $_ -match "[�]" }
        if ($badEncoding.Count -gt 0) {
          $badFiles += $fullName
          Write-Host "Bad encoding found in $($fullName.Substring($pwd.Path.Length + 1))" -ForegroundColor Red
        }
      }
    }
  }
}

if (Test-Path 'diff.txt') {
  Remove-Item diff.txt | Out-Null
}

if ($badFiles.Count -gt 0) {
  Write-Host "Aborting due to bad encoding found in one or more SQL files"
  Pop-Location
  return
}

git add .
git commit -a -m "merging $sourceBranch"
git branch --set-upstream-to=origin/$branch $branch
git push

Pop-Location