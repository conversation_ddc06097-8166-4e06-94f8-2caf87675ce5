#
# deploy.Sql-Format.ps1
#
# Applies SqlPrompt style formatting to all sql files
param (
  [switch]$skipSchema,
  [switch]$skipScripts
)

cd $PSScriptRoot
$workingDir = $pwd.Path
Push-Location $workingDir

. .\deploy.Functions.ps1
Clean-StdOutErr
$config = gc .\deploy.Config.json | ConvertFrom-Json

Write-Host 'Formatting SQL files..'

$sqlPromptFormatter = Get-SqlPromptFormatter
if (-not $sqlPromptFormatter) {
    Write-Host "Warning - SQL Prompt is not installed or not found"
    $useSqlPrompt = $false
  } else {
    $useSqlPrompt = $true
  }
$sqlProjectPath = "$workingDir\$($config.sqlProjectPath)"
$sqlPromptSchemaPath = "$workingDir\SqlPrompt-Schema.json"
$sqlPromptScriptsPath = "$workingDir\SqlPrompt-Scripts.json"

if ((Test-Path $sqlPromptFormatter) -and (Test-Path $sqlPromptSchemaPath) -and (Test-Path $sqlPromptScriptsPath)) {
  $excludeDirs = $config.sqlPromptExcludeDirs.Split('|') | Foreach-Object { "$workingDir\$($config.sqlProjectPath)\$_" }
  $includeDirs = $config.sqlPromptIncludeDirs.Split('|') | Foreach-Object { "$workingDir\$($config.sqlProjectPath)\$_" }

  Get-ChildItem -Path $sqlProjectPath -Recurse -Include *.sql | ForEach-Object {
    $fullName = $_.FullName
    if (!$skipSchema -and $true -notin ($excludeDirs | Foreach-Object { $fullName.StartsWith($_) })) {
      if (!(Sql-Prompt -fullName $fullName -formatPath $sqlPromptSchemaPath)) {
        Write-Host "SqlPrompt failed processing $($fullName.Substring($pwd.Path.Length + 1))" -ForegroundColor Red
      }
    } elseif (!$skipScripts -and $true -in ($includeDirs | Foreach-Object { $fullName.StartsWith($_) })) {
      if (!(Sql-Prompt -fullName $fullName -formatPath $sqlPromptScriptsPath)) {
        Write-Host "SqlPrompt failed processing $($fullName.Substring($pwd.Path.Length + 1))" -ForegroundColor Red
      }
    }
  }

  # These three commands undo any file changes that are whitespace only
  git diff -U0 -w --no-color | git apply --cached --ignore-whitespace --unidiff-zero -
  git restore .
  git restore --staged .
} else {
  Write-Host "Warning - you do not have SQL Prompt installed and/or SQL Prompt is not configured in deploy.Config.json"
}
Pop-Location