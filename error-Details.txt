Local time	Type	Details
08:47:49.699	Request	Name: RefreshAnalysisServer, Successful request: true, Response time: 58.5 mins
08:47:49.721	Trace	Severity level: Information, Message: Message received from the service bus: {"RefreshType":"Full","DatabaseName":"GeneralAnalytics_AS_TAB","InstanceLogId":2520,"ProcessSessionId":202506170000000429}
08:47:49.739	Trace	Severity level: Information, Message: DefaultAzureCredential.GetToken invoked. Scopes: [ https://westeurope.asazure.windows.net/.default ] ParentRequestId:
08:47:49.739	Trace	Severity level: Information, Message: ManagedIdentityCredential.GetToken invoked. Scopes: [ https://westeurope.asazure.windows.net/.default ] ParentRequestId:
08:47:49.739	Trace	Severity level: Information, Message: ManagedIdentitySource TokenExchangeManagedIdentitySource was attempted. IsSelected=False.
08:47:49.739	Trace	Severity level: Information, Message: False MSAL ******** MSAL.NetCore .NET 8.0.16 Microsoft Windows 10.0.20348 [2025-06-17 07:47:49Z - f06237b6-eb38-42d8-b79a-36a42531132e] MSAL MSAL.NetCore with assembly version '********'. CorrelationId(f06237b6-eb38-42d8-b79a-36a42531132e)
08:47:49.739	Trace	Severity level: Information, Message: False MSAL ******** MSAL.NetCore .NET 8.0.16 Microsoft Windows 10.0.20348 [2025-06-17 07:47:49Z - f06237b6-eb38-42d8-b79a-36a42531132e] === AcquireTokenForManagedIdentityParameters ===
ForceRefresh: False
Resource: https://westeurope.asazure.windows.net
08:47:49.740	Trace	Severity level: Information, Message: False MSAL ******** MSAL.NetCore .NET 8.0.16 Microsoft Windows 10.0.20348 [2025-06-17 07:47:49Z - f06237b6-eb38-42d8-b79a-36a42531132e] 
=== Request Data ===
Authority Provided? - True
Scopes - https://westeurope.asazure.windows.net
Extra Query Params Keys (space separated) - 
ApiId - AcquireTokenForSystemAssignedManagedIdentity
IsConfidentialClient - False
SendX5C - False
LoginHint ? False
IsBrokerConfigured - False
HomeAccountId - False
CorrelationId - f06237b6-eb38-42d8-b79a-36a42531132e
UserAssertion set: False
LongRunningOboCacheKey set: False
Region configured:
08:47:49.740	Trace	Severity level: Information, Message: False MSAL ******** MSAL.NetCore .NET 8.0.16 Microsoft Windows 10.0.20348 [2025-06-17 07:47:49Z - f06237b6-eb38-42d8-b79a-36a42531132e] === Token Acquisition (ManagedIdentityAuthRequest) started:
	 Scopes: https://westeurope.asazure.windows.net
	Authority Host: login.microsoftonline.com
08:47:49.740	Trace	Severity level: Information, Message: False MSAL ******** MSAL.NetCore .NET 8.0.16 Microsoft Windows 10.0.20348 [2025-06-17 07:47:49Z - f06237b6-eb38-42d8-b79a-36a42531132e] [Internal cache] Total number of cache partitions found while getting access tokens: 1
08:47:49.740	Trace	Severity level: Information, Message: False MSAL ******** MSAL.NetCore .NET 8.0.16 Microsoft Windows 10.0.20348 [2025-06-17 07:47:49Z - f06237b6-eb38-42d8-b79a-36a42531132e] [FindAccessTokenAsync] Discovered 4 access tokens in cache using partition key: system_assigned_managed_identity_managed_identity_AppTokenCache
08:47:49.741	Trace	Severity level: Information, Message: False MSAL ******** MSAL.NetCore .NET 8.0.16 Microsoft Windows 10.0.20348 [2025-06-17 07:47:49Z - f06237b6-eb38-42d8-b79a-36a42531132e] [Region discovery] Not using a regional authority.
08:47:49.741	Trace	Severity level: Information, Message: False MSAL ******** MSAL.NetCore .NET 8.0.16 Microsoft Windows 10.0.20348 [2025-06-17 07:47:49Z - f06237b6-eb38-42d8-b79a-36a42531132e] Skipping Instance discovery for Aad authority because it is not enabled.
08:47:49.748	Trace	Severity level: Information, Message: False MSAL ******** MSAL.NetCore .NET 8.0.16 Microsoft Windows 10.0.20348 [2025-06-17 07:47:49Z - f06237b6-eb38-42d8-b79a-36a42531132e] Access token is not expired. Returning the found cache entry. [Current time (06/17/2025 07:47:49) - Expiration Time (06/18/2025 07:47:44 +00:00) - Extended Expiration Time (06/17/2025 07:47:45 +00:00)]
08:47:49.748	Trace	Severity level: Information, Message: False MSAL ******** MSAL.NetCore .NET 8.0.16 Microsoft Windows 10.0.20348 [2025-06-17 07:47:49Z - f06237b6-eb38-42d8-b79a-36a42531132e] [ManagedIdentityRequest] Access token retrieved from cache.
08:47:49.748	Trace	Severity level: Information, Message: False MSAL ******** MSAL.NetCore .NET 8.0.16 Microsoft Windows 10.0.20348 [2025-06-17 07:47:49Z - f06237b6-eb38-42d8-b79a-36a42531132e] 
	=== Token Acquisition finished successfully:
08:47:49.748	Trace	Severity level: Information, Message: False MSAL ******** MSAL.NetCore .NET 8.0.16 Microsoft Windows 10.0.20348 [2025-06-17 07:47:49Z - f06237b6-eb38-42d8-b79a-36a42531132e]  AT expiration time: 6/18/2025 7:47:44 AM +00:00, scopes: https://westeurope.asazure.windows.net. source: Cache
08:47:49.748	Trace	Severity level: Information, Message: False MSAL ******** MSAL.NetCore .NET 8.0.16 Microsoft Windows 10.0.20348 [2025-06-17 07:47:49Z - f06237b6-eb38-42d8-b79a-36a42531132e] 
[LogMetricsFromAuthResult] Cache Refresh Reason: NotApplicable
[LogMetricsFromAuthResult] DurationInCacheInMs: 0
[LogMetricsFromAuthResult] DurationTotalInMs: 8
[LogMetricsFromAuthResult] DurationInHttpInMs: 0
08:47:49.748	Trace	Severity level: Information, Message: False MSAL ******** MSAL.NetCore .NET 8.0.16 Microsoft Windows 10.0.20348 [2025-06-17 07:47:49Z - f06237b6-eb38-42d8-b79a-36a42531132e] TokenEndpoint: ****
08:47:49.748	Trace	Severity level: Information, Message: ManagedIdentityCredential.GetToken succeeded. Scopes: [ https://westeurope.asazure.windows.net/.default ] ParentRequestId:  ExpiresOn: 2025-06-18T07:47:44.3318705+00:00
08:47:49.748	Trace	Severity level: Information, Message: DefaultAzureCredential credential selected: Azure.Identity.ManagedIdentityCredential
08:47:49.748	Trace	Severity level: Information, Message: DefaultAzureCredential.GetToken succeeded. Scopes: [ https://westeurope.asazure.windows.net/.default ] ParentRequestId:  ExpiresOn: 2025-06-18T07:47:44.3318705+00:00
08:47:50.837	Trace	Severity level: Information, Message: InstanceLogID: 2520; ProcessSessionId:202506170000000429;Message:Token generation Successfull
08:47:52.925	Trace	Severity level: Information, Message: InstanceLogID: 2520; ProcessSessionId:202506170000000429;ModelName:crbbropsem20qssas(GeneralAnalytics_AS_TAB);Message:SSAS Server Connected
08:47:52.925	Trace	Severity level: Information, Message: InstanceLogID: 2520; ProcessSessionId:202506170000000429;ModelName:crbbropsem20qssas(GeneralAnalytics_AS_TAB);RefreshType:Full
08:47:52.931	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:AuditUser;Message:Process Start(1 of 79)
08:47:59.827	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:AuditUser;Message:Process End(1 of 79);Status:Success;ProcessingTime:6901 ms
08:47:59.827	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:AuditUserContract;Message:Process Start(2 of 79)
08:48:05.213	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:AuditUserContract;Message:Process End(2 of 79);Status:Success;ProcessingTime:5339 ms
08:48:05.213	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:AuditUserCR;Message:Process Start(3 of 79)
08:48:08.107	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:AuditUserCR;Message:Process End(3 of 79);Status:Success;ProcessingTime:2894 ms
08:48:08.108	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:AuditUserMR;Message:Process Start(4 of 79)
08:48:11.039	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:AuditUserMR;Message:Process End(4 of 79);Status:Success;ProcessingTime:2932 ms
08:48:11.040	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:AuditUserPlacement;Message:Process Start(5 of 79)
08:48:13.737	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:AuditUserPlacement;Message:Process End(5 of 79);Status:Success;ProcessingTime:2698 ms
08:48:13.738	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:AuditUserSubmission;Message:Process Start(6 of 79)
08:48:16.484	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:AuditUserSubmission;Message:Process End(6 of 79);Status:Success;ProcessingTime:2746 ms
08:48:16.484	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:Bands;Message:Process Start(7 of 79)
08:48:20.839	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:Bands;Message:Process End(7 of 79);Status:Success;ProcessingTime:4354 ms
08:48:20.839	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:Brazil Client;Message:Process Start(8 of 79)
08:48:40.923	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:Brazil Client;Message:Process End(8 of 79);Status:Success;ProcessingTime:20083 ms
08:48:40.923	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:Carrier Submission;Message:Process Start(9 of 79)
08:49:00.458	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:Carrier Submission;Message:Process End(9 of 79);Status:Success;ProcessingTime:19533 ms
08:49:00.458	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:CarrierReconciliation;Message:Process Start(10 of 79)
08:49:17.101	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:CarrierReconciliation;Message:Process End(10 of 79);Status:Success;ProcessingTime:16636 ms
08:49:17.102	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:Carriers;Message:Process Start(11 of 79)
08:49:24.217	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:Carriers;Message:Process End(11 of 79);Status:Success;ProcessingTime:7115 ms
08:49:24.217	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:ClassOfBusiness;Message:Process Start(12 of 79)
08:49:32.963	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:ClassOfBusiness;Message:Process End(12 of 79);Status:Success;ProcessingTime:8745 ms
08:49:32.963	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:Contract;Message:Process Start(13 of 79)
08:49:37.277	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:Contract;Message:Process End(13 of 79);Status:Success;ProcessingTime:4313 ms
08:49:37.277	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:ContractClausesAndConditions;Message:Process Start(14 of 79)
08:49:42.292	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:ContractClausesAndConditions;Message:Process End(14 of 79);Status:Success;ProcessingTime:5015 ms
08:49:42.292	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:ContractCreateDate;Message:Process Start(15 of 79)
08:49:45.201	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:ContractCreateDate;Message:Process End(15 of 79);Status:Success;ProcessingTime:2908 ms
08:49:45.202	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:ContractDocument;Message:Process Start(16 of 79)
08:49:48.837	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:ContractDocument;Message:Process End(16 of 79);Status:Success;ProcessingTime:3635 ms
08:49:48.837	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:ContractElementAttributes;Message:Process Start(17 of 79)
08:49:52.573	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:ContractElementAttributes;Message:Process End(17 of 79);Status:Success;ProcessingTime:3730 ms
08:49:52.573	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:ContractEndorsement;Message:Process Start(18 of 79)
08:49:55.834	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:ContractEndorsement;Message:Process End(18 of 79);Status:Success;ProcessingTime:3260 ms
08:49:55.834	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:ContractEndorsementCreatedDate;Message:Process Start(19 of 79)
08:49:58.733	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:ContractEndorsementCreatedDate;Message:Process End(19 of 79);Status:Success;ProcessingTime:2899 ms
08:49:58.733	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:ContractExpiryDate;Message:Process Start(20 of 79)
08:50:02.140	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:ContractExpiryDate;Message:Process End(20 of 79);Status:Success;ProcessingTime:3407 ms
08:50:02.140	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:ContractHeader;Message:Process Start(21 of 79)
08:50:13.369	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:ContractHeader;Message:Process End(21 of 79);Status:Success;ProcessingTime:11228 ms
08:50:13.369	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:ContractInceptionDate;Message:Process Start(22 of 79)
08:50:16.673	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:ContractInceptionDate;Message:Process End(22 of 79);Status:Success;ProcessingTime:3304 ms
08:50:16.673	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:ContractLimits;Message:Process Start(23 of 79)
08:50:19.947	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:ContractLimits;Message:Process End(23 of 79);Status:Success;ProcessingTime:3273 ms
08:50:19.947	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:ContractMarket;Message:Process Start(24 of 79)
08:50:50.680	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:ContractMarket;Message:Process End(24 of 79);Status:Success;ProcessingTime:30731 ms
08:50:50.680	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:ContractMarketSecurity;Message:Process Start(25 of 79)
08:51:38.028	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:ContractMarketSecurity;Message:Process End(25 of 79);Status:Success;ProcessingTime:47345 ms
08:51:38.029	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:ContractNegotiation;Message:Process Start(26 of 79)
08:51:54.708	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:ContractNegotiation;Message:Process End(26 of 79);Status:Success;ProcessingTime:16678 ms
08:51:54.708	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:ContractRisk;Message:Process Start(27 of 79)
08:52:00.421	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:ContractRisk;Message:Process End(27 of 79);Status:Success;ProcessingTime:5713 ms
08:52:00.421	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:ContractSection;Message:Process Start(28 of 79)
08:52:06.556	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:ContractSection;Message:Process End(28 of 79);Status:Success;ProcessingTime:6135 ms
08:52:06.557	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:ContractSectionBasis;Message:Process Start(29 of 79)
08:52:10.207	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:ContractSectionBasis;Message:Process End(29 of 79);Status:Success;ProcessingTime:3650 ms
08:52:10.207	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:ContractSectionRiskCode;Message:Process Start(30 of 79)
08:52:13.477	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:ContractSectionRiskCode;Message:Process End(30 of 79);Status:Success;ProcessingTime:3270 ms
08:52:13.477	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:DisconnectedDate;Message:Process Start(31 of 79)
08:52:16.913	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:DisconnectedDate;Message:Process End(31 of 79);Status:Success;ProcessingTime:3435 ms
08:52:16.913	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:GlobalProducts;Message:Process Start(32 of 79)
08:52:27.489	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:GlobalProducts;Message:Process End(32 of 79);Status:Success;ProcessingTime:10575 ms
08:52:27.489	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:IncludedTranslations;Message:Process Start(33 of 79)
08:52:31.850	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:IncludedTranslations;Message:Process End(33 of 79);Status:Success;ProcessingTime:4361 ms
08:52:31.851	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:Layer Summary;Message:Process Start(34 of 79)
08:52:36.096	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:Layer Summary;Message:Process End(34 of 79);Status:Success;ProcessingTime:4245 ms
08:52:36.096	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:Layers;Message:Process Start(35 of 79)
08:52:43.955	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:Layers;Message:Process End(35 of 79);Status:Success;ProcessingTime:7858 ms
08:52:43.955	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:Market;Message:Process Start(36 of 79)
08:52:48.664	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:Market;Message:Process End(36 of 79);Status:Success;ProcessingTime:4709 ms
08:52:48.665	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:Market Activity;Message:Process Start(37 of 79)
08:57:08.990	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:Market Activity;Message:Process End(37 of 79);Status:Success;ProcessingTime:260308 ms
08:57:08.990	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:Market Activity Inception Date;Message:Process Start(38 of 79)
08:57:12.124	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:Market Activity Inception Date;Message:Process End(38 of 79);Status:Success;ProcessingTime:3134 ms
08:57:12.124	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:Market QI Rating;Message:Process Start(39 of 79)
08:57:18.566	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:Market QI Rating;Message:Process End(39 of 79);Status:Success;ProcessingTime:6441 ms
08:57:18.566	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:Market Response Element Attribute;Message:Process Start(40 of 79)
08:57:23.050	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:Market Response Element Attribute;Message:Process End(40 of 79);Status:Success;ProcessingTime:4483 ms
08:57:23.050	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:Market Security;Message:Process Start(41 of 79)
08:57:43.018	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:Market Security;Message:Process End(41 of 79);Status:Success;ProcessingTime:19967 ms
08:57:43.018	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:Median Whole Risk Summary;Message:Process Start(42 of 79)
08:57:47.832	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:Median Whole Risk Summary;Message:Process End(42 of 79);Status:Success;ProcessingTime:4808 ms
08:57:47.832	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:NA Commission Rate;Message:Process Start(43 of 79)
08:57:52.598	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:NA Commission Rate;Message:Process End(43 of 79);Status:Success;ProcessingTime:4765 ms
08:57:52.598	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:Negotiation Distribution;Message:Process Start(44 of 79)
08:57:58.387	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:Negotiation Distribution;Message:Process End(44 of 79);Status:Success;ProcessingTime:5789 ms
08:57:58.393	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:Negotiation Sent Date;Message:Process Start(45 of 79)
08:58:01.973	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:Negotiation Sent Date;Message:Process End(45 of 79);Status:Success;ProcessingTime:3575 ms
08:58:01.973	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:Party;Message:Process Start(46 of 79)
08:58:24.002	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:Party;Message:Process End(46 of 79);Status:Success;ProcessingTime:22028 ms
08:58:24.002	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:Placement;Message:Process Start(47 of 79)
09:00:09.407	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:Placement;Message:Process End(47 of 79);Status:Success;ProcessingTime:105398 ms
09:00:09.407	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:Placement Created Date;Message:Process Start(48 of 79)
09:00:12.655	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:Placement Created Date;Message:Process End(48 of 79);Status:Success;ProcessingTime:3247 ms
09:00:12.655	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:Placement Events;Message:Process Start(49 of 79)
09:00:26.442	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:Placement Events;Message:Process End(49 of 79);Status:Success;ProcessingTime:13786 ms
09:00:26.442	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:Placement Inception Date;Message:Process Start(50 of 79)
09:00:30.685	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:Placement Inception Date;Message:Process End(50 of 79);Status:Success;ProcessingTime:4243 ms
09:00:30.685	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:Placement Primary Roles;Message:Process Start(51 of 79)
09:01:00.358	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:Placement Primary Roles;Message:Process End(51 of 79);Status:Success;ProcessingTime:29670 ms
09:01:00.358	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:Placement Products;Message:Process Start(52 of 79)
09:01:06.361	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:Placement Products;Message:Process End(52 of 79);Status:Success;ProcessingTime:6002 ms
09:01:06.361	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:Placement Teams;Message:Process Start(53 of 79)
09:01:11.569	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:Placement Teams;Message:Process End(53 of 79);Status:Success;ProcessingTime:5208 ms
09:01:11.569	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:PlacementExposure;Message:Process Start(54 of 79)
09:01:15.742	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:PlacementExposure;Message:Process End(54 of 79);Status:Success;ProcessingTime:4167 ms
09:01:15.743	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:PlacementPartyRelationship;Message:Process Start(55 of 79)
09:01:44.828	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:PlacementPartyRelationship;Message:Process End(55 of 79);Status:Success;ProcessingTime:29083 ms
09:01:44.828	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:PlacementPolicyList;Message:Process Start(56 of 79)
09:07:58.131	Trace	Severity level: Warning, Message: RunOperation encountered an exception and will retry. Exception: Azure.Messaging.ServiceBus.ServiceBusException: The service was unable to process the request; please retry the operation. For more information on exception types and proper exception handling, please refer to http://go.microsoft.com/fwlink/?LinkId=761101 Reference:08180f94-e4aa-458c-b6ee-596b08516e3e, TrackingId:89e0d313-8326-4cc1-9414-8a4beab468c5_G15, SystemTracker:NoSystemTracker, Timestamp:2025-06-17T08:07:58 (GeneralError). For troubleshooting information, see https://aka.ms/azsdk/net/servicebus/exceptions/troubleshoot.
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.RenewMessageLockInternalAsync(Guid lockToken, TimeSpan timeout)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.<>c.<<RenewMessageLockAsync>b__63_0>d.MoveNext()
--- End of stack trace from previous location ---
   at Azure.Messaging.ServiceBus.ServiceBusRetryPolicy.RunOperation[T1,TResult](Func`4 operation, T1 t1, TransportConnectionScope scope, CancellationToken cancellationToken, Boolean logTimeoutRetriesAsVerbose)
09:08:34.640	Trace	Severity level: Error, Message: psadf-to-psfuncapp-ssas-refresh-event/Subscriptions/refreshmodel-832701f0-3fc2-4f1e-91cc-4de9c38bf4ac-Receiver: RenewLockAsync Exception: Azure.Messaging.ServiceBus.ServiceBusException: The lock supplied is invalid. Either the lock expired, or the message has already been removed from the queue. For more information please see https://aka.ms/ServiceBusExceptions . Reference:de0b299e-7727-4fcd-9806-e311b8cdb53a, TrackingId:0dd35c28-e12e-4108-8d9d-b8976f07db58_B23, SystemTracker:crbbro-bkt-q-em20-sb1:Topic:psadf-to-psfuncapp-ssas-refresh-event|refreshmodel, Timestamp:2025-06-17T08:08:34 (MessageLockLost). For troubleshooting information, see https://aka.ms/azsdk/net/servicebus/exceptions/troubleshoot.
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.RenewMessageLockInternalAsync(Guid lockToken, TimeSpan timeout)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.<>c.<<RenewMessageLockAsync>b__63_0>d.MoveNext()
--- End of stack trace from previous location ---
   at Azure.Messaging.ServiceBus.ServiceBusRetryPolicy.RunOperation[T1,TResult](Func`4 operation, T1 t1, TransportConnectionScope scope, CancellationToken cancellationToken, Boolean logTimeoutRetriesAsVerbose)
   at Azure.Messaging.ServiceBus.ServiceBusRetryPolicy.RunOperation[T1,TResult](Func`4 operation, T1 t1, TransportConnectionScope scope, CancellationToken cancellationToken, Boolean logTimeoutRetriesAsVerbose)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.RenewMessageLockAsync(Guid lockToken, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.ServiceBusReceiver.RenewMessageLockAsync(Guid lockToken, CancellationToken cancellationToken). LockToken = c2a832e4-a6e1-4c9a-83d5-c8702ccb5a78
09:08:34.778	Trace	Severity level: Error, Message: psadf-to-psfuncapp-ssas-refresh-event/Subscriptions/refreshmodel-832701f0-3fc2-4f1e-91cc-4de9c38bf4ac: Processor RenewMessageLock Exception: Azure.Messaging.ServiceBus.ServiceBusException: The lock supplied is invalid. Either the lock expired, or the message has already been removed from the queue. For more information please see https://aka.ms/ServiceBusExceptions . Reference:de0b299e-7727-4fcd-9806-e311b8cdb53a, TrackingId:0dd35c28-e12e-4108-8d9d-b8976f07db58_B23, SystemTracker:crbbro-bkt-q-em20-sb1:Topic:psadf-to-psfuncapp-ssas-refresh-event|refreshmodel, Timestamp:2025-06-17T08:08:34 (MessageLockLost). For troubleshooting information, see https://aka.ms/azsdk/net/servicebus/exceptions/troubleshoot.
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.RenewMessageLockInternalAsync(Guid lockToken, TimeSpan timeout)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.<>c.<<RenewMessageLockAsync>b__63_0>d.MoveNext()
--- End of stack trace from previous location ---
   at Azure.Messaging.ServiceBus.ServiceBusRetryPolicy.RunOperation[T1,TResult](Func`4 operation, T1 t1, TransportConnectionScope scope, CancellationToken cancellationToken, Boolean logTimeoutRetriesAsVerbose)
   at Azure.Messaging.ServiceBus.ServiceBusRetryPolicy.RunOperation[T1,TResult](Func`4 operation, T1 t1, TransportConnectionScope scope, CancellationToken cancellationToken, Boolean logTimeoutRetriesAsVerbose)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.RenewMessageLockAsync(Guid lockToken, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.ServiceBusReceiver.RenewMessageLockAsync(Guid lockToken, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.ServiceBusReceiver.RenewMessageLockAsync(ServiceBusReceivedMessage message, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.ReceiverManager.RenewMessageLockAsync(ProcessMessageEventArgs args, ServiceBusReceivedMessage message, CancellationTokenSource cancellationTokenSource). LockToken = c2a832e4-a6e1-4c9a-83d5-c8702ccb5a78
09:08:35.059	Exception	Call Stack: Azure.Messaging.ServiceBus.ServiceBusException:
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver+<RenewMessageLockInternalAsync>d__64.MoveNext (Azure.Messaging.ServiceBus, Version=7.17.5.0, Culture=neutral, PublicKeyToken=92742159e12e44c8)
   at System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw (System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e)
   at System.Runtime.CompilerServices.TaskAwaiter.ThrowForNonSuccess (System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e)
   at System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification (System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver+<>c+<<RenewMessageLockAsync>b__63_0>d.MoveNext (Azure.Messaging.ServiceBus, Version=7.17.5.0, Culture=neutral, PublicKeyToken=92742159e12e44c8)
   at System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw (System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e)
   at System.Runtime.CompilerServices.TaskAwaiter.ThrowForNonSuccess (System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e)
   at System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification (System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e)
   at Azure.Messaging.ServiceBus.ServiceBusRetryPolicy+<RunOperation>d__23`2.MoveNext (Azure.Messaging.ServiceBus, Version=7.17.5.0, Culture=neutral, PublicKeyToken=92742159e12e44c8)
   at System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw (System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e)
   at Azure.Messaging.ServiceBus.ServiceBusRetryPolicy+<RunOperation>d__23`2.MoveNext (Azure.Messaging.ServiceBus, Version=7.17.5.0, Culture=neutral, PublicKeyToken=92742159e12e44c8)
   at System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw (System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e)
   at System.Runtime.CompilerServices.TaskAwaiter.ThrowForNonSuccess (System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e)
   at System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification (System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver+<RenewMessageLockAsync>d__63.MoveNext (Azure.Messaging.ServiceBus, Version=7.17.5.0, Culture=neutral, PublicKeyToken=92742159e12e44c8)
   at System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw (System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e)
   at System.Runtime.CompilerServices.TaskAwaiter.ThrowForNonSuccess (System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e)
   at System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification (System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e)
   at Azure.Messaging.ServiceBus.ServiceBusReceiver+<RenewMessageLockAsync>d__61.MoveNext (Azure.Messaging.ServiceBus, Version=7.17.5.0, Culture=neutral, PublicKeyToken=92742159e12e44c8)
   at System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw (System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e)
   at System.Runtime.CompilerServices.TaskAwaiter.ThrowForNonSuccess (System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e)
   at System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification (System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e)
   at Azure.Messaging.ServiceBus.ServiceBusReceiver+<RenewMessageLockAsync>d__60.MoveNext (Azure.Messaging.ServiceBus, Version=7.17.5.0, Culture=neutral, PublicKeyToken=92742159e12e44c8)
   at System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw (System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e)
   at System.Runtime.CompilerServices.TaskAwaiter.ThrowForNonSuccess (System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e)
   at System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification (System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e)
   at System.Runtime.CompilerServices.ConfiguredTaskAwaitable+ConfiguredTaskAwaiter.GetResult (System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e)
   at Azure.Messaging.ServiceBus.ReceiverManager+<RenewMessageLockAsync>d__22.MoveNext (Azure.Messaging.ServiceBus, Version=7.17.5.0, Culture=neutral, PublicKeyToken=92742159e12e44c8)
, Message: The lock supplied is invalid. Either the lock expired, or the message has already been removed from the queue. For more information please see https://aka.ms/ServiceBusExceptions . Reference:de0b299e-7727-4fcd-9806-e311b8cdb53a, TrackingId:0dd35c28-e12e-4108-8d9d-b8976f07db58_B23, SystemTracker:crbbro-bkt-q-em20-sb1:Topic:psadf-to-psfuncapp-ssas-refresh-event|refreshmodel, Timestamp:2025-06-17T08:08:34 (MessageLockLost). For troubleshooting information, see https://aka.ms/azsdk/net/servicebus/exceptions/troubleshoot.
09:08:35.059	Trace	Severity level: Error, Message: Message processing error (Action=RenewLock, EntityPath=psadf-to-psfuncapp-ssas-refresh-event/Subscriptions/refreshmodel, Endpoint=crbbro-bkt-q-em20-sb1.servicebus.windows.net)
09:12:16.365	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:PlacementPolicyList;Message:Process End(56 of 79);Status:Success;ProcessingTime:631493 ms
09:12:16.366	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:PlacementProductSummary;Message:Process Start(57 of 79)
09:12:32.767	Exception	Call Stack: Microsoft.AnalysisServices.OperationException:
   at Microsoft.AnalysisServices.Tabular.Model.SaveChangesImpl (Microsoft.AnalysisServices.Tabular, Version=*********, Culture=neutral, PublicKeyToken=89845dcd8080cc91)
   at Microsoft.AnalysisServices.Tabular.Model.SaveChanges (Microsoft.AnalysisServices.Tabular, Version=*********, Culture=neutral, PublicKeyToken=89845dcd8080cc91)
   at PsFunc.Functions.ServiceBus.RefreshAnalysisServer+<Run>d__9.MoveNext (PsFunc, Version=2025.9.0.0, Culture=neutral, PublicKeyToken=null: /azp/_work/7/s/src/PsFunc/Functions/ServiceBus/RefreshAnalysisServer.cs:109)
, Message: Failed to save modifications to the server. Error returned: 'An unexpected exception occurred.

Technical Details:
RootActivityId: 2351e63e-b351-4557-84d4-dfaa7cd40dae
Date (UTC): 6/17/2025 8:12:16 AM
'.
09:12:32.845	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:PlacementRiskProfile;Message:Process Start(58 of 79)
09:12:38.836	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:PlacementRiskProfile;Message:Process End(58 of 79);Status:Success;ProcessingTime:5991 ms
09:12:38.837	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:PlacementSecurity;Message:Process Start(59 of 79)
09:27:48.590	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:PlacementSecurity;Message:Process End(59 of 79);Status:Success;ProcessingTime:909690 ms
09:27:48.591	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:PlacementSystemRole;Message:Process Start(60 of 79)
09:27:54.491	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:PlacementSystemRole;Message:Process End(60 of 79);Status:Success;ProcessingTime:5900 ms
09:27:54.491	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:PlacementSystemUsers;Message:Process Start(61 of 79)
09:28:01.148	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:PlacementSystemUsers;Message:Process End(61 of 79);Status:Success;ProcessingTime:6651 ms
09:28:01.148	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:Policy Audit;Message:Process Start(62 of 79)
09:35:43.077	Exception	Call Stack: Microsoft.AnalysisServices.OperationException:
   at Microsoft.AnalysisServices.Tabular.Model.SaveChangesImpl (Microsoft.AnalysisServices.Tabular, Version=*********, Culture=neutral, PublicKeyToken=89845dcd8080cc91)
   at Microsoft.AnalysisServices.Tabular.Model.SaveChanges (Microsoft.AnalysisServices.Tabular, Version=*********, Culture=neutral, PublicKeyToken=89845dcd8080cc91)
   at PsFunc.Functions.ServiceBus.RefreshAnalysisServer+<Run>d__9.MoveNext (PsFunc, Version=2025.9.0.0, Culture=neutral, PublicKeyToken=null: /azp/_work/7/s/src/PsFunc/Functions/ServiceBus/RefreshAnalysisServer.cs:109)
, Message: Failed to save modifications to the server. Error returned: 'The operation was canceled because a deadlock condition was detected with another transaction. This usually occurs when two transactions need to lock the same objects, but do so in a different order. Please retry your operation.

Technical Details:
RootActivityId: 19b17126-b4a0-4e97-9d47-dbb3662d53d3
Date (UTC): 6/17/2025 8:35:34 AM
'.
09:35:43.078	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:Policy Inception Date;Message:Process Start(63 of 79)
09:35:47.750	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:Policy Inception Date;Message:Process End(63 of 79);Status:Success;ProcessingTime:4672 ms
09:35:47.750	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:Policy Organisation;Message:Process Start(64 of 79)
09:35:52.345	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:Policy Organisation;Message:Process End(64 of 79);Status:Success;ProcessingTime:4594 ms
09:35:52.345	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:PolicyPremium;Message:Process Start(65 of 79)
09:37:23.952	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:PolicyPremium;Message:Process End(65 of 79);Status:Success;ProcessingTime:91600 ms
09:37:23.952	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:PolicyTeam;Message:Process Start(66 of 79)
09:37:55.936	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:PolicyTeam;Message:Process End(66 of 79);Status:Success;ProcessingTime:31981 ms
09:37:55.936	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:Premium Categories;Message:Process Start(67 of 79)
09:38:02.637	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:Premium Categories;Message:Process End(67 of 79);Status:Success;ProcessingTime:6701 ms
09:38:02.638	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:Primary Insured Details;Message:Process Start(68 of 79)
09:38:29.432	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:Primary Insured Details;Message:Process End(68 of 79);Status:Success;ProcessingTime:26791 ms
09:38:29.432	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:Product;Message:Process Start(69 of 79)
09:38:45.124	Exception	Call Stack: Microsoft.AnalysisServices.OperationException:
   at Microsoft.AnalysisServices.Tabular.Model.SaveChangesImpl (Microsoft.AnalysisServices.Tabular, Version=*********, Culture=neutral, PublicKeyToken=89845dcd8080cc91)
   at Microsoft.AnalysisServices.Tabular.Model.SaveChanges (Microsoft.AnalysisServices.Tabular, Version=*********, Culture=neutral, PublicKeyToken=89845dcd8080cc91)
   at PsFunc.Functions.ServiceBus.RefreshAnalysisServer+<Run>d__9.MoveNext (PsFunc, Version=2025.9.0.0, Culture=neutral, PublicKeyToken=null: /azp/_work/7/s/src/PsFunc/Functions/ServiceBus/RefreshAnalysisServer.cs:109)
, Message: Failed to save modifications to the server. Error returned: 'The operation was canceled because a deadlock condition was detected with another transaction. This usually occurs when two transactions need to lock the same objects, but do so in a different order. Please retry your operation.

Technical Details:
RootActivityId: 9242f741-56f7-4ed3-bd9c-ea92868e244c
Date (UTC): 6/17/2025 8:38:29 AM
'.
09:38:45.124	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:Related Placements;Message:Process Start(70 of 79)
09:39:10.951	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:Related Placements;Message:Process End(70 of 79);Status:Success;ProcessingTime:25825 ms
09:39:10.958	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:Response Date;Message:Process Start(71 of 79)
09:43:22.272	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:Response Date;Message:Process End(71 of 79);Status:Success;ProcessingTime:251296 ms
09:43:22.272	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:RiskAttributes;Message:Process Start(72 of 79)
09:43:29.210	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:RiskAttributes;Message:Process End(72 of 79);Status:Success;ProcessingTime:6937 ms
09:43:29.210	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:Security Teams;Message:Process Start(73 of 79)
09:44:09.000	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:Security Teams;Message:Process End(73 of 79);Status:Success;ProcessingTime:39787 ms
09:44:09.000	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:Submission;Message:Process Start(74 of 79)
09:44:14.298	Exception	Call Stack: Microsoft.AnalysisServices.OperationException:
   at Microsoft.AnalysisServices.Tabular.Model.SaveChangesImpl (Microsoft.AnalysisServices.Tabular, Version=*********, Culture=neutral, PublicKeyToken=89845dcd8080cc91)
   at Microsoft.AnalysisServices.Tabular.Model.SaveChanges (Microsoft.AnalysisServices.Tabular, Version=*********, Culture=neutral, PublicKeyToken=89845dcd8080cc91)
   at PsFunc.Functions.ServiceBus.RefreshAnalysisServer+<Run>d__9.MoveNext (PsFunc, Version=2025.9.0.0, Culture=neutral, PublicKeyToken=null: /azp/_work/7/s/src/PsFunc/Functions/ServiceBus/RefreshAnalysisServer.cs:109)
, Message: Failed to save modifications to the server. Error returned: 'The operation was canceled because a deadlock condition was detected with another transaction. This usually occurs when two transactions need to lock the same objects, but do so in a different order. Please retry your operation.

Technical Details:
RootActivityId: 654e256d-5113-4eb6-9d05-2f94c73b565d
Date (UTC): 6/17/2025 8:44:09 AM
'.
09:44:14.308	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:Submission Documents;Message:Process Start(75 of 79)
09:44:19.068	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:Submission Documents;Message:Process End(75 of 79);Status:Success;ProcessingTime:4760 ms
09:44:19.068	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:Submission Sent Date;Message:Process Start(76 of 79)
09:44:22.227	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:Submission Sent Date;Message:Process End(76 of 79);Status:Success;ProcessingTime:3159 ms
09:44:22.227	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:SubmissionContainerMarket;Message:Process Start(77 of 79)
09:44:26.047	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:SubmissionContainerMarket;Message:Process End(77 of 79);Status:Success;ProcessingTime:3813 ms
09:44:26.047	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:UserSecurity;Message:Process Start(78 of 79)
09:44:59.959	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:UserSecurity;Message:Process End(78 of 79);Status:Success;ProcessingTime:33910 ms
09:44:59.960	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:Whole Risk Summary;Message:Process Start(79 of 79)
09:46:18.271	Trace	Severity level: Information, Message: InstanceLogID:2520 ;ProcessSessionId:202506170000000429;ModelName:GeneralAnalytics_AS_TAB;tableName:Whole Risk Summary;Message:Process End(79 of 79);Status:Success;ProcessingTime:78306 ms
09:46:18.688	Trace	Severity level: Information, Message: InstanceLogID: 2520; ProcessSessionId:202506170000000429;Database:crbbropsem20qssas(GeneralAnalytics_AS_TAB);Message:SSAS Server Disconnected
09:46:18.688	Trace	Severity level: Information, Message: InstanceLogID: 2520; ProcessSessionId:202506170000000429;Database:crbbropsem20qssas(GeneralAnalytics_AS_TAB);Total Tables count:79
09:46:18.688	Trace	Severity level: Information, Message: InstanceLogID: 2520; ProcessSessionId:202506170000000429;Database:crbbropsem20qssas(GeneralAnalytics_AS_TAB);Processed Tables count:79
09:46:19.174	Trace	Severity level: Error, Message: psadf-to-psfuncapp-ssas-refresh-event/Subscriptions/refreshmodel-832701f0-3fc2-4f1e-91cc-4de9c38bf4ac-Receiver: CompleteAsync Exception: Azure.Messaging.ServiceBus.ServiceBusException: The lock supplied is invalid. Either the lock expired, or the message has already been removed from the queue. For more information please see https://aka.ms/ServiceBusExceptions . Reference:e68d2f3a-05a5-4912-b2d3-18625c5262c3, TrackingId:21f6826d-cc66-4465-8efd-766abfd9c1b2_B23, SystemTracker:crbbro-bkt-q-em20-sb1:Topic:psadf-to-psfuncapp-ssas-refresh-event|refreshmodel, Timestamp:2025-06-17T08:46:18 (MessageLockLost). For troubleshooting information, see https://aka.ms/azsdk/net/servicebus/exceptions/troubleshoot.
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.DisposeMessageRequestResponseAsync(Guid lockToken, TimeSpan timeout, DispositionStatus dispositionStatus, String sessionId, IDictionary`2 propertiesToModify, String deadLetterReason, String deadLetterDescription)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.DisposeMessageAsync(Guid lockToken, Outcome outcome, DispositionStatus disposition, TimeSpan timeout, IDictionary`2 propertiesToModify, String deadLetterReason, String deadLetterDescription)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.CompleteInternalAsync(Guid lockToken, TimeSpan timeout)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.<>c.<<CompleteAsync>b__46_0>d.MoveNext()
--- End of stack trace from previous location ---
   at Azure.Messaging.ServiceBus.ServiceBusRetryPolicy.<>c__22`1.<<RunOperation>b__22_0>d.MoveNext()
--- End of stack trace from previous location ---
   at Azure.Messaging.ServiceBus.ServiceBusRetryPolicy.RunOperation[T1,TResult](Func`4 operation, T1 t1, TransportConnectionScope scope, CancellationToken cancellationToken, Boolean logTimeoutRetriesAsVerbose)
   at Azure.Messaging.ServiceBus.ServiceBusRetryPolicy.RunOperation[T1,TResult](Func`4 operation, T1 t1, TransportConnectionScope scope, CancellationToken cancellationToken, Boolean logTimeoutRetriesAsVerbose)
   at Azure.Messaging.ServiceBus.ServiceBusRetryPolicy.RunOperation[T1](Func`4 operation, T1 t1, TransportConnectionScope scope, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.CompleteAsync(Guid lockToken, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.ServiceBusReceiver.CompleteMessageAsync(ServiceBusReceivedMessage message, CancellationToken cancellationToken). LockToken = c2a832e4-a6e1-4c9a-83d5-c8702ccb5a78
09:46:19.194	Exception	Call Stack: Azure.Messaging.ServiceBus.ServiceBusException:
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver+<DisposeMessageRequestResponseAsync>d__57.MoveNext (Azure.Messaging.ServiceBus, Version=7.17.5.0, Culture=neutral, PublicKeyToken=92742159e12e44c8)
   at System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw (System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e)
   at System.Runtime.CompilerServices.TaskAwaiter.ThrowForNonSuccess (System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e)
   at System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification (System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e)
   at System.Runtime.CompilerServices.ConfiguredTaskAwaitable+ConfiguredTaskAwaiter.GetResult (System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver+<DisposeMessageAsync>d__48.MoveNext (Azure.Messaging.ServiceBus, Version=7.17.5.0, Culture=neutral, PublicKeyToken=92742159e12e44c8)
   at System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw (System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e)
   at System.Runtime.CompilerServices.TaskAwaiter.ThrowForNonSuccess (System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e)
   at System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification (System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e)
   at System.Runtime.CompilerServices.ConfiguredTaskAwaitable+ConfiguredTaskAwaiter.GetResult (System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver+<CompleteInternalAsync>d__47.MoveNext (Azure.Messaging.ServiceBus, Version=7.17.5.0, Culture=neutral, PublicKeyToken=92742159e12e44c8)
   at System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw (System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e)
   at System.Runtime.CompilerServices.TaskAwaiter.ThrowForNonSuccess (System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e)
   at System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification (System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e)
   at System.Runtime.CompilerServices.ConfiguredTaskAwaitable+ConfiguredTaskAwaiter.GetResult (System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver+<>c+<<CompleteAsync>b__46_0>d.MoveNext (Azure.Messaging.ServiceBus, Version=7.17.5.0, Culture=neutral, PublicKeyToken=92742159e12e44c8)
   at System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw (System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e)
   at System.Runtime.CompilerServices.TaskAwaiter.ThrowForNonSuccess (System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e)
   at System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification (System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e)
   at Azure.Messaging.ServiceBus.ServiceBusRetryPolicy+<>c__22`1+<<RunOperation>b__22_0>d.MoveNext (Azure.Messaging.ServiceBus, Version=7.17.5.0, Culture=neutral, PublicKeyToken=92742159e12e44c8)
   at System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw (System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e)
   at System.Runtime.CompilerServices.TaskAwaiter.ThrowForNonSuccess (System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e)
   at System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification (System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e)
   at System.Threading.Tasks.ValueTask`1.get_Result (System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e)
   at System.Runtime.CompilerServices.ConfiguredValueTaskAwaitable`1+ConfiguredValueTaskAwaiter.GetResult (System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e)
   at Azure.Messaging.ServiceBus.ServiceBusRetryPolicy+<RunOperation>d__23`2.MoveNext (Azure.Messaging.ServiceBus, Version=7.17.5.0, Culture=neutral, PublicKeyToken=92742159e12e44c8)
   at System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw (System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e)
   at Azure.Messaging.ServiceBus.ServiceBusRetryPolicy+<RunOperation>d__23`2.MoveNext (Azure.Messaging.ServiceBus, Version=7.17.5.0, Culture=neutral, PublicKeyToken=92742159e12e44c8)
   at System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw (System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e)
   at System.Runtime.CompilerServices.TaskAwaiter.ThrowForNonSuccess (System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e)
   at System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification (System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e)
   at Azure.Messaging.ServiceBus.ServiceBusRetryPolicy+<RunOperation>d__22`1.MoveNext (Azure.Messaging.ServiceBus, Version=7.17.5.0, Culture=neutral, PublicKeyToken=92742159e12e44c8)
   at System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw (System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e)
   at System.Runtime.CompilerServices.TaskAwaiter.ThrowForNonSuccess (System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e)
   at System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification (System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver+<CompleteAsync>d__46.MoveNext (Azure.Messaging.ServiceBus, Version=7.17.5.0, Culture=neutral, PublicKeyToken=92742159e12e44c8)
   at System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw (System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e)
   at System.Runtime.CompilerServices.TaskAwaiter.ThrowForNonSuccess (System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e)
   at System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification (System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e)
   at System.Runtime.CompilerServices.ConfiguredTaskAwaitable+ConfiguredTaskAwaiter.GetResult (System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e)
   at Azure.Messaging.ServiceBus.ServiceBusReceiver+<CompleteMessageAsync>d__49.MoveNext (Azure.Messaging.ServiceBus, Version=7.17.5.0, Culture=neutral, PublicKeyToken=92742159e12e44c8)
   at System.Runtime.ExceptionServices.ExceptionDispatchInfo.Throw (System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e)
   at System.Runtime.CompilerServices.TaskAwaiter.ThrowForNonSuccess (System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e)
   at System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification (System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e)
   at System.Runtime.CompilerServices.ConfiguredTaskAwaitable+ConfiguredTaskAwaiter.GetResult (System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e)
   at Azure.Messaging.ServiceBus.ReceiverManager+<ProcessOneMessage>d__17.MoveNext (Azure.Messaging.ServiceBus, Version=7.17.5.0, Culture=neutral, PublicKeyToken=92742159e12e44c8)
, Message: The lock supplied is invalid. Either the lock expired, or the message has already been removed from the queue. For more information please see https://aka.ms/ServiceBusExceptions . Reference:e68d2f3a-05a5-4912-b2d3-18625c5262c3, TrackingId:21f6826d-cc66-4465-8efd-766abfd9c1b2_B23, SystemTracker:crbbro-bkt-q-em20-sb1:Topic:psadf-to-psfuncapp-ssas-refresh-event|refreshmodel, Timestamp:2025-06-17T08:46:18 (MessageLockLost). For troubleshooting information, see https://aka.ms/azsdk/net/servicebus/exceptions/troubleshoot.
09:46:19.194	Trace	Severity level: Error, Message: Message processing error (Action=Complete, EntityPath=psadf-to-psfuncapp-ssas-refresh-event/Subscriptions/refreshmodel, Endpoint=crbbro-bkt-q-em20-sb1.servicebus.windows.net)