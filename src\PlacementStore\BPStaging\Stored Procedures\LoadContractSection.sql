/*
Lineage
dbo.ContractSection.ContractSectionId=BPStaging.ContractSection.Id
dbo.ContractSection.ContractId=PS.Contract.ContractId
dbo.ContractSection.DisplayIndex=BPStaging.ContractSection.DisplayIndex
dbo.ContractSection.ShortName=BPStaging.ContractSection.ShortName
dbo.ContractSection.Description=BPStaging.ContractSection.Description
dbo.ContractSection.LayerTypeId=ref.LayerType.LayerTypeId
dbo.ContractSection.LimitCurrencyId=Reference.Currency.CurrencyId
dbo.ContractSection.Limit=BPStaging.ContractSection.Limit
dbo.ContractSection.AttachmentPointCurrencyId=Reference.Currency.CurrencyId
dbo.ContractSection.AttachmentPoint=BPStaging.ContractSection.AttachmentPoint
dbo.ContractSection.OrderPercentage=BPStaging.ContractSection.OrderPercentage
dbo.ContractSection.ValidFrom=BPStaging.ContractSection.ValidTo
dbo.ContractSection.ValidFrom=BPStaging.ContractSection.ValidFrom
dbo.ContractSection.IsDeleted=BPStaging.ContractSection.ValidTo
*/
CREATE PROCEDURE BPStaging.LoadContractSection
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'dbo.ContractSection';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    MERGE dbo.ContractSection T
    USING (
        SELECT
            ContractSectionId = inner_select.Id
          , c.ContractId
          , inner_select.DisplayIndex
          , inner_select.ShortName
          , inner_select.Description
          , lt.LayerTypeId
          , LimitCurrencyId = lccy.CurrencyId
          , inner_select.Limit
          , AttachmentPointCurrencyId = apccy.CurrencyId
          , inner_select.AttachmentPoint
          , inner_select.OrderPercentage
          , ValidFrom = CASE WHEN YEAR(inner_select.ValidTo) < 9999
                                 THEN inner_select.ValidTo
                             ELSE inner_select.ValidFrom END
          , IsDeleted = CASE WHEN YEAR(inner_select.ValidTo) < 9999
                                 THEN 1
                             ELSE 0 END
        FROM (
        SELECT
            Id
          , ContractId
          , DisplayIndex
          , ShortName
          , Description
          , LayerTypeId
          , LimitCurrencyTypeId
          , Limit
          , AttachmentPointCurrencyTypeId
          , AttachmentPoint
          , OrderPercentage
          , ValidFrom
          , ValidTo
          , RowNo = ROW_NUMBER() OVER (PARTITION BY Id ORDER BY ValidFrom DESC, ValidTo ASC)
        FROM
            BPStaging.ContractSection
    ) inner_select
             INNER JOIN
            -- To get the ContractId and ensure we don't have a missing foreign key.
            PS.Contract c
                 ON c.ContractId = inner_select.ContractId

             LEFT JOIN Reference.Currency lccy
                 ON lccy.CurrencyId = inner_select.LimitCurrencyTypeId

             LEFT JOIN Reference.Currency apccy
                 ON apccy.CurrencyId = inner_select.AttachmentPointCurrencyTypeId

             LEFT JOIN ref.LayerType lt
                 ON lt.LayerTypeId = inner_select.LayerTypeId
        WHERE
            inner_select.RowNo = 1
    ) S
    ON T.ContractSectionId = S.ContractSectionId
    WHEN NOT MATCHED
        THEN INSERT (
                 ContractSectionId
               , ContractId
               , DisplayIndex
               , ShortName
               , Description
               , LayerTypeId
               , LimitCurrencyId
               , Limit
               , AttachmentPointCurrencyId
               , AttachmentPoint
               , OrderPercentage
               , ValidFrom
               , CreatedUTCDate
               , LastUpdatedUTCDate
               , IsDeleted
             )
             VALUES
                 (
                     S.ContractSectionId
                   , S.ContractId
                   , S.DisplayIndex
                   , S.ShortName
                   , S.Description
                   , S.LayerTypeId
                   , S.LimitCurrencyId
                   , S.Limit
                   , S.AttachmentPointCurrencyId
                   , S.AttachmentPoint
                   , S.OrderPercentage
                   , S.ValidFrom
                   , GETUTCDATE()
                   , GETUTCDATE()
                   , S.IsDeleted
                 )
    WHEN MATCHED AND NOT EXISTS (
                             SELECT
                                 T.ContractId
                               , T.DisplayIndex
                               , T.ShortName
                               , T.Description
                               , T.LayerTypeId
                               , T.LimitCurrencyId
                               , T.Limit
                               , T.AttachmentPointCurrencyId
                               , T.AttachmentPoint
                               , T.OrderPercentage
                               , T.ValidFrom
                               , T.IsDeleted
                             INTERSECT
                             SELECT
                                 S.ContractId
                               , S.DisplayIndex
                               , S.ShortName
                               , S.Description
                               , S.LayerTypeId
                               , S.LimitCurrencyId
                               , S.Limit
                               , S.AttachmentPointCurrencyId
                               , S.AttachmentPoint
                               , S.OrderPercentage
                               , S.ValidFrom
                               , S.IsDeleted
                         )
        THEN UPDATE SET
                 T.ContractId = S.ContractId
               , T.DisplayIndex = S.DisplayIndex
               , T.ShortName = S.ShortName
               , T.Description = S.Description
               , T.LayerTypeId = S.LayerTypeId
               , T.LimitCurrencyId = S.LimitCurrencyId
               , T.Limit = S.Limit
               , T.AttachmentPointCurrencyId = S.AttachmentPointCurrencyId
               , T.AttachmentPoint = S.AttachmentPoint
               , T.OrderPercentage = S.OrderPercentage
               , T.ValidFrom = S.ValidFrom
               , T.LastUpdatedUTCDate = GETUTCDATE()
               , T.IsDeleted = S.IsDeleted
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);