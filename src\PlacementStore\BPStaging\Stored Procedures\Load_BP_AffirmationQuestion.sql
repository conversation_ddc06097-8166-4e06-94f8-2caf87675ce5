/*
Lineage
BP.AffirmationQuestion.Id=BPStaging.AffirmationQuestion.Id
BP.AffirmationQuestion.AffirmationQuestionSetId=BPStaging.AffirmationQuestion.AffirmationQuestionSetId
BP.AffirmationQuestion.LabelTranslationDescription=BPStaging.AffirmationQuestion.LabelTranslationDescription
BP.AffirmationQuestion.DescriptionTranslationText=BPStaging.AffirmationQuestion.DescriptionTranslationText
BP.AffirmationQuestion.RequiredAnswer=BPStaging.AffirmationQuestion.RequiredAnswer
BP.AffirmationQuestion.Url=BPStaging.AffirmationQuestion.Url
BP.AffirmationQuestion.UrlCaptionTranslationText=BPStaging.AffirmationQuestion.UrlCaptionTranslationText
BP.AffirmationQuestion.SourceUpdatedDate=BPStaging.AffirmationQuestion.ValidTo
BP.AffirmationQuestion.SourceUpdatedDate=BPStaging.AffirmationQuestion.ValidFrom
BP.AffirmationQuestion.IsDeprecated=BPStaging.AffirmationQuestion.IsDeprecated
BP.AffirmationQuestion.IsDeprecated=BPStaging.AffirmationQuestion.ValidTo
*/
CREATE PROCEDURE BPStaging.Load_BP_AffirmationQuestion
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'BP.AffirmationQuestion';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

IF EXISTS (SELECT * FROM BPStaging.AffirmationQuestion)
BEGIN
    BEGIN TRY
        MERGE BP.AffirmationQuestion t
        USING (
            SELECT
                inner_select.Id
              , inner_select.AffirmationQuestionSetId
              , inner_select.LabelTranslationDescription
              , inner_select.DescriptionTranslationText
              , inner_select.RequiredAnswer
              , inner_select.Url
              , inner_select.UrlCaptionTranslationText
              , SourceUpdatedDate = CASE WHEN YEAR(inner_select.ValidTo) < 9999
                                             THEN inner_select.ValidTo
                                         ELSE inner_select.ValidFrom END
              , IsDeprecated = CASE WHEN YEAR(inner_select.ValidTo) < 9999
                                        THEN 1
                                    ELSE inner_select.IsDeprecated END
            FROM (
            SELECT
                Id
              , AffirmationQuestionSetId
              , LabelTranslationDescription
              , DescriptionTranslationText
              , IsDeprecated
              , RequiredAnswer
              , Url
              , UrlCaptionTranslationText
              , ValidFrom
              , ValidTo
              , RowNo = ROW_NUMBER() OVER (PARTITION BY Id ORDER BY ValidFrom DESC, ValidTo ASC)
            FROM
                BPStaging.AffirmationQuestion
        ) inner_select
            WHERE
                inner_select.RowNo = 1
        ) s
        ON t.Id = s.Id
        WHEN NOT MATCHED
            THEN INSERT (
                     Id
                   , AffirmationQuestionSetId
                   , LabelTranslationDescription
                   , DescriptionTranslationText
                   , RequiredAnswer
                   , Url
                   , UrlCaptionTranslationText
                   , SourceUpdatedDate
                   , IsDeprecated
                 )
                 VALUES
                     (
                         s.Id
                       , s.AffirmationQuestionSetId
                       , s.LabelTranslationDescription
                       , s.DescriptionTranslationText
                       , s.RequiredAnswer
                       , s.Url
                       , s.UrlCaptionTranslationText
                       , s.SourceUpdatedDate
                       , s.IsDeprecated
                     )
        WHEN MATCHED AND NOT EXISTS (
    SELECT
        t.AffirmationQuestionSetId
      , t.LabelTranslationDescription
      , t.DescriptionTranslationText
      , t.RequiredAnswer
      , t.Url
      , t.UrlCaptionTranslationText
      , t.SourceUpdatedDate
      , t.IsDeprecated
    INTERSECT
    SELECT
        s.AffirmationQuestionSetId
      , s.LabelTranslationDescription
      , s.DescriptionTranslationText
      , s.RequiredAnswer
      , s.Url
      , s.UrlCaptionTranslationText
      , s.SourceUpdatedDate
      , s.IsDeprecated
)
            THEN UPDATE SET
                     t.AffirmationQuestionSetId = s.AffirmationQuestionSetId
                   , t.LabelTranslationDescription = s.LabelTranslationDescription
                   , t.DescriptionTranslationText = s.DescriptionTranslationText
                   , t.RequiredAnswer = s.RequiredAnswer
                   , t.Url = s.Url
                   , t.UrlCaptionTranslationText = s.UrlCaptionTranslationText
                   , t.SourceUpdatedDate = s.SourceUpdatedDate
                   , t.ETLUpdatedDate = GETUTCDATE()
                   , t.IsDeprecated = s.IsDeprecated
        OUTPUT $ACTION
        INTO @Actions;

        SELECT
            @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                          THEN 1
                                      ELSE 0 END
                             )
          , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                         THEN 1
                                     ELSE 0 END
                            )
          , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                         THEN 1
                                     ELSE 0 END
                            )
        FROM
            @Actions;
    END TRY
    BEGIN CATCH
        DECLARE @ErrorMessage NVARCHAR(MAX);

        SET @ErrorMessage = ERROR_MESSAGE();

        EXEC ADF.StoredProcErrorLog
            @SprocName
          , @ErrorMessage;

        SET @RejectedCount = 1;
    END CATCH;
END;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);