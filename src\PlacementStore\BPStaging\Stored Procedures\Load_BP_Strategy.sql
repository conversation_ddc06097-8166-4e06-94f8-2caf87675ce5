/*
Lineage
BP.Strategy.Id=BPStaging.Strategy.Id
BP.Strategy.PlacementId=BPStaging.Strategy.PlacementId
BP.Strategy.Name=BPStaging.Strategy.Name
BP.Strategy.DisplayIndex=BPStaging.Strategy.DisplayIndex
BP.Strategy.Accepted=BPStaging.Strategy.Accepted
BP.Strategy.SourceUpdatedDate=BPStaging.Strategy.ValidTo
BP.Strategy.SourceUpdatedDate=BPStaging.Strategy.ValidFrom
BP.Strategy.IsDeleted=BPStaging.Strategy.ValidTo
*/
CREATE PROCEDURE BPStaging.Load_BP_Strategy
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(255) = 'BP.Strategy';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET NOCOUNT ON;

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    MERGE BP.Strategy t
    USING (
        SELECT
            Id
          , PlacementId
          , Name
          , DisplayIndex
          , Accepted
          , SourceUpdatedDate = CASE WHEN YEAR(ValidTo) < 9999
                                         THEN ValidTo
                                     ELSE ValidFrom END
          , IsDeleted = CASE WHEN YEAR(ValidTo) < 9999
                                 THEN 1
                             ELSE 0 END
        FROM (
        SELECT
            Id
          , PlacementId
          , Name
          , DisplayIndex
          , Accepted
          , ValidFrom
          , ValidTo
          , RowNo = ROW_NUMBER() OVER (PARTITION BY Id ORDER BY ValidFrom DESC, ValidTo ASC)
        FROM
            BPStaging.Strategy
    ) inner_select
        WHERE
            inner_select.RowNo = 1
    ) s
    ON t.Id = s.Id
    WHEN NOT MATCHED
        THEN INSERT (
                 Id
               , PlacementId
               , Name
               , DisplayIndex
               , Accepted
               , SourceUpdatedDate
               , ETLCreatedDate
               , ETLUpdatedDate
               , IsDeleted
             )
             VALUES
                 (
                     s.Id
                   , s.PlacementId
                   , s.Name
                   , s.DisplayIndex
                   , s.Accepted
                   , s.SourceUpdatedDate
                   , GETUTCDATE()
                   , GETUTCDATE()
                   , s.IsDeleted
                 )
    WHEN MATCHED AND NOT EXISTS (
                             SELECT
                                 s.PlacementId
                               , s.Name
                               , s.DisplayIndex
                               , s.Accepted
                               , s.SourceUpdatedDate
                               , s.IsDeleted
                             INTERSECT
                             SELECT
                                 t.PlacementId
                               , t.Name
                               , t.DisplayIndex
                               , t.Accepted
                               , t.SourceUpdatedDate
                               , t.IsDeleted
                         )
        THEN UPDATE SET
                 t.PlacementId = s.PlacementId
               , t.Name = s.Name
               , t.DisplayIndex = s.DisplayIndex
               , t.Accepted = s.Accepted
               , t.SourceUpdatedDate = s.SourceUpdatedDate
               , t.ETLUpdatedDate = GETUTCDATE()
               , t.IsDeleted = s.IsDeleted

    /* Supporting incremental so no delete of any kind */
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = CONCAT(N'Merge ', @TargetTable);

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);

RETURN 0;