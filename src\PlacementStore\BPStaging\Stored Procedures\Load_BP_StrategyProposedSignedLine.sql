/*
Lineage
BP.StrategyProposedSignedLine.Id=BPStaging.StrategyProposedSignedLine.Id
BP.StrategyProposedSignedLine.StrategyId=BPStaging.StrategyProposedSignedLine.StrategyId
BP.StrategyProposedSignedLine.MarketQuoteResponseId=BPStaging.StrategyProposedSignedLine.MarketQuoteResponseId
BP.StrategyProposedSignedLine.SignedLine=BPStaging.StrategyProposedSignedLine.SignedLine
BP.StrategyProposedSignedLine.SignedLineRate=BPStaging.StrategyProposedSignedLine.SignedLineRate
BP.StrategyProposedSignedLine.SignedLineRateOfOrder=BPStaging.StrategyProposedSignedLine.SignedLineRateOfOrder
BP.StrategyProposedSignedLine.SourceUpdatedDate=BPStaging.StrategyProposedSignedLine.ValidTo
BP.StrategyProposedSignedLine.SourceUpdatedDate=BPStaging.StrategyProposedSignedLine.ValidFrom
BP.StrategyProposedSignedLine.IsDeleted=BPStaging.StrategyProposedSignedLine.ValidTo
*/
CREATE PROCEDURE BPStaging.Load_BP_StrategyProposedSignedLine
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(255) = 'BP.StrategyProposedSignedLine';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET NOCOUNT ON;

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    MERGE BP.StrategyProposedSignedLine t
    USING (
        SELECT
            Id
          , StrategyId
          , MarketQuoteResponseId
          , SignedLine
          , SignedLineRate
          , SignedLineRateOfOrder
          , SourceUpdatedDate = CASE WHEN YEAR(ValidTo) < 9999
                                         THEN ValidTo
                                     ELSE ValidFrom END
          , IsDeleted = CASE WHEN YEAR(ValidTo) < 9999
                                 THEN 1
                             ELSE 0 END
        FROM (
        SELECT
            Id
          , StrategyId
          , MarketQuoteResponseId
          , SignedLine
          , SignedLineRate
          , SignedLineRateOfOrder
          , ValidFrom
          , ValidTo
          , RowNo = ROW_NUMBER() OVER (PARTITION BY Id ORDER BY ValidFrom DESC, ValidTo ASC)
        FROM
            BPStaging.StrategyProposedSignedLine
    ) inner_select
        WHERE
            inner_select.RowNo = 1
    ) s
    ON t.Id = s.Id
    WHEN NOT MATCHED
        THEN INSERT (
                 Id
               , StrategyId
               , MarketQuoteResponseId
               , SignedLine
               , SignedLineRate
               , SignedLineRateOfOrder
               , SourceUpdatedDate
               , ETLCreatedDate
               , ETLUpdatedDate
               , IsDeleted
             )
             VALUES
                 (
                     s.Id
                   , s.StrategyId
                   , s.MarketQuoteResponseId
                   , s.SignedLine
                   , s.SignedLineRate
                   , s.SignedLineRateOfOrder
                   , s.SourceUpdatedDate
                   , GETUTCDATE()
                   , GETUTCDATE()
                   , s.IsDeleted
                 )
    WHEN MATCHED AND NOT EXISTS (
                             SELECT
                                 s.StrategyId
                               , s.MarketQuoteResponseId
                               , s.SignedLine
                               , s.SignedLineRate
                               , s.SignedLineRateOfOrder
                               , s.SourceUpdatedDate
                               , s.IsDeleted
                             INTERSECT
                             SELECT
                                 t.StrategyId
                               , t.MarketQuoteResponseId
                               , t.SignedLine
                               , t.SignedLineRate
                               , t.SignedLineRateOfOrder
                               , t.SourceUpdatedDate
                               , t.IsDeleted
                         )
        THEN UPDATE SET
                 t.StrategyId = s.StrategyId
               , t.MarketQuoteResponseId = s.MarketQuoteResponseId
               , t.SignedLine = s.SignedLine
               , t.SignedLineRate = s.SignedLineRate
               , t.SignedLineRateOfOrder = s.SignedLineRateOfOrder
               , t.SourceUpdatedDate = s.SourceUpdatedDate
               , t.ETLUpdatedDate = GETUTCDATE()
               , t.IsDeleted = s.IsDeleted

    /* Supporting incremental so no delete of any kind */
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = CONCAT(N'Merge ', @TargetTable);

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);

RETURN 0;