/*
Lineage
BP.UserEnhancedBrokerDetails.UserId=BPStaging.UserEnhancedBrokerDetails.UserId
BP.UserEnhancedBrokerDetails.BrokerNumber=BPStaging.UserEnhancedBrokerDetails.BrokerNumber
BP.UserEnhancedBrokerDetails.BrokerRegistrationNumber=BPStaging.UserEnhancedBrokerDetails.BrokerRegistrationNumber
BP.UserEnhancedBrokerDetails.BrokerRegistrationDate=BPStaging.UserEnhancedBrokerDetails.BrokerRegistrationDate
BP.UserEnhancedBrokerDetails.BrokerRegistrationType=BPStaging.UserEnhancedBrokerDetails.BrokerRegistrationType
BP.UserEnhancedBrokerDetails.BrokerQualification=BPStaging.UserEnhancedBrokerDetails.BrokerQualification
BP.UserEnhancedBrokerDetails.TelephoneNumber=BPStaging.UserEnhancedBrokerDetails.TelephoneNumber
*/

CREATE PROCEDURE BPStaging.Load_BP_UserEnhancedBrokerDetails
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'BP.UserEnhancedBrokerDetails';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

IF EXISTS (SELECT * FROM BPStaging.UserEnhancedBrokerDetails)
BEGIN
    BEGIN TRY
        MERGE BP.UserEnhancedBrokerDetails WITH (HOLDLOCK) t
        USING (
            SELECT
                UserId
              , BrokerNumber
              , BrokerRegistrationNumber
              , BrokerRegistrationDate
              , BrokerRegistrationType
              , BrokerQualification
              , TelephoneNumber
              , IsDeleted = 0
            FROM
                BPStaging.UserEnhancedBrokerDetails
        ) s
        ON t.UserId = s.UserId
        WHEN NOT MATCHED
            THEN INSERT (
                     UserId
                   , BrokerNumber
                   , BrokerRegistrationNumber
                   , BrokerRegistrationDate
                   , BrokerRegistrationType
                   , BrokerQualification
                   , TelephoneNumber
                   , ETLCreatedDate
                   , ETLUpdatedDate
                   , IsDeleted
                 )
                 VALUES
                     (
                         s.UserId
                       , s.BrokerNumber
                       , s.BrokerRegistrationNumber
                       , s.BrokerRegistrationDate
                       , s.BrokerRegistrationType
                       , s.BrokerQualification
                       , s.TelephoneNumber
                       , GETUTCDATE()
                       , GETUTCDATE()
                       , IsDeleted
                     )
        WHEN MATCHED AND NOT EXISTS (
    SELECT
        t.BrokerNumber
      , t.BrokerRegistrationNumber
      , t.BrokerRegistrationDate
      , t.BrokerRegistrationType
      , t.BrokerQualification
      , t.TelephoneNumber
      , t.IsDeleted
    INTERSECT
    SELECT
        s.BrokerNumber
      , s.BrokerRegistrationNumber
      , s.BrokerRegistrationDate
      , s.BrokerRegistrationType
      , s.BrokerQualification
      , s.TelephoneNumber
      , s.IsDeleted
)
            THEN UPDATE SET
                     t.BrokerNumber = s.BrokerNumber
                   , t.BrokerRegistrationNumber = s.BrokerRegistrationNumber
                   , t.BrokerRegistrationDate = s.BrokerRegistrationDate
                   , t.BrokerRegistrationType = s.BrokerRegistrationType
                   , t.BrokerQualification = s.BrokerQualification
                   , t.TelephoneNumber = s.TelephoneNumber
                   , t.ETLUpdatedDate = GETUTCDATE()
                   , t.IsDeleted = s.IsDeleted
        WHEN NOT MATCHED BY SOURCE AND t.IsDeleted = 0
            THEN UPDATE SET
                     t.IsDeleted = 1
                   , t.ETLUpdatedDate = GETUTCDATE()
        OUTPUT $ACTION
        INTO @Actions;

        SELECT
            @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                          THEN 1
                                      ELSE 0 END
                             )
          , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                         THEN 1
                                     ELSE 0 END
                            )
          , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                         THEN 1
                                     ELSE 0 END
                            )
        FROM
            @Actions;
    END TRY
    BEGIN CATCH
        DECLARE @ErrorMessage NVARCHAR(MAX);

        SET @ErrorMessage = ERROR_MESSAGE();

        EXEC ADF.StoredProcErrorLog
            @SprocName
          , @ErrorMessage;

        SET @RejectedCount = 1;
    END CATCH;
END;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);