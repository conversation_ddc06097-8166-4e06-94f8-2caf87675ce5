/*
Lineage
ref.BasisOfCover.BasisOfCoverKey=BPStaging.BasisOfCover.Id
ref.BasisOfCover.TranslationKey=BPStaging.BasisOfCover.TranslationKey
ref.BasisOfCover.BasisOfCover=BPStaging.BasisOfCover.Text
ref.BasisOfCover.ScopeId=BPStaging.BasisOfCover.ScopeId
ref.BasisOfCover.SourceUpdatedDate=BPStaging.BasisOfCover.ValidTo
ref.BasisOfCover.SourceUpdatedDate=BPStaging.BasisOfCover.ValidFrom
ref.BasisOfCover.IsDeprecated=BPStaging.BasisOfCover.IsDeprecated
ref.BasisOfCover.IsDeprecated=BPStaging.BasisOfCover.ValidTo
*/

CREATE PROCEDURE BPStaging.Load_ref_BasisOfCover
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'ref.BasisOfCover';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);
DECLARE @BPDataSourceInstanceId INT = 50366;

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

IF EXISTS (SELECT * FROM BPStaging.BasisOfCover)
BEGIN
    BEGIN TRY
        MERGE ref.BasisOfCover t
        USING (
            SELECT
                BasisOfCoverKey = CAST(Id AS NVARCHAR(100))
              , TranslationKey
              , BasisOfCover = Text
              , ScopeId
              , DataSourceInstanceId = 50366
              , SourceUpdatedDate = CASE WHEN YEAR(ValidTo) < 9999
                                             THEN ValidTo
                                         ELSE ValidFrom END
              , IsDeprecated = IIF(YEAR(ValidTo) < 9999, 1, IsDeprecated)
            FROM
                BPStaging.BasisOfCover
        ) s
        ON s.BasisOfCoverKey = t.BasisOfCoverKey
           AND s.DataSourceInstanceId = t.DataSourceInstanceId
        WHEN NOT MATCHED
            THEN INSERT (
                     DataSourceInstanceId
                   , BasisOfCoverKey
                   , TranslationKey
                   , BasisOfCover
                   , ScopeId
                   , ETLCreatedDate
                   , ETLUpdatedDate
                   , SourceUpdatedDate
                   , IsDeprecated
                 )
                 VALUES
                     (
                         s.DataSourceInstanceId
                       , s.BasisOfCoverKey
                       , s.TranslationKey
                       , s.BasisOfCover
                       , s.ScopeId
                       , GETUTCDATE()
                       , GETUTCDATE()
                       , s.SourceUpdatedDate
                       , s.IsDeprecated
                     )
        WHEN MATCHED AND NOT EXISTS (
    SELECT
        t.TranslationKey
      , t.BasisOfCover
      , t.ScopeId
      , t.SourceUpdatedDate
      , t.IsDeprecated
    INTERSECT
    SELECT
        s.TranslationKey
      , s.BasisOfCover
      , s.ScopeId
      , s.SourceUpdatedDate
      , s.IsDeprecated
)
            THEN UPDATE SET
                     t.TranslationKey = s.TranslationKey
                   , t.BasisOfCover = s.BasisOfCover
                   , t.ScopeId = s.ScopeId
                   , t.ETLUpdatedDate = GETUTCDATE()
                   , t.SourceUpdatedDate = s.SourceUpdatedDate
                   , t.IsDeprecated = s.IsDeprecated
        WHEN NOT MATCHED BY SOURCE AND t.DataSourceInstanceId = @BPDataSourceInstanceId
                                       AND t.IsDeprecated = 0
            THEN UPDATE SET
                     t.IsDeprecated = 1
                   , t.ETLUpdatedDate = GETUTCDATE()
        OUTPUT $ACTION
        INTO @Actions;

        SELECT
            @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                          THEN 1
                                      ELSE 0 END
                             )
          , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                         THEN 1
                                     ELSE 0 END
                            )
        FROM
            @Actions;
    END TRY
    BEGIN CATCH
        DECLARE @ErrorMessage NVARCHAR(MAX);

        SET @ErrorMessage = ERROR_MESSAGE();

        EXEC ADF.StoredProcErrorLog
            @SprocName
          , @ErrorMessage;

        SET @RejectedCount = 1;
    END CATCH;
END;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , 0
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = 0
  , RejectedCount = ISNULL(@RejectedCount, 0);

RETURN 0;
