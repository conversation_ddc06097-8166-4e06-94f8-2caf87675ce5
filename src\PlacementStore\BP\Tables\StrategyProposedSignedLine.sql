CREATE TABLE BP.StrategyProposedSignedLine (
    Id                    INT            NOT NULL
  , StrategyId            INT            NOT NULL
  , MarketQuoteResponseId INT            NOT NULL
  , SignedLine            DECIMAL(19, 4) NULL
  , SignedLineRate        DECIMAL(21, 6) NULL
  , SignedLineRateOfOrder DECIMAL(21, 6) NULL
  , SourceUpdatedDate     DATETIME2(7)   NOT NULL
  , ETLCreatedDate        DATETIME2(7)   NOT NULL
  , ETLUpdatedDate        DATETIME2(7)   NOT NULL
  , IsDeleted             BIT            NOT NULL
        DEFAULT (0)
  , CONSTRAINT PK_BP_StrategyProposedSignedLine
        PRIMARY KEY
        (
            Id
        )
  , CONSTRAINT FK_BP_StrategyProposedSignedLine_BP_Strategy
        FOREIGN KEY
        (
            StrategyId
        )
        REFERENCES BP.Strategy
        (
            Id
        )
);
