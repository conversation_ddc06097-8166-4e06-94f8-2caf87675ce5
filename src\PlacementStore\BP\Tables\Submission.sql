CREATE TABLE BP.Submission (
    Id                    INT              NOT NULL
  , Submission<PERSON><PERSON>rId INT              NOT NULL
  , Sent                  DATETIME         NOT NULL
  , UserId                INT              NOT NULL
  , MessageHtml           NVARCHAR(MAX)    NOT NULL
  , LeadQuoteIdTo<PERSON>ollow   INT              NULL
  , ExposureSnapshotGuid  UNIQUEIDENTIFIER NULL
  , SourceUpdatedDate     DATETIME2(7)     NOT NULL
  , ETLCreatedDate        DATETIME2(7)     NOT NULL
        DEFAULT GETUTCDATE()
  , ETLUpdatedDate        DATETIME2(7)     NOT NULL
        DEFAULT GETUTCDATE()
  , IsDeleted             BIT              NOT NULL
  , CONSTRAINT PK_BP_Submission
        PRIMARY KEY
        (
            Id
        )
);
GO

CREATE INDEX IX_BP_Submission_SubmissionContainerId
ON BP.Submission
(
    SubmissionContainerId
);