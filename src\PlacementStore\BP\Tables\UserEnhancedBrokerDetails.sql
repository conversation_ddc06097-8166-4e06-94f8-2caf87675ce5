﻿CREATE TABLE BP.UserEnhancedBrokerDetails (
    UserId                   INT          NOT NULL
  , BrokerNumber             VARCHAR(MAX) NULL
  , BrokerRegistrationNumber VARCHAR(MAX) NULL
  , BrokerRegistrationDate   DATE         NULL
  , BrokerRegistrationType   VARCHAR(MAX) NULL
  , BrokerQualification      VARCHAR(MAX) NULL
  , TelephoneNumber          VARCHAR(MAX) NULL
  , ETLCreatedDate           DATETIME2(7) NOT NULL
        DEFAULT GETUTCDATE()
  , ETLUpdatedDate           DATETIME2(7) NOT NULL
        DEFAULT GETUTCDATE()
  , IsDeleted                BIT          NOT NULL
        DEFAULT (0)
  , CONSTRAINT PK_BP_UserEnhancedBrokerDetails
        PRIMARY KEY
        (
            UserId
        )
);
