/*
Lineage
FMATemp.staging_vw_ReferenceDataProductMapping.Id=dbo.Product.ProductId
FMATemp.staging_vw_ReferenceDataProductMapping.ClassOfBusinessKey=dbo.Product.ProductKey
FMATemp.staging_vw_ReferenceDataProductMapping.LineOfBusinessKey=dbo.Product.ProductKey
FMATemp.staging_vw_ReferenceDataProductMapping.ProductId=rpt.ProductHierarchy.ProductId
FMATemp.staging_vw_ReferenceDataProductMapping.IsDeprecated=dbo.Product.IsDeleted
*/
CREATE PROCEDURE FMATemp.StageReferenceDataProductMapping
    @LastUpdatedDate DATETIME2(7)
AS
DECLARE @MaxLastUpdatedUTCDate DATETIME2(7);
DECLARE @InsertedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @SourceTable VARCHAR(50) = 'dbo.Product,rpt.ProductHierarchy';
DECLARE @TargetTable VARCHAR(50) = 'FMATemp.staging_vw_ReferenceDataProductMapping';
DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET NOCOUNT ON;

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    TRUNCATE TABLE FMATemp.staging_vw_ReferenceDataProductMapping;

    DROP TABLE IF EXISTS #ReferenceDataProductMapping;

    SELECT
        Id = p.ProductId --Not possible to use the ProductId from rpt.ProductHierarchy as we split the Class and Line and load these seperately. A line can have more than one class in BP but we dont represent this in the ProductHierarchy.. 
      , ClassOfBusinessKey = LEFT(p.ProductKey, CHARINDEX('|', p.ProductKey) - 1)
      , LineOfBusinessKey = SUBSTRING(
                                p.ProductKey
                              , CHARINDEX('|', p.ProductKey) + 1
                              , LEN(p.ProductKey) - CHARINDEX('|', p.ProductKey)
                            )
      , rph.ProductId
      , ETLUpdatedDate = p.LastUpdatedUTCDate
      , IsDeprecated = p.IsDeleted
    INTO #ReferenceDataProductMapping
    FROM
        dbo.Product p
        INNER JOIN rpt.ProductHierarchy rph
            ON rph.ProductId = p.ReferenceProductId
    WHERE
        p.DataSourceInstanceId = 50366
        AND (
            p.ProductKey LIKE 'classOfBusiness%|lineOfBusiness_%'
            OR p.ProductKey LIKE '|lineOfBusiness_%'
        )
        AND (
            p.CreatedUTCDate > @LastUpdatedDate
            OR p.LastUpdatedUTCDate > @LastUpdatedDate
        );

    /* No point doing too much if no records returned */
    IF (
        SELECT COUNT(*) FROM #ReferenceDataProductMapping
    ) > 0
    BEGIN
        INSERT INTO
            FMATemp.staging_vw_ReferenceDataProductMapping
            (
                Id
              , ClassOfBusinessKey
              , LineOfBusinessKey
              , ProductId
              , IsDeprecated
            )
        SELECT
            Id
          , ClassOfBusinessKey
          , LineOfBusinessKey
          , ProductId
          , IsDeprecated
        FROM
            #ReferenceDataProductMapping;

        SELECT @InsertedCount = @@ROWCOUNT;
    END;

    /* ETLUpdatedDate or equivalent */
    SELECT @MaxLastUpdatedUTCDate = ISNULL(MAX(ETLUpdatedDate), @LastUpdatedDate)
    FROM
        #ReferenceDataProductMapping;

    DROP TABLE #ReferenceDataProductMapping;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action =
    CONCAT(
        N'Load '
      , @TargetTable
      , N' from '
      , @SourceTable
      , N'; @LastUpdatedDate = '
      , CONVERT(NVARCHAR(50), @LastUpdatedDate, 21)
      , N'.'
    );

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , NULL
  , NULL
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0)
  , MaxLastUpdatedUTCDate = ISNULL(@MaxLastUpdatedUTCDate, @LastUpdatedDate);

RETURN 0;