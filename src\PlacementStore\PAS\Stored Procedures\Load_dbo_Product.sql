﻿/*
Lineage
dbo.Product.DataSourceInstanceId=PAS.Product.DataSourceInstanceId
dbo.Product.ProductKey=PAS.Product.ProductKey
dbo.Product.ProductLine=PAS.ProductAttribute.EclipseProduct
dbo.Product.ProductLine=PAS.Product.ProductDescription
dbo.Product.ProductLine=PAS.ProductAttribute.EpicPolicyLineType
dbo.Product.ProductClass=PAS.ProductAttribute.EclipseProductClass
dbo.Product.ProductClass=PAS.Product.ProductDescription
dbo.Product.ProductClass=PAS.ProductAttribute.eGlobalTBL_FULLNAME
dbo.Product.ProductClass=PAS.ProductAttribute.WIBSAreaDescription
dbo.Product.SourceProductId=PAS.Product.PASProductId
dbo.Product.ReferenceProductId=PAS.Product.GlobalProductId
dbo.Product.SourceProductName=PAS.ProductAttribute.EclipseBusinessClass
dbo.Product.SourceProductName=PAS.Product.ProductDescription
dbo.Product.SourceProductName=PAS.ProductAttribute.eGlobalRI_DESCRIPTIONA
dbo.Product.SourceProductName=PAS.ProductAttribute.eGlobalRI_ABBRNAME
dbo.Product.SourceProductName=PAS.Product.Product
dbo.Product.SourceProductDescription=PAS.Product.ProductDescription
dbo.Product.SourceParentId=PAS.Product.PASProductId
dbo.Product.ReferenceSourceProductName=PAS.ProductAttribute.EpicPolicyLineType
dbo.Product.ReferenceSourceProductName=PAS.ProductAttribute.WIBSDescription
dbo.Product.ReferenceSourceProductName=Reference.ProductMapping.SourceProductName
dbo.Product.ReferenceSourceProductDescription=Reference.ProductMapping.SourceProductDescription
dbo.Product.SourceLastUpdateDate=PAS.Product.ETLUpdatedDate
dbo.Product.IsMultiRisk=PAS.ProductAttribute.eGlobalRI_MULTIRISK
dbo.Product.IsInactive=PAS.ProductAttribute.eGlobalRI_INACTIVERISK
dbo.Product.IsDeleted=PAS.Product.IsDeleted
dbo.Product.ParentProductId=dbo.Product.ProductId
*/

CREATE PROCEDURE PAS.Load_dbo_Product
AS
BEGIN
    DECLARE @InsertedCount INT = 0;
    DECLARE @UpdatedCount INT = 0;
    DECLARE @DeletedCount INT = 0;
    DECLARE @RejectedCount INT = 0;

    DECLARE @Actions TABLE (
        Change VARCHAR(20)
    );

    DECLARE @SprocName VARCHAR(255);
    DECLARE @Action NVARCHAR(255);

    SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

    EXEC ADF.StoredProcStartLog @SprocName;

    BEGIN TRY
        -- Only process is something staged.
        IF (EXISTS (SELECT 1 FROM PAS.Product))
        BEGIN
            CREATE TABLE #Product (
                DataSourceInstanceId              INT            NOT NULL
              , ProductKey                        NVARCHAR(200)  NOT NULL
              , ProductLine                       NVARCHAR(200)  NULL
              , ProductClass                      NVARCHAR(200)  NULL
              , SourceProductId                   INT            NULL
              , ReferenceProductId                INT            NULL
              , SourceProductName                 NVARCHAR(200)  NULL
              , SourceProductDescription          NVARCHAR(4000) NULL
              , SourceParentId                    INT            NULL
              , ReferenceSourceProductName        NVARCHAR(200)  NULL
              , ReferenceSourceProductDescription NVARCHAR(4000) NULL
              , SourceLastUpdateDate              DATETIME2(7)   NULL
              , SourcedFromBrokingPlatform        BIT            NOT NULL
              , IsMultiRisk                       BIT            NULL
              , IsInactive                        BIT            NULL
              , IsDeleted                         BIT            NOT NULL
            );

            INSERT INTO
                #Product
                (
                    DataSourceInstanceId
                  , ProductKey
                  , ProductLine
                  , ProductClass
                  , SourceProductId
                  , ReferenceProductId
                  , SourceProductName
                  , SourceProductDescription
                  , SourceParentId
                  , ReferenceSourceProductName
                  , ReferenceSourceProductDescription
                  , SourceLastUpdateDate
                  , SourcedFromBrokingPlatform
                  , IsMultiRisk
                  , IsInactive
                  , IsDeleted
                )
            SELECT
                p.DataSourceInstanceId
              , p.ProductKey
              , ProductLine = CASE WHEN p.DataSourceInstanceId = 50000 --Eclipse
                                       THEN pa.EclipseProduct
                                   WHEN p.DataSourceInstanceId = 50003 -- COL
                                       THEN CASE WHEN colpr.Product3 IS NOT NULL
                                                      AND p.ProductKey NOT LIKE 'CEL%'
                                                      AND p.ProductKey NOT LIKE 'RAM%'
                                                     THEN colpr.Product2
                                                 WHEN colpr.Product3 IS NULL
                                                      AND colpr.Product2 IS NOT NULL
                                                      AND p.ProductKey NOT LIKE 'CEL%'
                                                      AND p.ProductKey NOT LIKE 'RAM%'
                                                     THEN colpr.Product1
                                                 WHEN colpr.Product3 IS NULL
                                                      AND colpr.Product2 IS NULL
                                                      AND colpr.Product1 IS NOT NULL
                                                      AND p.ProductKey LIKE 'RAM%'
                                                     THEN colpr.Product1 END
                                   WHEN dsi.DataSourceId = 50002 --EPIC (NA & Canada)
                                       THEN pa.EpicPolicyLineType END
              , ProductClass = CASE WHEN p.DataSourceInstanceId = 50000 -- Eclipse
                                        THEN pa.EclipseProductClass
                                    WHEN p.DataSourceInstanceId = 50003 -- COL
                                        THEN CASE WHEN colpr.Product3 IS NOT NULL
                                                       AND p.ProductKey NOT LIKE 'CEL%'
                                                       AND p.ProductKey NOT LIKE 'RAM%'
                                                      THEN colpr.Product1
                                                  WHEN colpr.Product3 IS NULL
                                                       AND colpr.Product2 IS NULL
                                                       AND colpr.Product1 IS NOT NULL
                                                       AND p.ProductKey LIKE 'CEL%'
                                                      THEN colpr.Product1 END
                                    WHEN dsi.DataSourceId = 50001 -- eGlobal (All Instances)
                                        THEN pa.eGlobalTBL_FULLNAME
                                    WHEN p.DataSourceInstanceId = 50045 --Italy
                                        THEN pa.WIBSAreaDescription END
              , SourceProductId = p.PASProductId
              , ReferenceProductId = p.GlobalProductId
              , SourceProductName = CASE WHEN p.DataSourceInstanceId = 50000 --Eclipse
                                             THEN pa.EclipseBusinessClass
                                         WHEN p.DataSourceInstanceId = 50003 -- COL
                                             THEN CASE WHEN colpr.Product3 IS NOT NULL
                                                           THEN colpr.Product3
                                                       WHEN colpr.Product3 IS NULL
                                                            AND colpr.Product2 IS NOT NULL
                                                            AND p.ProductKey NOT LIKE 'CEL%'
                                                            AND p.ProductKey NOT LIKE 'RAM%'
                                                           THEN colpr.Product2
                                                       WHEN colpr.Product3 IS NULL
                                                            AND colpr.Product2 IS NULL
                                                            AND colpr.Product1 IS NOT NULL
                                                            AND p.ProductKey NOT LIKE 'CEL%'
                                                            AND p.ProductKey NOT LIKE 'RAM%'
                                                           THEN colpr.Product1 END
                                         WHEN dsi.DataSourceId = 50001 -- eGlobal (All Instances)
                                             THEN CASE WHEN p.DataSourceInstanceId = 50010 -- eGlobal Netherlands
                                                           THEN pa.eGlobalRI_DESCRIPTIONA
                                                       ELSE pa.eGlobalRI_ABBRNAME END
                                         WHEN p.DataSourceInstanceId = 50045 --Italy
                                             THEN p.Product
                                         ELSE p.ProductDescription END
              , SourceProductDescription = CASE WHEN dsi.DataSourceId = 50001 -- eGlobal (All Instances)
                                                    THEN p.ProductDescription
                                                WHEN p.DataSourceInstanceId = 50003 -- COL
                                                    THEN colpr.ProductDescription
                                                ELSE p.ProductDescription END
              , SourceParentId = pp.PASProductId
              , ReferenceSourceProductName = CASE WHEN p.DataSourceInstanceId = 50001 -- EPIC US
                                                      THEN pa.EpicPolicyLineType
                                                  WHEN p.DataSourceInstanceId = 50045 --Italy
                                                      THEN pa.WIBSDescription
                                                  ELSE prdmap.SourceProductName END
              , ReferenceSourceProductDescription = prdmap.SourceProductDescription
              , SourceLastUpdateDate = p.ETLUpdatedDate
              , SourcedFromBrokingPlatform = 0
              , IsMultiRisk = CASE WHEN dsi.DataSourceId = 50001 -- eGlobal (All Instances)
                                       THEN CASE WHEN pa.eGlobalRI_MULTIRISK = -1
                                                     THEN 1
                                                 ELSE NULL END END
              , IsInactive = CASE WHEN dsi.DataSourceId = 50001 -- eGlobal (All Instances)
                                      THEN CASE WHEN pa.eGlobalRI_INACTIVERISK = -1
                                                    THEN 1
                                                ELSE NULL END END
              , p.IsDeleted
            FROM
                PAS.Product p
                LEFT JOIN PAS.Product pp
                    ON pp.ProductKey = p.ParentKey
                       AND pp.DataSourceInstanceId = p.DataSourceInstanceId

                INNER JOIN Reference.DataSourceInstance dsi
                    ON dsi.DataSourceInstanceId = p.DataSourceInstanceId

                LEFT JOIN Reference.ProductMapping prdmap
                    ON prdmap.DataSourceInstanceId = p.DataSourceInstanceId
                       AND prdmap.SourceProductKey = p.ProductKey

                LEFT JOIN PAS.ProductAttribute pa
                    ON pa.ProductKey = p.ProductKey
                       AND pa.DataSourceInstanceId = p.DataSourceInstanceId
                       AND pa.IsDeleted = 0

                LEFT JOIN (
                    /* For COL - Breaks out the ProductDescription into 3 fields split on semi-colon */
                    SELECT
                        pvt.ProductKey
                      , pvt.DataSourceInstanceId
                      , pvt.ProductDescription
                      , Product1 = [1]
                      , Product2 = [2]
                      , Product3 = [3]
                    FROM (
                        SELECT
                            p.ProductKey
                          , p.DataSourceInstanceId
                          , p.ProductDescription
                          , ss.value
                          , RowNum = ROW_NUMBER() OVER (PARTITION BY p.ProductKey ORDER BY p.ProductKey)
                        FROM
                            PAS.Product p
                            CROSS APPLY STRING_SPLIT(ProductDescription, ';') ss
                        WHERE
                            p.DataSourceInstanceId = 50003
                    ) src
                    PIVOT (
                        MIN(value)
                        FOR RowNum IN (
                            [1], [2], [3]
                        )
                    ) AS pvt
                ) colpr
                    ON colpr.ProductKey = p.ProductKey
                       AND colpr.DataSourceInstanceId = p.DataSourceInstanceId;

            MERGE dbo.Product WITH (HOLDLOCK) t
            USING (
                SELECT
                    DataSourceInstanceId
                  , ProductKey
                  , ProductLine
                  , ProductClass
                  , SourceProductId
                  , ReferenceProductId
                  , SourceProductName
                  , SourceProductDescription
                  , SourceParentId
                  , ReferenceSourceProductName
                  , ReferenceSourceProductDescription
                  , SourceLastUpdateDate
                  , SourcedFromBrokingPlatform
                  , IsMultiRisk
                  , IsInactive
                  , IsDeleted
                FROM
                    #Product
            ) s
            ON t.SourceProductId = s.SourceProductId
               AND t.DataSourceInstanceId = s.DataSourceInstanceId
               AND t.SourcedFromBrokingPlatform = 0
            WHEN NOT MATCHED BY TARGET
                THEN INSERT (
                         DataSourceInstanceId
                       , ProductKey
                       , ProductLine
                       , ProductClass
                       , SourceProductId
                       , ReferenceProductId
                       , SourceProductName
                       , SourceProductDescription
                       , SourceParentId
                       , ReferenceSourceProductName
                       , ReferenceSourceProductDescription
                       , SourceLastUpdateDate
                       , SourcedFromBrokingPlatform
                       , IsMultiRisk
                       , IsInactive
                       , CreatedUTCDate
                       , LastUpdatedUTCDate
                       , IsDeleted
                     )
                     VALUES
                         (
                             s.DataSourceInstanceId
                           , s.ProductKey
                           , s.ProductLine
                           , s.ProductClass
                           , s.SourceProductId
                           , s.ReferenceProductId
                           , s.SourceProductName
                           , s.SourceProductDescription
                           , s.SourceParentId
                           , s.ReferenceSourceProductName
                           , s.ReferenceSourceProductDescription
                           , s.SourceLastUpdateDate
                           , s.SourcedFromBrokingPlatform
                           , s.IsMultiRisk
                           , s.IsInactive
                           , GETUTCDATE()
                           , GETUTCDATE()
                           , s.IsDeleted
                         )
            WHEN MATCHED AND NOT EXISTS (
            SELECT
                s.ProductKey
              , s.ProductLine
              , s.ProductClass
              , s.ReferenceProductId
              , s.SourceProductName
              , s.SourceProductDescription
              , s.SourceParentId
              , s.ReferenceSourceProductName
              , s.ReferenceSourceProductDescription
              , s.IsMultiRisk
              , s.IsInactive
              , s.IsDeleted
            INTERSECT
            SELECT
                t.ProductKey
              , t.ProductLine
              , t.ProductClass
              , t.ReferenceProductId
              , t.SourceProductName
              , t.SourceProductDescription
              , t.SourceParentId
              , t.ReferenceSourceProductName
              , t.ReferenceSourceProductDescription
              , t.IsMultiRisk
              , t.IsInactive
              , t.IsDeleted
        )
                THEN UPDATE SET
                         t.ProductKey = s.ProductKey
                       , t.ProductLine = s.ProductLine
                       , t.ProductClass = s.ProductClass
                       , t.ReferenceProductId = s.ReferenceProductId
                       , t.SourceProductName = s.SourceProductName
                       , t.SourceProductDescription = s.SourceProductDescription
                       , t.SourceParentId = s.SourceParentId
                       , t.ReferenceSourceProductName = s.ReferenceSourceProductName
                       , t.ReferenceSourceProductDescription = s.ReferenceSourceProductDescription
                       , t.IsMultiRisk = s.IsMultiRisk
                       , t.IsInactive = s.IsInactive
                       , t.SourceLastUpdateDate = s.SourceLastUpdateDate
                       , t.LastUpdatedUTCDate = GETUTCDATE()
                       , t.IsDeleted = s.IsDeleted
            OUTPUT $ACTION
            INTO @Actions;

            -- Updated ParentProductId
            UPDATE p
            SET
                p.ParentProductId = pp.ProductId
              , p.LastUpdatedUTCDate = GETUTCDATE()
            OUTPUT 'UPDATE'
            INTO @Actions
            FROM
                #Product src
                INNER JOIN dbo.Product p
                    ON p.SourceProductId = src.SourceProductId
                       AND p.DataSourceInstanceId = src.DataSourceInstanceId
                       AND p.SourcedFromBrokingPlatform = 0

                INNER JOIN dbo.Product pp
                    ON pp.SourceProductId = src.SourceParentId
                       AND pp.DataSourceInstanceId = src.DataSourceInstanceId
                       AND pp.SourcedFromBrokingPlatform = 0;
        END;
    END TRY
    BEGIN CATCH
        DECLARE @ErrorMessage NVARCHAR(MAX);

        SET @ErrorMessage = ERROR_MESSAGE();

        EXEC ADF.StoredProcErrorLog
            @SprocName
          , @ErrorMessage;

        SET @RejectedCount = 1;
    END CATCH;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;

    SET @Action = N'Load dbo.Product from PAS.Product';

    EXEC ADF.StoredProcSetSqlLog
        @SprocName
      , @InsertedCount
      , @UpdatedCount
      , @DeletedCount
      , @RejectedCount
      , @Action
      , NULL;

    EXEC ADF.StoredProcEndLog @SprocName;

    SELECT
        InsertedCount = ISNULL(@InsertedCount, 0)
      , UpdatedCount = ISNULL(@UpdatedCount, 0)
      , DeletedCount = ISNULL(@DeletedCount, 0)
      , RejectedCount = ISNULL(@RejectedCount, 0);

    RETURN 0;
END;