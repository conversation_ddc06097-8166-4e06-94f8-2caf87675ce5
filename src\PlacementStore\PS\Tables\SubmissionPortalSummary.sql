CREATE TABLE PS.SubmissionPortalSummary (
    SubmissionContainerMarketId INT NOT NULL
  , SubmissionId                INT NOT NULL
  , SubmissionCount             INT
  , NumberOfPortalUsers         INT
  , NotifiedCount               INT
  , OpenedCount                 INT
  , FirstOpened                 DATETIME2(7)
  , LastOpened                  DATETIME2(7)
  , FirstNotified               DATETIME2(7)
  , LastNotified                DATETIME2(7)
  , CONSTRAINT PK_PS_SubmissionPortalSummary
        PRIMARY KEY
        (
            SubmissionContainerMarketId
          , SubmissionId
        )
  , CONSTRAINT FK_PS_SubmissionPortalSummary_BP_Submission
        FOREIGN KEY
        (
            SubmissionId
        )
        REFERENCES BP.Submission
        (
            Id
        ) ON DELETE CASCADE
);
GO

CREATE INDEX IX_PS_SubmissionPortalSummary_SubmissionContainerMarketId
ON PS.SubmissionPortalSummary
(
    SubmissionContainerMarketId
);
GO

CREATE INDEX IX_PS_SubmissionPortalSummary_SubmissionId
ON PS.SubmissionPortalSummary
(
    SubmissionId
);
