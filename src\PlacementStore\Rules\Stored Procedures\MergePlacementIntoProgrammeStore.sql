/*
Lineage
*/

/*
LineageOptions=NoScan
This stored procedure uses dynamic SQL to create records into the dbo.Placement and dbo.PlacementPolicy table
*/

/*
    Run the grouping rules for a given data source instance
*/
CREATE PROCEDURE Rules.MergePlacementIntoProgrammeStore
    @RunId                INT
  , @DataSourceInstanceId INT
  , @Success              BIT OUTPUT
AS
SET NOCOUNT ON;

DECLARE @SprocName VARCHAR(255);
DECLARE @SQLF NVARCHAR(MAX);
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @Action NVARCHAR(255);

SET @SprocName =
    OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID) + '(' + CONVERT(VARCHAR(255), @DataSourceInstanceId)
    + ')' + ' - ' + 'RunID:' + CONVERT(VARCHAR(255), @RunId);

EXEC ADF.StoredProcStartLog @SprocName;

-- Get Complete Status
DECLARE @PlacementStatus INT;

SELECT @PlacementStatus = PlacementStatusId
FROM
    ref.PlacementStatus WITH (NOLOCK)
WHERE
    PlacementStatus = 'Complete'
    AND DataSourceInstanceId = 50366
    AND IsDeprecated = 0;

DECLARE @PlacementName NVARCHAR(500);
DECLARE @SQL NVARCHAR(MAX);
DECLARE @SQL1 NVARCHAR(MAX);
DECLARE @SQL2 NVARCHAR(MAX);

-- This bit get the SQL Query used to build the placement name.
-- It is the reason we have a dynamic query below as this bit is dynamic.
SELECT @PlacementName = IdSql
FROM
    PactConfig.[Rule] WITH (NOLOCK)
WHERE
    DataSourceInstanceId = @DataSourceInstanceId
    AND RuleTypeId = 7
    AND IsDeleted = 0;

SELECT
    @PlacementName =
    ISNULL(
        @PlacementName
      , 'SUBSTRING(MIN(isnull(p.[PolicyDescription], CLNT.PartyName + ''_'' + convert(VARCHAR(10), year(p.ExpiryDate)))), 1, 255)'
    ); --> Set Default one

BEGIN TRY
    DROP TABLE IF EXISTS #EpicNotRemarketing;

    -- PBI140113 - Setting NA Appraisal Type based on Business Process Indicator
    -- Get the Epic data ready if required. This is a Tacit flag.
    CREATE TABLE #EpicNotRemarketing (
        PolicyId       BIGINT NOT NULL
      , NotRemarketing BIT    NOT NULL
    );

    IF @DataSourceInstanceId IN (
           50001, 50354
       )
    BEGIN
        INSERT INTO
            #EpicNotRemarketing
        SELECT DISTINCT
               ps.PolicyId
             , NotRemarketing = CAST(1 AS BIT)
        FROM
            dbo.PolicySection ps WITH (NOLOCK)
            INNER JOIN PAS.PolicySection pasps WITH (NOLOCK)
                ON pasps.PASPolicySectionId = ps.PACTPolicySectionId

            INNER JOIN PAS.PolicySectionAttribute paspsa WITH (NOLOCK)
                ON paspsa.PolicySectionKey = pasps.PolicySectionKey
                   AND paspsa.DataSourceInstanceId = pasps.DataSourceInstanceId
                   AND paspsa.EpicBPI LIKE 'Automatic Renewal %'
                   AND paspsa.IsDeleted = 0
        WHERE
            ps.DataSourceInstanceId = @DataSourceInstanceId
            AND ps.IsDeleted = 0
            AND ps.SourceQueryId NOT IN (
                    3685, 7737
                ) -- Dummies
        ;

        CREATE INDEX IX_#Epic
        ON #EpicNotRemarketing
        (
            PolicyId
        );
    END;

    DROP TABLE IF EXISTS #PolicyProduct;

    SELECT
        ROW_NO = ROW_NUMBER() OVER (PARTITION BY ps.PolicyId
                                    ORDER BY
                                        ps.IsDeleted ASC
                                      , psp.IsDeleted ASC
                                      , prd.IsDeleted ASC
                                      , COALESCE(
                                            mprd.SourceProductDescription
                                          , prdparent.SourceProductName
                                          , prd.SourceProductDescription
                                          , prd.SourceProductName
                                        ) ASC
                              )
      , ps.PolicyId
      , prd.ProductId
      , prd.SourceProductDescription
      , prd.SourceProductName
      , prd.ReferenceSourceProductName
      , ParentProductName = CASE WHEN ps.DataSourceInstanceId = 50010
                                     THEN prdparent.SourceProductDescription + ' ' + ISNULL(pa.COLProductCode, '')
                                 ELSE prdparent.SourceProductName END
      , MasterProductName = mprd.SourceProductDescription
    INTO #PolicyProduct
    FROM
        dbo.PolicySection ps
        INNER JOIN Rules.PlacementPolicies pp
            ON pp.PolicyId = ps.PolicyId

        INNER JOIN dbo.PolicySectionProduct psp
            ON psp.PolicySectionId = ps.PolicySectionId

        INNER JOIN dbo.Product prd
            ON prd.ProductId = psp.ProductId

        LEFT JOIN dbo.Product prdparent --> consIdering Parent Product for NL
            ON prdparent.ProductId = prd.ParentProductId /*we have parentproductid in dbo product
                                                        which can be used to get parent child relation*/
               AND prdparent.DataSourceInstanceId = prd.DataSourceInstanceId
               AND prdparent.DataSourceInstanceId = 50010
               AND prdparent.IsMultiRisk = 1

        LEFT JOIN PS.ProductAttribute pa --> consIdering Parent Product for Brazil
            ON pa.ProductId = prd.ProductId
               AND pa.DataSourceInstanceId = 50003
               AND pa.IsDeleted = 0

        LEFT JOIN dbo.Product mprd
            ON mprd.ProductKey = pa.COLProductCode
               AND mprd.DataSourceInstanceId = pa.DataSourceInstanceId
    WHERE
        ps.DataSourceInstanceId = CAST(@DataSourceInstanceId AS NVARCHAR(10))
        AND pp.RunId = CAST(@RunId AS NVARCHAR(20));

    CREATE INDEX IX_PolicyProduct_Key
    ON #PolicyProduct
    (
        PolicyId
    )
    INCLUDE
    (
        ROW_NO
    );

    -- Merge in our resulting placements
    SELECT @SQL = N'MERGE [dbo].[Placement] AS T
         USING (
            SELECT ' + @PlacementName + N' AS [PlacementName],';

    -- Cast the first string in the expression to NVARCHAR(MAX) so it should force them
    -- all to be. It doesn't use the target to work out what the type should be.
    SELECT
        @SQL1 =
        CAST('
                CASE
                    WHEN p.DataSourceInstanceId = 50003 OR DSI.DataSourceId = 50001 THEN MIN(CLNT.[PartyId])
                    ELSE MIN(PTY.PartyId)
                END AS PartyId,
                ' AS NVARCHAR(MAX)) + CAST(@PlacementStatus AS NVARCHAR(5))
        + N' AS [PlacementStatusId],
                MIN(pp.InceptionDate) AS InceptionDate,
                MIN(pp.ExpiryDate) AS ExpiryDate,
                0 AS IsDeleted,
                pp.PlacementId AS PlacementReference,
                NULL AS CancellationReasonId,
                NULL AS CollectionId,
                NULL AS RenewedFromPlacementId,
                NULL AS RenewedToPlacementId,
                ''PACTImport'' AS CreatedUser,
                GETUTCDATE() AS CreatedUTCDate,
                ''PACTImport'' AS LastUpdatedUser,
                GETUTCDATE() AS LastUpdatedUTCDate,
                pp.DataSourceInstanceId,
                pp.RunId,
                pp.RuleId,
                MIN(i.RefInsuranceTypeId) AS RefInsuranceTypeId,
                MIN(p.BrokingSegmentId) AS BrokingSegmentId,
                MIN(p.BrokingRegionId) AS BrokingRegionId,
                MIN(p.BrokingSubSegmentId) AS BrokingSubSegmentId,
                NULL AS IndustryId,
                MIN(POff.ProducingOfficeId) AS ProducingOfficeId,
                MAX(
                    CASE
                        WHEN p.DataSourceInstanceId = 50000 THEN 4  /* Marketing (per Jilans request under PRB0048597) */
                        WHEN p.DataSourceInstanceId = 50010 AND p.AutoRenewFlag = 1 AND p.IndexationTacit = 1 THEN 6 /* Indexation Tacit */
                        WHEN p.DataSourceInstanceId = 50010 and p.AutoRenewFlag = 1 AND p.IndexationTacit = 0 THEN 5 /* Tacit */
                        WHEN p.DataSourceInstanceId = 50010 and p.AutoRenewFlag = 0 AND p.IndexationTacit = 1 THEN 4 /* Marketing */
                        WHEN p.DataSourceInstanceId = 50010 and p.AutoRenewFlag = 0 AND p.IndexationTacit = 0 THEN 4 /* Marketing */
                        WHEN epic.NotRemarketing = 1 THEN 3 /* Not Remarketing */
                        WHEN p.DataSourceInstanceId = 50029 THEN 5 /* Tacit (PBI 138096 Can''t rely on the attribute so asked to hard code for now) */
                        WHEN p.DataSourceInstanceId = 50045 and p.AutoRenewFlag = 1 THEN 5 /* Tacit */
                        ELSE 4 /* PBI 140113 Default all to Marketing. */
                   END)
                AS AppraisalTypeId,
                CASE
                    WHEN p.DataSourceInstanceId = 50000 THEN 1
                    ELSE NULL
                END AS RenewableOptionId,
                '  + CAST(@DataSourceInstanceId AS NVARCHAR(10))
        + N' AS ServicingPlatformId
            FROM [Rules].PlacementPolicies AS pp
            INNER JOIN dbo.[Policy] AS p
                ON pp.PolicyId = p.PolicyId
            LEFT JOIN Reference.DataSourceInstance AS DSI
                ON DSI.DataSourceInstanceId = P.DataSourceInstanceId
            LEFT JOIN ref.InsuranceType AS i
                ON p.InsuranceTypeId = i.InsuranceTypeId
            LEFT JOIN (
                    SELECT ROW_NUMBER() OVER (
                            PARTITION BY PRR.PolicyId ORDER BY PRR.IsDeleted ASC,
                                PTY.IsDeleted ASC,
                                PTY.PartyName ASC
                            ) AS ROW_NO,
                        PRR.PolicyId,
                        PTY.PartyId,
                        PTY.PartyName
                    FROM dbo.PolicyPartyRelationship AS PRR
                    LEFT JOIN dbo.Party AS PTY
                        ON PTY.PartyId = PRR.PartyId
                            AND PTY.SourceQueryId = CASE
                                WHEN PTY.DataSourceInstanceId = 50010
                                    THEN 1245
                                ELSE PTY.SourceQueryId
                                END
                    WHERE PTY.DataSourceInstanceId = ' + CAST(@DataSourceInstanceId AS NVARCHAR(10))
        + N'
                ) AS PTY
                ON PTY.PolicyId = P.PolicyId
                    AND PTY.ROW_NO = 1
            LEFT JOIN (
                    SELECT ROW_NUMBER() OVER (
                            PARTITION BY PRR.PolicyId ORDER BY PRR.IsDeleted ASC,
                                PTY.IsDeleted ASC,
                                PTY.PartyName ASC
                            ) AS ROW_NO,
                        PRR.PolicyId,
                        PTY.PartyId,
                        PTY.PartyName,
                        PTY.PartyKey,
                        PR.PartyRoleId,
                        PR.PartyRoleKey,
                        PR.PartyRole,
                        PR.GlobalPartyRoleId,
                        CASE WHEN PTY.PartyKey LIKE ''%|%'' Then RIGHT(PTY.PartyKey, CHARINDEX(''|'', REVERSE(PTY.PartyKey))-1) END AS PName
                    FROM dbo.PolicyPartyRelationship AS PRR
                    INNER JOIN dbo.Party AS PTY
                        ON PTY.PartyId = PRR.PartyId
                    INNER JOIN ref.PartyRole AS PR
                        ON PR.PartyRoleId = PRR.PartyRoleId
                            AND (PR.PartyRoleKey = ''CLIENT'' OR PR.PartyRole = ''Client'')
                    WHERE PR.DataSourceInstanceId =' + CAST(@DataSourceInstanceId AS NVARCHAR(10))
        + N'
                ) AS CLNT
                ON CLNT.PolicyId = P.PolicyId
                    AND CLNT.ROW_NO = 1
            LEFT JOIN (
                    SELECT ROW_NUMBER() OVER (
                            PARTITION BY PRR.PolicyId ORDER BY PRR.IsDeleted ASC,
                                PTY.IsDeleted ASC,
                                PTY.PartyName ASC
                            ) AS ROW_NO,
                        PRR.PolicyId,
                        PTY.PartyId,
                        PTY.PartyName,
                        PTY.PartyKey,
                        PR.PartyRoleId,
                        PR.PartyRoleKey,
                        PR.PartyRole,
                        PR.GlobalPartyRoleId
                    FROM dbo.PolicyPartyRelationship AS PRR
                    INNER JOIN dbo.Party AS PTY
                        ON PTY.PartyId = PRR.PartyId
                    INNER JOIN ref.PartyRole AS PR
                        ON PR.PartyRoleId = PRR.PartyRoleId
                            AND (PR.PartyRoleKey = ''INSURED'' OR PR.PartyRole = ''Insured'')
                    WHERE PR.DataSourceInstanceId =' + CAST(@DataSourceInstanceId AS NVARCHAR(10))
        + N'
                ) AS INSR
                ON INSR.PolicyId = P.PolicyId
                    AND INSR.ROW_NO = 1
            LEFT JOIN #PolicyProduct AS Prod
                ON Prod.PolicyId = p.PolicyId
                    AND Prod.ROW_NO = 1
            LEFT JOIN (
                        SELECT
                            PolicyId,
                            RelatedOrganisationId AS ProducingOfficeId
                        FROM (
                            SELECT
                                ROW_NUMBER() OVER ( PARTITION BY PolOrg.PolicyId
                                                    ORDER BY PolOrg.ETLUpdatedDate, PolOrg.PolicyId DESC) AS rnk,
                                PolOrg.PolicyId,
                                RelatedOrganisationId = RO.OrganisationSK,
                                PolOrg.ETLUpdatedDate
                            FROM dbo.PolicyOrganisation AS PolOrg
                            INNER JOIN PS.Organisation PO ON PO.OrganisationSK = PolOrg.OrganisationId
                            INNER JOIN PS.OrganisationRelationship AS OrgR
                                ON OrgR.OrganisationKey = PO.OrganisationKey
                                AND OrgR.DataSourceInstanceId = PO.DataSourceInstanceId
                            AND OrgR.OrganisationRelationshipType = ''branch''
                                AND OrgR.IsDeleted = 0
                            INNER JOIN PS.Organisation RO ON RO.OrganisationKey = OrgR.RelatedOrganisationKey
                                AND RO.DataSourceInstanceId = OrgR.DataSourceInstanceId
                            INNER JOIN APIv1.WillisOffice AS poff
                                ON RO.OrganisationSK = poff.OfficeId
                            WHERE PolOrg.DataSourceInstanceId = 50001 AND
                                    PolOrg.IsDeleted = 0
                        ) AS B
                        WHERE B.rnk = 1
                ) AS POff
                ON P.PolicyId = POff.PolicyId
            LEFT JOIN #EpicNotRemarketing AS epic
                ON epic.PolicyId = P.PolicyId
            WHERE pp.ServicingPlatformId = ' + CAST(@DataSourceInstanceId AS NVARCHAR(10))
        + N'
                AND pp.RunId = ' + CAST(@RunId AS NVARCHAR(20))
        + N'
                AND NOT EXISTS(
                        SELECT *
                        FROM dbo.PlacementPolicy AS pp2
                        INNER JOIN dbo.Placement AS pl2
                            ON pl2.PlacementId = pp2.PlacementId
                        WHERE pp2.PolicyId = pp.PolicyId
                            AND pp2.PlacementPolicyRelationshipTypeId = 1
                            AND pp2.IsDeleted = 0
                            AND pl2.IsDeleted = 0
                    )
            GROUP BY pp.PlacementId,
                pp.DataSourceInstanceId,
                pp.RunId,
                pp.RuleId,
                p.DataSourceInstanceId,
                p.BrokingRegionId,
                DSI.DataSourceId
        ) AS S ';

    SELECT
        @SQL2 =
        N'
            ON T.PlacementReference = S.PlacementReference
            AND T.DataSourceInstanceId = S.DataSourceInstanceId
            AND	T.GroupingRunId = S.RunId
            AND	T.GroupingRuleId = S.RuleId
            AND T.ServicingPlatformId = S.ServicingPlatformId
        WHEN NOT MATCHED BY TARGET THEN INSERT
        (
            PlacementName,
            PartyId,
            PlacementStatusId,
            InceptionDate,
            ExpiryDate,
            IsDeleted,
            PlacementReference,
            CancellationReasonId,
            CollectionId,
            RenewedFromPlacementId,
            RenewedToPlacementId,
            CreatedUser,
            CreatedUTCDate,
            LastUpdatedUser,
            LastUpdatedUTCDate,
            DataSourceInstanceId,
            GroupingRunId,
            GroupingRuleId,
            RefInsuranceTypeId,
            BrokingSegmentId,
            BrokingRegionId,
            BrokingSubSegmentId,
            IndustryId,
            ProducingOfficeId,
            AppraisalTypeId,
            RenewableOptionId,
            ServicingPlatformId
        )
        VALUES
        (
            S.PlacementName,
            S.PartyId,
            S.PlacementStatusId,
            S.InceptionDate,
            S.ExpiryDate,
            S.IsDeleted,
            S.PlacementReference,
            S.CancellationReasonId,
            S.CollectionId,
            S.RenewedFromPlacementId,
            S.RenewedToPlacementId,
            S.CreatedUser,
            S.CreatedUTCDate,
            S.LastUpdatedUser,
            S.LastUpdatedUTCDate,
            S.DataSourceInstanceId,
            S.RunId,
            S.RuleId,
            S.RefInsuranceTypeId,
            S.BrokingSegmentId,
            S.BrokingRegionId,
            S.BrokingSubSegmentId,
            S.IndustryId,
            S.ProducingOfficeId,
            S.AppraisalTypeId,
            S.RenewableOptionId,
            S.ServicingPlatformId
        );';

    EXEC (@SQL + @SQL1 + @SQL2);

    SET @SQLF = @SQL + @SQL1 + @SQL2;

    EXEC ADF.StoredProcSetSqlLog
        @SprocName
      , NULL
      , NULL
      , NULL
      , NULL
      , 'Merge Placement'
      , @SQLF;

    -- Ensure the temp table is deleted.
    DROP TABLE #EpicNotRemarketing;

    -- Create a PlacementPolicy record
    INSERT INTO
        dbo.PlacementPolicy
        (
            PlacementId
          , PolicyId
          , DataSourceInstanceId
          , PlacementPolicyRelationshipTypeId
          , IsDeleted
          , CreatedDate
          , UpdatedDate
          , SourceUpdatedDate
          , ServicingPlatformId
        )
    SELECT DISTINCT
           P.PlacementId
         , POL.PolicyId
         , 50366 /* Broking Platform */
         , 1
         , 0
         , GETUTCDATE()
         , GETUTCDATE()
         , NULL -- No source
         , ServicingPlatformId = POL.DataSourceInstanceId
    FROM
        dbo.Policy POL
        INNER JOIN Rules.PlacementPolicies PP
            ON POL.PolicyId = PP.PolicyId

        INNER JOIN dbo.Placement P
            ON P.PlacementReference = PP.PlacementId
               AND P.GroupingRunId = PP.RunId
               AND P.GroupingRuleId = PP.RuleId
    WHERE
        PP.ServicingPlatformId = @DataSourceInstanceId
        AND PP.RunId = @RunId;

    SET @InsertedCount = @@ROWCOUNT;

    -- set the GroupingRule onto year 2+ cases.
    UPDATE PL
    SET
        PL.GroupingRuleId = PP.RuleId
      , PL.GroupingRunId = PP.RunId
      , PL.LastUpdatedUTCDate = GETUTCDATE()
      , PL.LastUpdatedUser = 'Renewal'
    FROM
        dbo.Placement PL
        INNER JOIN Rules.PlacementPolicies PP
            ON PL.PlacementId = PP.ExistingPlacementId
    WHERE
        PP.ServicingPlatformId = @DataSourceInstanceId
        AND PP.RunId = @RunId
        AND PP.ExistingPlacementId IS NOT NULL;

    SET @UpdatedCount = @@ROWCOUNT;
    SET @Action = CONCAT('RunID:', @RunId, ', DataSourceInstanceId:', @DataSourceInstanceId);

    EXEC ADF.StoredProcSetSqlLog
        @SprocName
      , @InsertedCount
      , @UpdatedCount
      , 0
      , 0
      , @Action
      , NULL;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @Success = 0;
END CATCH;

EXEC ADF.StoredProcEndLog @SprocName;

RETURN 0;