/*
    Post-Deployment Script.

    When this is cleared down for a new release don't forget about <PERSON><PERSON><PERSON>.PreDeployment.OneOffScript which
    may also have scripts for the previous release.

*/

/*
    ***** DO NOT DELETE *****
    Template for script part.

    SET @scriptName = '<< Function name >>';
    IF NOT EXISTS (SELECT * FROM devops.ScriptDeploymentRegister WHERE ScriptName = @scriptName AND ScriptType = @scriptType)
    BEGIN
        PRINT CONCAT(@scriptName, '-', @scriptType);

        -- SQL

        INSERT INTO devops.ScriptDeploymentRegister
            (ScriptName, ScriptType)
        VALUES
            (@scriptName, @scriptType);
    END;
*/

DECLARE @scriptName NVARCHAR(100);
DECLARE @scriptType CHAR(4) = 'Post';

PRINT N'Post-deployment One-Off scripts (if any):';

SET @scriptName = N'326632-Populate-new-BP-MarketKind-attributes';

IF NOT EXISTS
    (SELECT *
     FROM
         devops.ScriptDeploymentRegister
     WHERE
         ScriptName = @scriptName
         AND ScriptType = @scriptType)
BEGIN

    /*
    Populate the new MarketKind attributes to allow removal of the old BPStaging tables and data
    The MarketKind load from BP will need to be updated to allow for any future updates to these values once the export view has been extended
    */
    PRINT CONCAT(@scriptName, '-', @scriptType);

    IF OBJECT_ID('BP.MarketKind') IS NOT NULL
    BEGIN
        DROP TABLE IF EXISTS #MarketKind;

        CREATE TABLE #MarketKind (
            Id                INT NOT NULL
          , IsChildMarketKind BIT NOT NULL
          , IsThirdParty      BIT NOT NULL
          , CanDistribute     BIT NOT NULL
        );

        INSERT INTO #MarketKind
            (Id, IsChildMarketKind, IsThirdParty, CanDistribute)
        VALUES
            (1, 0, 0, 1)
          , (2, 0, 0, 0)
          , (3, 1, 0, 1)
          , (4, 0, 0, 0)
          , (5, 1, 0, 1)
          , (6, 0, 1, 1)
          , (7, 0, 1, 1)
          , (8, 0, 1, 1)
          , (9, 0, 1, 1)
          , (10, 0, 1, 1)
          , (11, 1, 0, 0)
          , (12, 0, 1, 1);

        UPDATE mk
        SET IsChildMarketKind = tmk.IsChildMarketKind, IsThirdParty = tmk.IsThirdParty, CanDistribute = tmk.CanDistribute
        FROM
            BP.MarketKind mk
            INNER JOIN #MarketKind tmk
                ON tmk.Id = mk.Id;
    END;

    INSERT INTO devops.ScriptDeploymentRegister
        (ScriptName, ScriptType)
    VALUES
        (@scriptName, @scriptType);
END;

SET @scriptName = N'326632-BP-SubmissionContainerMarket-SourceCreatedDate';

IF NOT EXISTS
    (SELECT *
     FROM
         devops.ScriptDeploymentRegister
     WHERE
         ScriptName = @scriptName
         AND ScriptType = @scriptType)
BEGIN
    -- Description
    PRINT CONCAT(@scriptName, '-', @scriptType);

    IF OBJECT_ID('BPStaging.SubmissionMarketDeprecated') IS NOT NULL
    BEGIN
        UPDATE scm
        SET scm.SubmissionDate = smd.SubmissionDate
        FROM
            BP.SubmissionContainerMarket scm
            INNER JOIN BPStaging.SubmissionMarketDeprecated smd
                ON smd.SubmissionContainerMarketId = scm.Id;
    END;

    INSERT INTO devops.ScriptDeploymentRegister
        (ScriptName, ScriptType)
    VALUES
        (@scriptName, @scriptType);
END;

SET @scriptName = N'347042-HandlingInvalidStatus';

IF NOT EXISTS
    (SELECT *
     FROM
         devops.ScriptDeploymentRegister
     WHERE
         ScriptName = @scriptName
         AND ScriptType = @scriptType)
BEGIN
    PRINT CONCAT(@scriptName, '-', @scriptType);

    -- Need to refresh from BP for where we are now using the column.
    EXEC Support.OverrideProcessLastUpdatedDate @ProcessName = 'BPStaging.ExpiringResponseElement', @DateToUse = '1900-01-01';

    EXEC Support.OverrideProcessLastUpdatedDate @ProcessName = 'BPStaging.MarketResponse', @DateToUse = '1900-01-01';

    INSERT INTO devops.ScriptDeploymentRegister
        (ScriptName, ScriptType)
    VALUES
        (@scriptName, @scriptType);
END; -- 3347042-HandlingInvalidStatus

SET @scriptName = N'328120-PASInsuranceTypeMigration';

IF NOT EXISTS
    (SELECT *
     FROM
         devops.ScriptDeploymentRegister
     WHERE
         ScriptName = @scriptName
         AND ScriptType = @scriptType)
BEGIN
    PRINT CONCAT(@scriptName, '-', @scriptType);

    -- Updating the InsuranceTypeKey on ref.InsuranceType
    UPDATE rit
    SET rit.InsuranceTypeKey = pasit.InsuranceTypeKey, rit.PASInsuranceTypeId = rit.PACTInsuranceTypeId
    FROM
        ref.InsuranceType rit
        INNER JOIN PAS.InsuranceType pasit
            ON pasit.PASInsuranceTypeId = rit.PACTInsuranceTypeId
    WHERE
        rit.DataSourceInstanceId = pasit.DataSourceInstanceId
        AND rit.InsuranceTypeKey <> pasit.InsuranceTypeKey;

    INSERT INTO devops.ScriptDeploymentRegister
        (ScriptName, ScriptType)
    VALUES
        (@scriptName, @scriptType);
END; -- 3328120-PASInsuranceTypeMigration

SET @scriptName = N'328121-PASOpportunityTypeMigration';

IF NOT EXISTS
    (SELECT *
     FROM
         devops.ScriptDeploymentRegister
     WHERE
         ScriptName = @scriptName
         AND ScriptType = @scriptType)
BEGIN
    PRINT CONCAT(@scriptName, '-', @scriptType);

    -- Need to refresh the ref.OpportunityType data to populate the new PASOpportunityType field.
    EXEC Support.OverrideProcessLastUpdatedDate @ProcessName = 'PASStaging.rpt_vwOpportunityType', @DateToUse = '1900-01-01';

    INSERT INTO devops.ScriptDeploymentRegister
        (ScriptName, ScriptType)
    VALUES
        (@scriptName, @scriptType);
END; -- 328121-PASOpportunityTypeMigration

SET @scriptName = N'328128-PASPolicyTypeMigration';

IF NOT EXISTS
    (SELECT *
     FROM
         devops.ScriptDeploymentRegister
     WHERE
         ScriptName = @scriptName
         AND ScriptType = @scriptType)
BEGIN
    PRINT CONCAT(@scriptName, '-', @scriptType);

    -- Populating the PolicyTypeKey on dbo.Policy
    UPDATE p
    SET p.PolicyTypeKey = paspt.PolicyTypeKey
    FROM
        dbo.Policy p
        INNER JOIN dbo.PolicyType pt
            ON pt.PolicyTypeId = p.PolicyTypeId

        INNER JOIN PAS.PolicyType paspt
            ON paspt.PASPolicyTypeId = pt.PACTPolicyTypeId;

    -- Populating the PolicyTypeKey on dbo.PolicyMarket
    UPDATE pm
    SET pm.PolicyTypeKey = paspt.PolicyTypeKey
    FROM
        dbo.PolicyMarket pm
        INNER JOIN dbo.PolicyType pt
            ON pt.PolicyTypeId = pm.PolicyTypeId

        INNER JOIN PAS.PolicyType paspt
            ON paspt.PASPolicyTypeId = pt.PACTPolicyTypeId;

    INSERT INTO devops.ScriptDeploymentRegister
        (ScriptName, ScriptType)
    VALUES
        (@scriptName, @scriptType);
END; -- 328128-PASPolicyTypeMigration

SET @scriptName = N'345902-ExtraElementInfo';

IF NOT EXISTS
    (SELECT *
     FROM
         devops.ScriptDeploymentRegister
     WHERE
         ScriptName = @scriptName
         AND ScriptType = @scriptType)
BEGIN
    PRINT CONCAT(@scriptName, '-', @scriptType);

    INSERT INTO ref.ElementAttributeType
        (ElementAttributeTypeId, DataSourceInstanceId, ElementAttributeTypeLabelKey, ElementAttributeType, ElementStructureContextId, IsDeprecated, ETLCreatedDate, ETLUpdatedDate, SourceUpdatedDate)
    SELECT ElementAttributeTypeId, DataSourceInstanceId = ISNULL(DataSourceInstanceId, 50366), ElementAttributeTypeLabelKey = ElementAttributeTypeKey, ElementAttributeType, ElementStructureContextId = NULL /* Need a data load. */
         , IsDeprecated = IsDeleted, ETLCreatedDate = ISNULL(ETLCreatedDate, ETLUpdatedDate)/* Yes there are some NULL ETLCreatedDate */
         , ETLUpdatedDate, SourceUpdatedDate
    FROM
        dbo.ElementAttributeType;

    /* As we already have this we need to reload to get the new columns. */
    EXEC Support.OverrideProcessLastUpdatedDate @ProcessName = 'BPStaging.ElementAttributeType', @DateToUse = '1900-01-01';

    INSERT INTO devops.ScriptDeploymentRegister
        (ScriptName, ScriptType)
    VALUES
        (@scriptName, @scriptType);
END; -- 345902-ExtraElementInfo

SET @scriptName = N'328125-PASPolicyStatusMigration';

IF NOT EXISTS
    (SELECT *
     FROM
         devops.ScriptDeploymentRegister
     WHERE
         ScriptName = @scriptName
         AND ScriptType = @scriptType)
BEGIN
    PRINT CONCAT(@scriptName, '-', @scriptType);

    -- Populating the PolicyStatusKey on dbo.Policy
    UPDATE p
    SET p.PolicyStatusKey = pasps.PolicyStatusKey
    FROM
        dbo.Policy p
        INNER JOIN dbo.PolicyStatus ps
            ON ps.PolicyStatusId = p.PolicyStatusId

        INNER JOIN PAS.PolicyStatus pasps
            ON pasps.PASPolicyStatusId = ps.PACTPolicyStatusId;

    INSERT INTO devops.ScriptDeploymentRegister
        (ScriptName, ScriptType)
    VALUES
        (@scriptName, @scriptType);
END; -- 328125-PASPolicyStatusMigration

SET @scriptName = N'351092-PASLegalEntityMigration';

IF NOT EXISTS
    (SELECT *
     FROM
         devops.ScriptDeploymentRegister
     WHERE
         ScriptName = @scriptName
         AND ScriptType = @scriptType)
BEGIN
    PRINT CONCAT(@scriptName, '-', @scriptType);

    -- Updating the LegalEntityKey on ref.LegalEntity
    UPDATE le
    SET le.LegalEntityKey = pasle.LegalEntityKey
    FROM
        ref.LegalEntity le
        INNER JOIN PAS.LegalEntity pasle
            ON pasle.PASLegalEntityId = le.PACTLegalEntityId
    WHERE
        le.DataSourceInstanceId = pasle.DataSourceInstanceId
        AND le.LegalEntityKey <> pasle.LegalEntityKey;

    -- Updating the PASLegalEntityId on ref.LegalEntity
    UPDATE le
    SET le.PASLegalEntityId = pasle.PASLegalEntityId
    FROM
        ref.LegalEntity le
        INNER JOIN PAS.LegalEntity pasle
            ON pasle.PASLegalEntityId = le.PACTLegalEntityId;

    INSERT INTO devops.ScriptDeploymentRegister
        (ScriptName, ScriptType)
    VALUES
        (@scriptName, @scriptType);
END; -- 351092-PASLegalEntityMigration

SET @scriptName = N'351141-PASFinancialGeographyMigration';

IF NOT EXISTS
    (SELECT *
     FROM
         devops.ScriptDeploymentRegister
     WHERE
         ScriptName = @scriptName
         AND ScriptType = @scriptType)
BEGIN
    PRINT CONCAT(@scriptName, '-', @scriptType);

    -- Updating the PASFinancialGeographyId on ref.FinancialGeography
    UPDATE fg
    SET fg.PASFinancialGeographyId = pasfg.PASFinancialGeographyId
    FROM
        ref.FinancialGeography fg
        INNER JOIN PAS.FinancialGeography pasfg
            ON pasfg.PASFinancialGeographyId = fg.PACTFinancialGeographyId;

    INSERT INTO devops.ScriptDeploymentRegister
        (ScriptName, ScriptType)
    VALUES
        (@scriptName, @scriptType);
END; -- 351141-PASFinancialGeographyMigration

SET @scriptName = N'351183-PASFinancialSegmentMigration';

IF NOT EXISTS
    (SELECT *
     FROM
         devops.ScriptDeploymentRegister
     WHERE
         ScriptName = @scriptName
         AND ScriptType = @scriptType)
BEGIN
    PRINT CONCAT(@scriptName, '-', @scriptType);

    -- Updating the PASFinancialSegmentId on ref.FinancialSegment
    UPDATE fs
    SET fs.PASFinancialSegmentId = pasfs.PASFinancialSegmentId
    FROM
        ref.FinancialSegment fs
        INNER JOIN PAS.FinancialSegment pasfs
            ON pasfs.PASFinancialSegmentId = fs.PACTFinancialSegmentId;

    INSERT INTO devops.ScriptDeploymentRegister
        (ScriptName, ScriptType)
    VALUES
        (@scriptName, @scriptType);
END; -- 351183-PASFinancialSegmentMigration

SET @scriptName = N'352173-RefLegalEntityDuplicateKeys';

IF NOT EXISTS
    (SELECT *
     FROM
         devops.ScriptDeploymentRegister
     WHERE
         ScriptName = @scriptName
         AND ScriptType = @scriptType)
BEGIN
    PRINT CONCAT(@scriptName, '-', @scriptType);

    -- Delete records from ref.LegalEntity that have duplicate records by DSI and KEY
    -- One record is IsDeprecated = 0 and the other is IsDeprecated = 1
    -- This will delete the record WHERE IsDeprecated = 1
    DELETE
    trg
    FROM
        ref.LegalEntity trg
        INNER JOIN
            (SELECT a.DataSourceInstanceId, a.LegalEntityKey
             FROM
                 ref.LegalEntity a
                 INNER JOIN
                     (SELECT DataSourceInstanceId, LegalEntityKey
                      FROM
                          ref.LegalEntity
                      WHERE
                          PACTLegalEntityId IS NOT NULL
                      GROUP BY
                          DataSourceInstanceId, LegalEntityKey
                      HAVING
                          COUNT(*) > 1) b
                     ON a.LegalEntityKey = b.LegalEntityKey
                        AND a.DataSourceInstanceId = b.DataSourceInstanceId
             WHERE
                 a.IsDeprecated = 0) src
            ON src.LegalEntityKey = trg.LegalEntityKey
               AND src.DataSourceInstanceId = trg.DataSourceInstanceId
    WHERE
        trg.IsDeprecated = 1;

    INSERT INTO devops.ScriptDeploymentRegister
        (ScriptName, ScriptType)
    VALUES
        (@scriptName, @scriptType);
END; -- 352173-RefLegalEntityDuplicateKeys

SET @scriptName = N'352172-RefFinancialGeographyDuplicateKeys';

IF NOT EXISTS
    (SELECT *
     FROM
         devops.ScriptDeploymentRegister
     WHERE
         ScriptName = @scriptName
         AND ScriptType = @scriptType)
BEGIN
    PRINT CONCAT(@scriptName, '-', @scriptType);

    -- Delete records from ref.FinancialGeography that have duplicate records by DSI and KEY
    -- One record is IsDeprecated = 0 and the other is IsDeprecated = 1
    -- This will delete the record WHERE IsDeprecated = 1
    DELETE
    trg
    FROM
        ref.FinancialGeography trg
        INNER JOIN
            (SELECT a.DataSourceInstanceId, a.FinancialGeographyKey
             FROM
                 ref.FinancialGeography a
                 INNER JOIN
                     (SELECT DataSourceInstanceId, FinancialGeographyKey
                      FROM
                          ref.FinancialGeography
                      WHERE
                          PACTFinancialGeographyId IS NOT NULL
                      GROUP BY
                          DataSourceInstanceId, FinancialGeographyKey
                      HAVING
                          COUNT(*) > 1) b
                     ON a.FinancialGeographyKey = b.FinancialGeographyKey
                        AND a.DataSourceInstanceId = b.DataSourceInstanceId
             WHERE
                 a.IsDeprecated = 0) src
            ON src.FinancialGeographyKey = trg.FinancialGeographyKey
               AND src.DataSourceInstanceId = trg.DataSourceInstanceId
    WHERE
        trg.IsDeprecated = 1;

    INSERT INTO devops.ScriptDeploymentRegister
        (ScriptName, ScriptType)
    VALUES
        (@scriptName, @scriptType);
END; -- 352172-RefFinancialGeographyDuplicateKeys

SET @scriptName = N'352716-DisableCRBDataLakeADFProcessesByDefault';

IF NOT EXISTS
    (SELECT *
     FROM
         devops.ScriptDeploymentRegister
     WHERE
         ScriptName = @scriptName
         AND ScriptType = @scriptType)
BEGIN
    PRINT CONCAT(@scriptName, '-', @scriptType);

    UPDATE adf.Process
    SET IsDisabled = 1
    WHERE
        Name LIKE '%CRB Data Lake%'
        AND IsDisabled = 0;

    INSERT INTO devops.ScriptDeploymentRegister
        (ScriptName, ScriptType)
    VALUES
        (@scriptName, @scriptType);
END;

SET @scriptName = N'353001-MarketResponseBasisLoadFailure';

IF NOT EXISTS
    (SELECT *
     FROM
         devops.ScriptDeploymentRegister
     WHERE
         ScriptName = @scriptName
         AND ScriptType = @scriptType)
BEGIN
    PRINT CONCAT(@scriptName, '-', @scriptType);

    EXEC Support.OverrideProcessLastUpdatedDate @ProcessName = 'BPStaging.ContractSection', @DateToUse = '1 Jan 1900', @IsActive = 1;

    INSERT INTO devops.ScriptDeploymentRegister
        (ScriptName, ScriptType)
    VALUES
        (@scriptName, @scriptType);
END;

SET @scriptName = N'353882-AdditionalAffirmationQuestionColumns';

IF NOT EXISTS
    (SELECT *
     FROM
         devops.ScriptDeploymentRegister
     WHERE
         ScriptName = @scriptName
         AND ScriptType = @scriptType)
BEGIN
    PRINT CONCAT(@scriptName, '-', @scriptType);

    EXEC support.OverrideProcessLastUpdatedDate @ProcessName = 'BPStaging.AffirmationQuestion', @DateToUse = '1900-01-01';

    INSERT INTO devops.ScriptDeploymentRegister
        (ScriptName, ScriptType)
    VALUES
        (@scriptName, @scriptType);
END;

SET @scriptName = N'354595-RefFacilitySectionPolicyStatusMismatch';

IF NOT EXISTS
    (SELECT *
     FROM
         devops.ScriptDeploymentRegister
     WHERE
         ScriptName = @scriptName
         AND ScriptType = @scriptType)
BEGIN
    PRINT CONCAT(@scriptName, '-', @scriptType);

    -- Populating the PolicyStatusKey on ref.Facility
    UPDATE f
    SET f.PolicyStatusKey = pasps.PolicyStatusKey
    FROM
        ref.Facility f
        INNER JOIN dbo.PolicyStatus ps
            ON ps.PolicyStatusId = f.PolicyStatusId

        INNER JOIN PAS.PolicyStatus pasps
            ON pasps.PASPolicyStatusId = ps.PACTPolicyStatusId;

    INSERT INTO devops.ScriptDeploymentRegister
        (ScriptName, ScriptType)
    VALUES
        (@scriptName, @scriptType);
END; -- 354595-RefFacilitySectionPolicyStatusMismatch


PRINT N'End of post deployment One-Off scripts.';