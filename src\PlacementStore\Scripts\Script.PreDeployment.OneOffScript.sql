/*
    Placeholder for pre-deployment one-off scripts.
    You can tidy up data here that otherwise would cause the deployment to fail.
    But no point altering the schema because the DACPAC will not be aware of it when it works out what to do.

    This should be cleared down at the start of each release.
*/

/*
    ***** DO NOT DELETE *****
    Template for script part.

    SET @scriptName = N'<< Function name >>-PreDeployment';
    IF NOT EXISTS (SELECT * FROM devops.ScriptDeploymentRegister WHERE ScriptName = @scriptName AND ScriptType = @scriptType)
    BEGIN
        PRINT CONCAT(@scriptName, '-', @scriptType);

        -- SQL

        INSERT INTO devops.ScriptDeploymentRegister
            (ScriptName, ScriptType)
        VALUES
            (@scriptName, @scriptType);
    END;
*/

DECLARE @scriptName NVARCHAR(100);
DECLARE @scriptType CHAR(4) = 'Pre';

PRINT N'Pre-deployment One-Off scripts (if any):';

SET @scriptName = N'325022-PreDeployment-ContractClauseAndCondition-Truncate';

IF NOT EXISTS
    (SELECT *
     FROM
         devops.ScriptDeploymentRegister
     WHERE
         ScriptName = @scriptName
         AND ScriptType = @scriptType)
BEGIN
    -- Description
    PRINT CONCAT(@scriptName, '-', @scriptType);

    TRUNCATE TABLE PS.ContractClauseAndCondition;

    INSERT INTO devops.ScriptDeploymentRegister
        (ScriptName, ScriptType)
    VALUES
        (@scriptName, @scriptType);
END;

SET @scriptName = N'217238-ElementTypePreDeployment';

IF NOT EXISTS
    (SELECT *
     FROM
         devops.ScriptDeploymentRegister
     WHERE
         ScriptName = @scriptName
         AND ScriptType = @scriptType)
BEGIN
    PRINT CONCAT(@scriptName, '-', @scriptType);

    IF OBJECT_ID('dbo.ElementType') IS NOT NULL
    BEGIN
        SELECT ElementTypeId, ElementTypeKey, ElementType, IsDeleted, ETLCreatedDate, ETLUpdatedDate, DatasourceInstanceId, SourceUpdatedDate
        INTO dbo.ElementTypeDeprecated
        FROM
            dbo.ElementType;
    END;

    INSERT INTO devops.ScriptDeploymentRegister
        (ScriptName, ScriptType)
    VALUES
        (@scriptName, @scriptType);
END;

SET @scriptName = N'337654-Pre-Deployment-ResponseAcceptedDate';

IF NOT EXISTS
    (SELECT *
     FROM
         devops.ScriptDeploymentRegister
     WHERE
         ScriptName = @scriptName
         AND ScriptType = @scriptType)
BEGIN
    PRINT CONCAT(@scriptName, '-', @scriptType);

    TRUNCATE TABLE rpt.MarketInteractionResponse;

    TRUNCATE TABLE rpt.MarketActivity;

    INSERT INTO devops.ScriptDeploymentRegister
        (ScriptName, ScriptType)
    VALUES
        (@scriptName, @scriptType);
END;

SET @scriptName = N'347042-HandlingInvalidStatus';

IF NOT EXISTS
    (SELECT *
     FROM
         devops.ScriptDeploymentRegister
     WHERE
         ScriptName = @scriptName
         AND ScriptType = @scriptType)
BEGIN
    PRINT CONCAT(@scriptName, '-', @scriptType);

    TRUNCATE TABLE BPStaging.ExpiringResponseElement;

    INSERT INTO devops.ScriptDeploymentRegister
        (ScriptName, ScriptType)
    VALUES
        (@scriptName, @scriptType);
END;

SET @scriptName = N'345264-PreDeployment-BPStaging-MarketKind-Truncate';

IF NOT EXISTS
    (SELECT *
     FROM
         devops.ScriptDeploymentRegister
     WHERE
         ScriptName = @scriptName
         AND ScriptType = @scriptType)
BEGIN
    -- Description
    PRINT CONCAT(@scriptName, '-', @scriptType);

    TRUNCATE TABLE BPStaging.MarketKind;

    INSERT INTO devops.ScriptDeploymentRegister
        (ScriptName, ScriptType)
    VALUES
        (@scriptName, @scriptType);
END;

SET @scriptName = N'348003-R202508DevJobFailureFixes';

IF NOT EXISTS
    (SELECT *
     FROM
         devops.ScriptDeploymentRegister
     WHERE
         ScriptName = @scriptName
         AND ScriptType = @scriptType)
BEGIN
    -- We have some CARRES| records duplicated only in pre-prod. This deletes them so we can put a unique index on.
    -- It seems that the newer ones are the ones linked to PS.MarketQuoteResponsePolicyRef hence the partition
    -- Not being run against IAT or production.
    PRINT CONCAT(@scriptName, '-', @scriptType);

    IF devops.IsDevEnv() = 1
       OR devops.IsQaEnv() = 1
       OR devops.IsUatEnv() = 1
    BEGIN
        PRINT 'Checking for duplicates...';

        DROP TABLE IF EXISTS #dups;

        SELECT mqr.MarketQuoteResponseId, mqr.DataSourceInstanceId, mqr.SourceMarketQuoteResponseKey, rn = ROW_NUMBER() OVER (PARTITION BY mqr.DataSourceInstanceId, mqr.SourceMarketQuoteResponseKey ORDER BY mqr.ETLCreatedDate ASC)
        INTO #dups
        FROM
            PS.MarketQuoteResponse mqr
            INNER JOIN
                (SELECT DataSourceInstanceId, SourceMarketQuoteResponseKey
                 FROM
                     PS.MarketQuoteResponse
                 GROUP BY
                     DataSourceInstanceId, SourceMarketQuoteResponseKey
                 HAVING
                     COUNT(*) > 1) mqrdups
                ON mqrdups.DataSourceInstanceId = mqr.DataSourceInstanceId
                   AND mqrdups.SourceMarketQuoteResponseKey = mqr.SourceMarketQuoteResponseKey;

        DELETE
        mqr
        FROM
            PS.MarketQuoteResponse mqr
        WHERE
            EXISTS
            (SELECT * FROM #dups ds WHERE ds.MarketQuoteResponseId = mqr.MarketQuoteResponseId AND ds.rn = 1);

        PRINT CONCAT(N'Duplicate rows deleted from PS.MarketQuoteResponse : ', @@ROWCOUNT);

        DROP TABLE IF EXISTS #dups;
    END;

    INSERT INTO devops.ScriptDeploymentRegister
        (ScriptName, ScriptType)
    VALUES
        (@scriptName, @scriptType);
END; -- 348003-R202508DevJobFailureFixes

SET @scriptName = N'345902-ExtraElementInfo';

IF NOT EXISTS
    (SELECT *
     FROM
         devops.ScriptDeploymentRegister
     WHERE
         ScriptName = @scriptName
         AND ScriptType = @scriptType)
BEGIN
    PRINT CONCAT(@scriptName, '-', @scriptType);

    TRUNCATE TABLE BPStaging.ElementAttributeType;

    INSERT INTO devops.ScriptDeploymentRegister
        (ScriptName, ScriptType)
    VALUES
        (@scriptName, @scriptType);
END; -- 345902-ExtraElementInfo

SET @scriptName = N'329761-MREAPerformance-Truncate';

IF NOT EXISTS
    (SELECT *
     FROM
         devops.ScriptDeploymentRegister
     WHERE
         ScriptName = @scriptName
         AND ScriptType = @scriptType)
BEGIN
    -- Description
    PRINT CONCAT(@scriptName, '-', @scriptType);

    TRUNCATE TABLE PS.MarketResponseElementAttribute;

    INSERT INTO devops.ScriptDeploymentRegister
        (ScriptName, ScriptType)
    VALUES
        (@scriptName, @scriptType);
END; -- 329761-MREAPerformance-Truncate

SET @scriptName = N'353001-Marketresponsebasis make contract section incremetal';

IF NOT EXISTS
    (SELECT *
     FROM
         devops.ScriptDeploymentRegister
     WHERE
         ScriptName = @scriptName
         AND ScriptType = @scriptType)
BEGIN
    PRINT CONCAT(@scriptName, '-', @scriptType);

    TRUNCATE TABLE BPStaging.ContractSection;

    INSERT INTO devops.ScriptDeploymentRegister
        (ScriptName, ScriptType)
    VALUES
        (@scriptName, @scriptType);
END;

PRINT N'End of pre-deployment One-Off scripts.';