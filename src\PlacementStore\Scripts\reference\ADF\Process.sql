PRINT 'Running population script for reference\adf\Process';

/*
DELETE FROM adf.ProcessInterdependency
DELETE FROM adf.Process
*/

-- Set up the target state
DECLARE @Process TABLE (
    Name          NVARCHAR(100) NOT NULL
  , ProcessTypeId INT           NOT NULL
  , JSONConfig    NVARCHAR(MAX) NOT NULL
  , TargetTable   NVARCHAR(300) NULL
  , LoadTypeId    INT           NOT NULL
);

/* Load Types */
DECLARE @LoadType_Daily INT = 1;
DECLARE @LoadType_Intraday INT = 2;
DECLARE @LoadType_Model INT = 3;

/* Process Types */
DECLARE @ProcessType_Copy INT = 1;
DECLARE @ProcessType_SP INT = 2;
-- ProcessType_SSASTables = 3;
DECLARE @ProcessType_Event INT = 4;

-- ProcessType_RunRules = 5;
INSERT INTO @Process
    (LoadTypeId /* 1 Daily, 2 Intraday, 3 Process SASS Models, 9 Dev test subset */
   , Name
   , ProcessTypeId
   , JSONConfig
   , TargetTable /* For information */
)
VALUES
    (@LoadType_Daily, 'BP Load Event FMATemp.staging_vw_PartyCheckResult', @ProcessType_Event, '{"TopicName":"psadf-to-psfuncapp-event/messages","TargetConnection":"AzureServiceBus","BodyTemplate":"{\"SourceTable\":\"FMATemp.staging_vw_PartyCheckResult\",\"TargetTable\":\"staging.vw_PartyCheckResult\",\"ProcessId\":@ProcessId,\"SessionGuId\":\"@SessionGuid\",\"RunGuid\":\"@RunGuid\",\"InstanceLogId\":@InstanceLogId,\"ProcessSessionId\":\"@ProcessSessionId\",\"TriggerType\":\"Event\"}","HandlerUpdatesStatus":true}', '')
  , (@LoadType_Daily, 'CAM PS.Carrier', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceTable":"APIv1.CarrierHierarchyExtended", "SourceSQL":"SELECT DISTINCT CH.CarrierID,CH.DataSourceInstanceID as DataSourceInstanceId,DSI.DataSourceInstanceName,CH.CarrierName,CH.[SignetCarrierNode].ToString() AS CarrierNode,CH.GlobalName,cast(ISNULL(CH.FatcaRated, 0) as bit) AS FatcaRated,cast(ISNULL(CH.TobaRated, 0) as bit) AS TobaRated,CH.ApprovalStatus,CH.CompCode,CH.PartyCode,CH.RoleDescription, CH.ActiveFlag FROM [APIv1].[CarrierHierarchyExtended] CH (NOLOCK) JOIN Reference.DataSourceInstance DSI ON DSI.DataSourceInstanceID = CH.DataSourceInstanceID WHERE (CH.ActiveFlag = ''Y'' OR CH.LevelNum = 3 OR CH.DataSourceInstanceId = 50000) AND CH.CarrierName IS NOT NULL AND ISNULL(CH.PartyCode, '''') NOT LIKE (''HL%''); ", "TargetTable":"PS.Carrier", "TruncateTargetTable": true, "TargetConnection":"CAM"}', 'PS.Carrier')
  , (@LoadType_Daily, 'CAM PS.ElementTagInclusionRuleLink', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceTable":"BPStaging.ElementTagInclusionRule", "SourceSQL":"", "TargetTable":"PS.ElementTagInclusionRuleLink", "TruncateTargetTable": true, "TargetConnection":"CAM"}', 'PS.ElementTagInclusionRuleLink')
  , (@LoadType_Daily, 'CAM PS.Facility', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceTable":"ref.facility", "SourceSQL":"SELECT F.PSFacilityPolicyid AS FacilityId, F.DataSourceInstanceId, F.FacilityName, PolicyReference = F.FacilityReference, F.InceptionDate, F.ExpiryDate, F.RenewedFromPolicyId, F.FacilityType, F.RefPolicyStatusId, RPS.RefPolicyStatus, F.ETLCreatedDate, F.ETLUpdatedDate, F.SourceUpdatedDate, F.IsDeprecated FROM ref.Facility F LEFT JOIN PAS.RefPolicyStatus RPS ON RPS.RefPolicyStatusId = F.RefPolicyStatusId", "TargetTable":"PS.Facility", "TruncateTargetTable": true, "TargetConnection":"CAM"}', 'PS.Facility')
  , (@LoadType_Daily, 'CAM PS.FacilitySection', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceTable":"ref.FacilitySection", "SourceSQL":"SELECT FS.PSFacilityPolicySectionId AS FacilitySectionId, FS.DataSourceInstanceId, F.PSFacilityPolicyId AS FacilityId, F.FacilityName, F.FacilityReference AS PolicyReference  , FS.LineSlipRef, FS.SectionCode, F.InceptionDate, F.ExpiryDate, F.RenewedFromPolicyId, F.FacilityType, F.RefPolicyStatusId, RPS.RefPolicyStatus, PolicySectionStatus = COALESCE(PSS.PolicySectionStatus, PS.PolicyStatus, ''N/A''), FS.ETLCreatedDate, FS.ETLUpdatedDate, FS.SourceUpdatedDate , FS.IsDeprecated FROM ref.FacilitySection FS INNER JOIN ref.Facility F ON F.FacilityId = FS.FacilityId LEFT JOIN PAS.PolicySectionStatus PSS ON PSS.PolicySectionStatusKey = FS.PolicySectionStatusKey AND PSS.DataSourceInstanceId = FS.DataSourceInstanceId LEFT JOIN dbo.PolicyStatus PS ON F.PolicyStatusId = PS.PolicyStatusId LEFT JOIN PAS.RefPolicyStatus RPS  ON RPS.RefPolicyStatusId = F.RefPolicyStatusId", "TargetTable":"PS.FacilitySection", "TruncateTargetTable": true, "TargetConnection":"CAM"}', 'PS.FacilitySection')
  , (@LoadType_Daily, 'CAM PS.FacilitySectionCarrier', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceTable":"ref.FacilitySectionCarrier", "SourceSQL":"WITH FacilitySectionCarrier_cte AS ( SELECT fsc.FacilitySectionCarrierId, fsc.DataSourceInstanceId, fsc.FacilitySectionCarrierKey, FacilitySectionId = fs.PSFacilityPolicySectionId, fsc.CarrierId, IsFollow = CAST(fsc.IsFollow AS BIT), fsc.FacilityWrittenLine, fsc.FacilitySignedLine, fsc.ETLCreatedDate, fsc.ETLUpdatedDate, fsc.SourceUpdatedDate, fsc.IsDeprecated, RowNo = ROW_NUMBER() OVER (PARTITION BY fs.PSFacilityPolicySectionId, fsc.CarrierId ORDER BY fsc.IsDeprecated ASC, fsc.IsLead DESC) FROM ref.FacilitySectionCarrier fsc INNER JOIN ref.FacilitySection fs ON fs.FacilitySectionId = fsc.FacilitySectionId ) SELECT FacilitySectionCarrierId, DataSourceInstanceId, FacilitySectionCarrierKey, FacilitySectionId, CarrierId, IsFollow, FacilityWrittenLine, FacilitySignedLine, ETLCreatedDate, ETLUpdatedDate, SourceUpdatedDate, IsDeprecated FROM FacilitySectionCarrier_cte WHERE RowNo = 1", "TargetTable":"PS.FacilitySectionCarrier", "TruncateTargetTable": true, "TargetConnection":"CAM"}', 'PS.FacilitySectionCarrier')
  , (@LoadType_Daily, 'CAM PS.Lookup - BrokingRegion', @ProcessType_Copy, '{"SourceConnection":"PlacementStore","SourceTable":"ref.BrokingRegion","SourceSQL":"WITH BrokingRegionCTE AS ( SELECT BrokingRegionId, BrokingRegionName = BrokingRegion, BrokingRegionKey = TranslationKey, BrokingRegionNode = CAST(CONCAT(''/'', BrokingRegionId, ''/'') AS HIERARCHYID), BrokingRegionParentId, DatasourceInstanceID = ServicingPlatformId, PlacementDatasourceInstanceID = DataSourceInstanceId, CreatedUTCDate = ETLCreatedDate, LastUpdatedUTCDate = ETLUpdatedDate, IsDeleted = IsDeprecated FROM ref.BrokingRegion WHERE BrokingRegionParentId IS NULL UNION ALL SELECT BR.BrokingRegionId, BrokingRegionName = BR.BrokingRegion, BrokingRegionKey = BR.TranslationKey, BrokingRegionNode = CAST(CONCAT(BRC.BrokingRegionNode.ToString(), BR.BrokingRegionId, ''/'') AS HIERARCHYID), BR.BrokingRegionParentId, DatasourceInstanceID = BR.ServicingPlatformId, PlacementDatasourceInstanceID = BR.DataSourceInstanceId, CreatedUTCDate = BR.ETLCreatedDate, LastUpdatedUTCDate = BR.ETLUpdatedDate, IsDeleted = BR.IsDeprecated FROM ref.BrokingRegion BR INNER JOIN BrokingRegionCTE BRC ON BRC.BrokingRegionId = BR.BrokingRegionParentId) SELECT GroupReferenceId = ''BrokingRegion'', GroupName = ''Broking Region'', SubGroupReferenceId = 50369, SubGroupName = COALESCE(DSI.DataSourceInstanceName, CONVERT(NVARCHAR(10), 50369)), ReferenceId = L.BrokingRegionId, ParentReferenceId = L.BrokingRegionParentId, Name = L.BrokingRegionName, SourceReferenceId = NULL, L.IsDeleted, [Key] = NULL FROM BrokingRegionCTE L LEFT JOIN Reference.DataSourceInstance DSI ON DSI.DataSourceInstanceID = 50369;","TargetTable":"PS.Lookup","TruncateTargetTable": true,"TargetConnection":"CAM"}', 'PS.Lookup')
  , (@LoadType_Daily, 'CAM PS.Lookup - BrokingSegment', @ProcessType_Copy, '{"SourceConnection":"PlacementStore","SourceTable":"ref.BrokingSegment","SourceSQL":"SELECT GroupReferenceId = ''BrokingSegment'' , GroupName = ''Broking Segment'' , SubGroupReferenceId = 50369 , SubGroupName = COALESCE(DSI.DataSourceInstanceName, CONVERT(NVARCHAR(10), 50369)) , ReferenceId = L.BrokingSegmentId , ParentReferenceId = L.BrokingSegmentParentId , Name = L.BrokingSegment , SourceReferenceId = NULL , IsDeleted = L.IsDeprecated , [Key] = NULL FROM ref.BrokingSegment L LEFT JOIN Reference.DataSourceInstance DSI ON DSI.DataSourceInstanceID = 50369;","TargetTable":"PS.Lookup","TruncateTargetTable": false,"TargetConnection":"CAM"}', 'PS.Lookup')
  , (@LoadType_Daily, 'CAM PS.Lookup - BrokingSubSegment', @ProcessType_Copy, '{"SourceConnection":"PlacementStore","SourceTable":"ref.BrokingSubSegment","SourceSQL":"SELECT GroupReferenceId = ''BrokingSubSegment'' , GroupName = ''Broking SubSegment'' , SubGroupReferenceId = 50369 , SubGroupName = COALESCE(DSI.DataSourceInstanceName, CONVERT(NVARCHAR(10), 50369)) , ReferenceId = L.BrokingSubSegmentId , ParentReferenceId = L.BrokingSubSegmentParentId , Name = L.BrokingSubSegment , SourceReferenceId = NULL , DSI.IsDeleted , [Key] = NULL FROM ref.BrokingSubSegment L LEFT JOIN Reference.DataSourceInstance DSI ON DSI.DataSourceInstanceID = 50369;","TargetTable":"PS.Lookup","TruncateTargetTable": false,"TargetConnection":"CAM"}', 'PS.Lookup')
  , (@LoadType_Daily, 'CAM PS.Lookup - Geography', @ProcessType_Copy, '{"SourceConnection":"PlacementStore","SourceTable":"Reference.Country","SourceSQL":"SELECT L.GroupReferenceId , L.GroupName , L.SubGroupReferenceId , SubGroupName = COALESCE(L.SubGroupName, DSI.DataSourceInstanceName, CONVERT(NVARCHAR(10), L.SubGroupReferenceId)) , L.ReferenceId , L.ParentReferenceId , L.Name , L.SourceReferenceId , L.IsDeleted , L.[Key] FROM ( SELECT GroupReferenceId = ''Geography'' , GroupName = ''Geography'' , SubGroupReferenceId = 50369 , SubGroupName = NULL , ReferenceId = CountryId , ParentReferenceId = NULL , Name = CountryName , SourceReferenceId = CountryAlpha3Code , IsDeleted , [Key] = CountryAlpha3Code FROM Reference.Country UNION SELECT GroupReferenceId = ''Geography'' , GroupName = ''Geography'' , SubGroupReferenceId = 50369 , SubGroupName = NULL , ReferenceId = CountrySubdivisionId , ParentReferenceId = CountryId , Name = CountrySubdivisionName , SourceReferenceId = CountrySubdivisionCode , IsDeleted = CAST(0 AS BIT) , [Key] = CountrySubdivisionCode FROM APIv1.CountrySubdivision UNION SELECT GroupReferenceId = ''Geography'' , GroupName = ''Geography'' , SubGroupReferenceId = GGS.GeographyGroupingSchemeID , SubGroupName = GGS.GeographyGroupingSchemeName , ReferenceId = GG.GeographyGroupID , ParentReferenceId = NULL , Name = GG.GeographyGroupName , SourceReferenceId = NULL , GG.IsDeleted , [Key] = CAST(GG.GeographyGroupID AS NVARCHAR(500)) FROM Reference.GeographyGroupingScheme GGS JOIN Reference.GeographyGroup GG ON GG.GeographyGroupingSchemeID = GGS.GeographyGroupingSchemeID UNION SELECT GroupReferenceId = ''Geography'' , GroupName = ''Geography'' , SubGroupReferenceId = GGS.GeographyGroupingSchemeID , SubGroupName = GGS.GeographyGroupingSchemeName , ReferenceId = GGM.CountryID , ParentReferenceId = GG.GeographyGroupId , Name = C.CountryName , SourceReferenceId = C.CountryAlpha3Code , GG.IsDeleted , [Key] = C.CountryAlpha3Code FROM Reference.GeographyGroupingScheme GGS JOIN Reference.GeographyGroup GG ON GG.GeographyGroupingSchemeID = GGS.GeographyGroupingSchemeID JOIN Reference.GeographyGroupMembership GGM ON GGM.GeographyGroupID = GG.GeographyGroupID JOIN Reference.Country C ON C.CountryId = GGM.CountryID UNION SELECT GroupReferenceId = ''Geography'' , GroupName = ''Geography'' , SubGroupReferenceId = GGS.GeographyGroupingSchemeID , SubGroupName = GGS.GeographyGroupingSchemeName , ReferenceId = SD.CountrySubdivisionId , ParentReferenceId = SD.CountryId , Name = SD.CountrySubdivisionName , SourceReferenceId = SD.CountrySubdivisionCode , IsDeleted = CAST(0 AS BIT) , [Key] = SD.CountrySubdivisionCode FROM APIv1.CountrySubdivision SD CROSS JOIN Reference.GeographyGroupingScheme GGS ) L LEFT JOIN Reference.DataSourceInstance DSI ON DSI.DataSourceInstanceID = L.SubGroupReferenceId ORDER BY GroupName , SubGroupName , ParentReferenceId , ReferenceId;","TargetTable":"PS.Lookup","TruncateTargetTable": false,"TargetConnection":"CAM"}', 'PS.Lookup')
  , (@LoadType_Daily, 'CAM PS.Lookup - Industry', @ProcessType_Copy, '{"SourceConnection":"PlacementStore","SourceTable":"APIv1.IndustryHierarchy","SourceSQL":"SELECT GroupReferenceId = ''Industry'' , GroupName = ''Industry'' , SubGroupReferenceId = IH.DataSourceInstanceID , SubGroupName = COALESCE(DSI.DataSourceInstanceName, CONVERT(NVARCHAR(10), IH.DataSourceInstanceID)) , ReferenceId = IH.IndustryID , ParentReferenceId = CASE WHEN R.IndustryID IS NOT NULL THEN NULL ELSE IH.ParentID END , Name = IH.IndustryName , SourceReferenceId = IH.IndustryCode , IH.IsDeleted , [Key] = NULL FROM APIv1.IndustryHierarchy IH LEFT JOIN (SELECT DataSourceInstanceId, IndustryID FROM APIv1.IndustryHierarchy WHERE ParentID IS NULL) R ON R.DataSourceInstanceID = IH.DataSourceInstanceID AND R.IndustryID = IH.ParentID LEFT JOIN Reference.DataSourceInstance DSI ON DSI.DataSourceInstanceID = IH.DataSourceInstanceID WHERE IH.ParentID IS NOT NULL; ","TargetTable":"PS.Lookup","TruncateTargetTable": false,"TargetConnection":"CAM"}', 'PS.Lookup')
  , (@LoadType_Daily, 'CAM PS.Lookup - InsuranceType', @ProcessType_Copy, '{"SourceConnection":"PlacementStore","SourceTable":"ods.vw_ref_InsuranceType","SourceSQL":"SELECT GroupReferenceId = ''InsuranceType'', GroupName = ''Insurance Type'', SubGroupReferenceId = IT.DataSourceInstanceId, SubGroupName = COALESCE(DSI.DataSourceInstanceName, CONVERT(NVARCHAR(10), IT.DataSourceInstanceID)), ReferenceId = InsuranceTypeId, ParentReferenceId = NULL, Name = InsuranceType, SourceReferenceId = InsuranceTypeKey, IsDeleted = IsDeprecated, [Key] = InsuranceTypeKey FROM ods.vw_ref_InsuranceType IT LEFT JOIN Reference.DataSourceInstance DSI ON DSI.DataSourceInstanceID = IT.DataSourceInstanceId; ","TargetTable":"PS.Lookup","TruncateTargetTable": false,"TargetConnection":"CAM"}', 'PS.Lookup')
  , (@LoadType_Daily, 'CAM PS.Lookup - Product', @ProcessType_Copy, '{"SourceConnection":"PlacementStore","SourceTable":"APIv1.ProductHierarchy","SourceSQL":"SELECT GroupReferenceId = ''Product'' , GroupName = ''Product'' , SubGroupReferenceId = PH.DataSourceInstanceID , SubGroupName = COALESCE(DSI.DataSourceInstanceName, CONVERT(NVARCHAR(10), PH.DataSourceInstanceID)) , ReferenceId = PH.ProductID , ParentReferenceId = CASE WHEN R.ProductID IS NOT NULL THEN NULL ELSE PH.ParentID END , Name = ISNULL(PH.ProductName, '''') , SourceReferenceId = PH.ProductKey , IsDeleted = PH.IsDeprecated , [Key] = NULL FROM APIv1.ProductHierarchy PH LEFT JOIN ( SELECT DataSourceInstanceId , ProductID FROM APIv1.ProductHierarchy WHERE ParentID IS NULL OR ( DataSourceInstanceId = 50351 AND LevelNum = 1 ) ) R ON R.DataSourceInstanceID = PH.DataSourceInstanceID AND R.ProductID = PH.ParentID LEFT JOIN Reference.DataSourceInstance DSI ON DSI.DataSourceInstanceID = PH.DataSourceInstanceID WHERE PH.ParentID IS NOT NULL AND NOT ( PH.DataSourceInstanceId = 50351 AND PH.LevelNum = 1 ); ","TargetTable":"PS.Lookup","TruncateTargetTable": false,"TargetConnection":"CAM"}', 'PS.Lookup')
  , (@LoadType_Daily, 'CAM PS.Lookup - RiskClassOfBusiness', @ProcessType_Copy, '{"SourceConnection":"PlacementStore","SourceTable":"ods.vw_ref_ClassOfBusiness","SourceSQL":"SELECT GroupReferenceId = ''RiskClassOfBusiness'' , GroupName = ''Risk Class Of Business'' , SubGroupReferenceId = cob.DataSourceInstanceId , SubGroupName = COALESCE(DSI.DataSourceInstanceName, CONVERT(NVARCHAR(10), cob.DataSourceInstanceID)) , ReferenceId = cob.ClassOfBusinessId , ParentReferenceId = NULL , Name = ISNULL(cob.ClassOfBusiness, '''') , SourceReferenceId = cob.ClassOfBusinessKey , IsDeleted = cob.IsDeprecated , [Key] = cob.ClassOfBusinessKey FROM ods.vw_ref_ClassOfBusiness cob LEFT JOIN Reference.DataSourceInstance DSI ON DSI.DataSourceInstanceID = cob.DataSourceInstanceId;","TargetTable":"PS.Lookup","TruncateTargetTable": false,"TargetConnection":"CAM"}', 'PS.Lookup')
  , (@LoadType_Daily, 'CAM PS.Lookup - RiskCoverageType', @ProcessType_Copy, '{"SourceConnection":"PlacementStore","SourceTable":"ods.vw_ref_CoverageType","SourceSQL":"SELECT GroupReferenceId = ''RiskCoverageType'' , GroupName = ''Risk Coverage Type'' , SubGroupReferenceId = ct.DataSourceInstanceId , SubGroupName = COALESCE(DSI.DataSourceInstanceName, CONVERT(NVARCHAR(10), ct.DataSourceInstanceID)) , ReferenceId = ct.CoverageTypeId , ParentReferenceId = NULL , Name = ISNULL(ct.CoverageType, '''') , SourceReferenceId = ct.CoverageTypeKey , IsDeleted = ct.IsDeprecated , [Key] = ct.CoverageTypeKey FROM ods.vw_ref_CoverageType ct LEFT JOIN Reference.DataSourceInstance DSI ON DSI.DataSourceInstanceID = ct.DataSourceInstanceId; ","TargetTable":"PS.Lookup","TruncateTargetTable": false,"TargetConnection":"CAM"}', 'PS.Lookup')
  , (@LoadType_Daily, 'CAM PS.Lookup - RiskIndustry', @ProcessType_Copy, '{"SourceConnection":"PlacementStore","SourceTable":"ods.vw_ref_Industry","SourceSQL":"SELECT GroupReferenceId = ''RiskIndustry'' , GroupName = ''Risk Industry'' , SubGroupReferenceId = i.DataSourceInstanceID , SubGroupName = COALESCE(DSI.DataSourceInstanceName, CONVERT(NVARCHAR(10), i.DataSourceInstanceID)) , ReferenceId = i.IndustryID , ParentReferenceId = CASE WHEN R.ParentID IS NOT NULL THEN NULL ELSE i.ParentID END , Name = i.IndustryName , SourceReferenceId = i.ElementTagTypeKey , IsDeleted = i.IsDeprecated , [Key] = i.ElementTagTypeKey FROM ods.vw_ref_Industry i LEFT JOIN (SELECT DataSourceInstanceId, IndustryID, ParentID FROM ods.vw_ref_Industry WHERE ParentID IS NULL) R ON R.DataSourceInstanceID = i.DataSourceInstanceID AND R.IndustryID = i.ParentID LEFT JOIN Reference.DataSourceInstance DSI ON DSI.DataSourceInstanceID = i.DataSourceInstanceId;","TargetTable":"PS.Lookup","TruncateTargetTable": false,"TargetConnection":"CAM"}', 'PS.Lookup')
  , (@LoadType_Daily, 'CAM PS.Lookup - RiskProduct', @ProcessType_Copy, '{"SourceConnection":"PlacementStore","SourceTable":"ods.vw_ref_LineOfBusiness","SourceSQL":"SELECT GroupReferenceId = ''RiskProduct'' , GroupName = ''Risk Product'' , SubGroupReferenceId = lob.DataSourceInstanceId , SubGroupName = COALESCE(DSI.DataSourceInstanceName, CONVERT(NVARCHAR(10), lob.DataSourceInstanceID)) , ReferenceId = lob.LineOfBusinessId , ParentReferenceId = NULL , Name = ISNULL(lob.LineOfBusiness, '''') , SourceReferenceId = lob.LineOfBusinessKey , IsDeleted = lob.IsDeprecated , [Key] = lob.LineOfBusinessKey FROM ods.vw_ref_LineOfBusiness lob LEFT JOIN Reference.DataSourceInstance DSI ON DSI.DataSourceInstanceID = lob.DataSourceInstanceId;","TargetTable":"PS.Lookup","TruncateTargetTable": false,"TargetConnection":"CAM"}', 'PS.Lookup')
  , (@LoadType_Daily, 'CAM PS.Lookup - RiskTrigger', @ProcessType_Copy, '{"SourceConnection":"PlacementStore","SourceTable":"ods.vw_ref_Trigger","SourceSQL":"SELECT GroupReferenceId = ''RiskTrigger'' , GroupName = ''Risk Trigger'' , SubGroupReferenceId = t.DataSourceInstanceId , SubGroupName = COALESCE(DSI.DataSourceInstanceName, CONVERT(NVARCHAR(10), t.DataSourceInstanceID)) , ReferenceId = t.TriggerId , ParentReferenceId = NULL , Name = ISNULL(t.[Trigger], '''') , SourceReferenceId = t.TriggerKey , IsDeleted = t.IsDeprecated , [Key] = t.TriggerKey FROM ods.vw_ref_Trigger t LEFT JOIN Reference.DataSourceInstance DSI ON DSI.DataSourceInstanceID = t.DataSourceInstanceId; ","TargetTable":"PS.Lookup","TruncateTargetTable": false,"TargetConnection":"CAM"}', 'PS.Lookup')
  , (@LoadType_Daily, 'CAM PS.MergeData', @ProcessType_SP, '{"StoredProcedure":"PS.MergeData", "TargetConnection":"CAM" ,"ResultType":"DummyRow"}', 'ref.Carrier')
  , (@LoadType_Daily, 'CAM PS.MergeFacilityData', @ProcessType_SP, '{"StoredProcedure":"PS.MergeFacilityData", "TargetConnection":"CAM"  ,"ResultType":"DummyRow"}', 'dbo.Facility')
  , (@LoadType_Daily, 'CAM PS.MergeFacilitySectionCarrierData', @ProcessType_SP, '{"StoredProcedure":"PS.MergeFacilitySectionCarrierData", "TargetConnection":"CAM" ,"ResultType":"DummyRow"}', 'dbo.FacilitySectionCarrier')
  , (@LoadType_Daily, 'CAM PS.MergeFacilitySectionData', @ProcessType_SP, '{"StoredProcedure":"PS.MergeFacilitySectionData", "TargetConnection":"CAM" ,"ResultType":"DummyRow"}', 'dbo.FacilitySection')
  , (@LoadType_Daily, 'CAM PS.MergeLinkedElementData', @ProcessType_SP, '{"StoredProcedure":"PS.MergeLinkedElementData", "TargetConnection":"CAM" ,"ResultType":"DummyRow"}', 'dbo.Translation')
  , (@LoadType_Daily, 'CAM PS.MergeLookups', @ProcessType_SP, '{"StoredProcedure":"PS.MergeLookups", "TargetConnection":"CAM", "ResultType":"DummyRow"}', 'PS.Lookup')
  , (@LoadType_Daily, 'CAM PS.MergeUserGroups', @ProcessType_SP, '{"StoredProcedure":"PS.MergeUserGroups", "TargetConnection":"CAM" ,"ResultType":"DummyRow"}', 'dbo.Translation')
  , (@LoadType_Daily, 'CAM PS.UserGroups', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceTable":"ods.vw_ref_Team", "SourceSQL":"SELECT DISTINCT TeamId, TeamName, OrgLevel1, OrgLevel2, OrgLevel3, OrgLevel4, OrgLevel5, OrgLevel6, CAST(ParentId AS NVARCHAR(200)) AS ParentId, DatasourceInstanceID = DataSourceInstanceID, LevelNum, OrganisationRole, ETLCreatedDate as CreatedDateTime, ETLUpdatedDate as UpdatedDateTime, IsDeprecated, CAST(OrgNode AS nvarchar(4000)) AS ''OrgNode'', FullPath FROM ods.vw_ref_Team", "TargetTable":"PS.UserGroups", "TruncateTargetTable": true, "TargetConnection":"CAM"}', 'PS.UserGroups')
  , (@LoadType_Daily, 'CMSStaging.AppetiteResponse', @ProcessType_Copy, '{"SourceConnection":"CMS", "SourceTable":"dbo.AppetiteResponse", "SourceSQL":"SELECT AppetiteResponseId, PlacementId, InsurerSubscriptionId, InsurerId, InAppetite, RestrictionNarative, MarketingDecisionId, MarketingDecisionComment, CreatedDate, (SELECT MAX(v) FROM (VALUES (CreatedDate), (LastUpdatedDate), (DeletedDate)) AS value(v)) AS LastUpdatedDate, IsDeleted FROM dbo.AppetiteResponse", "TargetTable":"CMSStaging.dbo_AppetiteResponse", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'CMSStaging.AppetiteResponse')
  , (@LoadType_Daily, 'CMSStaging.BUHierarchy', @ProcessType_Copy, '{"SourceConnection":"CMS", "SourceTable":"Ref.BUHierarchy", "SourceSQL":"SELECT BUH.BUHierarchyId, BUHT.BUHierarchyDescription, BUH.BUHierarchyLevel, BUH.BUHierarchyParentId, BUH.CreatedDate, (SELECT MAX(v) FROM (VALUES (BUH.CreatedDate), (BUH.LastUpdatedDate), (BUH.DeletedDate), (BUHT.CreatedDate), (BUHT.LastUpdatedDate), (BUHT.DeletedDate)) AS value(v)) AS LastUpdatedDate, BUH.IsDeleted, BUH.DataSourceInstanceId, BUH.InScopeForPolicyFeed, BUH.InScopeForTeamInUI, BUH.BUHierarchySourceId FROM Ref.BUHierarchy AS BUH LEFT JOIN Ref.BUHierarchyTranslation AS BUHT ON BUHT.BUHierarchyId = BUH.BUHierarchyId AND BUHT.LanguageId = 49", "TargetTable":"CMSStaging.ref_BUHierarchy", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'CMSStaging.BUHierarchy')
  , (@LoadType_Daily, 'CMSStaging.BusinessType', @ProcessType_Copy, '{"SourceConnection":"CMS", "SourceTable":"ref.BusinessType", "SourceSQL":"SELECT BusinessTypeId, BusinessType AS BusinessTypeDescription, CreatedDate, (SELECT MAX(v) FROM (VALUES (CreatedDate), (UpdatedDate)) AS value(v)) AS LastUpdatedDate, IsDeleted FROM Ref.BusinessType", "TargetTable":"CMSStaging.ref_BusinessType", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'CMSStaging.BusinessType')
  , (@LoadType_Daily, 'CMSStaging.CompanyOwnership', @ProcessType_Copy, '{"SourceConnection":"CMS", "SourceTable":"Ref.CompanyOwnership", "SourceSQL":"SELECT CO.CompanyOwnershipId, COT.CompanyOwnershipDescription, CO.CreatedDate, (SELECT MAX(v) FROM (VALUES (CO.CreatedDate), (CO.LastUpdatedDate), (CO.DeletedDate), (COT.CreatedDate), (COT.LastUpdatedDate), (COT.DeletedDate)) AS value(v)) AS LastUpdatedDate, CO.IsDeleted FROM Ref.CompanyOwnership AS CO LEFT JOIN Ref.CompanyOwnershipTranslation AS COT ON COT.CompanyOwnershipId = CO.CompanyOwnershipId AND COT.LanguageId = 49", "TargetTable":"CMSStaging.ref_CompanyOwnership", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'CMSStaging.CompanyOwnership')
  , (@LoadType_Daily, 'CMSStaging.Currency', @ProcessType_Copy, '{"SourceConnection":"CMS", "SourceTable":"Ref.CurrencyTranslation", "SourceSQL":"SELECT C.CurrencyId, CT.CurrencyName, C.CurrencyAlphaCode, C.CurrencyMinorUnit, C.CurrencyNumericCode, C.IsFund, C.IsWithdrawn, C.WithdrawalPeriod, C.ETLDate AS CreatedDate, (SELECT MAX(v) FROM (VALUES (C.ETLDate), (C.LastUpdateTime), (CT.CreatedDate), (CT.LastUpdatedDate)) AS value(v)) AS LastUpdatedDate, C.IsDeleted FROM Ref.Currency AS C LEFT JOIN (SELECT CT1.CurrencyId, CT1.CurrencyName, CT2.CreatedDate, CT2.LastUpdatedDate, ROW_NUMBER() OVER(PARTITION BY CT1.CurrencyId, CT1.LanguageId ORDER BY CT1.IsDeleted ASC, CT1.LastUpdatedDate DESC) AS ROW_NO FROM Ref.CurrencyTranslation AS CT1 INNER JOIN (SELECT CurrencyId, MIN(CreatedDate) AS CreatedDate, MAX(LastUpdatedDate) AS LastUpdatedDate, CAST(MIN(CAST(IsDeleted AS INT)) AS BIT) AS IsDeleted FROM (SELECT CurrencyId, CreatedDate, (SELECT MAX(v) FROM (VALUES (CreatedDate), (LastUpdatedDate), (DeletedDate)) AS value(v)) AS LastUpdatedDate, IsDeleted FROM Ref.CurrencyTranslation WHERE LanguageId = 49) AS A GROUP BY CurrencyId) AS CT2 ON CT2.CurrencyId = CT1.CurrencyId WHERE CT1.LanguageId = 49) AS CT ON CT.CurrencyId = C.CurrencyId AND CT.ROW_NO = 1", "TargetTable":"CMSStaging.ref_Currency", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'CMSStaging.Currency')
  , (@LoadType_Daily, 'CMSStaging.DistributionChannel', @ProcessType_Copy, '{"SourceConnection":"CMS", "SourceTable":"ref.DistributionChannel", "SourceSQL":"SELECT DC.DistributionChannelId, DCT.DistributionChannelDescription, DC.CreatedDate, (SELECT MAX(v) FROM (VALUES (DC.CreatedDate), (DC.LastUpdatedDate), (DC.DeletedDate), (DCT.CreatedDate), (DCT.LastUpdatedDate), (DCT.DeletedDate)) AS value(v)) AS LastUpdatedDate, DC.IsDeleted FROM Ref.DistributionChannel AS DC LEFT JOIN Ref.DistributionChannelTranslation AS DCT ON DCT.DistributionChannelId = DC.DistributionChannelId AND DCT.LanguageId = 49", "TargetTable":"CMSStaging.ref_DistributionChannel", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'CMSStaging.DistributionChannel')
  , (@LoadType_Daily, 'CMSStaging.ExceptionType', @ProcessType_Copy, '{"SourceConnection":"CMS", "SourceTable":"Ref.ExceptionType", "SourceSQL":"SELECT ET.ExceptionTypeId, ETT.ExceptionDescription, ET.CreatedDate, (SELECT MAX(v) FROM (VALUES (ET.CreatedDate), (ET.LastUpdatedDate), (ET.DeletedDate), (ETT.CreatedDate), (ETT.LastUpdatedDate), (ETT.DeletedDate)) AS value(v)) AS LastUpdatedDate, ET.IsDeleted FROM Ref.ExceptionType AS ET LEFT JOIN Ref.ExceptionTypeTranslation AS ETT ON ETT.ExceptionTypeId = ET.ExceptionTypeId AND ETT.LanguageId = 49", "TargetTable":"CMSStaging.ref_ExceptionType", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'CMSStaging.ExceptionType')
  , (@LoadType_Daily, 'CMSStaging.ExposureMeasure', @ProcessType_Copy, '{"SourceConnection":"CMS", "SourceTable":"Ref.ExposureMeasure", "SourceSQL":"SELECT EM.ExposureMeasureId, EMT.ExposureMeasureQuestion, EM.ExposureMeasureType, EMT.QuestionListData, EM.CreatedDate, (SELECT MAX(v) FROM (VALUES (EM.CreatedDate), (EM.LastUpdatedDate), (EM.DeletedDate), (EMT.CreatedDate), (EMT.LastUpdatedDate), (EMT.DeletedDate)) AS value(v)) AS LastUpdatedDate, EM.IsDeleted FROM Ref.ExposureMeasure AS EM LEFT JOIN Ref.ExposureMeasureTranslation AS EMT ON EMT.ExposureMeasureId = EM.ExposureMeasureId AND EMT.LanguageId = 49", "TargetTable":"CMSStaging.ref_ExposureMeasure", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'CMSStaging.ExposureMeasure')
  , (@LoadType_Daily, 'CMSStaging.GeographyGroup', @ProcessType_Copy, '{"SourceConnection":"CMS", "SourceTable":"Ref.GeographyGroup", "SourceSQL":"SELECT GG.GeographyGroupId, GGT.GeographyGroupDescription, GG.CreatedDate, (SELECT MAX(v) FROM (VALUES (GG.CreatedDate), (GG.LastUpdatedDate), (GG.DeletedDate), (GGT.CreatedDate), (GGT.LastUpdatedDate), (GGT.DeletedDate)) AS value(v)) AS LastUpdatedDate, GG.IsDeleted FROM Ref.GeographyGroup AS GG LEFT JOIN Ref.GeographyGroupTranslation AS GGT ON GGT.GeographyGroupId = GG.GeographyGroupId AND GGT.LanguageId = 49", "TargetTable":"CMSStaging.ref_GeographyGroup", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'CMSStaging.GeographyGroup')
  , (@LoadType_Daily, 'CMSStaging.GeographyGroupMembership', @ProcessType_Copy, '{"SourceConnection":"CMS", "SourceTable":"dbo.GeographyGroupMembership", "SourceSQL":"SELECT GeographyGroupMembershipId, GeographyGroupId, CountryId, CreatedDate, (SELECT MAX(v) FROM (VALUES (CreatedDate), (LastUpdatedDate), (DeletedDate)) AS value(v)) AS LastUpdatedDate, IsDeleted FROM dbo.GeographyGroupMembership", "TargetTable":"CMSStaging.ref_GeographyGroupMembership", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'CMSStaging.GeographyGroupMembership')
  , (@LoadType_Daily, 'CMSStaging.Industry', @ProcessType_Copy, '{"SourceConnection":"CMS", "SourceTable":"ref.Industry", "SourceSQL":"SELECT I.IndustryId, IT.IndustryDescription, I.IndustryCode, I.IndustryGroupId, I.ISICDivision, I.ISICCode, I.CreatedDate, (SELECT MAX(v) FROM (VALUES (I.CreatedDate), (I.LastUpdatedDate), (I.DeletedDate), (IT.CreatedDate), (IT.LastUpdatedDate), (IT.DeletedDate)) AS value(v)) AS LastUpdatedDate, I.IsDeleted FROM Ref.Industry AS I LEFT JOIN Ref.IndustryTranslation AS IT ON IT.IndustryId = I.IndustryId AND IT.LanguageId = 49", "TargetTable":"CMSStaging.ref_Industry", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'CMSStaging.Industry')
  , (@LoadType_Daily, 'CMSStaging.IndustryGroup', @ProcessType_Copy, '{"SourceConnection":"CMS", "SourceTable":"ref.IndustryGroup", "SourceSQL":"SELECT IG.IndustryGroupId, IGT.IndustryGroupDescription, IG.ISICDivision, IG.CreatedDate, (SELECT MAX(v) FROM (VALUES (IG.CreatedDate), (IG.LastUpdatedDate), (IG.DeletedDate), (IGT.CreatedDate), (IGT.LastUpdatedDate), (IGT.DeletedDate)) AS value(v)) AS LastUpdatedDate, IG.IsDeleted FROM Ref.IndustryGroup AS IG LEFT JOIN Ref.IndustryGroupTranslation AS IGT ON IGT.IndustryGroupId = IG.IndustryGroupId AND IGT.LanguageId = 49", "TargetTable":"CMSStaging.ref_IndustryGroup", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'CMSStaging.IndustryGroup')
  , (@LoadType_Daily, 'CMSStaging.Layer', @ProcessType_Copy, '{"SourceConnection":"CMS", "SourceTable":"dbo.Layer", "SourceSQL":"SELECT LayerId, PlacementId, LayerNumber, LayerTypeId, OrderPctWhole, LimitCurrencyId, Limit, ExcessCurrencyId, Excess, LayerComment, CreatedDate, (SELECT MAX(v) FROM (VALUES (CreatedDate), (LastUpdatedDate), (DeletedDate)) AS value(v)) AS LastUpdatedDate, IsDeleted FROM dbo.Layer", "TargetTable":"CMSStaging.dbo_Layer", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'CMSStaging.Layer')
  , (@LoadType_Daily, 'CMSStaging.Load_dbo_Placement', @ProcessType_SP, '{"StoredProcedure":"CMSStaging.Load_dbo_Placement", "TargetConnection":"PlacementStore"}', 'dbo.Placement')
  , (@LoadType_Daily, 'CMSStaging.Load_dbo_PlacementPolicy', @ProcessType_SP, '{"StoredProcedure":"CMSStaging.Load_dbo_PlacementPolicy", "TargetConnection":"PlacementStore"}', 'dbo.PlacementPolicy')
  , (@LoadType_Daily, 'CMSStaging.Load_ref_Industry', @ProcessType_SP, '{"StoredProcedure":"CMSStaging.Load_ref_Industry", "TargetConnection":"PlacementStore"}', 'dbo.Industry')
  , (@LoadType_Daily, 'CMSStaging.Load_ref_IndustryGroup', @ProcessType_SP, '{"StoredProcedure":"CMSStaging.Load_ref_IndustryGroup", "TargetConnection":"PlacementStore"}', 'dbo.Industry')
  , (@LoadType_Daily, 'CMSStaging.Load_ref_PlacementStatus', @ProcessType_SP, '{"StoredProcedure":"CMSStaging.Load_ref_PlacementStatus", "TargetConnection":"PlacementStore"}', 'ref.PlacementStatus')
  , (@LoadType_Daily, 'CMSStaging.Load_ref_Product', @ProcessType_SP, '{"StoredProcedure":"CMSStaging.Load_ref_Product", "TargetConnection":"PlacementStore"}', 'dbo.Product')
  , (@LoadType_Daily, 'CMSStaging.MarketingDecision', @ProcessType_Copy, '{"SourceConnection":"CMS", "SourceTable":"ref.MarketingDecision", "SourceSQL":"SELECT MD.MarketingDecisionId, MDT.MarketingDecisionDescription, MD.IsCommentRequired, MD.Tier2, MD.Tier3, MD.CreatedDate, (SELECT MAX(v) FROM (VALUES (MD.CreatedDate), (MD.LastUpdatedDate), (MD.DeletedDate), (MDT.CreatedDate), (MDT.LastUpdatedDate), (MDT.DeletedDate)) AS value(v)) AS LastUpdatedDate, MD.IsDeleted FROM Ref.MarketingDecision AS MD LEFT JOIN Ref.MarketingDecisionTranslation AS MDT ON MDT.MarketingDecisionId = MD.MarketingDecisionId AND MDT.LanguageId = 49", "TargetTable":"CMSStaging.ref_MarketingDecision", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'CMSStaging.MarketingDecision')
  , (@LoadType_Daily, 'CMSStaging.OutcomeReason', @ProcessType_Copy, '{"SourceConnection":"CMS", "SourceTable":"ref.OutcomeReason", "SourceSQL":"SELECT OC.OutcomeReasonId, ORT.OutcomeReasonDescription, OC.QuoteOutcomeId, OC.CreatedDate, (SELECT MAX(v) FROM (VALUES (OC.CreatedDate), (OC.LastUpdatedDate), (OC.DeletedDate), (ORT.CreatedDate), (ORT.LastUpdatedDate), (ORT.DeletedDate)) AS value(v)) AS LastUpdatedDate, OC.IsDeleted FROM Ref.OutcomeReason AS OC LEFT JOIN Ref.OutcomeReasonTranslation AS ORT ON ORT.OutcomeReasonId = OC.OutcomeReasonId AND ORT.LanguageId = 49", "TargetTable":"CMSStaging.ref_OutcomeReason", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'CMSStaging.OutcomeReason')
  , (@LoadType_Daily, 'CMSStaging.Placement', @ProcessType_Copy, '{"SourceConnection":"CMS", "SourceTable":"dbo.Placement", "SourceSQL":"SELECT PlacementId, PlacementStatusId, FirstPlacementId, RenewedFromPlacementId, Insured, PlacingBrokerId, PlacingBroker2Id, BUHierarchyId, IsRenewal, RenewabilityStatusId, PlacementTypeId, ProductId, SubProductId, IsReinsurance, DistributionChannelId, InsuredTerritoryId, IndustryId, PricingFactorId, ExceptionById, ExceptionDate, KeyPlacementDataChangeFlag, KeyAppetiteDataChangeFlag, KeyRiskDetailsDataChangeFlag, PlacementComment, CreatedDate, (SELECT MAX(v) FROM (VALUES (CreatedDate), (LastUpdatedDate), (DeletedDate)) AS value(v)) AS LastUpdatedDate, IsDeleted, InceptionDate, RenewabilityComment, IsWarning, CompanyOwnershipId, AppetiteMatchLastRunDate FROM dbo.Placement", "TargetTable":"CMSStaging.dbo_Placement", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'CMSStaging.Placement')
  , (@LoadType_Daily, 'CMSStaging.PlacementStatus', @ProcessType_Copy, '{"SourceConnection":"CMS", "SourceTable":"ref.PlacementStatus", "SourceSQL":"SELECT PS.PlacementStatusId, PST.PlacementStatusDescription, PS.CreatedDate, (SELECT MAX(v) FROM (VALUES (PS.CreatedDate), (PS.LastUpdatedDate), (PS.DeletedDate), (PST.CreatedDate), (PST.LastUpdatedDate), (PST.DeletedDate)) AS value(v)) AS LastUpdatedDate, PS.IsDeleted FROM Ref.PlacementStatus AS PS LEFT JOIN Ref.PlacementStatusTranslation AS PST ON PST.PlacementStatusId = PS.PlacementStatusId AND PST.LanguageId = 49", "TargetTable":"CMSStaging.ref_PlacementStatus", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'CMSStaging.PlacementStatus')
  , (@LoadType_Daily, 'CMSStaging.PlacementType', @ProcessType_Copy, '{"SourceConnection":"CMS", "SourceTable":"ref.PlacementType", "SourceSQL":"SELECT PT.PlacementTypeId, PTT.PlacementTypeDescription, PT.CreatedDate, (SELECT MAX(v) FROM (VALUES (PT.CreatedDate), (PT.LastUpdatedDate), (PT.DeletedDate), (PTT.CreatedDate), (PTT.LastUpdatedDate), (PTT.DeletedDate)) AS value(v)) AS LastUpdatedDate, PT.IsDeleted FROM Ref.PlacementType AS PT LEFT JOIN Ref.PlacementTypeTranslation AS PTT ON PTT.PlacementTypeId = PT.PlacementTypeId AND PTT.LanguageId = 49", "TargetTable":"CMSStaging.ref_PlacementType", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'CMSStaging.PlacementType')
  , (@LoadType_Daily, 'CMSStaging.Policy', @ProcessType_Copy, '{"SourceConnection":"CMS", "SourceTable":"dbo.policy", "SourceSQL":"", "TargetTable":"CMSStaging.dbo_Policy", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "DataSourceInstanceIdLookupSql":"SELECT Value = ADF.GetDataSourceInstanceList();", "DataSourceInstanceIdLookupConnection":"PlacementStore"}', 'CMSStaging.Policy')
  , (@LoadType_Daily, 'CMSStaging.PolicyLayerLink', @ProcessType_Copy, '{"SourceConnection":"CMS", "SourceTable":"dbo.PolicyLayerLink", "SourceSQL":"SELECT PolicyLayerLinkId, PolicyId, LayerId, CreatedDate, (SELECT MAX(v) FROM (VALUES (CreatedDate), (LastUpdatedDate), (DeletedDate)) AS value(v)) AS LastUpdatedDate, IsDeleted FROM dbo.PolicyLayerLink", "TargetTable":"CMSStaging.dbo_PolicyLayerLink", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'CMSStaging.PolicyLayerLink')
  , (@LoadType_Daily, 'CMSStaging.PricingFactor', @ProcessType_Copy, '{"SourceConnection":"CMS", "SourceTable":"ref.PricingFactor", "SourceSQL":"SELECT PF.PricingFactorId, PFT.PricingFactorDescription, PF.PricingFactorPercentage, PF.CreatedDate, (SELECT MAX(v) FROM (VALUES (PF.CreatedDate), (PF.LastUpdatedDate), (PF.DeletedDate), (PFT.CreatedDate), (PFT.LastUpdatedDate), (PFT.DeletedDate)) AS value(v)) AS LastUpdatedDate, PF.IsDeleted FROM Ref.PricingFactor AS PF LEFT JOIN Ref.PricingFactorTranslation AS PFT ON PFT.PricingFactorId = PF.PricingFactorId AND PFT.LanguageId = 49", "TargetTable":"CMSStaging.ref_PricingFactor", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'CMSStaging.PricingFactor')
  , (@LoadType_Daily, 'CMSStaging.Product', @ProcessType_Copy, '{"SourceConnection":"CMS", "SourceTable":"ref.Product", "SourceSQL":"SELECT P.ProductId, PT.ProductDescription, P.GlobalLineId, P.CreatedDate, (SELECT MAX(v) FROM (VALUES (P.CreatedDate), (P.LastUpdatedDate), (P.DeletedDate), (PT.CreatedDate), (PT.LastUpdatedDate), (PT.DeletedDate)) AS value(v)) AS LastUpdatedDate, P.IsDeleted FROM Ref.Product AS P LEFT JOIN Ref.ProductTranslation AS PT ON PT.ProductId = P.ProductId AND PT.LanguageId = 49", "TargetTable":"CMSStaging.ref_Product", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'CMSStaging.Product')
  , (@LoadType_Daily, 'CMSStaging.QPF', @ProcessType_Copy, '{"SourceConnection":"CMS", "SourceTable":"dbo.QPF", "SourceSQL":"SELECT QPFId, AppetiteResponseId, UnderwriterContactName, QuoteSubmitDate, QuoteResponseRecievedDate, LineSubmittedDate, LinePutDownDate, PrimarySliplead, QuoteOutcomeId, OutcomeReasonId, OutcomeReasonComment, CreatedDate, (SELECT MAX(v) FROM (VALUES (CreatedDate), (LastUpdatedDate), (DeletedDate)) AS value(v)) AS LastUpdatedDate, IsDeleted, IsFailedToRespond, IsQuotedToLeadPrimary, QuoteRequiredByDate, IsQuoteResponseNotRequired FROM dbo.QPF", "TargetTable":"CMSStaging.dbo_QPF", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'CMSStaging.QPF')
  , (@LoadType_Daily, 'CMSStaging.QuoteOutcome', @ProcessType_Copy, '{"SourceConnection":"CMS", "SourceTable":"ref.QuoteOutcome", "SourceSQL":"SELECT QO.QuoteOutcomeId, QOT.QuoteOutcomeDescription, QO.CreatedDate, (SELECT MAX(v) FROM (VALUES (QO.CreatedDate), (QO.LastUpdatedDate), (QO.DeletedDate), (QOT.CreatedDate), (QOT.LastUpdatedDate), (QOT.DeletedDate)) AS value(v)) AS LastUpdatedDate, QO.IsDeleted FROM Ref.QuoteOutcome AS QO LEFT JOIN Ref.QuoteOutcomeTranslation AS QOT ON QOT.QuoteOutcomeId = QO.QuoteOutcomeId AND QOT.LanguageId = 49", "TargetTable":"CMSStaging.ref_QuoteOutcome", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'CMSStaging.QuoteOutcome')
  , (@LoadType_Daily, 'CMSStaging.RenewabilityStatus', @ProcessType_Copy, '{"SourceConnection":"CMS", "SourceTable":"ref.RenewabilityStatus", "SourceSQL":"SELECT RS.RenewabilityStatusId, RST.RenewabilityStatusDescription, RS.CreatedDate, (SELECT MAX(v) FROM (VALUES (RS.CreatedDate), (RS.LastUpdatedDate), (RS.DeletedDate), (RST.CreatedDate), (RST.LastUpdatedDate), (RST.DeletedDate)) AS value(v)) AS LastUpdatedDate, RS.IsDeleted FROM Ref.RenewabilityStatus AS RS LEFT JOIN Ref.RenewabilityStatusTranslation AS RST ON RST.RenewabilityStatusId = RS.RenewabilityStatusId AND RST.LanguageId = 49", "TargetTable":"CMSStaging.ref_RenewabilityStatus", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'CMSStaging.RenewabilityStatus')
  , (@LoadType_Daily, 'CMSStaging.RenewalStatus', @ProcessType_Copy, '{"SourceConnection":"CMS", "SourceTable":"ref.RenewalStatus", "SourceSQL":"SELECT RenewalStatusId, RenewalStatus AS RenewalStatusDescription, CreatedDate, (SELECT MAX(v) FROM (VALUES (CreatedDate), (UpdatedDate)) AS value(v)) AS LastUpdatedDate, IsDeleted FROM Ref.RenewalStatus", "TargetTable":"CMSStaging.ref_RenewalStatus", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'CMSStaging.RenewalStatus')
  , (@LoadType_Daily, 'CMSStaging.RiskExposure', @ProcessType_Copy, '{"SourceConnection":"CMS", "SourceTable":"dbo.RiskExposure", "SourceSQL":"SELECT RiskExposureId, PlacementId, QuestionOrder, ExposureMeasureId, ResponseValue, ResponseDisposal, ResponseCurrency, CreatedDate, (SELECT MAX(v) FROM (VALUES (CreatedDate), (LastUpdatedDate), (DeletedDate)) AS value(v)) AS LastUpdatedDate, IsDeleted, IsUnknown FROM dbo.RiskExposure", "TargetTable":"CMSStaging.dbo_RiskExposure", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'CMSStaging.RiskExposure')
  , (@LoadType_Daily, 'CMSStaging.SubProduct', @ProcessType_Copy, '{"SourceConnection":"CMS", "SourceTable":"ref.SubProduct", "SourceSQL":"SELECT SP.SubProductId, SPT.SubProductDescription, SP.ProductId, SP.CreatedDate, (SELECT MAX(v) FROM (VALUES (SP.CreatedDate), (SP.LastUpdatedDate), (SP.DeletedDate), (SPT.CreatedDate), (SPT.LastUpdatedDate), (SPT.DeletedDate)) AS value(v)) AS LastUpdatedDate, SP.IsDeleted FROM Ref.SubProduct AS SP LEFT JOIN Ref.SubProductTranslation AS SPT ON SPT.SubProductId = SP.SubProductId AND SPT.LanguageId = 49", "TargetTable":"CMSStaging.ref_SubProduct", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'CMSStaging.SubProduct')
  , (@LoadType_Daily, 'COLStaging.CarrierProduct', @ProcessType_Copy, '{"SourceConnection":"COL", "SourceTable":"dbo.Tabela_Produtos", "SourceSQL":"SELECT CAST(ProductKey AS NVARCHAR(71)) AS ProductKey, LTRIM(RTRIM(CAST(SUBSTRING(ProductName, 1, SpaceIndex) AS NVARCHAR(100)))) AS ProductCode, CAST(SUBSTRING(ProductName, SpaceIndex + 3, 4000) AS NVARCHAR(160)) AS ProductName, CAST(ISNULL(LineName, ''INDEFINIDO'') AS NVARCHAR(80)) AS LineName, CAST(ISNULL(ClassName, ''INDEFINIDO'') AS NVARCHAR(30)) AS ClassName, Data_inclusao AS CreatedDate, COALESCE(Data_alteracao, Data_inclusao) AS LastUpdatedDate, CAST(CASE WHEN Situacao = ''I'' THEN 1 ELSE 0 END AS BIT) AS IsDeleted, 50003 AS DataSourceInstanceId  FROM  ( SELECT DISTINCT P.[Nome] AS ProductName, L.Descricao AS LineName, L.Situacao AS IsLineDeleted, C.Descricao AS ClassName, C.Situacao AS IsClassDeleted, CHARINDEX('' '', P.[Nome]) AS SpaceIndex, CONCAT (LTRIM(RTRIM(ISNULL(C.Celula,0))), ''.'', LTRIM(RTRIM(ISNULL(L.Ramo,0))), ''.'', LTRIM(RTRIM(P.Produto))) AS ProductKey, P.Data_alteracao, P.Data_inclusao, P.Situacao FROM dbo.Tabela_Produtos P LEFT JOIN dbo.Tabela_Ramos L ON L.Ramo = P.Ramo LEFT JOIN dbo.Tabela_Celulas C ON C.Celula = P.Celula  WHERE  P.[Nome] LIKE ''[0-9]%''  ) A", "TargetTable":"COLStaging.CarrierProduct", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'COLStaging.CarrierProduct')
  , (@LoadType_Daily, 'COLStaging.Load_COLStaging_Product', @ProcessType_SP, '{"StoredProcedure":"COLStaging.Load_COLStaging_Product", "TargetConnection":"PlacementStore"}', 'COLStaging.Product')
  , (@LoadType_Daily, 'COLStaging.Load_dbo_Lookup', @ProcessType_SP, '{"StoredProcedure":"COLStaging.Load_dbo_Lookup", "TargetConnection":"PlacementStore"}', 'dbo.Lookup')
  , (@LoadType_Daily, 'COLStaging.Load_dbo_LookupGroup', @ProcessType_SP, '{"StoredProcedure":"COLStaging.Load_dbo_LookupGroup", "TargetConnection":"PlacementStore"}', 'dbo.LookupGroup')
  , (@LoadType_Daily, 'COLStaging.Load_dbo_PartyAttribute', @ProcessType_SP, '{"StoredProcedure":"COLStaging.Load_dbo_PartyAttribute", "TargetConnection":"PlacementStore"}', 'dbo.PartyAttribute')
  , (@LoadType_Daily, 'COLStaging.Load_dbo_Product', @ProcessType_SP, '{"StoredProcedure":"COLStaging.Load_dbo_Product", "TargetConnection":"PlacementStore"}', 'dbo.Product')
  , (@LoadType_Daily, 'COLStaging.Load_PS_ProductAttribute', @ProcessType_SP, '{"StoredProcedure":"COLStaging.Load_PS_ProductAttribute", "TargetConnection":"PlacementStore"}', 'PS.ProductAttribute')
  , (@LoadType_Daily, 'COLStaging.Tabela_Caracterists', @ProcessType_Copy, '{"SourceConnection":"COL", "SourceTable":"dbo.Tabela_Caracterists", "SourceSQL":"SELECT src.[Tipo_caracteristica], src.[Caracteristica], src.[Descricao], src.[Situacao], src.[Data_inclusao], src.[Data_alteracao] FROM dbo.Tabela_Caracterists AS src", "TargetTable":"COLStaging.Tabela_Caracterists", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'COLStaging.Tabela_Caracterists')
  , (@LoadType_Daily, 'COLStaging.Tabela_ClientCaracs', @ProcessType_Copy, '{"SourceConnection":"COL", "SourceTable":"dbo.Tabela_ClientCaracs", "SourceSQL":"SELECT [Cliente], [Tipo_caracteristica], [Caracteristica], /* [Comp_data], */ /* [Comp_inteiro], */ /* [Comp_valor], */ /* [Comp_carac], */ [Situacao], [Data_inclusao], [Data_alteracao] /* , [Destacar] */ FROM dbo.Tabela_ClientCaracs WITH (NOLOCK) WHERE ISNULL(Data_inclusao, ''1901-01-01'') > CAST(@ETLUpdatedDate AS DATETIME2) OR ISNULL(Data_alteracao, ''1901-01-01'') > CAST(@ETLUpdatedDate AS DATETIME2)", "TargetTable":"COLStaging.Tabela_ClientCaracs", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"SourceUpdatedDate"}', 'COLStaging.Tabela_ClientCaracs')
  , (@LoadType_Daily, 'COLStaging.Tabela_Documentos', @ProcessType_Copy, '{"SourceConnection":"COL", "SourceTable":"dbo.Tabela_Documentos", "SourceSQL":"SELECT src.[Documento], src.[Alteracao], src.[Proposta], src.[Tipo_documento], src.[Sub_tipo], src.[Tipo_negocio], src.[Cliente], src.[Termino_vigencia], src.[Seguradora], src.[Produto], src.[Apolice] FROM dbo.Tabela_Documentos AS src", "TargetTable":"COLStaging.Tabela_Documentos", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'COLStaging.Tabela_Documentos')
  , (@LoadType_Daily, 'COLStaging.Tabela_GruposHierarq', @ProcessType_Copy, '{"SourceConnection":"COL", "SourceTable":"dbo.Tabela_GruposHierarq", "SourceSQL":"SELECT src.[Grupo_hierarquico], src.[Nome], src.[Situacao], src.[Data_inclusao], src.[Data_alteracao] FROM dbo.Tabela_GruposHierarq AS src", "TargetTable":"COLStaging.Tabela_GruposHierarq", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'COLStaging.Tabela_GruposHierarq')
  , (@LoadType_Daily, 'COLStaging.Tabela_TiposCaracter', @ProcessType_Copy, '{"SourceConnection":"COL", "SourceTable":"dbo.Tabela_TiposCaracter", "SourceSQL":"SELECT src.[Tipo_caracteristica], src.[Descricao], src.[Situacao], src.[Data_inclusao], src.[Data_alteracao] FROM dbo.Tabela_TiposCaracter AS src", "TargetTable":"COLStaging.Tabela_TiposCaracter", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'COLStaging.Tabela_TiposCaracter')
  , (@LoadType_Daily, 'CRB Data Lake Export - ods.vw_ps_Policy', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ps_Policy", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ps_Policy.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ps_Policy/"}', 'vw_ps_Policy.snappy.parquet')
  , (@LoadType_Daily, 'CRB Data Lake Export - ods.vw_ps_PolicyAttribute', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ps_PolicyAttribute", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ps_PolicyAttribute.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ps_PolicyAttribute/"}', 'vw_ps_PolicyAttribute.snappy.parquet')
  , (@LoadType_Daily, 'CRB Data Lake Export - ods.vw_ref_Carrier', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ref_Carrier", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ref_Carrier.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ref_Carrier/"}', 'vw_ref_Carrier.snappy.parquet')
  , (@LoadType_Daily, 'CRB Data Lake Export - ods.vw_ref_CarrierRelationship', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ref_CarrierRelationship", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ref_CarrierRelationship.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ref_CarrierRelationship/"}', 'vw_ref_CarrierRelationship.snappy.parquet')
  , (@LoadType_Daily, 'CRB Data Lake Export - ods.vw_ref_CarrierType', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ref_CarrierType", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ref_CarrierType.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ref_CarrierType/"}', 'vw_ref_CarrierType.snappy.parquet')
  , (@LoadType_Daily, 'CRB Data Lake Export - ods.vw_ref_Facility', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ref_Facility", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ref_Facility.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ref_Facility/"}', 'vw_ref_Facility.snappy.parquet')
  , (@LoadType_Daily, 'CRB Data Lake Export - ods.vw_ref_FacilitySection', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ref_FacilitySection", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ref_FacilitySection.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ref_FacilitySection/"}', 'vw_ref_FacilitySection.snappy.parquet')
  , (@LoadType_Daily, 'CRB Data Lake Export - ods.vw_ref_FacilitySectionCarrier', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ref_FacilitySectionCarrier", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ref_FacilitySectionCarrier.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ref_FacilitySectionCarrier/"}', 'vw_ref_FacilitySectionCarrier.snappy.parquet')
  , (@LoadType_Daily, 'CRB Data Lake Export - ods.vw_ref_Geography', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ref_Geography", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ref_Geography.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ref_Geography/"}', 'vw_ref_Geography.snappy.parquet')
  , (@LoadType_Daily, 'CRB Data Lake Export - ods.vw_ref_InsuranceType', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ref_InsuranceType", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ref_InsuranceType.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ref_InsuranceType/"}', 'vw_ref_InsuranceType.snappy.parquet')
  , (@LoadType_Daily, 'CRB Data Lake Export - ods.vw_ref_NotRemarketingReason', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ref_NotRemarketingReason", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ref_NotRemarketingReason.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ref_NotRemarketingReason/"}', 'vw_ref_NotRemarketingReason.snappy.parquet')
  , (@LoadType_Daily, 'CRB Data Lake Export - ods.vw_ref_Party', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ref_Party", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ref_Party.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ref_Party/"}', 'vw_ref_Party.snappy.parquet')
  , (@LoadType_Daily, 'CRB Data Lake Export - ods.vw_ref_PartyAttribute', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ref_PartyAttribute", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ref_PartyAttribute.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ref_PartyAttribute/"}', 'vw_ref_PartyAttribute.snappy.parquet')
  , (@LoadType_Daily, 'CRB Data Lake Export - ods.vw_ref_PartyRole', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ref_PartyRole", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ref_PartyRole.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ref_PartyRole/"}', 'vw_ref_PartyRole.snappy.parquet')
  , (@LoadType_Daily, 'CRB Data Lake Export - ods.vw_ref_PolicyStatus', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ref_PolicyStatus", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ref_PolicyStatus.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ref_PolicyStatus/"}', 'vw_ref_PolicyStatus.snappy.parquet')
  , (@LoadType_Daily, 'CRB Data Lake Export - ods.vw_ref_PremiumRange', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ref_PremiumRange", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ref_PremiumRange.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ref_PremiumRange/"}', 'vw_ref_PremiumRange.snappy.parquet')
  , (@LoadType_Daily, 'CRB Data Lake Export - ods.vw_ref_ProducingOffice', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ref_ProducingOffice", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ref_ProducingOffice.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ref_ProducingOffice/"}', 'vw_ref_ProducingOffice.snappy.parquet')
  , (@LoadType_Daily, 'CRB Data Lake Export - ods.vw_ref_Product', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ref_Product", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ref_Product.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ref_Product/"}', 'vw_ref_Product.snappy.parquet')
  , (@LoadType_Daily, 'CRB Data Lake Export - ods.vw_ref_RenewableOption', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ref_RenewableOption", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ref_RenewableOption.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ref_RenewableOption/"}', 'vw_ref_RenewableOption.snappy.parquet')
  , (@LoadType_Daily, 'Eclipse.LoadPoolMember', @ProcessType_SP, '{"StoredProcedure":"EclipseStaging.Load_Eclipse_PoolMember", "TargetConnection":"PlacementStore"}', 'Eclipse.PoolMember')
  , (@LoadType_Daily, 'Eclipse.LoadRole', @ProcessType_SP, '{"StoredProcedure":"EclipseStaging.Load_Eclipse_Role", "TargetConnection":"PlacementStore"}', 'Eclipse.Role')
  , (@LoadType_Daily, 'Eclipse.LoadUWGroupVersion', @ProcessType_SP, '{"StoredProcedure":"EclipseStaging.Load_Eclipse_UWGroupVersion", "TargetConnection":"PlacementStore"}', 'Eclipse.UWGroupVersion')
  , (@LoadType_Daily, 'EclipseStaging.Policy', @ProcessType_Copy, '{"SourceConnection":"PASStaging", "SourceTable":"Eclipse.Policy", "SourceSQL":"", "TargetTable":"EclipseStaging.Policy", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'EclipseStaging.Policy')
  , (@LoadType_Daily, 'EclipseStaging.PolicyBrokerOrder', @ProcessType_Copy, '{"SourceConnection":"PASStaging", "SourceTable":"Eclipse.PolicyBrokerOrder", "SourceSQL":"", "TargetTable":"EclipseStaging.PolicyBrokerOrder", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'EclipseStaging.PolicyBrokerOrder')
  , (@LoadType_Daily, 'EclipseStaging.PolicyMarket', @ProcessType_Copy, '{"SourceConnection":"PASStaging", "SourceTable":"Eclipse.PolicyMarket", "SourceSQL":"", "TargetTable":"EclipseStaging.PolicyMarket", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'EclipseStaging.PolicyMarket')
  , (@LoadType_Daily, 'EclipseStaging.PoolMember', @ProcessType_Copy, '{"SourceConnection":"PASStaging", "SourceTable":"Eclipse.PoolMember", "SourceSQL":"", "TargetTable":"EclipseStaging.PoolMember", "TruncateTargetTable": true, "TargetConnection":"PlacementStore" ,"AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate"}', 'EclipseStaging.PoolMember')
  , (@LoadType_Daily, 'EclipseStaging.Role', @ProcessType_Copy, '{"SourceConnection":"PASStaging", "SourceTable":"Eclipse.Role", "SourceSQL":"", "TargetTable":"EclipseStaging.Role", "TargetConnection":"PlacementStore" ,"AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate"}', 'EclipseStaging.Role')
  , (@LoadType_Daily, 'EclipseStaging.UWGroupVersion', @ProcessType_Copy, '{"SourceConnection":"PASStaging", "SourceTable":"Eclipse.UWGroupVersion", "SourceSQL":"", "TargetTable":"EclipseStaging.UWGroupVersion", "TruncateTargetTable": true, "TargetConnection":"PlacementStore" ,"AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate"}', 'EclipseStaging.UWGroupVersion')
  , (@LoadType_Daily, 'EPICStaging.MergeLineAgencyDefinedIntoPolicySectionAttribute', @ProcessType_SP, '{"StoredProcedure":"EPICStaging.MergeLineAgencyDefinedIntoPolicySectionAttribute", "TargetConnection":"PlacementStore"}', 'dbo.PolicySectionAttribute')
  , (@LoadType_Daily, 'Export to Data Lake - Broking Team', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT TeamId, DataSourceInstanceId, TeamName, ParentId, ServicingPlatformId, LevelNum, OrgNode = OrgNode.ToString(), FullPath, OrgLevel1, OrgLevel2, OrgLevel3, OrgLevel4, OrgLevel5, OrgLevel6, ETLCreatedDate, ETLUpdatedDate, SourceUpdatedDate, IsDeprecated FROM ods.vw_ref_Team", "TargetConnection":"CRBDataLake", "TargetFile":"BrokingTeam.snappy.parquet", "CompressionType":"Snappy", "Container":"placement-broking", "Path":"/placement-store[env]/raw/BrokingTeam/"}', 'BrokingTeam.snappy.parquet')
  , (@LoadType_Daily, 'Export to Data Lake - CRM Placements', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM APIV1.CRMPlacements", "TargetConnection":"CRBDataLake", "TargetFile":"CRMPlacements.snappy.parquet", "CompressionType":"Snappy", "Container":"placement-broking", "Path":"/placement-store[env]/raw/CRMPlacements/"}', 'CRMPlacements.snappy.parquet')
  , (@LoadType_Daily, 'Export to Data Lake - Service Hub Tasks', @ProcessType_Copy, '{"SourceConnection":"ServiceHubMI", "SourceSQL":"SELECT t.TaskID, tp.PartyID, tp.PartyKey, PartyName = tp.Party, tp.PartyRoleDescription, t.TaskSourceType, team.BusinessContextName, TeamName = team.CollectionName, t.TaskCategory, t.TaskTemplateName, t.TaskCreatedUTCDate, t.DueDate, t.CompletedUTCDate, ts.TaskStatus, w.WorkerName, w.UserPrincipalName, b.UserPrincipalName AS BrokerUPN, CONCAT (b.FirstName, char(32), b.LastName) AS BrokerName FROM fact.Task t LEFT JOIN fact.TaskParty tp ON tp.Task_sk = t.Task_sk AND tp.IsDeleted = 0 INNER JOIN dim.Team team ON t.CollectionID = team.CollectionID INNER JOIN dim.TaskStatus ts ON ts.TaskStatusID = t.TaskStatusID AND ISNULL(ts.TaskStatusReasonID, -1) = ISNULL(t.TaskStatusReasonID, -1) AND ts.IsDeleted = 0 LEFT JOIN dim.WillisWorker w ON w.WorkerId = t.WorkerID LEFT JOIN dim.Broker b ON b.Broker_sk = t.Broker_sk", "TargetConnection":"CRBDataLake", "TargetFile":"TasksData.snappy.parquet", "CompressionType":"Snappy" , "Container":"policy-administration-servicing", "Path":"/servicehub[env]/raw/"}', 'TasksData.snappy.parquet')
  , (@LoadType_Daily, 'Export to Data Lake - Servicing Role', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT psr.PlacementServicingRoleId, psr.DataSourceInstanceId, psr.PlacementId, sr.ServicingRole, w.WorkerKey, w.UserPrincipalName, w.GivenName, w.Surname, w.ActiveDirectoryId, w.GlobalWorkerId FROM ods.vw_ps_PlacementServicingRole psr INNER JOIN ods.vw_ps_Placement pl ON pl.PlacementId = psr.PlacementId INNER JOIN ods.vw_ref_ServicingRole sr ON sr.ServicingRoleId = psr.ServicingRoleId INNER JOIN ods.vw_ref_Worker w ON w.WorkerId = psr.WorkerId WHERE psr.IsDeleted = 0", "TargetConnection":"CRBDataLake", "TargetFile":"PlacementServicingRole.snappy.parquet", "CompressionType":"Snappy", "Container":"placement-broking", "Path":"/placement-store[env]/PlacementServicingRole/"}', 'PlacementServicingRole.snappy.parquet')
  , (@LoadType_Daily, 'FMATemp ForceReload to BP', @ProcessType_SP, '{"StoredProcedure" : "Support.SetBPPolicyReload", "TargetConnection":"PlacementStore", "Parameters":""}', 'Four FMATemp tables.')
  , (@LoadType_Daily, 'FMATemp.StagePartyCheckResult', @ProcessType_SP, '{"StoredProcedure":"FMATemp.StagePartyCheckResult", "TargetConnection":"PlacementStore"}', 'FMATemp.staging_vw_PartyCheckResult')
  , (@LoadType_Daily, 'PACTStaging.MergeClientUnderwriterPremiumIntoProgrammeStore', @ProcessType_SP, '{"StoredProcedure":"PACTStaging.MergeClientUnderwriterPremiumIntoProgrammeStore", "TargetConnection":"PlacementStore"}', 'dbo.ClientUnderwriterPremium')
  , (@LoadType_Daily, 'PACTStaging.MergeFacilityPoliciesIntoProgrammeStore', @ProcessType_SP, '{"StoredProcedure":"PACTStaging.MergeFacilityPoliciesIntoProgrammeStore", "TargetConnection":"PlacementStore"}', 'dbo.Policy')
  , (@LoadType_Daily, 'PACTStaging.MergeIntoPartyAddress', @ProcessType_SP, '{"StoredProcedure":"PACTStaging.MergeIntoPartyAddress", "TargetConnection":"PlacementStore"}', 'dbo.PartyAddress')
  , (@LoadType_Daily, 'PACTStaging.MergePartyAttributestoParty', @ProcessType_SP, '{"StoredProcedure":"PACTStaging.MergePartyAttributestoParty", "TargetConnection":"PlacementStore", "ResultType":"DummyRow"}', 'dbo.Party')
  , (@LoadType_Daily, 'PACTStaging.MergePartyIntoProgrammeStore', @ProcessType_SP, '{"StoredProcedure":"PACTStaging.MergePartyIntoProgrammeStore", "TargetConnection":"PlacementStore"}', 'dbo.Party')
  , (@LoadType_Daily, 'PACTStaging.MergePoliciesIntoProgrammeStore', @ProcessType_SP, '{"StoredProcedure":"PACTStaging.MergePoliciesIntoProgrammeStore", "TargetConnection":"PlacementStore"}', 'dbo.Policy')
  , (@LoadType_Daily, 'PACTStaging.MergePolicyAttributeIntoProgrammeStore', @ProcessType_SP, '{"StoredProcedure":"PACTStaging.MergePolicyAttributeIntoProgrammeStore", "TargetConnection":"PlacementStore"}', 'dbo.PolicyAttribute')
  , (@LoadType_Daily, 'PACTStaging.MergePolicyCarrierDetailsIntoProgrammeStore', @ProcessType_SP, '{"StoredProcedure":"PACTStaging.MergePolicyCarrierDetailsIntoProgrammeStore", "TargetConnection":"PlacementStore"}', 'dbo.PolicyCarrierDetails')
  , (@LoadType_Daily, 'PACTStaging.MergePolicyCarriersIntoProgrammeStore', @ProcessType_SP, '{"StoredProcedure":"PACTStaging.MergePolicyCarriersIntoProgrammeStore", "TargetConnection":"PlacementStore"}', 'dbo.PolicyPartyRelationship')
  , (@LoadType_Daily, 'PACTStaging.MergePolicyClientsIntoProgrammeStore', @ProcessType_SP, '{"StoredProcedure":"PACTStaging.MergePolicyClientsIntoProgrammeStore", "TargetConnection":"PlacementStore"}', 'dbo.PolicyPartyRelationship')
  , (@LoadType_Daily, 'PACTStaging.MergePolicyMarketIntoProgrammeStore', @ProcessType_SP, '{"StoredProcedure":"PACTStaging.MergePolicyMarketIntoProgrammeStore", "TargetConnection":"PlacementStore"}', 'dbo.PolicyMarket')
  , (@LoadType_Daily, 'PACTStaging.MergePolicyOrgsIntoProgrammeStore', @ProcessType_SP, '{"StoredProcedure":"PACTStaging.MergePolicyOrgsIntoProgrammeStore", "TargetConnection":"PlacementStore"}', 'dbo.PolicyOrganisation')
  , (@LoadType_Daily, 'PACTStaging.MergePolicySectionProductsIntoProgrammeStore', @ProcessType_SP, '{"StoredProcedure":"PACTStaging.MergePolicySectionProductsIntoProgrammeStore", "TargetConnection":"PlacementStore"}', 'dbo.PolicySectionProduct')
  , (@LoadType_Daily, 'PACTStaging.MergePolicySectionsIntoProgrammeStore', @ProcessType_SP, '{"StoredProcedure":"PACTStaging.MergePolicySectionsIntoProgrammeStore", "TargetConnection":"PlacementStore"}', 'dbo.PolicySection')
  , (@LoadType_Daily, 'PACTStaging.MergePolicyWorkersIntoProgrammeStore', @ProcessType_SP, '{"StoredProcedure":"PACTStaging.MergePolicyWorkersIntoProgrammeStore", "TargetConnection":"PlacementStore"}', 'dbo.PolicyWorker')
  , (@LoadType_Daily, 'PACTStaging.rpt_vwAttribute', @ProcessType_Copy, '{"SourceConnection":"PACTAzure", "SourceTable":"rpt.vwAttribute", "SourceSQL":"SELECT A.AttributeId, A.Object, A.ObjectId, A.ObjectType, A.Value, A.ETLCreatedDate, A.ETLUpdatedDate, A.IsDeleted, CAST(A.SegmentCode AS NCHAR(6)) AS SegmentCode FROM rpt.vwAttribute AS A INNER JOIN (SELECT LEFT(value, CHARINDEX(''^'', value) - 1) AS Object, SUBSTRING(value, CHARINDEX(''^'', value) + 1, LEN(value)) AS ObjectType FROM string_split(@OptionalValue1, ''|'') WHERE value <> '''') AS CA ON CA.Object = A.Object AND CA.ObjectType = A.ObjectType WHERE A.AttributeId IS NOT NULL AND A.ObjectId IS NOT NULL AND A.ETLUpdatedDate >= @ETLUpdatedDate;", "TargetTable":"PACTStaging.rpt_vwAttribute", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate", "DontCheckForDataSourceInstanceId":false, "OptionalValue1LookupSql":"SELECT Value = STRING_AGG(CONCAT([Object], ''^'', ObjectType), ''|'') FROM PactConfig.rptAttribute WHERE [Enabled] = 1 AND IsDeleted = 0;", "OptionalValue1LookupConnection":"PlacementStore"}', 'PACTStaging.rpt_vwAttribute')
  , (@LoadType_Daily, 'PACTStaging.rpt_vwClientUnderwriterPremium', @ProcessType_Copy, '{"SourceConnection":"IMReporting", "SourceTable":"rpt.vwClientUnderwriterPremium", "SourceSQL":"SELECT [PolicyID] /* ,[PolicyReference] */ /* ,[InsuredId] */ /* ,[ReinsuredId] */ /* ,[UW] */  ,[UWId]  /* ,[UWRole] */ /* ,[UWRef] */         ,[UWCompCode]      ,[GlobalProductId]   /* ,[Lead] */ ,CAST(COALESCE([LineSlipRef],[ParentReference]) AS NVARCHAR(32)) AS LineSlipRef   /* ,[PlacementType]  ,SUM([AdditionalCommissionUSD]) AS AdditionalCommissionUSD    ,SUM([AdjustmentsUSD]) AS AdjustmentsUSD */ ,SUM([ClaimUSD]) AS ClaimUSD   /* ,SUM([ClientAdditionalCommissionUSD]) AS ClientAdditionalCommissionUSD */ /* ,SUM([ClientCommissionUSD]) AS ClientCommissionUSD */ /* ,SUM([ClientDeductionUSD]) AS ClientDeductionUSD */ /* ,SUM([ClientFeeUSD]) AS ClientFeeUSD */ /* ,SUM([ClientGrossPremiumUSD]) AS ClientGrossPremiumUSD */ /* ,SUM([ClientMDIUSD]) AS ClientMDIUSD */ /* ,SUM([ClientNetPremiumUSD]) AS ClientNetPremiumUSD */ /* ,SUM([ContingentCommissionUSD]) AS ContingentCommissionUSD */ /* ,SUM([CostOtherExpenseUSD]) AS CostOtherExpenseUSD */ /* ,SUM([CostCompanyExpenseUSD]) AS CostCompanyExpenseUSD */ /* ,SUM([DeductionUSD]) AS DeductionUSD */ /* ,SUM([ExpensesUSD]) AS ExpensesUSD */ /* ,SUM([FeeUSD]) AS FeeUSD */ /* ,SUM([GrossBrokerageUSD]) AS GrossBrokerageUSD */ ,SUM([GrossPremiumUSD]) AS GrossPremiumUSD /* ,SUM([NetClientPremiumUSD]) AS NetClientPremiumUSD */ ,SUM([NetPaymentFromClientUSD]) AS NetPaymentFromClientUSD ,SUM([NetPremiumtoUWUSD]) AS NetPremiumtoUWUSD /* ,SUM([NetUWPremiumUSD]) AS NetUWPremiumUSD */ /* ,SUM([NetUWTotDedsUSD]) AS NetUWTotDedsUSD */ ,SUM([OutstandingClaimUSD]) AS OutstandingClaimUSD /* ,SUM([OutstandingFeeUSD]) AS OutstandingFeeUSD */ /* ,SUM([TPAdditionalCommissionUSD]) AS TPAdditionalCommissionUSD */ /* ,SUM([TPCommissionUSD]) AS TPCommissionUSD */ /* ,SUM([TPDeductionUSD]) AS TPDeductionUSD */ /* ,SUM([TPFeeUSD]) AS TPFeeUSD */ /* ,SUM([TPMDIUSD]) AS TPMDIUSD */ /* ,SUM([TreatyStatementUSD]) AS TreatyStatementUSD */ /* ,SUM([UWAdditionalCommissionUSD]) AS UWAdditionalCommissionUSD */ /* ,SUM([UWDeductionUSD]) AS UWDeductionUSD */ /* ,SUM([UWFeeUSD]) AS UWFeeUSD */ /* ,SUM([UWGrossPremiumUSD]) AS UWGrossPremiumUSD */ /* ,SUM([UWMDIUSD]) AS UWMDIUSD */ /* ,SUM([UWNetPremiumUSD]) AS UWNetPremiumUSD */ /* ,SUM([UWNetTotDedsUSD]) AS UWNetTotDedsUSD */ /* ,SUM([WriteOffUSD]) AS WriteOffUSD */ /* ,SUM([WTWNetRevenueUSD]) AS WTWNetRevenueUSD */ /* ,SUM([WTWGrossRevenueUSD]) AS WTWGrossRevenueUSD */ FROM [rpt].[vwClientUnderwriterPremium] WHERE SegmentCode = ''CRB'' AND InceptionDate >= ''2019-01-01'' GROUP BY [SourceId],[PolicyId]   /*,[PolicyReference] */ /* ,[InsuredId] */ /* ,[ReinsuredId]   ,[UW] */ ,[UWId] /* ,[UWRole] */ /* ,[UWDescName] */ /* ,[UWRef] */ ,[UWCompCode] ,[GlobalProductId] /* ,[Lead] */ ,COALESCE([LineSlipRef],[ParentReference]) /* ,[PlacementType] */  ;", "TargetTable":"PACTStaging.rpt_vwClientUnderwriterPremium", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"  , "DontCheckForDataSourceInstanceId":false}', 'PACTStaging.rpt_vwClientUnderwriterPremium')
  , (@LoadType_Daily, 'PACTStaging.rpt_vwParty', @ProcessType_Copy, '{"SourceConnection":"PACTAzure", "SourceTable":"rpt.vwParty", "SourceSQL":"SELECT P.PartyId ,P.DataSourceInstanceId ,P.PartyKey ,P.BusinessKey ,P.BusinessOwnerOrganisationId ,P.CompCode ,P.DUNSNumber ,P.Email ,CASE WHEN P.GeographyId = -1 AND a.GeographyId IS NOT NULL THEN a.GeographyId ELSE P.GeographyId END AS GeographyId ,IsIndividual ,IsObfuscated ,ParentId ,Party ,P.PhoneNumber ,P.SourceLastUpdateDate ,P.SourceQueryId ,P.ETLCreatedDate ,P.ETLUpdatedDate ,P.IsDeleted ,P.SourcePartyId ,P.GlobalPartyId, P.SourceQuery, P.IsActive FROM rpt.vwParty AS P LEFT JOIN rpt.vwAddress AS a ON a.PartyId = P.PartyId AND a.AddressType = ''STREET'' WHERE P.DataSourceInstanceId IN (SELECT value AS Id FROM string_split(@DataSourceInstanceId, '','')) /* AND P.ETLUpdatedDate >= @ETLUpdatedDate */;", "TargetTable":"PACTStaging.rpt_vwParty", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "DontCheckForDataSourceInstanceId":false, "DataSourceInstanceIdLookupSql":"SELECT Value = ADF.GetDataSourceInstanceList();", "DataSourceInstanceIdLookupConnection":"PlacementStore"}', 'PACTStaging.rpt_vwParty')
  , (@LoadType_Daily, 'PACTStaging.rpt_vwPartyAddress', @ProcessType_Copy, '{"SourceConnection":"PACTAzure", "SourceTable":"rpt.vwAddress", "SourceSQL":"SELECT A.AddressId, A.DataSourceInstanceId, A.AddressKey, A.AddressLine1, A.AddressLine2, A.AddressLine3, A.AddressType, A.City, A.Country, A.Email, A.GeographyId, A.IsPrimaryAddress, A.PartyId, A.PhoneNumber, A.PostCode, A.StateOrProvince, A.SourceQueryId, A.ETLCreatedDate, A.ETLUpdatedDate, A.IsDeleted FROM rpt.vwAddress AS A INNER JOIN rpt.vwParty AS WO ON WO.PartyId = A.PartyId WHERE A.DataSourceInstanceId IN (SELECT value AS Id FROM string_split(@DataSourceInstanceId, '','')) AND A.ETLUpdatedDate > @ETLUpdatedDate;", "TargetTable":"PACTStaging.rpt_vwPartyAddress", "TruncateTargetTable": true, "AlwaysFullLoad": false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate", "TargetConnection":"PlacementStore" , "DontCheckForDataSourceInstanceId":false, "DataSourceInstanceIdLookupSql":"SELECT Value = ADF.GetDataSourceInstanceList();", "DataSourceInstanceIdLookupConnection":"PlacementStore"}', 'PACTStaging.rpt_vwPartyAddress')
  , (@LoadType_Daily, 'PACTStaging.rpt_vwPolicy', @ProcessType_Copy, '{"SourceConnection":"PACTAzure", "SourceTable":"rpt.vwPolicy", "SourceSQL":"", "TargetTable":"PACTStaging.rpt_vwPolicy", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate" , "DontCheckForDataSourceInstanceId":false, "DataSourceInstanceIdLookupSql":"SELECT Value = ADF.GetDataSourceInstanceList();", "DataSourceInstanceIdLookupConnection":"PlacementStore"}', 'PACTStaging.rpt_vwPolicy')
  , (@LoadType_Daily, 'PACTStaging.rpt_vwPolicyAttribute', @ProcessType_Copy, '{"SourceConnection":"PAS", "SourceTable":"rpt.vwPolicyAttribute", "SourceSQL":"", "TruncateTargetTable": true, "TargetTable":"PACTStaging.rpt_vwPolicyAttribute", "TargetConnection":"PlacementStore", "DontCheckForDataSourceInstanceId":true }', 'PACTStaging.rpt_vwPolicyAttribute')
  , (@LoadType_Daily, 'PACTStaging.rpt_vwPolicyOrganisationRole', @ProcessType_Copy, '{"SourceConnection":"PAS", "SourceTable":"rpt.vwPolicyOrganisationRole", "SourceSQL":"", "TargetTable":"PACTStaging.rpt_vwPolicyOrganisationRole", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate" , "DontCheckForDataSourceInstanceId":false, "DataSourceInstanceIdLookupSql":"SELECT Value = ADF.GetDataSourceInstanceList();", "DataSourceInstanceIdLookupConnection":"PlacementStore"}', 'PACTStaging.rpt_vwPolicyOrganisationRole')
  , (@LoadType_Daily, 'PACTStaging.rpt_vwPolicyPartyRole', @ProcessType_Copy, '{"SourceConnection":"PACTAzure", "SourceTable":"rpt.vwPolicyPartyRole", "SourceSQL":"", "TargetTable":"PACTStaging.rpt_vwPolicyPartyRole", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate" , "DontCheckForDataSourceInstanceId":false, "DataSourceInstanceIdLookupSql":"SELECT Value = ADF.GetDataSourceInstanceList();", "DataSourceInstanceIdLookupConnection":"PlacementStore"}', 'PACTStaging.rpt_vwPolicyPartyRole')
  , (@LoadType_Daily, 'PACTStaging.rpt_vwPolicySection', @ProcessType_Copy, '{"SourceConnection":"PACTAzure", "SourceTable":"rpt.vwPolicySection", "SourceSQL":"", "TargetTable":"PACTStaging.rpt_vwPolicySection", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate" , "DontCheckForDataSourceInstanceId":false, "DataSourceInstanceIdLookupSql":"SELECT Value = ADF.GetDataSourceInstanceList();", "DataSourceInstanceIdLookupConnection":"PlacementStore"}', 'PACTStaging.rpt_vwPolicySection')
  , (@LoadType_Daily, 'PACTStaging.rpt_vwPolicySectionProduct', @ProcessType_Copy, '{"SourceConnection":"PACTAzure", "SourceTable":"rpt.vwPolicySectionProduct", "SourceSQL":"", "TargetTable":"PACTStaging.rpt_vwPolicySectionProduct", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate" , "DontCheckForDataSourceInstanceId":false, "DataSourceInstanceIdLookupSql":"SELECT Value = ADF.GetDataSourceInstanceList();", "DataSourceInstanceIdLookupConnection":"PlacementStore"}', 'PACTStaging.rpt_vwPolicySectionProduct')
  , (@LoadType_Daily, 'PACTStaging.rpt_vwPolicyServicingRole', @ProcessType_Copy, '{"SourceConnection":"PACTAzure", "SourceTable":"rpt.vwPolicyServicingRole", "SourceSQL":"", "TargetTable":"PACTStaging.rpt_vwPolicyServicingRole", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate" , "DontCheckForDataSourceInstanceId":false, "DataSourceInstanceIdLookupSql":"SELECT Value = ADF.GetDataSourceInstanceList();", "DataSourceInstanceIdLookupConnection":"PlacementStore"}', 'PACTStaging.rpt_vwPolicyServicingRole')
  , (@LoadType_Daily, 'PACTStaging.UpdatePolicyTacitConsecutiveCount', @ProcessType_SP, '{"StoredProcedure":"PACTStaging.UpdatePolicyTacitConsecutiveCount", "TargetConnection":"PlacementStore"}', 'dbo.Policy')
  , (@LoadType_Daily, 'PACTStaging.UpdatePolicyWithAttributes', @ProcessType_SP, '{"StoredProcedure":"PACTStaging.UpdatePolicyWithAttributes", "TargetConnection":"PlacementStore"}', 'dbo.Policy')
  , (@LoadType_Daily, 'PACTStaging.UpdatePolicyWithCUP', @ProcessType_SP, '{"StoredProcedure":"PACTStaging.UpdatePolicyWithCUP", "TargetConnection":"PlacementStore"}', 'dbo.Policy')
  , (@LoadType_Daily, 'PACTStaging.UpdatePolicyWithEclipseAuthorizedDatePolicyAttribute', @ProcessType_SP, '{"StoredProcedure":"PACTStaging.UpdatePolicyWithEclipseAuthorizedDatePolicyAttribute", "TargetConnection":"PlacementStore"}', 'dbo.Policy')
  , (@LoadType_Daily, 'PAS.Load_APIv1_OrganisationHierarchyTable', @ProcessType_SP, '{"StoredProcedure":"PAS.Load_APIv1_OrganisationHierarchyTable", "TargetConnection":"PlacementStore"}', 'APIv1.OrganisationHierarchyTable')
  , (@LoadType_Daily, 'PAS.Load_dbo_Product', @ProcessType_SP, '{"StoredProcedure":"PAS.Load_dbo_Product", "TargetConnection":"PlacementStore"}', 'dbo.Product')
  , (@LoadType_Daily, 'PAS.Load_PS_Organisation', @ProcessType_SP, '{"StoredProcedure":"PAS.Load_PS_Organisation", "TargetConnection":"PlacementStore"}', 'PS.Organisation')
  , (@LoadType_Daily, 'PAS.Load_PS_OrganisationAddress', @ProcessType_SP, '{"StoredProcedure":"PAS.Load_PS_OrganisationAddress", "TargetConnection":"PlacementStore"}', 'PS.OrganisationAddress')
  , (@LoadType_Daily, 'PAS.Load_PS_OrganisationRelationship', @ProcessType_SP, '{"StoredProcedure":"PAS.Load_PS_OrganisationRelationship", "TargetConnection":"PlacementStore"}', 'PS.OrganisationRelationship')
  , (@LoadType_Daily, 'PAS.Load_PS_OrganisationRole', @ProcessType_SP, '{"StoredProcedure":"PAS.Load_PS_OrganisationRole", "TargetConnection":"PlacementStore"}', 'PS.OrganisationRole')
  , (@LoadType_Daily, 'PAS.Load_PS_Worker', @ProcessType_SP, '{"StoredProcedure":"PAS.Load_PS_Worker", "TargetConnection":"PlacementStore"}', 'PS.Worker')
  , (@LoadType_Daily, 'PASStaging.Load_dbo_Carrier', @ProcessType_SP, '{"StoredProcedure":"PASStaging.Load_dbo_Carrier", "TargetConnection":"PlacementStore"}', 'dbo.Carrier')
  , (@LoadType_Daily, 'PASStaging.Load_dbo_Carrier_GlobalParent', @ProcessType_SP, '{"StoredProcedure":"PASStaging.Load_dbo_Carrier_GlobalParent", "TargetConnection":"PlacementStore"}', 'dbo.Carrier')
  , (@LoadType_Daily, 'PASStaging.Load_dbo_Carrier_OperatingCompany', @ProcessType_SP, '{"StoredProcedure":"PASStaging.Load_dbo_Carrier_OperatingCompany", "TargetConnection":"PlacementStore"}', 'dbo.Carrier')
  , (@LoadType_Daily, 'PASStaging.Load_PAS_Currency', @ProcessType_SP, '{"StoredProcedure":"PASStaging.Load_PAS_Currency", "TargetConnection":"PlacementStore"}', 'PAS.Currency')
  , (@LoadType_Daily, 'PASStaging.Load_PAS_FinancialGeography', @ProcessType_SP, '{"StoredProcedure":"PASStaging.Load_PAS_FinancialGeography", "TargetConnection":"PlacementStore"}', 'PAS.FinancialGeography')
  , (@LoadType_Daily, 'PASStaging.Load_PAS_FinancialSegment', @ProcessType_SP, '{"StoredProcedure":"PASStaging.Load_PAS_FinancialSegment", "TargetConnection":"PlacementStore"}', 'PAS.FinancialSegment')
  , (@LoadType_Daily, 'PASStaging.Load_PAS_Geography', @ProcessType_SP, '{"StoredProcedure":"PASStaging.Load_PAS_Geography", "TargetConnection":"PlacementStore"}', 'PAS.Geography')
  , (@LoadType_Daily, 'PASStaging.Load_PAS_InsuranceType', @ProcessType_SP, '{"StoredProcedure":"PASStaging.Load_PAS_InsuranceType", "TargetConnection":"PlacementStore"}', 'PAS.InsuranceType')
  , (@LoadType_Daily, 'PASStaging.Load_PAS_LegalEntity', @ProcessType_SP, '{"StoredProcedure":"PASStaging.Load_PAS_LegalEntity", "TargetConnection":"PlacementStore"}', 'PAS.LegalEntity')
  , (@LoadType_Daily, 'PASStaging.Load_PAS_OpportunityType', @ProcessType_SP, '{"StoredProcedure":"PASStaging.Load_PAS_OpportunityType", "TargetConnection":"PlacementStore"}', 'PAS.OpportunityType')
  , (@LoadType_Daily, 'PASStaging.Load_PAS_Organisation', @ProcessType_SP, '{"StoredProcedure":"PASStaging.Load_PAS_Organisation", "TargetConnection":"PlacementStore"}', 'PAS.Organisation')
  , (@LoadType_Daily, 'PASStaging.Load_PAS_OrganisationAddress', @ProcessType_SP, '{"StoredProcedure":"PASStaging.Load_PAS_OrganisationAddress", "TargetConnection":"PlacementStore"}', 'PAS.OrganisationAddress')
  , (@LoadType_Daily, 'PASStaging.Load_PAS_OrganisationAttribute', @ProcessType_SP, '{"StoredProcedure":"PASStaging.Load_PAS_OrganisationAttribute", "TargetConnection":"PlacementStore"}', 'PAS.OrganisationAttribute')
  , (@LoadType_Daily, 'PASStaging.Load_PAS_OrganisationRole', @ProcessType_SP, '{"StoredProcedure":"PASStaging.Load_PAS_OrganisationRole", "TargetConnection":"PlacementStore"}', 'PAS.OrganisationRole')
  , (@LoadType_Daily, 'PASStaging.Load_PAS_Party', @ProcessType_SP, '{"StoredProcedure":"PASStaging.Load_PAS_Party", "TargetConnection":"PlacementStore"}', 'PAS.Party')
  , (@LoadType_Daily, 'PASStaging.Load_PAS_PartyAddress', @ProcessType_SP, '{"StoredProcedure":"PASStaging.Load_PAS_PartyAddress", "TargetConnection":"PlacementStore"}', 'PAS.PartyAddress')
  , (@LoadType_Daily, 'PASStaging.Load_PAS_PartyAttribute', @ProcessType_SP, '{"StoredProcedure":"PASStaging.Load_PAS_PartyAttribute", "TargetConnection":"PlacementStore"}', 'PAS.PartyAttribute')
  , (@LoadType_Daily, 'PASStaging.Load_PAS_PartyRole', @ProcessType_SP, '{"StoredProcedure":"PASStaging.Load_PAS_PartyRole", "TargetConnection":"PlacementStore"}', 'PAS.PartyRole')
  , (@LoadType_Daily, 'PASStaging.Load_PAS_Policy', @ProcessType_SP, '{"StoredProcedure":"PASStaging.Load_PAS_Policy", "TargetConnection":"PlacementStore"}', 'PAS.Policy')
  , (@LoadType_Daily, 'PASStaging.Load_PAS_PolicyAttribute', @ProcessType_SP, '{"StoredProcedure":"PASStaging.Load_PAS_PolicyAttribute", "TargetConnection":"PlacementStore"}', 'PAS.PolicyAttribute')
  , (@LoadType_Daily, 'PASStaging.Load_PAS_PolicyOrganisationRole', @ProcessType_SP, '{"StoredProcedure":"PASStaging.Load_PAS_PolicyOrganisationRole", "TargetConnection":"PlacementStore"}', 'PAS.PolicyOrganisationRole')
  , (@LoadType_Daily, 'PASStaging.Load_PAS_PolicyPartyRole', @ProcessType_SP, '{"StoredProcedure":"PASStaging.Load_PAS_PolicyPartyRole", "TargetConnection":"PlacementStore"}', 'PAS.PolicyPartyRole')
  , (@LoadType_Daily, 'PASStaging.Load_PAS_PolicySection', @ProcessType_SP, '{"StoredProcedure":"PASStaging.Load_PAS_PolicySection", "TargetConnection":"PlacementStore"}', 'PAS.PolicySection')
  , (@LoadType_Daily, 'PASStaging.Load_PAS_PolicySectionAttribute', @ProcessType_SP, '{"StoredProcedure":"PASStaging.Load_PAS_PolicySectionAttribute", "TargetConnection":"PlacementStore"}', 'PAS.PolicySectionAttribute')
  , (@LoadType_Daily, 'PASStaging.Load_PAS_PolicySectionProduct', @ProcessType_SP, '{"StoredProcedure":"PASStaging.Load_PAS_PolicySectionProduct", "TargetConnection":"PlacementStore"}', 'PAS.PolicySectionProduct')
  , (@LoadType_Daily, 'PASStaging.Load_PAS_PolicySectionStatus', @ProcessType_SP, '{"StoredProcedure":"PASStaging.Load_PAS_PolicySectionStatus", "TargetConnection":"PlacementStore"}', 'PAS.PolicySectionStatus')
  , (@LoadType_Daily, 'PASStaging.Load_PAS_PolicyStatus', @ProcessType_SP, '{"StoredProcedure":"PASStaging.Load_PAS_PolicyStatus", "TargetConnection":"PlacementStore"}', 'PAS.PolicyStatus')
  , (@LoadType_Daily, 'PASStaging.Load_PAS_PolicyType', @ProcessType_SP, '{"StoredProcedure":"PASStaging.Load_PAS_PolicyType", "TargetConnection":"PlacementStore"}', 'PAS.PolicyType')
  , (@LoadType_Daily, 'PASStaging.Load_PAS_PolicyWorkerRole', @ProcessType_SP, '{"StoredProcedure":"PASStaging.Load_PAS_PolicyWorkerRole", "TargetConnection":"PlacementStore"}', 'PAS.PolicyWorkerRole')
  , (@LoadType_Daily, 'PASStaging.Load_PAS_Product', @ProcessType_SP, '{"StoredProcedure":"PASStaging.Load_PAS_Product", "TargetConnection":"PlacementStore"}', 'PAS.Product')
  , (@LoadType_Daily, 'PASStaging.Load_PAS_ProductAttribute', @ProcessType_SP, '{"StoredProcedure":"PASStaging.Load_PAS_ProductAttribute", "TargetConnection":"PlacementStore"}', 'PAS.ProductAttribute')
  , (@LoadType_Daily, 'PASStaging.Load_PAS_RefInsuranceType', @ProcessType_SP, '{"StoredProcedure":"PASStaging.Load_PAS_RefInsuranceType", "TargetConnection":"PlacementStore"}', 'PAS.RefInsuranceType')
  , (@LoadType_Daily, 'PASStaging.Load_PAS_RefPolicyStatus', @ProcessType_SP, '{"StoredProcedure":"PASStaging.Load_PAS_RefPolicyStatus", "TargetConnection":"PlacementStore"}', 'PAS.RefPolicyStatus')
  , (@LoadType_Daily, 'PASStaging.Load_PAS_ServicingRole', @ProcessType_SP, '{"StoredProcedure":"PASStaging.Load_PAS_ServicingRole", "TargetConnection":"PlacementStore"}', 'PAS.ServicingRole')
  , (@LoadType_Daily, 'PASStaging.Load_PAS_TransactionSummaryUSD', @ProcessType_SP, '{"StoredProcedure":"PASStaging.Load_PAS_TransactionSummaryUSD", "TargetConnection":"PlacementStore"}', 'PASStaging.TransactionSummaryUSD')
  , (@LoadType_Daily, 'PASStaging.Load_PAS_Worker', @ProcessType_SP, '{"StoredProcedure":"PASStaging.Load_PAS_Worker", "TargetConnection":"PlacementStore"}', 'PAS.Worker')
  , (@LoadType_Daily, 'PASStaging.Load_ref_FinancialGeography', @ProcessType_SP, '{"StoredProcedure":"PASStaging.Load_ref_FinancialGeography", "TargetConnection":"PlacementStore"}', 'ref.FinancialGeography')
  , (@LoadType_Daily, 'PASStaging.Load_ref_FinancialSegment', @ProcessType_SP, '{"StoredProcedure":"PASStaging.Load_ref_FinancialSegment", "TargetConnection":"PlacementStore"}', 'ref.FinancialSegment')
  , (@LoadType_Daily, 'PASStaging.Load_ref_InsuranceType', @ProcessType_SP, '{"StoredProcedure":"PASStaging.Load_ref_InsuranceType", "TargetConnection":"PlacementStore"}', 'ref.InsuranceType')
  , (@LoadType_Daily, 'PASStaging.Load_ref_LegalEntity', @ProcessType_SP, '{"StoredProcedure":"PASStaging.Load_ref_LegalEntity", "TargetConnection":"PlacementStore"}', 'ref.LegalEntity')
  , (@LoadType_Daily, 'PASStaging.Load_ref_OpportunityType', @ProcessType_SP, '{"StoredProcedure":"PASStaging.Load_ref_OpportunityType", "TargetConnection":"PlacementStore"}', 'ref.OpportunityType')
  , (@LoadType_Daily, 'PASStaging.Load_ref_PartyRole', @ProcessType_SP, '{"StoredProcedure":"PASStaging.Load_ref_PartyRole", "TargetConnection":"PlacementStore"}', 'ref.PartyRole')
  , (@LoadType_Daily, 'PASStaging.Load_ref_RefInsuranceType', @ProcessType_SP, '{"StoredProcedure":"PASStaging.Load_ref_RefInsuranceType", "TargetConnection":"PlacementStore"}', 'ref.RefInsuranceType')
  , (@LoadType_Daily, 'PASStaging.rpt_vwCarrierHierarchy', @ProcessType_Copy, '{"SourceConnection":"PAS", "SourceTable":"rpt.vwCarrierHierarchy", "SourceSQL":"", "TargetTable":"PASStaging.rpt_vwCarrierHierarchy", "TruncateTargetTable": true, "AlwaysFullLoad":true, "IncrementalUpdatedDateColumn":"ETLUpdatedDate", "TargetConnection":"PlacementStore", "DontCheckForDataSourceInstanceId":false}', 'PASStaging.rpt_vwCarrierHierarchy')
  , (@LoadType_Daily, 'PASStaging.rpt_vwCurrency', @ProcessType_Copy, '{"SourceConnection":"PAS", "SourceTable":"rpt.vwCurrency", "SourceSQL":"", "TargetTable":"PASStaging.rpt_vwCurrency", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate" , "DontCheckForDataSourceInstanceId":false, "DataSourceInstanceIdLookupSql":"SELECT Value = ADF.GetDataSourceInstanceList();", "DataSourceInstanceIdLookupConnection":"PlacementStore"}', 'PASStaging.rpt_vwCurrency')
  , (@LoadType_Daily, 'PASStaging.rpt_vwFinancialGeography', @ProcessType_Copy, '{"SourceConnection":"PAS", "SourceTable":"rpt.vwFinancialGeography", "SourceSQL":"", "TargetTable":"PASStaging.rpt_vwFinancialGeography", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate" , "DontCheckForDataSourceInstanceId":false, "DataSourceInstanceIdLookupSql":"SELECT Value = ADF.GetDataSourceInstanceList();", "DataSourceInstanceIdLookupConnection":"PlacementStore"}', 'PASStaging.rpt_vwFinancialGeography')
  , (@LoadType_Daily, 'PASStaging.rpt_vwFinancialSegment', @ProcessType_Copy, '{"SourceConnection":"PAS", "SourceTable":"rpt.vwFinancialSegment", "SourceSQL":"", "TargetTable":"PASStaging.rpt_vwFinancialSegment", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate" , "DontCheckForDataSourceInstanceId":false, "DataSourceInstanceIdLookupSql":"SELECT Value = ADF.GetDataSourceInstanceList();", "DataSourceInstanceIdLookupConnection":"PlacementStore"}', 'PASStaging.rpt_vwFinancialSegment')
  , (@LoadType_Daily, 'PASStaging.rpt_vwGeography', @ProcessType_Copy, '{"SourceConnection":"PAS", "SourceTable":"rpt.vwGeography", "SourceSQL":"", "TargetTable":"PASStaging.rpt_vwGeography", "TruncateTargetTable": true, "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate", "TargetConnection":"PlacementStore" , "DontCheckForDataSourceInstanceId":false, "DataSourceInstanceIdLookupSql":"SELECT Value = ADF.GetDataSourceInstanceList();", "DataSourceInstanceIdLookupConnection":"PlacementStore"}', 'PACTStaging.rpt_vwGeography')
  , (@LoadType_Daily, 'PASStaging.rpt_vwInsuranceType', @ProcessType_Copy, '{"SourceConnection":"PAS", "SourceTable":"rpt.vwInsuranceType", "SourceSQL":"", "TargetTable":"PASStaging.rpt_vwInsuranceType", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate" , "DontCheckForDataSourceInstanceId":false, "DataSourceInstanceIdLookupSql":"SELECT Value = ADF.GetDataSourceInstanceList();", "DataSourceInstanceIdLookupConnection":"PlacementStore"}', 'PASStaging.rpt_vwInsuranceType')
  , (@LoadType_Daily, 'PASStaging.rpt_vwLegalEntity', @ProcessType_Copy, '{"SourceConnection":"PAS", "SourceTable":"rpt.vwLegalEntity", "SourceSQL":"", "TargetTable":"PASStaging.rpt_vwLegalEntity", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate" , "DontCheckForDataSourceInstanceId":false, "DataSourceInstanceIdLookupSql":"SELECT Value = ADF.GetDataSourceInstanceList();", "DataSourceInstanceIdLookupConnection":"PlacementStore"}', 'PASStaging.rpt_vwLegalEntity')
  , (@LoadType_Daily, 'PASStaging.rpt_vwOpportunityType', @ProcessType_Copy, '{"SourceConnection":"PAS", "SourceTable":"rpt.vwOpportunityType", "SourceSQL":"", "TargetTable":"PASStaging.rpt_vwOpportunityType", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate" , "DontCheckForDataSourceInstanceId":false, "DataSourceInstanceIdLookupSql":"SELECT Value = ADF.GetDataSourceInstanceList();", "DataSourceInstanceIdLookupConnection":"PlacementStore" }', 'PASStaging.rpt_vwOpportunityType')
  , (@LoadType_Daily, 'PASStaging.rpt_vwOrganisation', @ProcessType_Copy, '{"SourceConnection":"PAS", "SourceTable":"rpt.vwOrganisation", "SourceSQL":"", "TargetTable":"PASStaging.rpt_vwOrganisation", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate" , "DontCheckForDataSourceInstanceId":false, "DataSourceInstanceIdLookupSql":"SELECT Value = ADF.GetDataSourceInstanceList();", "DataSourceInstanceIdLookupConnection":"PlacementStore"}', 'PASStaging.rpt_vwOrganisation')
  , (@LoadType_Daily, 'PASStaging.rpt_vwOrganisationAddress', @ProcessType_Copy, '{"SourceConnection":"PAS", "SourceTable":"rpt.vwOrganisationAddress", "SourceSQL":"", "TargetTable":"PASStaging.rpt_vwOrganisationAddress", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate", "DontCheckForDataSourceInstanceId":false, "DataSourceInstanceIdLookupSql":"SELECT Value = ADF.GetDataSourceInstanceList();", "DataSourceInstanceIdLookupConnection":"PlacementStore"}', 'PASStaging.rpt_vwOrganisationAddress')
  , (@LoadType_Daily, 'PASStaging.rpt_vwOrganisationAttribute', @ProcessType_Copy, '{"SourceConnection":"PAS", "SourceTable":"rpt.vwOrganisationAttribute", "SourceSQL":"", "TargetTable":"PASStaging.rpt_vwOrganisationAttribute", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate" , "DontCheckForDataSourceInstanceId":false, "DataSourceInstanceIdLookupSql":"SELECT Value = ADF.GetDataSourceInstanceList();", "DataSourceInstanceIdLookupConnection":"PlacementStore"}', 'PASStaging.rpt_vwOrganisationAttribute')
  , (@LoadType_Daily, 'PASStaging.rpt_vwOrganisationRole', @ProcessType_Copy, '{"SourceConnection":"PAS", "SourceTable":"rpt.vwOrganisationRole", "SourceSQL":"", "TargetTable":"PASStaging.rpt_vwOrganisationRole", "TruncateTargetTable": true,"IncrementalUpdatedDateColumn":"ETLUpdatedDate", "TargetConnection":"PlacementStore"  , "DontCheckForDataSourceInstanceId":false, "DataSourceInstanceIdLookupSql":"SELECT Value = ADF.GetDataSourceInstanceList();", "DataSourceInstanceIdLookupConnection":"PlacementStore"}', 'PASStaging.rpt_vwOrganisationRole')
  , (@LoadType_Daily, 'PASStaging.rpt_vwParty', @ProcessType_Copy, '{"SourceConnection":"PAS", "SourceTable":"rpt.vwParty", "SourceSQL":"", "TargetTable":"PASStaging.rpt_vwParty", "TruncateTargetTable": true, "AlwaysFullLoad": false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate", "TargetConnection":"PlacementStore" , "DontCheckForDataSourceInstanceId":false, "DataSourceInstanceIdLookupSql":"SELECT Value = ADF.GetDataSourceInstanceList();", "DataSourceInstanceIdLookupConnection":"PlacementStore", "SuccessfulRunOffset": 0}', 'PASStaging.rpt_vwParty')
  , (@LoadType_Daily, 'PASStaging.rpt_vwPartyAddress', @ProcessType_Copy, '{"SourceConnection":"PAS", "SourceTable":"rpt.vwPartyAddress", "SourceSQL":"", "TargetTable":"PASStaging.rpt_vwPartyAddress", "TruncateTargetTable": true, "AlwaysFullLoad": false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate", "TargetConnection":"PlacementStore" , "DontCheckForDataSourceInstanceId":false, "DataSourceInstanceIdLookupSql":"SELECT Value = ADF.GetDataSourceInstanceList();", "DataSourceInstanceIdLookupConnection":"PlacementStore", "SuccessfulRunOffset": 0}', 'PASStaging.rpt_vwPartyAddress')
  , (@LoadType_Daily, 'PASStaging.rpt_vwPartyAttribute', @ProcessType_Copy, '{"SourceConnection":"PAS", "SourceTable":"rpt.vwPartyAttribute", "SourceSQL":"", "TargetTable":"PASStaging.rpt_vwPartyAttribute", "TruncateTargetTable": true, "AlwaysFullLoad": false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate", "TargetConnection":"PlacementStore" , "DontCheckForDataSourceInstanceId":false, "DataSourceInstanceIdLookupSql":"SELECT Value = ADF.GetDataSourceInstanceList();", "DataSourceInstanceIdLookupConnection":"PlacementStore"}', 'PASStaging.rpt_vwPartyAttribute')
  , (@LoadType_Daily, 'PASStaging.rpt_vwPartyRole', @ProcessType_Copy, '{"SourceConnection":"PAS", "SourceTable":"rpt.vwPartyRole", "SourceSQL":"", "TargetTable":"PASStaging.rpt_vwPartyRole", "TruncateTargetTable": true, "AlwaysFullLoad": false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate", "TargetConnection":"PlacementStore" , "DontCheckForDataSourceInstanceId":false, "DataSourceInstanceIdLookupSql":"SELECT Value = ADF.GetDataSourceInstanceList();", "DataSourceInstanceIdLookupConnection":"PlacementStore"}', 'PASStaging.rpt_vwPartyRole')
  , (@LoadType_Daily, 'PASStaging.rpt_vwPolicy', @ProcessType_Copy, '{"SourceConnection":"PAS", "SourceTable":"rpt.vwPolicy", "SourceSQL":"", "TargetTable":"PASStaging.rpt_vwPolicy", "TruncateTargetTable": true, "AlwaysFullLoad": false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate", "TargetConnection":"PlacementStore" , "DontCheckForDataSourceInstanceId":false, "DataSourceInstanceIdLookupSql":"SELECT Value = ADF.GetDataSourceInstanceList();", "DataSourceInstanceIdLookupConnection":"PlacementStore", "SuccessfulRunOffset": 0}', 'PASStaging.rpt_vwPolicy')
  , (@LoadType_Daily, 'PASStaging.rpt_vwPolicyAttribute', @ProcessType_Copy, '{"SourceConnection":"PAS", "SourceTable":"rpt.vwPolicyAttribute", "SourceSQL":"", "TargetTable":"PASStaging.rpt_vwPolicyAttribute", "TruncateTargetTable": true, "AlwaysFullLoad": false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate", "TargetConnection":"PlacementStore" , "DontCheckForDataSourceInstanceId":false, "DataSourceInstanceIdLookupSql":"SELECT Value = ADF.GetDataSourceInstanceList();", "DataSourceInstanceIdLookupConnection":"PlacementStore", "SuccessfulRunOffset": 0}', 'PASStaging.rpt_vwPolicyAttribute')
  , (@LoadType_Daily, 'PASStaging.rpt_vwPolicyOrganisationRole', @ProcessType_Copy, '{"SourceConnection":"PAS", "SourceTable":"rpt.vwPolicyOrganisationRole", "SourceSQL":"", "TargetTable":"PASStaging.rpt_vwPolicyOrganisationRole", "TruncateTargetTable": true, "AlwaysFullLoad": false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate", "TargetConnection":"PlacementStore" , "DontCheckForDataSourceInstanceId":false, "DataSourceInstanceIdLookupSql":"SELECT Value = ADF.GetDataSourceInstanceList();", "DataSourceInstanceIdLookupConnection":"PlacementStore", "SuccessfulRunOffset": 0}', 'PASStaging.rpt_vwPolicyOrganisationRole')
  , (@LoadType_Daily, 'PASStaging.rpt_vwPolicyPartyRole', @ProcessType_Copy, '{"SourceConnection":"PAS", "SourceTable":"rpt.vwPolicyPartyRole", "SourceSQL":"", "TargetTable":"PASStaging.rpt_vwPolicyPartyRole", "TruncateTargetTable": true, "AlwaysFullLoad": false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate", "TargetConnection":"PlacementStore" , "DontCheckForDataSourceInstanceId":false, "DataSourceInstanceIdLookupSql":"SELECT Value = ADF.GetDataSourceInstanceList();", "DataSourceInstanceIdLookupConnection":"PlacementStore", "SuccessfulRunOffset": 0}', 'PASStaging.rpt_vwPolicyPartyRole')
  , (@LoadType_Daily, 'PASStaging.rpt_vwPolicySection', @ProcessType_Copy, '{"SourceConnection":"PAS", "SourceTable":"rpt.vwPolicySection", "SourceSQL":"", "TargetTable":"PASStaging.rpt_vwPolicySection", "TruncateTargetTable": true, "AlwaysFullLoad": false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate", "TargetConnection":"PlacementStore" , "DontCheckForDataSourceInstanceId":false, "DataSourceInstanceIdLookupSql":"SELECT Value = ADF.GetDataSourceInstanceList();", "DataSourceInstanceIdLookupConnection":"PlacementStore", "SuccessfulRunOffset": 0}', 'PASStaging.rpt_vwPolicySection')
  , (@LoadType_Daily, 'PASStaging.rpt_vwPolicySectionAttribute', @ProcessType_Copy, '{"SourceConnection":"PAS", "SourceTable":"rpt.vwPolicySectionAttribute", "SourceSQL":"", "TargetTable":"PASStaging.rpt_vwPolicySectionAttribute", "TruncateTargetTable": true, "AlwaysFullLoad": false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate", "TargetConnection":"PlacementStore" , "DontCheckForDataSourceInstanceId":false, "DataSourceInstanceIdLookupSql":"SELECT Value = ADF.GetDataSourceInstanceList();", "DataSourceInstanceIdLookupConnection":"PlacementStore", "SuccessfulRunOffset": 0}', 'PASStaging.rpt_vwPolicySectionAttribute')
  , (@LoadType_Daily, 'PASStaging.rpt_vwPolicySectionProduct', @ProcessType_Copy, '{"SourceConnection":"PAS", "SourceTable":"rpt.vwPolicySectionProduct", "SourceSQL":"", "TargetTable":"PASStaging.rpt_vwPolicySectionProduct", "TruncateTargetTable": true, "AlwaysFullLoad": false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate", "TargetConnection":"PlacementStore" , "DontCheckForDataSourceInstanceId":false, "DataSourceInstanceIdLookupSql":"SELECT Value = ADF.GetDataSourceInstanceList();", "DataSourceInstanceIdLookupConnection":"PlacementStore", "SuccessfulRunOffset": 0}', 'PASStaging.rpt_vwPolicySectionProduct')
  , (@LoadType_Daily, 'PASStaging.rpt_vwPolicySectionStatus', @ProcessType_Copy, '{"SourceConnection":"PAS", "SourceTable":"rpt.vwPolicySectionStatus", "SourceSQL":"", "TargetTable":"PASStaging.rpt_vwPolicySectionStatus", "TruncateTargetTable": true, "TargetConnection":"PlacementStore" , "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate" , "DontCheckForDataSourceInstanceId":false, "DataSourceInstanceIdLookupSql":"SELECT Value = ADF.GetDataSourceInstanceList();", "DataSourceInstanceIdLookupConnection":"PlacementStore"}', 'PASStaging.rpt_vwPolicySectionStatus')
  , (@LoadType_Daily, 'PASStaging.rpt_vwPolicyStatus', @ProcessType_Copy, '{"SourceConnection":"PAS", "SourceTable":"rpt.vwPolicyStatus", "SourceSQL":"", "TargetTable":"PASStaging.rpt_vwPolicyStatus", "TruncateTargetTable": true, "TargetConnection":"PlacementStore" , "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate" ,  "DontCheckForDataSourceInstanceId":false, "DataSourceInstanceIdLookupSql":"SELECT Value = ADF.GetDataSourceInstanceList();", "DataSourceInstanceIdLookupConnection":"PlacementStore"}', 'PASStaging.rpt_vwPolicyStatus')
  , (@LoadType_Daily, 'PASStaging.rpt_vwPolicyType', @ProcessType_Copy, '{"SourceConnection":"PAS", "SourceTable":"rpt.vwPolicyType", "SourceSQL":"", "TargetTable":"PASStaging.rpt_vwPolicyType", "TruncateTargetTable": true, "TargetConnection":"PlacementStore" , "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate", "DontCheckForDataSourceInstanceId":false, "DataSourceInstanceIdLookupSql":"SELECT Value = ADF.GetDataSourceInstanceList();", "DataSourceInstanceIdLookupConnection":"PlacementStore"}', 'PASStaging.rpt_vwPolicyType')
  , (@LoadType_Daily, 'PASStaging.rpt_vwPolicyWorkerRole', @ProcessType_Copy, '{"SourceConnection":"PAS", "SourceTable":"rpt.vwPolicyWorkerRole", "SourceSQL":"", "TargetTable":"PASStaging.rpt_vwPolicyWorkerRole", "TruncateTargetTable": true, "AlwaysFullLoad": false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate", "TargetConnection":"PlacementStore" , "DontCheckForDataSourceInstanceId":false, "DataSourceInstanceIdLookupSql":"SELECT Value = ADF.GetDataSourceInstanceList();", "DataSourceInstanceIdLookupConnection":"PlacementStore"}', 'PASStaging.rpt_vwPolicyWorkerRole')
  , (@LoadType_Daily, 'PASStaging.rpt_vwProduct', @ProcessType_Copy, '{"SourceConnection":"PAS", "SourceTable":"rpt.vwProduct", "SourceSQL":"", "TargetTable":"PASStaging.rpt_vwProduct", "TruncateTargetTable": true, "TargetConnection":"PlacementStore" , "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate", "DontCheckForDataSourceInstanceId":false, "DataSourceInstanceIdLookupSql":"SELECT Value = ADF.GetDataSourceInstanceList();", "DataSourceInstanceIdLookupConnection":"PlacementStore"}', 'PASStaging.rpt_vwProduct')
  , (@LoadType_Daily, 'PASStaging.rpt_vwProductAttribute', @ProcessType_Copy, '{"SourceConnection":"PAS", "SourceTable":"rpt.vwProductAttribute", "SourceSQL":"", "TargetTable":"PASStaging.rpt_vwProductAttribute", "TruncateTargetTable": true, "TargetConnection":"PlacementStore" , "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate", "DontCheckForDataSourceInstanceId":false, "DataSourceInstanceIdLookupSql":"SELECT Value = ADF.GetDataSourceInstanceList();", "DataSourceInstanceIdLookupConnection":"PlacementStore"}', 'PASStaging.rpt_vwProductAttribute')
  , (@LoadType_Daily, 'PASStaging.rpt_vwRefInsuranceType', @ProcessType_Copy, '{"SourceConnection":"PAS", "SourceTable":"rpt.vwRefInsuranceType", "SourceSQL":"", "TargetTable":"PASStaging.rpt_vwRefInsuranceType", "TruncateTargetTable": true, "TargetConnection":"PlacementStore" , "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate" , "DontCheckForDataSourceInstanceId":false }', 'PASStaging.rpt_vwRefInsuranceType')
  , (@LoadType_Daily, 'PASStaging.rpt_vwRefPolicyStatus', @ProcessType_Copy, '{"SourceConnection":"PAS", "SourceTable":"rpt.vwRefPolicyStatus", "SourceSQL":"", "TargetTable":"PASStaging.rpt_vwRefPolicyStatus", "TruncateTargetTable": true, "TargetConnection":"PlacementStore" , "AlwaysFullLoad": false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate" , "DontCheckForDataSourceInstanceId":false }', 'PASStaging.rpt_vwRefPolicyStatus')
  , (@LoadType_Daily, 'PASStaging.rpt_vwServicingRole', @ProcessType_Copy, '{"SourceConnection":"PAS", "SourceTable":"rpt.vwServicingRole", "SourceSQL":"", "TargetTable":"PASStaging.rpt_vwServicingRole", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate" , "DontCheckForDataSourceInstanceId":false, "DataSourceInstanceIdLookupSql":"SELECT Value = ADF.GetDataSourceInstanceList();", "DataSourceInstanceIdLookupConnection":"PlacementStore"}', 'PASStaging.rpt_vwServicingRole')
  , (@LoadType_Daily, 'PASStaging.rpt_vwTransactionSummaryUSD', @ProcessType_Copy, '{"SourceConnection":"PAS", "SourceTable":"rpt.vwTransactionSummaryUSD", "SourceSQL":"/* Eclipse, Broking.net and WIBS only */ SELECT TSUMM.TransactionSummaryUSDId, TSUMM.DataSourceInstanceId, TSUMM.AccountPartyId, TSUMM.AccountPartyRole, TSUMM.IsLeadCarrier, TSUMM.Level1PartyId, TSUMM.Level1PartyRole, TSUMM.Level2PartyId, TSUMM.Level2PartyRole, TSUMM.Level3PartyId, TSUMM.Level3PartyRole, TSUMM.Level4PartyId, TSUMM.Level4PartyRole, TSUMM.PolicySectionId, TSUMM.TransactionDate, TSUMM.ETLUpdatedDate, TSUMM.SegmentCode FROM rpt.vwTransactionSummaryUSD TSUMM INNER JOIN rpt.vwPolicy p ON TSUMM.PolicyId = P.PolicyId AND P.ExpiryDate >= ''2018-01-01'' WHERE TSUMM.TransactionSummaryUSDId IS NOT NULL AND TSUMM.DataSourceInstanceId IN (50000, 50045, 50358) AND TSUMM.ETLUpdatedDate > @ETLUpdatedDate;", "TargetTable":"PASStaging.rpt_vwTransactionSummaryUSD", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate", "DontCheckForDataSourceInstanceId":false}', 'PASStaging.rpt_vwTransactionSummaryUSD')
  , (@LoadType_Daily, 'PASStaging.rpt_vwWorker', @ProcessType_Copy, '{"SourceConnection":"PAS", "SourceTable":"rpt.vwWorker", "SourceSQL":"", "TargetTable":"PASStaging.rpt_vwWorker", "TruncateTargetTable": true, "TargetConnection":"PlacementStore" , "DontCheckForDataSourceInstanceId":false, "DataSourceInstanceIdLookupSql":"SELECT Value = ADF.GetDataSourceInstanceList();", "DataSourceInstanceIdLookupConnection":"PlacementStore"}', 'PASStaging.rpt_vwWorker')
  , (@LoadType_Daily, 'PS.Load_dbo_Carrier', @ProcessType_SP, '{"StoredProcedure":"PS.Load_dbo_Carrier", "TargetConnection":"PlacementStore"}', 'dbo.Carrier')
  , (@LoadType_Daily, 'PS.Load_dbo_CarrierMapping', @ProcessType_SP, '{"StoredProcedure":"PS.Load_dbo_CarrierMapping", "TargetConnection":"PlacementStore"}', 'dbo.CarrierMapping')
  , (@LoadType_Daily, 'PS.Load_ref_Facility', @ProcessType_SP, '{"StoredProcedure":"PS.Load_ref_Facility", "TargetConnection":"PlacementStore"}', 'ref.Facility')
  , (@LoadType_Daily, 'PS.Load_ref_FacilitySection', @ProcessType_SP, '{"StoredProcedure":"PS.Load_ref_FacilitySection", "TargetConnection":"PlacementStore"}', 'ref.FacilitySection')
  , (@LoadType_Daily, 'PS.Load_ref_FacilitySectionCarrier', @ProcessType_SP, '{"StoredProcedure":"PS.Load_ref_FacilitySectionCarrier", "TargetConnection":"PlacementStore"}', 'ref.FacilitySectionCarrier')
  , (@LoadType_Daily, 'ReferenceStaging.Load_Reference_ClientDetails', @ProcessType_SP, '{"StoredProcedure":"ReferenceStaging.Load_Reference_ClientDetails", "TargetConnection":"PlacementStore"}', 'Reference.ClientDetails')
  , (@LoadType_Daily, 'ReferenceStaging.MergeIntoActiveDirectory', @ProcessType_SP, '{"StoredProcedure":"ReferenceStaging.MergeIntoActiveDirectory", "TargetConnection":"PlacementStore"}', 'Reference.ActiveDirectory')
  , (@LoadType_Daily, 'ReferenceStaging.MergeIntoCity', @ProcessType_SP, '{"StoredProcedure":"ReferenceStaging.MergeIntoCity", "TargetConnection":"PlacementStore"}', 'Reference.City')
  , (@LoadType_Daily, 'ReferenceStaging.MergeIntoContactType', @ProcessType_SP, '{"StoredProcedure":"ReferenceStaging.MergeIntoContactType", "TargetConnection":"PlacementStore"}', 'Reference.ContactType')
  , (@LoadType_Daily, 'ReferenceStaging.MergeIntoCountry', @ProcessType_SP, '{"StoredProcedure":"ReferenceStaging.MergeIntoCountry", "TargetConnection":"PlacementStore"}', 'Reference.Country')
  , (@LoadType_Daily, 'ReferenceStaging.MergeIntoCountrySubdivision', @ProcessType_SP, '{"StoredProcedure":"ReferenceStaging.MergeIntoCountrySubdivision", "TargetConnection":"PlacementStore"}', 'Reference.CountrySubdivision')
  , (@LoadType_Daily, 'ReferenceStaging.MergeIntoCurrency', @ProcessType_SP, '{"StoredProcedure":"ReferenceStaging.MergeIntoCurrency", "TargetConnection":"PlacementStore"}', 'Reference.Currency')
  , (@LoadType_Daily, 'ReferenceStaging.MergeIntoDataSource', @ProcessType_SP, '{"StoredProcedure":"ReferenceStaging.MergeIntoDataSource", "TargetConnection":"PlacementStore"}', 'Reference.DataSource')
  , (@LoadType_Daily, 'ReferenceStaging.MergeIntoDataSourceInstance', @ProcessType_SP, '{"StoredProcedure":"ReferenceStaging.MergeIntoDataSourceInstance", "TargetConnection":"PlacementStore"}', 'Reference.DataSourceInstance')
  , (@LoadType_Daily, 'ReferenceStaging.MergeIntoDboPartyAddress', @ProcessType_SP, '{"StoredProcedure":"ReferenceStaging.MergeIntoDboPartyAddress", "TargetConnection":"PlacementStore"}', 'dbo.PartyAddress')
  , (@LoadType_Daily, 'ReferenceStaging.MergeIntoEmploymentStatus', @ProcessType_SP, '{"StoredProcedure":"ReferenceStaging.MergeIntoEmploymentStatus", "TargetConnection":"PlacementStore"}', 'Reference.EmploymentStatus')
  , (@LoadType_Daily, 'ReferenceStaging.MergeIntoExchangeRate', @ProcessType_SP, '{"StoredProcedure":"ReferenceStaging.MergeIntoExchangeRate", "TargetConnection":"PlacementStore"}', 'Reference.ExchangeRate')
  , (@LoadType_Daily, 'ReferenceStaging.MergeIntoFinancialStructureMapping', @ProcessType_SP, '{"StoredProcedure":"ReferenceStaging.MergeIntoFinancialStructureMapping", "TargetConnection":"PlacementStore"}', 'Reference.FinancialStructureMapping')
  , (@LoadType_Daily, 'ReferenceStaging.MergeIntoGeographyGroup', @ProcessType_SP, '{"StoredProcedure":"ReferenceStaging.MergeIntoGeographyGroup", "TargetConnection":"PlacementStore"}', 'Reference.GeographyGroup')
  , (@LoadType_Daily, 'ReferenceStaging.MergeIntoGeographyGroupingScheme', @ProcessType_SP, '{"StoredProcedure":"ReferenceStaging.MergeIntoGeographyGroupingScheme", "TargetConnection":"PlacementStore"}', 'Reference.GeographyGroupingScheme')
  , (@LoadType_Daily, 'ReferenceStaging.MergeIntoGeographyGroupMembership', @ProcessType_SP, '{"StoredProcedure":"ReferenceStaging.MergeIntoGeographyGroupMembership", "TargetConnection":"PlacementStore"}', 'Reference.GeographyGroupMembership')
  , (@LoadType_Daily, 'ReferenceStaging.MergeIntoIndustry', @ProcessType_SP, '{"StoredProcedure":"ReferenceStaging.MergeIntoIndustry", "TargetConnection":"PlacementStore"}', 'Reference.Industry')
  , (@LoadType_Daily, 'ReferenceStaging.MergeIntoIndustrySector', @ProcessType_SP, '{"StoredProcedure":"ReferenceStaging.MergeIntoIndustrySector", "TargetConnection":"PlacementStore"}', 'Reference.IndustrySector')
  , (@LoadType_Daily, 'ReferenceStaging.MergeIntoIndustrySubSector', @ProcessType_SP, '{"StoredProcedure":"ReferenceStaging.MergeIntoIndustrySubSector", "TargetConnection":"PlacementStore"}', 'Reference.IndustrySubSector')
  , (@LoadType_Daily, 'ReferenceStaging.MergeIntoISIC4Industry', @ProcessType_SP, '{"StoredProcedure":"ReferenceStaging.MergeIntoISIC4Industry", "TargetConnection":"PlacementStore"}', 'Reference.ISIC4Industry')
  , (@LoadType_Daily, 'ReferenceStaging.MergeIntoLanguage', @ProcessType_SP, '{"StoredProcedure":"ReferenceStaging.MergeIntoLanguage", "TargetConnection":"PlacementStore"}', 'Reference.Language')
  , (@LoadType_Daily, 'ReferenceStaging.MergeIntoLegalEntity', @ProcessType_SP, '{"StoredProcedure":"ReferenceStaging.MergeIntoLegalEntity", "TargetConnection":"PlacementStore"}', 'Reference.LegalEntity')
  , (@LoadType_Daily, 'ReferenceStaging.MergeIntoOffice', @ProcessType_SP, '{"StoredProcedure":"ReferenceStaging.MergeIntoOffice", "TargetConnection":"PlacementStore"}', 'Reference.Office')
  , (@LoadType_Daily, 'ReferenceStaging.MergeIntoParty', @ProcessType_SP, '{"StoredProcedure":"ReferenceStaging.MergeIntoParty", "TargetConnection":"PlacementStore"}', 'Reference.Party')
  , (@LoadType_Daily, 'ReferenceStaging.MergeIntoPartyCheckResultHistory', @ProcessType_SP, '{"StoredProcedure":"ReferenceStaging.MergeIntoPartyCheckResultHistory", "TargetConnection":"PlacementStore"}', 'Reference.PartyCheckResultHistory')
  , (@LoadType_Daily, 'ReferenceStaging.MergeIntoPartyExternalReference', @ProcessType_SP, '{"StoredProcedure":"ReferenceStaging.MergeIntoPartyExternalReference", "TargetConnection":"PlacementStore"}', 'Uses ReferenceStaging.rpt_vwPartyExternalReference')
  , (@LoadType_Daily, 'ReferenceStaging.MergeIntoPartyRole', @ProcessType_SP, '{"StoredProcedure":"ReferenceStaging.MergeIntoPartyRole", "TargetConnection":"PlacementStore"}', 'Reference.PartyRole')
  , (@LoadType_Daily, 'ReferenceStaging.MergeIntoPartyRoleRelationship', @ProcessType_SP, '{"StoredProcedure":"ReferenceStaging.MergeIntoPartyRoleRelationship", "TargetConnection":"PlacementStore"}', 'Reference.PartyRoleRelationship')
  , (@LoadType_Daily, 'ReferenceStaging.MergeIntoProduct', @ProcessType_SP, '{"StoredProcedure":"ReferenceStaging.MergeIntoProduct", "TargetConnection":"PlacementStore"}', 'Reference.Product')
  , (@LoadType_Daily, 'ReferenceStaging.MergeIntoProductClass', @ProcessType_SP, '{"StoredProcedure":"ReferenceStaging.MergeIntoProductClass", "TargetConnection":"PlacementStore"}', 'Reference.ProductClass')
  , (@LoadType_Daily, 'ReferenceStaging.MergeIntoProductLine', @ProcessType_SP, '{"StoredProcedure":"ReferenceStaging.MergeIntoProductLine", "TargetConnection":"PlacementStore"}', 'Reference.ProductLine')
  , (@LoadType_Daily, 'ReferenceStaging.MergeIntoSegmentation', @ProcessType_SP, '{"StoredProcedure":"ReferenceStaging.MergeIntoSegmentation", "TargetConnection":"PlacementStore"}', 'Reference.Segmentation')
  , (@LoadType_Daily, 'ReferenceStaging.MergeIntoSIC87Industry', @ProcessType_SP, '{"StoredProcedure":"ReferenceStaging.MergeIntoSIC87Industry", "TargetConnection":"PlacementStore"}', 'Reference.SIC87Industry')
  , (@LoadType_Daily, 'ReferenceStaging.MergeIntoWorker', @ProcessType_SP, '{"StoredProcedure":"ReferenceStaging.MergeIntoWorker", "TargetConnection":"PlacementStore"}', 'Reference.Worker')
  , (@LoadType_Daily, 'ReferenceStaging.MergeIntoWorkerAccount', @ProcessType_SP, '{"StoredProcedure":"ReferenceStaging.MergeIntoWorkerAccount", "TargetConnection":"PlacementStore"}', 'Reference.WorkerAccount')
  , (@LoadType_Daily, 'ReferenceStaging.MergeIntoWorkerContact', @ProcessType_SP, '{"StoredProcedure":"ReferenceStaging.MergeIntoWorkerContact", "TargetConnection":"PlacementStore"}', 'Reference.WorkerContact')
  , (@LoadType_Daily, 'ReferenceStaging.MergeIntoWTWFinancialGeography', @ProcessType_SP, '{"StoredProcedure":"ReferenceStaging.MergeIntoWTWFinancialGeography", "TargetConnection":"PlacementStore"}', 'Reference.WTWFinancialGeography')
  , (@LoadType_Daily, 'ReferenceStaging.MergeIntoWTWFinancialGeographyAddress', @ProcessType_SP, '{"StoredProcedure":"ReferenceStaging.MergeIntoWTWFinancialGeographyAddress", "TargetConnection":"PlacementStore"}', 'Reference.WTWFinancialGeographyAddress')
  , (@LoadType_Daily, 'ReferenceStaging.MergeIntoWTWFinancialSegment', @ProcessType_SP, '{"StoredProcedure":"ReferenceStaging.MergeIntoWTWFinancialSegment", "TargetConnection":"PlacementStore"}', 'Reference.WTWFinancialSegment')
  , (@LoadType_Daily, 'ReferenceStaging.MergeIntoWTWLegalEntity', @ProcessType_SP, '{"StoredProcedure":"ReferenceStaging.MergeIntoWTWLegalEntity", "TargetConnection":"PlacementStore"}', 'Reference.WTWLegalEntity')
  , (@LoadType_Daily, 'ReferenceStaging.MergeReferencePartyIntoDboParty', @ProcessType_SP, '{"StoredProcedure":"ReferenceStaging.MergeReferencePartyIntoDboParty", "TargetConnection":"PlacementStore"}', 'dbo.Party')
  , (@LoadType_Daily, 'ReferenceStaging.rpt_vwActiveDirectory', @ProcessType_Copy, '{"SourceConnection":"WillisReference", "SourceTable":"rpt.vwActiveDirectory", "SourceSQL":"SELECT * FROM (       SELECT ROW_NUMBER() OVER(PARTITION BY ActiveDirectoryId ORDER BY ETLUpdatedDate) AS RNK             ,ActiveDirectoryId          ,AccountName          ,ObjectGUID          ,UserPrincipalName          ,ObjectSID          ,AzureSynchronizedFlag          ,CanonicalName          ,DistinguishedName          ,CreatedDate          ,ModifiedDate          ,EmailAddress          ,LastName          ,GivenName          ,Domain          ,Initials          ,DisplayName          ,OfficePhone          ,CommonName          ,LastLogonDate          ,AccountExpirationDate          ,[Enabled]          ,IsDeleted          ,LastUpdateTime          ,LastUpdateBy          ,ETLUpdatedDate         FROM rpt.vwActiveDirectory       WHERE ETLUpdatedDate > @ETLUpdatedDate      ) AS B      WHERE RNK = 1;", "TargetTable":"ReferenceStaging.rpt_vwActiveDirectory", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate"}', 'ReferenceStaging.rpt_vw_ActiveDirectory')
  , (@LoadType_Daily, 'ReferenceStaging.rpt_vwCity', @ProcessType_Copy, '{"SourceConnection":"WillisReference", "SourceTable":"rpt.vwCity", "SourceSQL":"", "TargetTable":"ReferenceStaging.rpt_vwCity", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate"}', 'ReferenceStaging.rpt_vwCity')
  , (@LoadType_Daily, 'ReferenceStaging.rpt_vwClientDetails', @ProcessType_Copy, '{"SourceConnection":"WillisReference", "SourceTable":"rpt.vwClientDetails", "SourceSQL":"", "TargetTable":"ReferenceStaging.rpt_vwClientDetails", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"LastUpdateTime", "DataSourceInstanceIdLookupSql":"SELECT Value = ADF.GetDataSourceInstanceList();", "DataSourceInstanceIdLookupConnection":"PlacementStore"}', 'ReferenceStaging.rpt_vwClientDetails')
  , (@LoadType_Daily, 'ReferenceStaging.rpt_vwCountry', @ProcessType_Copy, '{"SourceConnection":"WillisReference", "SourceTable":"rpt.vwCountry", "SourceSQL":"", "TargetTable":"ReferenceStaging.rpt_vwCountry", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate"}', 'ReferenceStaging.rpt_vwCountry')
  , (@LoadType_Daily, 'ReferenceStaging.rpt_vwCountrySubdivision', @ProcessType_Copy, '{"SourceConnection":"WillisReference", "SourceTable":"rpt.vwCountrySubdivision", "SourceSQL":"", "TargetTable":"ReferenceStaging.rpt_vwCountrySubdivision", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate"}', 'ReferenceStaging.rpt_vwCountrySubdivision')
  , (@LoadType_Daily, 'ReferenceStaging.rpt_vwCurrency', @ProcessType_Copy, '{"SourceConnection":"WillisReference", "SourceTable":"rpt.vwCurrency", "SourceSQL":"", "TargetTable":"ReferenceStaging.rpt_vwCurrency", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate"}', 'ReferenceStaging.rpt_vwCurrency')
  , (@LoadType_Daily, 'ReferenceStaging.rpt_vwDataSource from WillisReference', @ProcessType_Copy, '{"SourceConnection":"WillisReference", "SourceTable":"rpt.vwDataSource", "SourceSQL":"", "TargetTable":"ReferenceStaging.rpt_vwDataSource", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate"}', 'ReferenceStaging.rpt_vwDataSource')
  , (@LoadType_Daily, 'ReferenceStaging.rpt_vwDataSourceInstance', @ProcessType_Copy, '{"SourceConnection":"WillisReference", "SourceTable":"rpt.vwDataSourceInstance", "SourceSQL":"", "TargetTable":"ReferenceStaging.rpt_vwDataSourceInstance", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate", "DontCheckForDataSourceInstanceId":true }', 'ReferenceStaging.rpt_vwDataSourceInstance')
  , (@LoadType_Daily, 'ReferenceStaging.rpt_vwExchangeRate', @ProcessType_Copy, '{"SourceConnection":"WillisReference", "SourceTable":"rpt.vwExchangeRate", "SourceSQL":"SELECT ExchangeRateID,FromCurrencyId,ToCurrencyId,EffectiveDate,ExchangeRateTypeId,ExchangeRate,SourceLastUpdateTime,er.IsDeleted,er.LastUpdateTime,er.LastUpdateBy,er.ETLUpdatedDate FROM rpt.vwExchangeRate er WHERE EffectiveDate >= ''2017-01-01'' AND er.ETLUpdatedDate > @ETLUpdatedDate AND ExchangeRateTypeId IN (60002, 60003);", "TargetTable":"ReferenceStaging.rpt_vwExchangeRate", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate"}', 'ReferenceStaging.rpt_vwExchangeRate')
  , (@LoadType_Daily, 'ReferenceStaging.rpt_vwGeographyGroup', @ProcessType_Copy, '{"SourceConnection":"WillisReference", "SourceTable":"rpt.vwGeographyGroup", "SourceSQL":"", "TargetTable":"ReferenceStaging.rpt_vwGeographyGroup", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate"}', 'ReferenceStaging.rpt_vwGeographyGroup')
  , (@LoadType_Daily, 'ReferenceStaging.rpt_vwGeographyGroupingScheme', @ProcessType_Copy, '{"SourceConnection":"WillisReference", "SourceTable":"rpt.vwGeographyGroupingScheme", "SourceSQL":"", "TargetTable":"ReferenceStaging.rpt_vwGeographyGroupingScheme", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate"}', 'ReferenceStaging.rpt_vwGeographyGroupingScheme')
  , (@LoadType_Daily, 'ReferenceStaging.rpt_vwGeographyGroupMembership', @ProcessType_Copy, '{"SourceConnection":"WillisReference", "SourceTable":"rpt.vwGeographyGroupMembership", "SourceSQL":"", "TargetTable":"ReferenceStaging.rpt_vwGeographyGroupMembership", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate"}', 'ReferenceStaging.rpt_vwGeographyGroupMembership')
  , (@LoadType_Daily, 'ReferenceStaging.rpt_vwISIC4Industry', @ProcessType_Copy, '{"SourceConnection":"WillisReference", "SourceTable":"rpt.vwISIC4Class", "SourceSQL":"SELECT       s.ISIC4SectionId, s.ISIC4SectionCode, s.ISIC4SectionName, d.ISIC4DivisionId, d.ISIC4DivisionCode, d.ISIC4DivisionName, gr.ISIC4GroupId, gr.ISIC4GroupCode, gr.ISIC4GroupName, cl.ISIC4ClassId, cl.ISIC4ClassCode, cl.ISIC4FullCode, cl.ISIC4ClassName, m.WIllisIndustrySubSectorId, cl.LastUpdateTime, cl.LastUpdateBy, cl.IsDeleted, cl.ETLUpdatedDate      FROM rpt.vwISIC4Class cl      INNER JOIN rpt.vwISIC4Group gr ON cl.ISIC4GroupId = gr.ISIC4GroupId       AND gr.IsDeleted = 0      INNER JOIN rpt.vwISIC4Division d ON gr.ISIC4DivisionId = d.ISIC4DivisionId       AND d.IsDeleted = 0      INNER JOIN rpt.vwISIC4Section s ON d.ISIC4SectionId = s.ISIC4SectionId       AND s.IsDeleted = 0      LEFT JOIN rpt.vwISIC4Mapping m ON cl.ISIC4ClassId = m.ISIC4ClassId       AND m.IsDeleted = 0;", "TargetTable":"ReferenceStaging.rpt_vwISIC4Industry", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'ReferenceStaging.rpt_vwISIC4Industry')
  , (@LoadType_Daily, 'ReferenceStaging.rpt_vwLanguage', @ProcessType_Copy, '{"SourceConnection":"WillisReference", "SourceTable":"rpt.vwLanguage", "SourceSQL":"", "TargetTable":"ReferenceStaging.rpt_vwLanguage", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate"}', 'ReferenceStaging.rpt_vwLanguage')
  , (@LoadType_Daily, 'ReferenceStaging.rpt_vwLegacyWTWFinancialStructureMapping', @ProcessType_Copy, '{"SourceConnection":"WillisReference", "SourceTable":"rpt.vwLegacyWTWFinancialStructureMapping", "SourceSQL":"SELECT        LegacyWTWFinancialStructureMappingId       ,LWFSM.CommonFinancialLanguageStringId       ,CFL.LegalEntityName       ,CFL.OrganisationDivisionName       ,CFL.OrganisationDepartmentName       ,CFL.OrganisationLocationName       ,LWFSM.WTWLegalEntityId, LE.WTWLegalEntityName       ,LWFSM.WTWFinancialSegmentId       ,CONVERT(NVARCHAR(10),CASE WHEN seg.WTWFinancialSegmentCode = ''SEG050'' THEN ''CRB''        WHEN seg.WTWFinancialSegmentCode = ''SEG010'' THEN ''HCB''        WHEN seg.WTWFinancialSegmentCode = ''SEG020'' THEN ''IRR''        ELSE seg.WTWFinancialSegmentCode         END) AS SegmentCode       ,FS.WTWFinancialSegmentName       ,LWFSM.WTWFinancialGeographyId, FG.WTWFinancialGeographyName       ,LWFSM.IsDeleted       ,LWFSM.LastUpdateTime       ,LWFSM.LastUpdateBy       ,LWFSM.ETLUpdatedDate      FROM rpt.vwLegacyWTWFinancialStructureMapping AS LWFSM      LEFT JOIN rpt.vwFullCommonFinancialLanguageString AS CFL ON CFL.CommonFinancialLanguageStringId = LWFSM.CommonFinancialLanguageStringId      LEFT JOIN rpt.vwWTWLegalEntity AS LE ON LE.WTWLegalEntityId = LWFSM.WTWLegalEntityId      LEFT JOIN rpt.vwWTWFinancialSegment AS FS ON FS.WTWFinancialSegmentId = LWFSM.WTWFinancialSegmentId AND FS.IsDeleted = 0      LEFT JOIN rpt.vwWTWFinancialSegment AS serv ON serv.WTWFinancialSegmentId = FS.ParentWTWFinancialSegmentId AND serv.IsDeleted = 0      LEFT JOIN rpt.vwWTWFinancialSegment AS lob ON lob.WTWFinancialSegmentId  = serv.ParentWTWFinancialSegmentId AND lob.IsDeleted = 0      LEFT JOIN rpt.vwWTWFinancialSegment AS bus ON bus.WTWFinancialSegmentId  = lob.ParentWTWFinancialSegmentId AND bus.IsDeleted = 0      LEFT JOIN rpt.vwWTWFinancialSegment AS seg ON seg.WTWFinancialSegmentId  = bus.ParentWTWFinancialSegmentId AND seg.IsDeleted = 0      LEFT JOIN rpt.vwWTWFinancialGeography AS FG ON FG.WTWFinancialGeographyId = LWFSM.WTWFinancialGeographyId      WHERE LWFSM.ETLUpdatedDate > @ETLUpdatedDate;", "TargetTable":"ReferenceStaging.rpt_vwLegacyWTWFinancialStructureMapping", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate"}', 'ReferenceStaging.rpt_vwLegacyWTWFinancialStructureMapping')
  , (@LoadType_Daily, 'ReferenceStaging.rpt_vwLegalEntity', @ProcessType_Copy, '{"SourceConnection":"WillisReference", "SourceTable":"rpt.vwLegalEntity", "SourceSQL":"", "TargetTable":"ReferenceStaging.rpt_vwLegalEntity", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate"}', 'ReferenceStaging.rpt_vwLegalEntity')
  , (@LoadType_Daily, 'ReferenceStaging.rpt_vwOffice', @ProcessType_Copy, '{"SourceConnection":"WillisReference", "SourceTable":"rpt.vwOffice", "SourceSQL":"", "TargetTable":"ReferenceStaging.rpt_vwOffice", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate"}', 'ReferenceStaging.rpt_vwOffice')
  , (@LoadType_Daily, 'ReferenceStaging.rpt_vwPartyCheckResultHistory', @ProcessType_Copy, '{"SourceConnection":"WillisReference", "SourceTable":"rpt.vwPartyCheckResultHistory", "SourceSQL":"", "TargetTable":"ReferenceStaging.rpt_vwPartyCheckResultHistory", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate"}', 'ReferenceStaging.rpt_vwPartyCheckResult')
  , (@LoadType_Daily, 'ReferenceStaging.rpt_vwPartyExternalReference', @ProcessType_Copy, '{"SourceConnection":"WillisReference", "SourceTable":"rpt.vwPartyExternalReference", "SourceSQL":"SELECT PartyExternalReferenceId       , PartyId       , DataSourceInstanceID       , PartyExternalReference       , IsDeleted       , ETLUpdatedDate      FROM rpt.vwPartyExternalReference      WHERE ETLUpdatedDate > @ETLUpdatedDate       AND DataSourceInstanceID IN (          50023 /* D&B (DUNS) */        , 50276 /* SIGNET */        );", "TargetTable":"ReferenceStaging.rpt_vwPartyExternalReference", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate"}', 'ReferenceStaging.rpt_vwPartyExternalReference')
  , (@LoadType_Daily, 'ReferenceStaging.rpt_vwPartyRole', @ProcessType_Copy, '{"SourceConnection":"WillisReference", "SourceTable":"rpt.vwPartyRole", "SourceSQL":"", "TargetTable":"ReferenceStaging.rpt_vwPartyRole", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate"}', 'ReferenceStaging.rpt_vwPartyRole')
  , (@LoadType_Daily, 'ReferenceStaging.rpt_vwPartyRoleRelationship', @ProcessType_Copy, '{"SourceConnection":"WillisReference", "SourceTable":"rpt.vwPartyRoleRelationship", "SourceSQL":"/* CLIENT, INSURED, CARRIER, REINSURED*/      SELECT PartyRoleRelationshipId, PartyRoleId, PartyId, IsDeleted, LastUpdateTime, LastUpdateBy, ETLUpdatedDate      FROM  rpt.vwPartyRoleRelationship      WHERE PartyRoleId IN (100, 101, 102, 106)      AND ETLUpdatedDate > @ETLUpdatedDate;", "TargetTable":"ReferenceStaging.rpt_vwPartyRoleRelationship", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate"}', 'ReferenceStaging.rpt_vwPartyRoleRelationship')
  , (@LoadType_Daily, 'ReferenceStaging.rpt_vwProduct', @ProcessType_Copy, '{"SourceConnection":"WillisReference", "SourceTable":"rpt.vwProduct", "SourceSQL":"", "TargetTable":"ReferenceStaging.rpt_vwProduct", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate"}', 'ReferenceStaging.rpt_vwProduct')
  , (@LoadType_Daily, 'ReferenceStaging.rpt_vwProductClass', @ProcessType_Copy, '{"SourceConnection":"WillisReference", "SourceTable":"rpt.vwProductClass", "SourceSQL":"", "TargetTable":"ReferenceStaging.rpt_vwProductClass", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate"}', 'ReferenceStaging.rpt_vwProductClass')
  , (@LoadType_Daily, 'ReferenceStaging.rpt_vwProductLine', @ProcessType_Copy, '{"SourceConnection":"WillisReference", "SourceTable":"rpt.vwProductLine", "SourceSQL":"", "TargetTable":"ReferenceStaging.rpt_vwProductLine", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate"}', 'ReferenceStaging.rpt_vwProductLine')
  , (@LoadType_Daily, 'ReferenceStaging.rpt_vwSegmentation', @ProcessType_Copy, '{"SourceConnection":"WillisReference", "SourceTable":"rpt.vwSegmentation", "SourceSQL":"", "TargetTable":"ReferenceStaging.rpt_vwSegmentation", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate"}', 'ReferenceStaging.rpt_vwSegmentation')
  , (@LoadType_Daily, 'ReferenceStaging.rpt_vwSIC87Industry', @ProcessType_Copy, '{"SourceConnection":"WillisReference", "SourceTable":"rpt.vwSIC87Industry", "SourceSQL":"SELECT D.SIC87DivisionId, D.SIC87DivisionCode, D.SIC87DivisionName, MG.SIC87MajorGroupId, MG.SIC87MajorGroupCode, MG.SIC87MajorGroupName, IG.SIC87IndustryGroupId, IG.SIC87IndustryGroupCode, IG.SIC87IndustryGroupName, I.SIC87IndustryId, I.SIC87IndustryCode, I.SIC87IndustryName, M.WIllisIndustrySubSectorId, I.LastUpdateTime, I.LastUpdateBy, I.IsDeleted, I.ETLUpdatedDate      FROM rpt.vwSIC87Industry AS I      INNER JOIN rpt.vwSIC87IndustryGroup AS IG       ON IG.SIC87IndustryGroupId = I.SIC87IndustryGroupid        AND IG.IsDeleted = 0      INNER JOIN rpt.vwSIC87MajorGroup AS MG       ON MG.SIC87MajorGroupId = IG.SIC87MajorGroupId        AND MG.IsDeleted = 0      INNER JOIN rpt.vwSIC87Division AS D       ON D.SIC87DivisionId = MG.SIC87DivisionId        AND D.IsDeleted = 0      LEFT JOIN rpt.vwSIC87Mapping AS M       ON M.SIC87IndustryId = I.SIC87IndustryId        AND M.IsDeleted = 0;", "TargetTable":"ReferenceStaging.rpt_vwSIC87Industry", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'ReferenceStaging.rpt_vwSIC87Industry')
  , (@LoadType_Daily, 'ReferenceStaging.rpt_vwWillisIndustry', @ProcessType_Copy, '{"SourceConnection":"WillisReference", "SourceTable":"rpt.vwWillisIndustry", "SourceSQL":"", "TargetTable":"ReferenceStaging.rpt_vwWillisIndustry", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate"}', 'ReferenceStaging.rpt_vwWillisIndustry')
  , (@LoadType_Daily, 'ReferenceStaging.rpt_vwWillisIndustrySector', @ProcessType_Copy, '{"SourceConnection":"WillisReference", "SourceTable":"rpt.vwWillisIndustrySector", "SourceSQL":"", "TargetTable":"ReferenceStaging.rpt_vwWillisIndustrySector", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate"}', 'ReferenceStaging.rpt_vwWillisIndustrySector')
  , (@LoadType_Daily, 'ReferenceStaging.rpt_vwWillisIndustrySubsector', @ProcessType_Copy, '{"SourceConnection":"WillisReference", "SourceTable":"rpt.vwWillisIndustrySubSector", "SourceSQL":"", "TargetTable":"ReferenceStaging.rpt_vwWillisIndustrySubsector", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate"}', 'ReferenceStaging.rpt_vwWillisIndustrySubsector')
  , (@LoadType_Daily, 'ReferenceStaging.rpt_vwWTWFinancialGeography', @ProcessType_Copy, '{"SourceConnection":"WillisReference", "SourceTable":"rpt.vwWTWFinancialGeography", "SourceSQL":"", "TargetTable":"ReferenceStaging.rpt_vwWTWFinancialGeography", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate"}', 'ReferenceStaging.rpt_vwWTWFinancialGeography')
  , (@LoadType_Daily, 'ReferenceStaging.rpt_vwWTWFinancialGeographyAddress', @ProcessType_Copy, '{"SourceConnection":"WillisReference", "SourceTable":"rpt.vwWTWFinancialGeographyAddress", "SourceSQL":"", "TargetTable":"ReferenceStaging.rpt_vwWTWFinancialGeographyAddress", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate"}', 'ReferenceStaging.rpt_vwWTWFinancialGeographyAddress')
  , (@LoadType_Daily, 'ReferenceStaging.rpt_vwWTWFinancialSegment', @ProcessType_Copy, '{"SourceConnection":"WillisReference", "SourceTable":"rpt.vwWTWFinancialSegment", "SourceSQL":"", "TargetTable":"ReferenceStaging.rpt_vwWTWFinancialSegment", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate"}', 'ReferenceStaging.rpt_vwWTWFinancialSegment')
  , (@LoadType_Daily, 'ReferenceStaging.rpt_vwWTWLegalEntity', @ProcessType_Copy, '{"SourceConnection":"WillisReference", "SourceTable":"rpt.vwWTWLegalEntity", "SourceSQL":"", "TargetTable":"ReferenceStaging.rpt_vwWTWLegalEntity", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate"}', 'ReferenceStaging.rpt_vwWTWLegalEntity')
  , (@LoadType_Daily, 'ReferenceStaging.UpdateOperatingRevenueUSDOnParty', @ProcessType_SP, '{"StoredProcedure":"ReferenceStaging.UpdateOperatingRevenueUSDOnParty", "TargetConnection":"PlacementStore"}', 'Reference.Party')
  , (@LoadType_Daily, 'ReferenceStaging.wrk_vwContactType', @ProcessType_Copy, '{"SourceConnection":"WillisReference", "SourceTable":"wrk.vwContactType", "SourceSQL":"", "TargetTable":"ReferenceStaging.wrk_vwContactType", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate"}', 'ReferenceStaging.wrk_vwContactType')
  , (@LoadType_Daily, 'ReferenceStaging.wrk_vwEmploymentStatus', @ProcessType_Copy, '{"SourceConnection":"WillisReference", "SourceTable":"wrk.vwEmploymentStatus", "SourceSQL":"", "TargetTable":"ReferenceStaging.wrk_vwEmploymentStatus", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate"}', 'ReferenceStaging.wrk_vwEmploymentStatus')
  , (@LoadType_Daily, 'ReferenceStaging.wrk_vwWorker', @ProcessType_Copy, '{"SourceConnection":"WillisReference", "SourceTable":"wrk.vwWorker", "SourceSQL":"", "TargetTable":"ReferenceStaging.wrk_vwWorker", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate"}', 'ReferenceStaging.wrk_vwWorker')
  , (@LoadType_Daily, 'ReferenceStaging.wrk_vwWorkerAccount', @ProcessType_Copy, '{"SourceConnection":"WillisReference", "SourceTable":"wrk.vwWorkerAccount", "SourceSQL":"", "TargetTable":"ReferenceStaging.wrk_vwWorkerAccount", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'ReferenceStaging.wrk_vwWorkerAccount')
  , (@LoadType_Daily, 'ReferenceStaging.wrk_vwWorkerContact', @ProcessType_Copy, '{"SourceConnection":"WillisReference", "SourceTable":"wrk.vwWorkerContact", "SourceSQL":"", "TargetTable":"ReferenceStaging.wrk_vwWorkerContact", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate"}', 'ReferenceStaging.wrk_vwWorkerContact')
  , (@LoadType_Daily, 'rpt.Load_rpt_Organisation', @ProcessType_SP, '{"StoredProcedure":"rpt.Load_rpt_Organisation", "TargetConnection":"PlacementStore"}', 'rpt.Organisation')
  , (@LoadType_Daily, 'Rules.ClearSegmentationRules', @ProcessType_SP, '{"StoredProcedure":"Rules.ClearSegmentationRules", "TargetConnection":"PlacementStore", "ResultType":"DummyRow"}', NULL)
  , (@LoadType_Daily, 'Rules.EnabledDataSources_Run_IncludeForRenewal', @ProcessType_SP, '{"StoredProcedure":"Rules.EnabledDataSources_Run_IncludeForRenewal", "TargetConnection":"PlacementStore", "ResultType":"DummyRow"}', NULL)
  , (@LoadType_Daily, 'Rules.Run_PlacementRenewal', @ProcessType_SP, '{"StoredProcedure":"Rules.Run_PlacementRenewal", "TargetConnection":"PlacementStore"}', NULL)
  , (@LoadType_Daily, 'Rules.Run_SegmentationRules', @ProcessType_SP, '{"StoredProcedure":"Rules.Run_SegmentationRules", "TargetConnection":"PlacementStore", "ResultType":"DummyRow"}', NULL)
  , (@LoadType_Daily, 'SHStaging.ActiveDirectory', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceTable":"APIv1.ActiveDirectory", "SourceSQL":"SELECT DISTINCT [ActiveDirectoryId], [AccountName], [ObjectGUID], [UserPrincipalName], [ObjectSID], [AzureSynchronizedFlag], [CanonicalName], [DistinguishedName], [CreatedDate], [ModifiedDate], [EmailAddress], [LastName], [GivenName], [Domain], [Initials], [DisplayName], [OfficePhone], [CommonName], [LastLogonDate], [AccountExpirationDate], [Enabled], [IsDeleted], [LastUpdateTime], [ETLUpdatedDate] FROM APIv1.ActiveDirectory WHERE [ETLUpdatedDate]> @ETLUpdatedDate", "TargetTable":"SHUBTEMP.ActiveDirectory", "TruncateTargetTable": true, "TargetConnection":"ServiceHubMetadata", "IncrementalUpdatedDateColumn":"ETLUpdatedDate"}', 'SHUBTEMP.ActiveDirectory')
  , (@LoadType_Daily, 'SHStaging.vw_ActiveDirectory', @ProcessType_Event, '{"TopicName":"psadf-to-psfuncapp-event/messages","TargetConnection":"AzureServiceBus","BodyTemplate":"{\"SourceDatabase\":\"ServiceHubMetadata\", \"SourceTable\":\"SHUBTEMP.ActiveDirectory\", \"TargetDatabase\":\"ServiceHub\", \"TargetTable\":\"Staging.vw_ActiveDirectory\",\"ProcessId\":@ProcessId,\"SessionGuId\":\"@SessionGuid\",\"RunGuid\":\"@RunGuid\",\"InstanceLogId\":@InstanceLogId,\"ProcessSessionId\":\"@ProcessSessionId\",\"TriggerType\":\"Event\"}","HandlerUpdatesStatus":true}', 'Staging.vw_ActiveDirectory')
  , (@LoadType_Daily, 'SHStaging.vw_DataSource', @ProcessType_Event, '{"TopicName":"psadf-to-psfuncapp-event/messages","TargetConnection":"AzureServiceBus","BodyTemplate":"{ \"SourceDatabase\":\"Database\", \"SourceTable\":\"Reference.DataSource\", \"SourceQuery\":\"SELECT DataSourceId, DataSourceServiceNowCode, DataSourceName, DataSourceDescription, DataSourceEndDate, DataSourceComments, IsDeleted FROM Reference.DataSource;\", \"TargetDatabase\":\"ServiceHub\", \"TargetTable\":\"Staging.vw_DataSource\" ,\"ProcessId\":@ProcessId,\"SessionGuId\":\"@SessionGuid\",\"RunGuid\":\"@RunGuid\",\"InstanceLogId\":@InstanceLogId,\"ProcessSessionId\":\"@ProcessSessionId\",\"TriggerType\":\"Event\"}","HandlerUpdatesStatus":true}', 'Staging.vw_DataSource')
  , (@LoadType_Daily, 'SHStaging.vw_DataSourceInstance', @ProcessType_Event, '{"TopicName":"psadf-to-psfuncapp-event/messages","TargetConnection":"AzureServiceBus","BodyTemplate":"{ \"SourceDatabase\":\"Database\", \"SourceTable\":\"Reference.DataSourceInstance\", \"SourceQuery\":\"SELECT DataSourceInstanceID, DataSourceID, DataSourceInstanceServiceNowCode, DataSourceInstanceName, DataSourceInstanceDescription, DataSourceInstanceEndDate, DataSourceInstanceComments, IsDeleted FROM Reference.DataSourceInstance;\", \"TargetDatabase\":\"ServiceHub\", \"TargetTable\":\"Staging.vw_DataSourceInstance\" ,\"ProcessId\":@ProcessId,\"SessionGuId\":\"@SessionGuid\",\"RunGuid\":\"@RunGuid\",\"InstanceLogId\":@InstanceLogId,\"ProcessSessionId\":\"@ProcessSessionId\",\"TriggerType\":\"Event\"}","HandlerUpdatesStatus":true}', 'Staging.vw_DataSourceInstance')
  , (@LoadType_Daily, 'SHStaging.vw_WorkerAccount', @ProcessType_Event, '{"TopicName":"psadf-to-psfuncapp-event/messages","TargetConnection":"AzureServiceBus","BodyTemplate":"{\"SourceDatabase\":\"ServiceHubMetadata\", \"SourceTable\":\"SHUBTEMP.WorkerAccount\", \"TargetDatabase\":\"ServiceHub\", \"TargetTable\":\"Staging.vw_WorkerAccount\",\"ProcessId\":@ProcessId,\"SessionGuId\":\"@SessionGuid\",\"RunGuid\":\"@RunGuid\",\"InstanceLogId\":@InstanceLogId,\"ProcessSessionId\":\"@ProcessSessionId\",\"TriggerType\":\"Event\"}","HandlerUpdatesStatus":true}', 'Staging.vw_WorkerAccount')
  , (@LoadType_Daily, 'SHStaging.WorkerAccount', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceTable":"Reference.WorkerAccount", "SourceSQL":"", "TargetTable":"SHUBTEMP.WorkerAccount", "TruncateTargetTable": true, "TargetConnection":"ServiceHubMetadata", "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate"}', 'SHUBTEMP.WorkerAccount')
  , (@LoadType_Daily, 'SignetStaging.ApplyCarrierDataMasking', @ProcessType_SP, '{"StoredProcedure":"SignetStaging.ApplyCarrierDataMasking", "TargetConnection":"PlacementStore"}', NULL)
  , (@LoadType_Daily, 'SignetStaging.ApprovalStatus', @ProcessType_Copy, '{"SourceConnection":"Signet", "SourceTable":"dbo.tblApprovalStatus", "SourceSQL":"", "TargetTable":"SignetStaging.ApprovalStatus", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'SignetStaging.ApprovalStatus')
  , (@LoadType_Daily, 'SignetStaging.ApprovalType', @ProcessType_Copy, '{"SourceConnection":"Signet", "SourceTable":"dbo.tblApprovalType", "SourceSQL":"", "TargetTable":"SignetStaging.ApprovalType", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'SignetStaging.ApprovalType')
  , (@LoadType_Daily, 'SignetStaging.Carrier', @ProcessType_Copy, '{"SourceConnection":"Signet", "SourceTable":"dbo.tblCarrier", "SourceSQL":"", "TargetTable":"SignetStaging.Carrier", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'SignetStaging.Carrier')
  , (@LoadType_Daily, 'SignetStaging.CarrierRestriction', @ProcessType_Copy, '{"SourceConnection":"Signet", "SourceTable":"dbo.tblCarrierRestriction", "SourceSQL":"", "TargetTable":"SignetStaging.CarrierRestriction", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'SignetStaging.CarrierRestriction')
  , (@LoadType_Daily, 'SignetStaging.CompanyType', @ProcessType_Copy, '{"SourceConnection":"Signet", "SourceTable":"dbo.tblCompanyType", "SourceSQL":"", "TargetTable":"SignetStaging.CompanyType", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'SignetStaging.CompanyType')
  , (@LoadType_Daily, 'SignetStaging.FatcaReferences', @ProcessType_Copy, '{"SourceConnection":"Signet", "SourceTable":"dbo.tblFatcaReferences", "SourceSQL":"", "TargetTable":"SignetStaging.FatcaReferences", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'SignetStaging.FatcaReferences')
  , (@LoadType_Daily, 'SignetStaging.GroupCode', @ProcessType_Copy, '{"SourceConnection":"Signet", "SourceTable":"dbo.tblGroupCode", "SourceSQL":"", "TargetTable":"SignetStaging.GroupCode", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'SignetStaging.GroupCode')
  , (@LoadType_Daily, 'SignetStaging.LoadCarrier', @ProcessType_SP, '{"StoredProcedure":"SignetStaging.LoadCarrier", "TargetConnection":"PlacementStore"}', 'dbo.Carrier')
  , (@LoadType_Daily, 'SignetStaging.LoadCarrierAgencyRating', @ProcessType_SP, '{"StoredProcedure":"SignetStaging.LoadCarrierAgencyRating", "TargetConnection":"PlacementStore"}', 'dbo.CarrierAgencyRating')
  , (@LoadType_Daily, 'SignetStaging.LoadCarrierGroup', @ProcessType_SP, '{"StoredProcedure":"SignetStaging.LoadCarrierGroup", "TargetConnection":"PlacementStore"}', 'dbo.Carrier')
  , (@LoadType_Daily, 'SignetStaging.LoadCarrierRating', @ProcessType_SP, '{"StoredProcedure":"SignetStaging.LoadCarrierRating", "TargetConnection":"PlacementStore"}', 'dbo.CarrierRating')
  , (@LoadType_Daily, 'SignetStaging.LoadCarrierRatingOutlook', @ProcessType_SP, '{"StoredProcedure":"SignetStaging.LoadCarrierRatingOutlook", "TargetConnection":"PlacementStore"}', 'dbo.CarrierRatingOutlook')
  , (@LoadType_Daily, 'SignetStaging.LoadCarrierRestriction', @ProcessType_SP, '{"StoredProcedure":"SignetStaging.LoadCarrierRestriction", "TargetConnection":"PlacementStore"}', 'dbo.CarrierRestriction')
  , (@LoadType_Daily, 'SignetStaging.LoadCarrierRestrictionDefinition', @ProcessType_SP, '{"StoredProcedure":"SignetStaging.LoadCarrierRestrictionDefinition", "TargetConnection":"PlacementStore"}', 'dbo.CarrierRestrictionDefinition')
  , (@LoadType_Daily, 'SignetStaging.LoadCarrierStatus', @ProcessType_SP, '{"StoredProcedure":"SignetStaging.LoadCarrierStatus", "TargetConnection":"PlacementStore"}', 'dbo.CarrierStatus')
  , (@LoadType_Daily, 'SignetStaging.LoadCarrierTOBA', @ProcessType_SP, '{"StoredProcedure":"SignetStaging.LoadCarrierTOBA", "TargetConnection":"PlacementStore"}', 'dbo.CarrierTOBA')
  , (@LoadType_Daily, 'SignetStaging.LoadRatingAgency', @ProcessType_SP, '{"StoredProcedure":"SignetStaging.LoadRatingAgency", "TargetConnection":"PlacementStore"}', 'dbo.Agency')
  , (@LoadType_Daily, 'SignetStaging.PropertyType', @ProcessType_Copy, '{"SourceConnection":"Signet", "SourceTable":"dbo.tblPropertyType", "SourceSQL":"", "TargetTable":"SignetStaging.PropertyType", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'SignetStaging.PropertyType')
  , (@LoadType_Daily, 'SignetStaging.RatingAgency', @ProcessType_Copy, '{"SourceConnection":"Signet", "SourceTable":"dbo.tblRatingAgency", "SourceSQL":"", "TargetTable":"SignetStaging.RatingAgency", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'SignetStaging.RatingAgency')
  , (@LoadType_Daily, 'SignetStaging.RatingAgencyData', @ProcessType_Copy, '{"SourceConnection":"Signet", "SourceTable":"dbo.tblRatingAgencyData", "SourceSQL":"", "TargetTable":"SignetStaging.RatingAgencyData", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'SignetStaging.RatingAgencyData')
  , (@LoadType_Daily, 'SignetStaging.RatingSequence', @ProcessType_Copy, '{"SourceConnection":"Signet", "SourceTable":"dbo.tblRatingSequence", "SourceSQL":"", "TargetTable":"SignetStaging.RatingSequence", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'SignetStaging.RatingSequence')
  , (@LoadType_Daily, 'SignetStaging.RestrictionCode', @ProcessType_Copy, '{"SourceConnection":"Signet", "SourceTable":"dbo.tblRestrictionCode", "SourceSQL":"", "TargetTable":"SignetStaging.RestrictionCode", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'SignetStaging.RestrictionCode')
  , (@LoadType_Daily, 'SignetStaging.TOBA', @ProcessType_Copy, '{"SourceConnection":"Signet", "SourceTable":"dbo.tblTOBA", "SourceSQL":"", "TargetTable":"SignetStaging.TOBA", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'SignetStaging.TOBA')
  , (@LoadType_Daily, 'WIBSStaging.Load_dbo_CarrierMapping', @ProcessType_SP, '{"StoredProcedure":"WIBSStaging.Load_dbo_CarrierMapping", "TargetConnection":"PlacementStore"}', 'dbo.CarrierMapping')
  , (@LoadType_Daily, 'WIBSStaging.WIBS_PBANAMandCompPrd', @ProcessType_Copy, '{"SourceConnection":"PASStaging", "SourceTable":"WIBS.PBANAMandCompPrd", "SourceSQL":"", "TargetTable":"WIBSStaging.WIBS_PBANAMandCompPrd", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'WIBSStaging.WIBS_PBANAMandCompPrd');

INSERT INTO @Process
    (LoadTypeId /* 1 Daily, 2 Intraday, 3 Process SASS Models, 9 Dev test subset */
   , Name
   , ProcessTypeId
   , JSONConfig
   , TargetTable /* For information */
)
VALUES
    (@LoadType_Intraday, 'APIv1.PopulateAPIv1PartyAttributesTable', @ProcessType_SP, '{"StoredProcedure":"APIv1.PopulateAPIv1PartyAttributesTable", "TargetConnection":"PlacementStore"}', 'APIv1.PartyAttributesTable')
  , (@LoadType_Intraday, 'BP Load Event FMATemp.AdditionalDataItem', @ProcessType_Event, '{"TopicName":"psadf-to-psfuncapp-event/messages","TargetConnection":"AzureServiceBus","BodyTemplate":"{\"SourceTable\":\"FMATemp.AdditionalDataItem\",\"TargetTable\":\"staging.vw_AdditionalDataItem\",\"ProcessId\":@ProcessId,\"SessionGuId\":\"@SessionGuid\",\"RunGuid\":\"@RunGuid\",\"InstanceLogId\":@InstanceLogId,\"ProcessSessionId\":\"@ProcessSessionId\",\"TriggerType\":\"Event\"}","HandlerUpdatesStatus":true}', '')
  , (@LoadType_Intraday, 'BP Load Event FMATemp.AppPlacementPolicy', @ProcessType_Event, '{"TopicName":"psadf-to-psfuncapp-event/messages","TargetConnection":"AzureServiceBus","BodyTemplate":"{\"SourceTable\":\"FMATemp.AppPlacementPolicy\",\"TargetTable\":\"staging.vw_AppPlacementPolicy\",\"ProcessId\":@ProcessId,\"SessionGuId\":\"@SessionGuid\",\"RunGuid\":\"@RunGuid\",\"InstanceLogId\":@InstanceLogId,\"ProcessSessionId\":\"@ProcessSessionId\",\"TriggerType\":\"Event\"}","HandlerUpdatesStatus":true}', '')
  , (@LoadType_Intraday, 'BP Load Event FMATemp.Insured', @ProcessType_Event, '{"TopicName":"psadf-to-psfuncapp-event/messages","TargetConnection":"AzureServiceBus","BodyTemplate":"{\"SourceTable\":\"FMATemp.Insured\",\"TargetTable\":\"staging.vw_Insured_v2\",\"ProcessId\":@ProcessId,\"SessionGuId\":\"@SessionGuid\",\"RunGuid\":\"@RunGuid\",\"InstanceLogId\":@InstanceLogId,\"ProcessSessionId\":\"@ProcessSessionId\",\"TriggerType\":\"Event\"}","HandlerUpdatesStatus":true}', '')
  , (@LoadType_Intraday, 'BP Load Event FMATemp.Placement', @ProcessType_Event, '{"TopicName":"psadf-to-psfuncapp-event/messages","TargetConnection":"AzureServiceBus","BodyTemplate":"{\"SourceTable\":\"FMATemp.Placement\",\"TargetTable\":\"staging.vw_Placement_v8\",\"ProcessId\":@ProcessId,\"SessionGuId\":\"@SessionGuid\",\"RunGuid\":\"@RunGuid\",\"InstanceLogId\":@InstanceLogId,\"ProcessSessionId\":\"@ProcessSessionId\",\"TriggerType\":\"Event\"}","HandlerUpdatesStatus":true}', '')
  , (@LoadType_Intraday, 'BP Load Event FMATemp.PlacementCarrier', @ProcessType_Event, '{"TopicName":"psadf-to-psfuncapp-event/messages","TargetConnection":"AzureServiceBus","BodyTemplate":"{\"SourceTable\":\"FMATemp.PlacementCarrier\",\"TargetTable\":\"staging.vw_PlacementCarrier\",\"ProcessId\":@ProcessId,\"SessionGuId\":\"@SessionGuid\",\"RunGuid\":\"@RunGuid\",\"InstanceLogId\":@InstanceLogId,\"ProcessSessionId\":\"@ProcessSessionId\",\"TriggerType\":\"Event\"}","HandlerUpdatesStatus":true}', '')
  , (@LoadType_Intraday, 'BP Load Event FMATemp.PlacementPolicy', @ProcessType_Event, '{"TopicName":"psadf-to-psfuncapp-event/messages","TargetConnection":"AzureServiceBus","BodyTemplate":"{\"SourceTable\":\"FMATemp.PlacementPolicy\",\"TargetTable\":\"staging.vw_PlacementPolicy\",\"ProcessId\":@ProcessId,\"SessionGuId\":\"@SessionGuid\",\"RunGuid\":\"@RunGuid\",\"InstanceLogId\":@InstanceLogId,\"ProcessSessionId\":\"@ProcessSessionId\",\"TriggerType\":\"Event\"}","HandlerUpdatesStatus":true}', '')
  , (@LoadType_Intraday, 'BP Load Event FMATemp.PlacementProducts', @ProcessType_Event, '{"TopicName":"psadf-to-psfuncapp-event/messages","TargetConnection":"AzureServiceBus","BodyTemplate":"{\"SourceTable\":\"FMATemp.PlacementProducts\",\"TargetTable\":\"staging.vw_PlacementProduct\",\"ProcessId\":@ProcessId,\"SessionGuId\":\"@SessionGuid\",\"RunGuid\":\"@RunGuid\",\"InstanceLogId\":@InstanceLogId,\"ProcessSessionId\":\"@ProcessSessionId\",\"TriggerType\":\"Event\"}","HandlerUpdatesStatus":true}', '')
  , (@LoadType_Intraday, 'BP Load Event FMATemp.PlacementTeams', @ProcessType_Event, '{"TopicName":"psadf-to-psfuncapp-event/messages","TargetConnection":"AzureServiceBus","BodyTemplate":"{\"SourceTable\":\"FMATemp.PlacementTeams\",\"TargetTable\":\"staging.vw_UserGroupPlacement\",\"ProcessId\":@ProcessId,\"SessionGuId\":\"@SessionGuid\",\"RunGuid\":\"@RunGuid\",\"InstanceLogId\":@InstanceLogId,\"ProcessSessionId\":\"@ProcessSessionId\",\"TriggerType\":\"Event\"}","HandlerUpdatesStatus":true}', '')
  , (@LoadType_Intraday, 'BP Load Event FMATemp.PlacementUpdates', @ProcessType_Event, '{"TopicName":"psadf-to-psfuncapp-event/messages","TargetConnection":"AzureServiceBus","BodyTemplate":"{\"SourceTable\":\"FMATemp.PlacementUpdates\",\"TargetTable\":\"staging.vw_PlacementUpdate\",\"ProcessId\":@ProcessId,\"SessionGuId\":\"@SessionGuid\",\"RunGuid\":\"@RunGuid\",\"InstanceLogId\":@InstanceLogId,\"ProcessSessionId\":\"@ProcessSessionId\",\"TriggerType\":\"Event\"}","HandlerUpdatesStatus":true}', '')
  , (@LoadType_Intraday, 'BP Load Event FMATemp.PlacementWorkers', @ProcessType_Event, '{"TopicName":"psadf-to-psfuncapp-event/messages","TargetConnection":"AzureServiceBus","BodyTemplate":"{\"SourceTable\":\"FMATemp.PlacementWorkers\",\"TargetTable\":\"staging.vw_PlacementTeamMember_v2\",\"ProcessId\":@ProcessId,\"SessionGuId\":\"@SessionGuid\",\"RunGuid\":\"@RunGuid\",\"InstanceLogId\":@InstanceLogId,\"ProcessSessionId\":\"@ProcessSessionId\",\"TriggerType\":\"Event\"}","HandlerUpdatesStatus":true}', '')
  , (@LoadType_Intraday, 'BP Load Event FMATemp.Policy', @ProcessType_Event, '{"TopicName":"psadf-to-psfuncapp-event/messages","TargetConnection":"AzureServiceBus","BodyTemplate":"{\"SourceTable\":\"FMATemp.Policy\",\"TargetTable\":\"staging.vw_Policy_v7\",\"ProcessId\":@ProcessId,\"SessionGuId\":\"@SessionGuid\",\"RunGuid\":\"@RunGuid\",\"InstanceLogId\":@InstanceLogId,\"ProcessSessionId\":\"@ProcessSessionId\",\"TriggerType\":\"Event\"}","HandlerUpdatesStatus":true}', '')
  , (@LoadType_Intraday, 'BP Load Event FMATemp.PolicyCarrier', @ProcessType_Event, '{"TopicName":"psadf-to-psfuncapp-event/messages","TargetConnection":"AzureServiceBus","BodyTemplate":"{\"SourceTable\":\"FMATemp.PolicyCarrier\",\"TargetTable\":\"staging.vw_PolicyCarrier_v2\",\"ProcessId\":@ProcessId,\"SessionGuId\":\"@SessionGuid\",\"RunGuid\":\"@RunGuid\",\"InstanceLogId\":@InstanceLogId,\"ProcessSessionId\":\"@ProcessSessionId\",\"TriggerType\":\"Event\"}","HandlerUpdatesStatus":true}', '')
  , (@LoadType_Intraday, 'BP Load Event FMATemp.PolicyProduct', @ProcessType_Event, '{"TopicName":"psadf-to-psfuncapp-event/messages","TargetConnection":"AzureServiceBus","BodyTemplate":"{\"SourceTable\":\"FMATemp.PolicyProduct\",\"TargetTable\":\"staging.vw_PolicyProduct\",\"ProcessId\":@ProcessId,\"SessionGuId\":\"@SessionGuid\",\"RunGuid\":\"@RunGuid\",\"InstanceLogId\":@InstanceLogId,\"ProcessSessionId\":\"@ProcessSessionId\",\"TriggerType\":\"Event\"}","HandlerUpdatesStatus":true}', '')
  , (@LoadType_Intraday, 'BP Load Event FMATemp.staging_vw_Agency', @ProcessType_Event, '{"TopicName":"psadf-to-psfuncapp-event/messages","TargetConnection":"AzureServiceBus","BodyTemplate":"{\"SourceTable\":\"FMATemp.staging_vw_Agency\",\"TargetTable\":\"staging.vw_Agency\",\"ProcessId\":@ProcessId,\"SessionGuId\":\"@SessionGuid\",\"RunGuid\":\"@RunGuid\",\"InstanceLogId\":@InstanceLogId,\"ProcessSessionId\":\"@ProcessSessionId\",\"TriggerType\":\"Event\"}","HandlerUpdatesStatus":true}', '')
  , (@LoadType_Intraday, 'BP Load Event FMATemp.staging_vw_CarrierAdditionalData', @ProcessType_Event, '{"TopicName":"psadf-to-psfuncapp-event/messages","TargetConnection":"AzureServiceBus","BodyTemplate":"{\"SourceTable\":\"FMATemp.staging_vw_CarrierAdditionalData\",\"TargetTable\":\"staging.vw_CarrierAdditionalData\",\"ProcessId\":@ProcessId,\"SessionGuId\":\"@SessionGuid\",\"RunGuid\":\"@RunGuid\",\"InstanceLogId\":@InstanceLogId,\"ProcessSessionId\":\"@ProcessSessionId\",\"TriggerType\":\"Event\"}","HandlerUpdatesStatus":true}', '')
  , (@LoadType_Intraday, 'BP Load Event FMATemp.staging_vw_CarrierAgency', @ProcessType_Event, '{"TopicName":"psadf-to-psfuncapp-event/messages","TargetConnection":"AzureServiceBus","BodyTemplate":"{\"SourceTable\":\"FMATemp.staging_vw_CarrierAgency\",\"TargetTable\":\"staging.vw_CarrierAgency\",\"ProcessId\":@ProcessId,\"SessionGuId\":\"@SessionGuid\",\"RunGuid\":\"@RunGuid\",\"InstanceLogId\":@InstanceLogId,\"ProcessSessionId\":\"@ProcessSessionId\",\"TriggerType\":\"Event\"}","HandlerUpdatesStatus":true}', '')
  , (@LoadType_Intraday, 'BP Load Event FMATemp.staging_vw_CarrierAgencyRating', @ProcessType_Event, '{"TopicName":"psadf-to-psfuncapp-event/messages","TargetConnection":"AzureServiceBus","BodyTemplate":"{\"SourceTable\":\"FMATemp.staging_vw_CarrierAgencyRating\",\"TargetTable\":\"staging.vw_CarrierAgencyRating_v1\",\"ProcessId\":@ProcessId,\"SessionGuId\":\"@SessionGuid\",\"RunGuid\":\"@RunGuid\",\"InstanceLogId\":@InstanceLogId,\"ProcessSessionId\":\"@ProcessSessionId\",\"TriggerType\":\"Event\"}","HandlerUpdatesStatus":true}', '')
  , (@LoadType_Intraday, 'BP Load Event FMATemp.staging_vw_CarrierExtended', @ProcessType_Event, '{"TopicName":"psadf-to-psfuncapp-event/messages","TargetConnection":"AzureServiceBus","BodyTemplate":"{\"SourceTable\":\"FMATemp.staging_vw_CarrierExtended\",\"TargetTable\":\"staging.vw_Carrier_v8\",\"ProcessId\":@ProcessId,\"SessionGuId\":\"@SessionGuid\",\"RunGuid\":\"@RunGuid\",\"InstanceLogId\":@InstanceLogId,\"ProcessSessionId\":\"@ProcessSessionId\",\"TriggerType\":\"Event\"}","HandlerUpdatesStatus":true}', '')
  , (@LoadType_Intraday, 'BP Load Event FMATemp.staging_vw_CarrierQIRating', @ProcessType_Event, '{"TopicName":"psadf-to-psfuncapp-event/messages","TargetConnection":"AzureServiceBus","BodyTemplate":"{\"SourceTable\":\"FMATemp.staging_vw_CarrierQIRating\",\"TargetTable\":\"staging.vw_CarrierQIRating\",\"ProcessId\":@ProcessId,\"SessionGuId\":\"@SessionGuid\",\"RunGuid\":\"@RunGuid\",\"InstanceLogId\":@InstanceLogId,\"ProcessSessionId\":\"@ProcessSessionId\",\"TriggerType\":\"Event\"}","HandlerUpdatesStatus":true}', '')
  , (@LoadType_Intraday, 'BP Load Event FMATemp.staging_vw_CarrierRating', @ProcessType_Event, '{"TopicName":"psadf-to-psfuncapp-event/messages","TargetConnection":"AzureServiceBus","BodyTemplate":"{\"SourceTable\":\"FMATemp.staging_vw_CarrierRating\",\"TargetTable\":\"staging.vw_CarrierRating\",\"ProcessId\":@ProcessId,\"SessionGuId\":\"@SessionGuid\",\"RunGuid\":\"@RunGuid\",\"InstanceLogId\":@InstanceLogId,\"ProcessSessionId\":\"@ProcessSessionId\",\"TriggerType\":\"Event\"}","HandlerUpdatesStatus":true}', '')
  , (@LoadType_Intraday, 'BP Load Event FMATemp.staging_vw_CarrierRatingOutlookDefinition', @ProcessType_Event, '{"TopicName":"psadf-to-psfuncapp-event/messages","TargetConnection":"AzureServiceBus","BodyTemplate":"{\"SourceTable\":\"FMATemp.staging_vw_CarrierRatingOutlookDefinition\",\"TargetTable\":\"staging.vw_CarrierRatingOutlookDefinition\",\"ProcessId\":@ProcessId,\"SessionGuId\":\"@SessionGuid\",\"RunGuid\":\"@RunGuid\",\"InstanceLogId\":@InstanceLogId,\"ProcessSessionId\":\"@ProcessSessionId\",\"TriggerType\":\"Event\"}","HandlerUpdatesStatus":true}', '')
  , (@LoadType_Intraday, 'BP Load Event FMATemp.staging_vw_CarrierRelationship', @ProcessType_Event, '{"TopicName":"psadf-to-psfuncapp-event/messages","TargetConnection":"AzureServiceBus","BodyTemplate":"{\"SourceTable\":\"FMATemp.staging_vw_CarrierRelationship\",\"TargetTable\":\"staging.vw_CarrierRelationship\",\"ProcessId\":@ProcessId,\"SessionGuId\":\"@SessionGuid\",\"RunGuid\":\"@RunGuid\",\"InstanceLogId\":@InstanceLogId,\"ProcessSessionId\":\"@ProcessSessionId\",\"TriggerType\":\"Event\"}","HandlerUpdatesStatus":true}', '')
  , (@LoadType_Intraday, 'BP Load Event FMATemp.staging_vw_CarrierRestriction', @ProcessType_Event, '{"TopicName":"psadf-to-psfuncapp-event/messages","TargetConnection":"AzureServiceBus","BodyTemplate":"{\"SourceTable\":\"FMATemp.staging_vw_CarrierRestriction\",\"TargetTable\":\"staging.vw_CarrierRestriction_v2\",\"ProcessId\":@ProcessId,\"SessionGuId\":\"@SessionGuid\",\"RunGuid\":\"@RunGuid\",\"InstanceLogId\":@InstanceLogId,\"ProcessSessionId\":\"@ProcessSessionId\",\"TriggerType\":\"Event\"}","HandlerUpdatesStatus":true}', '')
  , (@LoadType_Intraday, 'BP Load Event FMATemp.staging_vw_CarrierRestrictionDefinition', @ProcessType_Event, '{"TopicName":"psadf-to-psfuncapp-event/messages","TargetConnection":"AzureServiceBus","BodyTemplate":"{\"SourceTable\":\"FMATemp.staging_vw_CarrierRestrictionDefinition\",\"TargetTable\":\"staging.vw_CarrierRestrictionDefinition\",\"ProcessId\":@ProcessId,\"SessionGuId\":\"@SessionGuid\",\"RunGuid\":\"@RunGuid\",\"InstanceLogId\":@InstanceLogId,\"ProcessSessionId\":\"@ProcessSessionId\",\"TriggerType\":\"Event\"}","HandlerUpdatesStatus":true}', '')
  , (@LoadType_Intraday, 'BP Load Event FMATemp.staging_vw_CarrierStatus', @ProcessType_Event, '{"TopicName":"psadf-to-psfuncapp-event/messages","TargetConnection":"AzureServiceBus","BodyTemplate":"{\"SourceTable\":\"FMATemp.staging_vw_CarrierStatus\",\"TargetTable\":\"staging.vw_CarrierStatus\",\"ProcessId\":@ProcessId,\"SessionGuId\":\"@SessionGuid\",\"RunGuid\":\"@RunGuid\",\"InstanceLogId\":@InstanceLogId,\"ProcessSessionId\":\"@ProcessSessionId\",\"TriggerType\":\"Event\"}","HandlerUpdatesStatus":true}', '')
  , (@LoadType_Intraday, 'BP Load Event FMATemp.staging_vw_CarrierStatusOverride', @ProcessType_Event, '{"TopicName":"psadf-to-psfuncapp-event/messages","TargetConnection":"AzureServiceBus","BodyTemplate":"{\"SourceTable\":\"FMATemp.staging_vw_CarrierStatusOverride\",\"TargetTable\":\"staging.vw_CarrierStatusOverride\",\"ProcessId\":@ProcessId,\"SessionGuId\":\"@SessionGuid\",\"RunGuid\":\"@RunGuid\",\"InstanceLogId\":@InstanceLogId,\"ProcessSessionId\":\"@ProcessSessionId\",\"TriggerType\":\"Event\"}","HandlerUpdatesStatus":true}', '')
  , (@LoadType_Intraday, 'BP Load Event FMATemp.staging_vw_CarrierTOBA', @ProcessType_Event, '{"TopicName":"psadf-to-psfuncapp-event/messages","TargetConnection":"AzureServiceBus","BodyTemplate":"{\"SourceTable\":\"FMATemp.staging_vw_CarrierTOBA\",\"TargetTable\":\"staging.vw_CarrierTOBA\",\"ProcessId\":@ProcessId,\"SessionGuId\":\"@SessionGuid\",\"RunGuid\":\"@RunGuid\",\"InstanceLogId\":@InstanceLogId,\"ProcessSessionId\":\"@ProcessSessionId\",\"TriggerType\":\"Event\"}","HandlerUpdatesStatus":true}', '')
  , (@LoadType_Intraday, 'BP Load Event FMATemp.staging_vw_CarrierType', @ProcessType_Event, '{"TopicName":"psadf-to-psfuncapp-event/messages","TargetConnection":"AzureServiceBus","BodyTemplate":"{\"SourceTable\":\"FMATemp.staging_vw_CarrierType\",\"TargetTable\":\"staging.vw_CarrierType\",\"ProcessId\":@ProcessId,\"SessionGuId\":\"@SessionGuid\",\"RunGuid\":\"@RunGuid\",\"InstanceLogId\":@InstanceLogId,\"ProcessSessionId\":\"@ProcessSessionId\",\"TriggerType\":\"Event\"}","HandlerUpdatesStatus":true}', '')
  , (@LoadType_Intraday, 'BP Load Event FMATemp.staging_vw_Country', @ProcessType_Event, '{"TopicName":"psadf-to-psfuncapp-event/messages","TargetConnection":"AzureServiceBus","BodyTemplate":"{\"SourceTable\":\"FMATemp.staging_vw_Country\",\"TargetTable\":\"staging.vw_Country\",\"ProcessId\":@ProcessId,\"SessionGuId\":\"@SessionGuid\",\"RunGuid\":\"@RunGuid\",\"InstanceLogId\":@InstanceLogId,\"ProcessSessionId\":\"@ProcessSessionId\",\"TriggerType\":\"Event\"}","HandlerUpdatesStatus":true}', '')
  , (@LoadType_Intraday, 'BP Load Event FMATemp.staging_vw_CountrySubdivision', @ProcessType_Event, '{"TopicName":"psadf-to-psfuncapp-event/messages","TargetConnection":"AzureServiceBus","BodyTemplate":"{\"SourceTable\":\"FMATemp.staging_vw_CountrySubdivision\",\"TargetTable\":\"staging.vw_CountrySubdivision\",\"ProcessId\":@ProcessId,\"SessionGuId\":\"@SessionGuid\",\"RunGuid\":\"@RunGuid\",\"InstanceLogId\":@InstanceLogId,\"ProcessSessionId\":\"@ProcessSessionId\",\"TriggerType\":\"Event\"}","HandlerUpdatesStatus":true}', '')
  , (@LoadType_Intraday, 'BP Load Event FMATemp.staging_vw_Currency', @ProcessType_Event, '{"TopicName":"psadf-to-psfuncapp-event/messages","TargetConnection":"AzureServiceBus","BodyTemplate":"{\"SourceTable\":\"FMATemp.staging_vw_Currency\",\"TargetTable\":\"staging.vw_Currency\",\"ProcessId\":@ProcessId,\"SessionGuId\":\"@SessionGuid\",\"RunGuid\":\"@RunGuid\",\"InstanceLogId\":@InstanceLogId,\"ProcessSessionId\":\"@ProcessSessionId\",\"TriggerType\":\"Event\"}","HandlerUpdatesStatus":true}', '')
  , (@LoadType_Intraday, 'BP Load Event FMATemp.staging_vw_Facility', @ProcessType_Event, '{"TopicName":"psadf-to-psfuncapp-event/messages","TargetConnection":"AzureServiceBus","BodyTemplate":"{\"SourceTable\":\"FMATemp.staging_vw_Facility\",\"TargetTable\":\"staging.vw_Facility_v3\",\"ProcessId\":@ProcessId,\"SessionGuId\":\"@SessionGuid\",\"RunGuid\":\"@RunGuid\",\"InstanceLogId\":@InstanceLogId,\"ProcessSessionId\":\"@ProcessSessionId\",\"TriggerType\":\"Event\"}","HandlerUpdatesStatus":true}', '')
  , (@LoadType_Intraday, 'BP Load Event FMATemp.staging_vw_FacilityCarrier', @ProcessType_Event, '{"TopicName":"psadf-to-psfuncapp-event/messages","TargetConnection":"AzureServiceBus","BodyTemplate":"{\"SourceTable\":\"FMATemp.staging_vw_FacilityCarrier\",\"TargetTable\":\"staging.vw_FacilityCarrier\",\"ProcessId\":@ProcessId,\"SessionGuId\":\"@SessionGuid\",\"RunGuid\":\"@RunGuid\",\"InstanceLogId\":@InstanceLogId,\"ProcessSessionId\":\"@ProcessSessionId\",\"TriggerType\":\"Event\"}","HandlerUpdatesStatus":true}', '')
  , (@LoadType_Intraday, 'BP Load Event FMATemp.staging_vw_FacilitySection', @ProcessType_Event, '{"TopicName":"psadf-to-psfuncapp-event/messages","TargetConnection":"AzureServiceBus","BodyTemplate":"{\"SourceTable\":\"FMATemp.staging_vw_FacilitySection\",\"TargetTable\":\"staging.vw_FacilitySection\",\"ProcessId\":@ProcessId,\"SessionGuId\":\"@SessionGuid\",\"RunGuid\":\"@RunGuid\",\"InstanceLogId\":@InstanceLogId,\"ProcessSessionId\":\"@ProcessSessionId\",\"TriggerType\":\"Event\"}","HandlerUpdatesStatus":true}', '')
  , (@LoadType_Intraday, 'BP Load Event FMATemp.staging_vw_FacilitySectionCarrier', @ProcessType_Event, '{"TopicName":"psadf-to-psfuncapp-event/messages","TargetConnection":"AzureServiceBus","BodyTemplate":"{\"SourceTable\":\"FMATemp.staging_vw_FacilitySectionCarrier\",\"TargetTable\":\"staging.vw_FacilitySectionCarrier\",\"ProcessId\":@ProcessId,\"SessionGuId\":\"@SessionGuid\",\"RunGuid\":\"@RunGuid\",\"InstanceLogId\":@InstanceLogId,\"ProcessSessionId\":\"@ProcessSessionId\",\"TriggerType\":\"Event\"}","HandlerUpdatesStatus":true}', '')
  , (@LoadType_Intraday, 'BP Load Event FMATemp.staging_vw_Industry', @ProcessType_Event, '{"TopicName":"psadf-to-psfuncapp-event/messages","TargetConnection":"AzureServiceBus","BodyTemplate":"{\"SourceTable\":\"FMATemp.staging_vw_Industry\",\"TargetTable\":\"staging.vw_Industry_v3\",\"ProcessId\":@ProcessId,\"SessionGuId\":\"@SessionGuid\",\"RunGuid\":\"@RunGuid\",\"InstanceLogId\":@InstanceLogId,\"ProcessSessionId\":\"@ProcessSessionId\",\"TriggerType\":\"Event\"}","HandlerUpdatesStatus":true}', '')
  , (@LoadType_Intraday, 'BP Load Event FMATemp.staging_vw_Lookup', @ProcessType_Event, '{"TopicName":"psadf-to-psfuncapp-event/messages","TargetConnection":"AzureServiceBus","BodyTemplate":"{\"SourceTable\":\"FMATemp.staging_vw_Lookup\",\"TargetTable\":\"staging.vw_Lookup\",\"ProcessId\":@ProcessId,\"SessionGuId\":\"@SessionGuid\",\"RunGuid\":\"@RunGuid\",\"InstanceLogId\":@InstanceLogId,\"ProcessSessionId\":\"@ProcessSessionId\",\"TriggerType\":\"Event\"}","HandlerUpdatesStatus":true}', '')
  , (@LoadType_Intraday, 'BP Load Event FMATemp.staging_vw_LookupGroup', @ProcessType_Event, '{"TopicName":"psadf-to-psfuncapp-event/messages","TargetConnection":"AzureServiceBus","BodyTemplate":"{\"SourceTable\":\"FMATemp.staging_vw_LookupGroup\",\"TargetTable\":\"staging.vw_LookupGroup\",\"ProcessId\":@ProcessId,\"SessionGuId\":\"@SessionGuid\",\"RunGuid\":\"@RunGuid\",\"InstanceLogId\":@InstanceLogId,\"ProcessSessionId\":\"@ProcessSessionId\",\"TriggerType\":\"Event\"}","HandlerUpdatesStatus":true}', '')
  , (@LoadType_Intraday, 'BP Load Event FMATemp.staging_vw_MaxPolicyPeriod', @ProcessType_Event, '{"TopicName":"psadf-to-psfuncapp-event/messages","TargetConnection":"AzureServiceBus","BodyTemplate":"{\"SourceTable\":\"FMATemp.staging_vw_MaxPolicyPeriod\",\"TargetTable\":\"staging.vw_MaxPolicyPeriod\",\"ProcessId\":@ProcessId,\"SessionGuId\":\"@SessionGuid\",\"RunGuid\":\"@RunGuid\",\"InstanceLogId\":@InstanceLogId,\"ProcessSessionId\":\"@ProcessSessionId\",\"TriggerType\":\"Event\"}","HandlerUpdatesStatus":true}', '')
  , (@LoadType_Intraday, 'BP Load Event FMATemp.staging_vw_Office', @ProcessType_Event, '{"TopicName":"psadf-to-psfuncapp-event/messages","TargetConnection":"AzureServiceBus","BodyTemplate":"{\"SourceTable\":\"FMATemp.staging_vw_Office\",\"TargetTable\":\"staging.vw_Office\",\"ProcessId\":@ProcessId,\"SessionGuId\":\"@SessionGuid\",\"RunGuid\":\"@RunGuid\",\"InstanceLogId\":@InstanceLogId,\"ProcessSessionId\":\"@ProcessSessionId\",\"TriggerType\":\"Event\"}","HandlerUpdatesStatus":true}', '')
  , (@LoadType_Intraday, 'BP Load Event FMATemp.staging_vw_OfficeAddress', @ProcessType_Event, '{"TopicName":"psadf-to-psfuncapp-event/messages","TargetConnection":"AzureServiceBus","BodyTemplate":"{\"SourceTable\":\"FMATemp.staging_vw_OfficeAddress\",\"TargetTable\":\"staging.vw_OfficeAddress_v1\",\"ProcessId\":@ProcessId,\"SessionGuId\":\"@SessionGuid\",\"RunGuid\":\"@RunGuid\",\"InstanceLogId\":@InstanceLogId,\"ProcessSessionId\":\"@ProcessSessionId\",\"TriggerType\":\"Event\"}","HandlerUpdatesStatus":true}', '')
  , (@LoadType_Intraday, 'BP Load Event FMATemp.staging_vw_Party', @ProcessType_Event, '{"TopicName":"psadf-to-psfuncapp-event/messages","TargetConnection":"AzureServiceBus","BodyTemplate":"{\"SourceTable\":\"FMATemp.staging_vw_Party\",\"TargetTable\":\"staging.vw_Party_v5\",\"ProcessId\":@ProcessId,\"SessionGuId\":\"@SessionGuid\",\"RunGuid\":\"@RunGuid\",\"InstanceLogId\":@InstanceLogId,\"ProcessSessionId\":\"@ProcessSessionId\",\"TriggerType\":\"Event\"}","HandlerUpdatesStatus":true}', '')
  , (@LoadType_Intraday, 'BP Load Event FMATemp.staging_vw_PartyAddress', @ProcessType_Event, '{"TopicName":"psadf-to-psfuncapp-event/messages","TargetConnection":"AzureServiceBus","BodyTemplate":"{\"SourceTable\":\"FMATemp.staging_vw_PartyAddress\",\"TargetTable\":\"staging.vw_PartyAddress\",\"ProcessId\":@ProcessId,\"SessionGuId\":\"@SessionGuid\",\"RunGuid\":\"@RunGuid\",\"InstanceLogId\":@InstanceLogId,\"ProcessSessionId\":\"@ProcessSessionId\",\"TriggerType\":\"Event\"}","HandlerUpdatesStatus":true}', '')
  , (@LoadType_Intraday, 'BP Load Event FMATemp.staging_vw_PolicyStatus', @ProcessType_Event, '{"TopicName":"psadf-to-psfuncapp-event/messages","TargetConnection":"AzureServiceBus","BodyTemplate":"{\"SourceTable\":\"FMATemp.staging_vw_PolicyStatus\",\"TargetTable\":\"staging.vw_PolicyStatus\",\"ProcessId\":@ProcessId,\"SessionGuId\":\"@SessionGuid\",\"RunGuid\":\"@RunGuid\",\"InstanceLogId\":@InstanceLogId,\"ProcessSessionId\":\"@ProcessSessionId\",\"TriggerType\":\"Event\"}","HandlerUpdatesStatus":true}', '')
  , (@LoadType_Intraday, 'BP Load Event FMATemp.staging_vw_Product', @ProcessType_Event, '{"TopicName":"psadf-to-psfuncapp-event/messages","TargetConnection":"AzureServiceBus","BodyTemplate":"{\"SourceTable\":\"FMATemp.staging_vw_Product\",\"TargetTable\":\"staging.vw_Product_v2\",\"ProcessId\":@ProcessId,\"SessionGuId\":\"@SessionGuid\",\"RunGuid\":\"@RunGuid\",\"InstanceLogId\":@InstanceLogId,\"ProcessSessionId\":\"@ProcessSessionId\",\"TriggerType\":\"Event\"}","HandlerUpdatesStatus":true}', '')
  , (@LoadType_Intraday, 'BP Load Event FMATemp.staging_vw_ReferenceDataProductMapping', @ProcessType_Event, '{"TopicName":"psadf-to-psfuncapp-event/messages","TargetConnection":"AzureServiceBus","BodyTemplate":"{\"SourceTable\":\"FMATemp.staging_vw_ReferenceDataProductMapping\",\"TargetTable\":\"staging.vw_ReferenceDataProductMapping\",\"ProcessId\":@ProcessId,\"SessionGuId\":\"@SessionGuid\",\"RunGuid\":\"@RunGuid\",\"InstanceLogId\":@InstanceLogId,\"ProcessSessionId\":\"@ProcessSessionId\",\"TriggerType\":\"Event\"}","HandlerUpdatesStatus":true}', '')
  , (@LoadType_Intraday, 'BP Load Event FMATemp.staging_vw_ServicingPlatform', @ProcessType_Event, '{"TopicName":"psadf-to-psfuncapp-event/messages","TargetConnection":"AzureServiceBus","BodyTemplate":"{\"SourceTable\":\"FMATemp.staging_vw_ServicingPlatform\",\"TargetTable\":\"staging.vw_ServicingPlatform_v1\",\"ProcessId\":@ProcessId,\"SessionGuId\":\"@SessionGuid\",\"RunGuid\":\"@RunGuid\",\"InstanceLogId\":@InstanceLogId,\"ProcessSessionId\":\"@ProcessSessionId\",\"TriggerType\":\"Event\"}","HandlerUpdatesStatus":true}', '')
  , (@LoadType_Intraday, 'BP Load Event FMATemp.staging_vw_Translation', @ProcessType_Event, '{"TopicName":"psadf-to-psfuncapp-event/messages","TargetConnection":"AzureServiceBus","BodyTemplate":"{\"SourceTable\":\"FMATemp.staging_vw_Translation\",\"TargetTable\":\"staging.vw_Translation\",\"ProcessId\":@ProcessId,\"SessionGuId\":\"@SessionGuid\",\"RunGuid\":\"@RunGuid\",\"InstanceLogId\":@InstanceLogId,\"ProcessSessionId\":\"@ProcessSessionId\",\"TriggerType\":\"Event\"}","HandlerUpdatesStatus":true}', '')
  , (@LoadType_Intraday, 'BP Load Event FMATemp.staging_vw_User', @ProcessType_Event, '{"TopicName":"psadf-to-psfuncapp-event/messages","TargetConnection":"AzureServiceBus","BodyTemplate":"{\"SourceTable\":\"FMATemp.staging_vw_User\",\"TargetTable\":\"staging.vw_User\",\"ProcessId\":@ProcessId,\"SessionGuId\":\"@SessionGuid\",\"RunGuid\":\"@RunGuid\",\"InstanceLogId\":@InstanceLogId,\"ProcessSessionId\":\"@ProcessSessionId\",\"TriggerType\":\"Event\"}","HandlerUpdatesStatus":true}', '')
  , (@LoadType_Intraday, 'BPStaging.ActionValidationRule', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_ActionValidationRule_Export", "SourceSQL":"", "TargetTable":"BPStaging.ActionValidationRule", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'BPStaging.ActionValidationRule')
  , (@LoadType_Intraday, 'BPStaging.AdditionalDataItem', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_AdditionalData_Export_v2", "SourceSQL":"", "TargetTable":"BPStaging.AdditionalDataItem", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "SystemVersioned":true, "IncrementalUpdatedDateColumn":"ValidFrom", "AlwaysFullLoad":false }', 'BPStaging.AdditionalDataItem')
  , (@LoadType_Intraday, 'BPStaging.AdjustmentResponseElement', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_AdjustmentResponseElement_Export_v1", "SourceSQL":"", "TargetTable":"BPStaging.AdjustmentResponseElement", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "SystemVersioned":true, "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ValidFrom"}', 'BPStaging.AdjustmentResponseElement')
  , (@LoadType_Intraday, 'BPStaging.AdjustmentResponseGroup', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_AdjustmentResponseGroup_Export", "SourceSQL":"", "TargetTable":"BPStaging.AdjustmentResponseGroup", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "SystemVersioned":true, "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ValidFrom"}', 'BPStaging.AdjustmentResponseGroup')
  , (@LoadType_Intraday, 'BPStaging.AffirmationQuestion', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_AffirmationQuestion_Export", "SourceSQL":"", "TargetTable":"BPStaging.AffirmationQuestion", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "SystemVersioned":true, "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ValidFrom"}', 'BPStaging.AffirmationQuestion')
  , (@LoadType_Intraday, 'BPStaging.AffirmationQuestionSet', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_AffirmationQuestionSet_Export", "SourceSQL":"", "TargetTable":"BPStaging.AffirmationQuestionSet", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "SystemVersioned":true, "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ValidFrom"}', 'BPStaging.AffirmationQuestionSet')
  , (@LoadType_Intraday, 'BPStaging.AppetiteLevel', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_AppetiteLevel_Export", "SourceSQL":"", "TargetTable":"BPStaging.AppetiteLevel", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'BPStaging.AppetiteLevel')
  , (@LoadType_Intraday, 'BPStaging.AppetiteNarrative', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_AppetiteNarrative_Export", "SourceSQL":"", "TargetTable":"BPStaging.AppetiteNarrative", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'BPStaging.AppetiteNarrative')
  , (@LoadType_Intraday, 'BPStaging.AppraisalType', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_AppraisalType_Export", "SourceSQL":"", "TargetTable":"BPStaging.AppraisalType", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ValidFrom"}', 'BPStaging.AppraisalType')
  , (@LoadType_Intraday, 'BPStaging.AutoFollowMarketEligibilityRule', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_AutoFollowMarketEligibilityRule_Export", "SourceSQL":"", "TargetTable":"BPStaging.AutoFollowMarketEligibilityRule", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "SystemVersioned":true, "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ValidFrom"}', 'BPStaging.AutoFollowMarketEligibilityRule')
  , (@LoadType_Intraday, 'BPStaging.BasisOfCover', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.BasisOfCover_Export", "SourceSQL":"", "TargetTable":"BPStaging.BasisOfCover", "TruncateTargetTable": true, "TargetConnection":"PlacementStore","AlwaysFullLoad":true}', 'BPStaging.BasisOfCover')
  , (@LoadType_Intraday, 'BPStaging.BespokeHeadingReason', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_BespokeHeadingReason_Export", "SourceSQL":"", "TargetTable":"BPStaging.BespokeHeadingReason", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'BPStaging.BespokeHeadingReason')
  , (@LoadType_Intraday, 'BPStaging.BoundPosition', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_BoundPosition_Export", "SourceSQL":"", "TargetTable":"BPStaging.BoundPosition", "TruncateTargetTable": true, "TargetConnection":"PlacementStore","SystemVersioned":true, "AlwaysFullLoad":false,"IncrementalUpdatedDateColumn":"ValidFrom"}', 'BPStaging.BoundPosition')
  , (@LoadType_Intraday, 'BPStaging.BoundPositionPlacementPolicy', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_BoundPositionPlacementPolicy_Export", "SourceSQL":"", "TargetTable":"BPStaging.BoundPositionPlacementPolicy", "TruncateTargetTable": true, "TargetConnection":"PlacementStore" ,"SystemVersioned":true, "AlwaysFullLoad":false,"IncrementalUpdatedDateColumn":"ValidFrom"}', 'BPStaging.BoundPositionPlacementPolicy')
  , (@LoadType_Intraday, 'BPStaging.BoundPositionType', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_BoundPositionType_Export", "SourceSQL":"", "TargetTable":"BPStaging.BoundPositionType", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'BPStaging.BoundPositionType')
  , (@LoadType_Intraday, 'BPStaging.BrokingSegment', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_BrokingSegment_Export", "SourceSQL":"", "TargetTable":"BPStaging.BrokingSegment", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate"}', 'BPStaging.BrokingSegment')
  , (@LoadType_Intraday, 'BPStaging.BrokingSubSegment', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_BrokingSubSegment_Export", "SourceSQL":"", "TargetTable":"BPStaging.BrokingSubSegment", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'BPStaging.BrokingSubSegment')
  , (@LoadType_Intraday, 'BPStaging.CarrierResponse', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_CarrierResponse_Export", "SourceSQL":"", "TargetTable":"BPStaging.CarrierResponse", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":true }', 'BPStaging.CarrierResponse')
  , (@LoadType_Intraday, 'BPStaging.ClientProfile', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_ClientProfile_Export", "SourceSQL":"", "TargetTable":"BPStaging.ClientProfile", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":true }', 'BPStaging.ClientProfile')
  , (@LoadType_Intraday, 'BPStaging.ClientProposal', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_ClientProposal_Export_v2", "SourceSQL":"", "TargetTable":"BPStaging.ClientProposal", "TruncateTargetTable": true, "TargetConnection":"PlacementStore","SystemVersioned":true, "AlwaysFullLoad":false,"IncrementalUpdatedDateColumn":"ValidFrom"}', 'BPStaging.ClientProposal')
  , (@LoadType_Intraday, 'BPStaging.ClientProposalCoverage', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_ClientProposalCoverage_Export_v2", "SourceSQL":"", "TargetTable":"BPStaging.ClientProposalCoverage", "TruncateTargetTable": true, "TargetConnection":"PlacementStore","SystemVersioned":true, "AlwaysFullLoad":false ,"IncrementalUpdatedDateColumn":"ValidFrom"}', 'BPStaging.ClientProposalCoverage')
  , (@LoadType_Intraday, 'BPStaging.ClientProposalCoverageNotice', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_ClientProposalCoverageNotice_Export_v2", "SourceSQL":"", "TargetTable":"BPStaging.ClientProposalCoverageNotice", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "SystemVersioned":true, "AlwaysFullLoad":false,"IncrementalUpdatedDateColumn":"ValidFrom"}', 'BPStaging.ClientProposalCoverageNotice')
  , (@LoadType_Intraday, 'BPStaging.ClientProposalPlacement', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_ClientProposalPlacement_Export_v2", "SourceSQL":"", "TargetTable":"BPStaging.ClientProposalPlacement", "TruncateTargetTable": true, "TargetConnection":"PlacementStore","SystemVersioned":true, "AlwaysFullLoad":false,"IncrementalUpdatedDateColumn":"ValidFrom"}', 'BPStaging.ClientProposalPlacement')
  , (@LoadType_Intraday, 'BPStaging.ClientProposalQuoteComparison', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_ClientProposalQuoteComparison_Export_v2", "SourceSQL":"", "TargetTable":"BPStaging.ClientProposalQuoteComparison", "TruncateTargetTable": true, "TargetConnection":"PlacementStore","SystemVersioned":true, "AlwaysFullLoad":false ,"IncrementalUpdatedDateColumn":"ValidFrom"}', 'BPStaging.ClientProposalQuoteComparison')
  , (@LoadType_Intraday, 'BPStaging.Contract', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_Contract_Export_v3", "SourceSQL":"", "TargetTable":"BPStaging.Contract", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "SystemVersioned":true, "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ValidFrom"}', 'BPStaging.Contract')
  , (@LoadType_Intraday, 'BPStaging.ContractDocumentBase', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_ContractDocumentBase_Export_v2", "SourceSQL":"", "TargetTable":"BPStaging.ContractDocumentBase", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false ,"SystemVersioned":true,"IncrementalUpdatedDateColumn":"ValidFrom"}', 'BPStaging.ContractDocumentBase')
  , (@LoadType_Intraday, 'BPStaging.ContractDocumentElement', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_ContractDocumentElement_Export", "SourceSQL":"", "TargetTable":"BPStaging.ContractDocumentElement", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":true }', 'BPStaging.ContractDocumentElement')
  , (@LoadType_Intraday, 'BPStaging.ContractDocumentVersion', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_ContractDocumentVersion_Export", "SourceSQL":"", "TargetTable":"BPStaging.ContractDocumentVersion", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "SystemVersioned":true, "AlwaysFullLoad":false,"IncrementalUpdatedDateColumn":"ValidFrom"}', 'BPStaging.ContractDocumentVersion')
  , (@LoadType_Intraday, 'BPStaging.ContractEndorsement', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_ContractEndorsement_Export", "SourceSQL":"", "TargetTable":"BPStaging.ContractEndorsement", "TruncateTargetTable": true, "TargetConnection":"PlacementStore" , "SystemVersioned":true, "AlwaysFullLoad":false,"IncrementalUpdatedDateColumn":"ValidFrom"}', 'BPStaging.ContractEndorsement')
  , (@LoadType_Intraday, 'BPStaging.ContractEndorsementDocumentElement', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_ContractEndorsementDocumentElement_Export", "SourceSQL":"", "TargetTable":"BPStaging.ContractEndorsementDocumentElement", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":true, "IncrementalUpdatedDateColumn":"ValidFrom"}', 'BPStaging.ContractEndorsementDocumentElement')
  , (@LoadType_Intraday, 'BPStaging.ContractEndorsementElementChangeSet', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_ContractEndorsementElementChangeSet_Export", "SourceSQL":"", "TargetTable":"BPStaging.ContractEndorsementElementChangeSet", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "SystemVersioned":true, "IncrementalUpdatedDateColumn":"ValidFrom"}', 'BPStaging.ContractEndorsementElementChangeSet')
  , (@LoadType_Intraday, 'BPStaging.ContractPolicy', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_ContractPolicy_Export", "SourceSQL":"", "TargetTable":"BPStaging.ContractPolicy", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":true }', 'BPStaging.ContractPolicy')
  , (@LoadType_Intraday, 'BPStaging.ContractRenewedFrom', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_ContractRenewedFrom_Export", "SourceSQL":"", "TargetTable":"BPStaging.ContractRenewedFrom", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":true }', 'BPStaging.ContractRenewedFrom')
  , (@LoadType_Intraday, 'BPStaging.ContractRisk', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_ContractRisk_Export", "SourceSQL":"", "TargetTable":"BPStaging.ContractRisk", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "SystemVersioned":true, "AlwaysFullLoad":true  , "IncrementalUpdatedDateColumn":"ValidFrom"}', 'BPStaging.ContractRisk')
  , (@LoadType_Intraday, 'BPStaging.ContractRiskCode', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_ContractRiskCode_Export", "SourceSQL":"", "TargetTable":"BPStaging.ContractRiskCode", "TruncateTargetTable": true, "SystemVersioned":true, "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ValidFrom", "TargetConnection":"PlacementStore"}', 'BPStaging.ContractRiskCode')
  , (@LoadType_Intraday, 'BPStaging.ContractSection', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_ContractSection_Export", "SourceSQL":"", "TargetTable":"BPStaging.ContractSection", "TruncateTargetTable": true, "SystemVersioned":true,"IncrementalUpdatedDateColumn":"ValidFrom", "TargetConnection":"PlacementStore", "AlwaysFullLoad":false }', 'BPStaging.ContractSection')
  , (@LoadType_Intraday, 'BPStaging.ContractSectionBasis', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_ContractSectionBasis_Export", "SourceSQL":"", "TargetTable":"BPStaging.ContractSectionBasis", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":true }', 'BPStaging.ContractSectionBasis')
  , (@LoadType_Intraday, 'BPStaging.ContractSectionBasisType', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_ContractSectionBasisType_Export", "SourceSQL":"", "TargetTable":"BPStaging.ContractSectionBasisType", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'BPStaging.ContractSectionBasisType')
  , (@LoadType_Intraday, 'BPStaging.ContractSectionRiskCode', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_ContractSectionRiskCode_Export", "SourceSQL":"", "TargetTable":"BPStaging.ContractSectionRiskCode", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":true }', 'BPStaging.ContractSectionRiskCode')
  , (@LoadType_Intraday, 'BPStaging.ContractStatus', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_ContractStatus_Export", "SourceSQL":"", "TargetTable":"BPStaging.ContractStatus", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'BPStaging.ContractStatus')
  , (@LoadType_Intraday, 'BPStaging.ContractTimeline', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.Contract_Timeline_Reporting_v2", "SourceSQL":"EXEC export.Contract_Timeline_Reporting_v2 @StartDate = @ETLUpdatedDate WITH RESULT SETS ((      ModifiedById  INT            NULL, PlacementId   INT            NULL, TableId             INT            NULL, TableKey   INT            NULL, DateOfEvent   DATETIME2 (7)  NULL, Description         NVARCHAR (MAX) NULL, UserPrincipalName   NVARCHAR (MAX) NULL, Detail              NVARCHAR (MAX) NULL, UserId    INT            NULL, GroupingSection     NVARCHAR (MAX) NULL, GroupingSectionType INT            NULL, EventType   NVARCHAR (255)  NULL, ContractId   INT NULL     ))", "TargetTable":"BPStaging.ContractTimeline", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"DateOfEvent"}', 'BPStaging.ContractTimeline')
  , (@LoadType_Intraday, 'BPStaging.ContractType', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_ContractType_Export", "SourceSQL":"", "TargetTable":"BPStaging.ContractType", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'BPStaging.ContractType')
  , (@LoadType_Intraday, 'BPStaging.Coverage', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_Coverage_Export", "SourceSQL":"", "TargetTable":"BPStaging.Coverage", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'BPStaging.Coverage')
  , (@LoadType_Intraday, 'BPStaging.CoverageBasis', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_CoverageBasis_Export", "SourceSQL":"", "TargetTable":"BPStaging.CoverageBasis", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'BPStaging.CoverageBasis')
  , (@LoadType_Intraday, 'BPStaging.CoverageGroup', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_CoverageGroup_Export", "SourceSQL":"", "TargetTable":"BPStaging.CoverageGroup", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'BPStaging.CoverageGroup')
  , (@LoadType_Intraday, 'BPStaging.CoverageGroupSection', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_CoverageGroupSection_Export", "SourceSQL":"", "TargetTable":"BPStaging.CoverageGroupSection", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'BPStaging.CoverageGroupSection')
  , (@LoadType_Intraday, 'BPStaging.CoverageType', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_CoverageType_Export", "SourceSQL":"", "TargetTable":"BPStaging.CoverageType", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'BPStaging.CoverageType')
  , (@LoadType_Intraday, 'BPStaging.CreatePlacementListener', @ProcessType_SP, '{"StoredProcedure":"BPStaging.CreatePlacementListener", "TargetConnection":"PlacementStore"}', 'dbo.PlacementListener')
  , (@LoadType_Intraday, 'BPStaging.DeclinationReason', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_DeclinationReason_Export", "SourceSQL":"", "TargetTable":"BPStaging.DeclinationReason", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'BPStaging.DeclinationReason')
  , (@LoadType_Intraday, 'BPStaging.DeductibleBasis', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_DeductibleBasis_Export", "SourceSQL":"", "TargetTable":"BPStaging.DeductibleBasis", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'BPStaging.DeductibleBasis')
  , (@LoadType_Intraday, 'BPStaging.DeltaType', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_DeltaType_Export", "SourceSQL":"", "TargetTable":"BPStaging.DeltaType", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'BPStaging.DeltaType')
  , (@LoadType_Intraday, 'BPStaging.DocumentElement', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_DocumentElement_v2_Export", "SourceSQL":"", "TargetTable":"BPStaging.DocumentElement", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "SystemVersioned":true, "AlwaysFullLoad":false,"IncrementalUpdatedDateColumn":"ValidFrom"}', 'BPStaging.DocumentElement')
  , (@LoadType_Intraday, 'BPStaging.DocumentTemplateElement', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_DocumentTemplateElement_Export", "SourceSQL":"", "TargetTable":"BPStaging.DocumentTemplateElement", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "SystemVersioned":true, "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ValidFrom"}', 'BPStaging.DocumentElement')
  , (@LoadType_Intraday, 'BPStaging.Element', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_Element_Export_v2", "SourceSQL":"", "TargetTable":"BPStaging.Element", "TruncateTargetTable": true, "TargetConnection":"PlacementStore" , "SystemVersioned":true,"AlwaysFullLoad":false ,"IncrementalUpdatedDateColumn":"ValidFrom"}', 'BPStaging.Element')
  , (@LoadType_Intraday, 'BPStaging.ElementAttribute', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_ElementAttribute_Export", "SourceSQL":"", "TargetTable":"BPStaging.ElementAttribute", "TruncateTargetTable": true, "TargetConnection":"PlacementStore" , "SystemVersioned":true, "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ValidFrom"}', 'BPStaging.ElementAttribute')
  , (@LoadType_Intraday, 'BPStaging.ElementAttributeDelta', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_ElementAttributeDelta_Export", "SourceSQL":"", "TargetTable":"BPStaging.ElementAttributeDelta", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "SystemVersioned":true, "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ValidFrom"}', 'BPStaging.ElementAttributeDelta')
  , (@LoadType_Intraday, 'BPStaging.ElementAttributeReferenceOption', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_ElementAttributeReferenceOption_Export"  , "SourceSQL":"", "TargetTable":"BPStaging.ElementAttributeReferenceOption", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":true }', 'BPStaging.ElementAttributeReferenceOption')
  , (@LoadType_Intraday, 'BPStaging.ElementAttributeType', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_ElementAttributeType_Export", "SourceSQL":"", "TargetTable":"BPStaging.ElementAttributeType", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":true, "SystemVersioned":false, "IncrementalUpdatedDateColumn":"ValidFrom"}', 'BPStaging.ElementAttributeType')
  , (@LoadType_Intraday, 'BPStaging.ElementBranch', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_ElementBranch_Export", "SourceSQL":"", "TargetTable":"BPStaging.ElementBranch", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'BPStaging.ElementBranch')
  , (@LoadType_Intraday, 'BPStaging.ElementChangeSet', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_ElementChangeSet_Export", "SourceSQL":"", "TargetTable":"BPStaging.ElementChangeSet", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "SystemVersioned":true, "IncrementalUpdatedDateColumn":"ValidFrom"}', 'BPStaging.ElementChangeSet')
  , (@LoadType_Intraday, 'BPStaging.ElementCompositionLinkedElement', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_ElementCompositionLinkedElement_Export", "SourceSQL":"", "TargetTable":"BPStaging.ElementCompositionLinkedElement", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "SystemVersioned":true, "IncrementalUpdatedDateColumn":"ValidFrom", "AlwaysFullLoad":false }', 'BPStaging.ElementCompositionLinkedElement')
  , (@LoadType_Intraday, 'BPStaging.ElementCompositionLinkedElementBranch', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_ElementCompositionLinkedElementBranch_Export", "SourceSQL":"", "TargetTable":"BPStaging.ElementCompositionLinkedElementBranch", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "SystemVersioned":true, "IncrementalUpdatedDateColumn":"ValidFrom", "AlwaysFullLoad":false }', 'BPStaging.ElementCompositionLinkedElementBranch')
  , (@LoadType_Intraday, 'BPStaging.ElementDelta', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_ElementDelta_Export", "SourceSQL":"", "TargetTable":"BPStaging.ElementDelta", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "SystemVersioned":true, "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ValidFrom"}', 'BPStaging.ElementDelta')
  , (@LoadType_Intraday, 'BPStaging.ElementStructureContext', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_ElementStructureContext_Export", "SourceSQL":"", "TargetTable":"BPStaging.ElementStructureContext", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "SystemVersioned":false, "AlwaysFullLoad":true, "IncrementalUpdatedDateColumn":"ValidFrom"}', 'BPStaging.ElementStructureContext')
  , (@LoadType_Intraday, 'BPStaging.ElementTag', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_ElementTag_Export_v2", "SourceSQL":"", "TargetTable":"BPStaging.ElementTag", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"  ,"SystemVersioned":true,"AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ValidFrom"}', 'BPStaging.ElementTag')
  , (@LoadType_Intraday, 'BPStaging.ElementTagDelta', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_ElementTagDelta_Export", "SourceSQL":"", "TargetTable":"BPStaging.ElementTagDelta", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "SystemVersioned":true, "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ValidFrom"}', 'BPStaging.ElementTagDelta')
  , (@LoadType_Intraday, 'BPStaging.ElementTagGroup', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_ElementTagGroup_Export", "SourceSQL":"", "TargetTable":"BPStaging.ElementTagGroup", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'BPStaging.ElementTagGroup')
  , (@LoadType_Intraday, 'BPStaging.ElementTagInclusionRule', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_ElementTagInclusionRule_Export", "SourceSQL":"", "TargetTable":"BPStaging.ElementTagInclusionRule", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'BPStaging.ElementTagInclusionRule')
  , (@LoadType_Intraday, 'BPStaging.ElementTagType', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_ElementTagType_v2_Export", "SourceSQL":"", "TargetTable":"BPStaging.ElementTagType", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'BPStaging.ElementTagType')
  , (@LoadType_Intraday, 'BPStaging.ElementType', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_ElementType_Export_v2", "SourceSQL":"", "TargetTable":"BPStaging.ElementType", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'BPStaging.ElementType')
  , (@LoadType_Intraday, 'BPStaging.ElementTypeMetaTag', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_ElementTypeMetaTag_Export", "SourceSQL":"", "TargetTable":"BPStaging.ElementTypeMetaTag", "TruncateTargetTable": true, "TargetConnection":"PlacementStore" , "SystemVersioned":true, "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ValidFrom"}', 'BPStaging.ElementTypeMetaTag')
  , (@LoadType_Intraday, 'BPStaging.ElementTypeParentType', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_ElementTypeParentType_Export", "SourceSQL":"", "TargetTable":"BPStaging.ElementTypeParentType", "TruncateTargetTable": true, "TargetConnection":"PlacementStore" , "SystemVersioned":true, "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ValidFrom"}', 'BPStaging.ElementTypeParentType')
  , (@LoadType_Intraday, 'BPStaging.EligibilityRule', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_EligibilityRule_Export", "SourceSQL":"", "TargetTable":"BPStaging.EligibilityRule", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'BPStaging.EligibilityRule')
  , (@LoadType_Intraday, 'BPStaging.EndorsementStatus', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_EndorsementStatus_Export", "SourceSQL":"", "TargetTable":"BPStaging.EndorsementStatus", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'BPStaging.EndorsementStatus')
  , (@LoadType_Intraday, 'BPStaging.ExpiringResponseElement', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_ExpiringResponseElement_Export", "SourceSQL":"", "TargetTable":"BPStaging.ExpiringResponseElement", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "SystemVersioned":true, "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ValidFrom"}', 'BPStaging.ExpiringResponseElement')
  , (@LoadType_Intraday, 'BPStaging.ExpiringResponseGroup', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_ExpiringResponseGroup_Export", "SourceSQL":"", "TargetTable":"BPStaging.ExpiringResponseGroup", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "SystemVersioned":true, "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ValidFrom"}', 'BPStaging.ExpiringResponseGroup')
  , (@LoadType_Intraday, 'BPStaging.ExposurePeriodGroup', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_ExposurePeriodGroup_Export", "SourceSQL":"", "TargetTable":"BPStaging.ExposurePeriodGroup", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'BPStaging.ExposurePeriodGroup')
  , (@LoadType_Intraday, 'BPStaging.ExposureSummaryElement', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_ExposureSummaryElement", "SourceSQL":"", "TargetTable":"BPStaging.ExposureSummaryElement", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "SystemVersioned":true, "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ValidFrom"}', 'BPStaging.ExposureSummaryElement')
  , (@LoadType_Intraday, 'BPStaging.ExposureSummaryElementRiskDefinitionItem', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_ExposureSummaryElementRiskDefinitionItem", "SourceSQL":"", "TargetTable":"BPStaging.ExposureSummaryElementRiskDefinitionItem", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "SystemVersioned":true, "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ValidFrom"}', 'BPStaging.ExposureSummaryElementRiskDefinitionItem')
  , (@LoadType_Intraday, 'BPStaging.ExposureType', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_ExposureType_Export", "SourceSQL":"", "TargetTable":"BPStaging.ExposureType", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'BPStaging.ExposureType')
  , (@LoadType_Intraday, 'BPStaging.ExtendedReportingPeriod', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_PremiumExtendedReportingPeriod", "SourceSQL":"", "TargetTable":"BPStaging.ExtendedReportingPeriod", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'BPStaging.ExtendedReportingPeriod')
  , (@LoadType_Intraday, 'BPStaging.FollowType', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_FollowType_Export", "SourceSQL":"", "TargetTable":"BPStaging.FollowType", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "SystemVersioned":false, "AlwaysFullLoad":true}', 'BPStaging.FollowType')
  , (@LoadType_Intraday, 'BPStaging.InsuredType', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_InsuredType_Export", "SourceSQL":"", "TargetTable":"BPStaging.InsuredType", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "SystemVersioned":true, "IncrementalUpdatedDateColumn":"ValidFrom", "AlwaysFullLoad":false }', 'BPStaging.InsuredType')
  , (@LoadType_Intraday, 'BPStaging.JustificationReason', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_JustificationReason_Export", "SourceSQL":"", "TargetTable":"BPStaging.JustificationReason", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'BPStaging.JustificationReason')
  , (@LoadType_Intraday, 'BPStaging.JustificationReasonType', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_JustificationReasonType_Export", "SourceSQL":"", "TargetTable":"BPStaging.JustificationReasonType", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'BPStaging.JustificationReasonType')
  , (@LoadType_Intraday, 'BPStaging.Layer', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_Layer_Export", "SourceSQL":"", "TargetTable":"BPStaging.Layer", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'BPStaging.Layer')
  , (@LoadType_Intraday, 'BPStaging.LayerType', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_LayerType_Export", "SourceSQL":"", "TargetTable":"BPStaging.LayerType", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ValidFrom"}', 'BPStaging.LayerType')
  , (@LoadType_Intraday, 'BPStaging.LegalEntity', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_LegalEntity_Export", "SourceSQL":"", "TargetTable":"BPStaging.LegalEntity", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'BPStaging.LegalEntity')
  , (@LoadType_Intraday, 'BPStaging.LibAdditionalDataItem', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_LibAdditionalDataItem_Export", "SourceSQL":"", "TargetTable":"BPStaging.LibAdditionalDataItem", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":true }', 'BPStaging.LibAdditionalDataItem')
  , (@LoadType_Intraday, 'BPStaging.LibraryElement', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_LibraryElement_Export", "SourceSQL":"", "TargetTable":"BPStaging.LibraryElement", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "SystemVersioned":true, "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ValidFrom"}', 'BPStaging.LibraryElement')
  , (@LoadType_Intraday, 'BPStaging.LinePercentageBasis', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_LinePercentageBasis_Export", "SourceSQL":"", "TargetTable":"BPStaging.LinePercentageBasis", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'BPStaging.LinePercentageBasis')
  , (@LoadType_Intraday, 'BPStaging.Load_BP_AdjustmentResponseElement', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_BP_AdjustmentResponseElement", "TargetConnection":"PlacementStore"}', 'BP.AdjustmentResponseElement')
  , (@LoadType_Intraday, 'BPStaging.Load_BP_AdjustmentResponseGroup', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_BP_AdjustmentResponseGroup", "TargetConnection":"PlacementStore"}', 'BP.AdjustmentResponseGroup')
  , (@LoadType_Intraday, 'BPStaging.Load_BP_AffirmationQuestion', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_BP_AffirmationQuestion", "TargetConnection":"PlacementStore"}', 'BP.AffirmationQuestion')
  , (@LoadType_Intraday, 'BPStaging.Load_BP_AffirmationQuestionSet', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_BP_AffirmationQuestionSet", "TargetConnection":"PlacementStore"}', 'BP.AffirmationQuestionSet')
  , (@LoadType_Intraday, 'BPStaging.Load_BP_AutoFollowMarketEligibilityRule', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_BP_AutoFollowMarketEligibilityRule", "TargetConnection":"PlacementStore"}', 'BP.AutoFollowMarketEligibilityRule')
  , (@LoadType_Intraday, 'BPStaging.Load_BP_BoundPosition', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_BP_BoundPosition", "TargetConnection":"PlacementStore"}', 'BP.BoundPosition')
  , (@LoadType_Intraday, 'BPStaging.Load_BP_BoundPositionPlacementPolicy', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_BP_BoundPositionPlacementPolicy", "TargetConnection":"PlacementStore"}', 'BP.BoundPositionPlacementPolicy')
  , (@LoadType_Intraday, 'BPStaging.Load_BP_ContractEndorsementElementChangeSet', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_BP_ContractEndorsementElementChangeSet", "TargetConnection":"PlacementStore"}', 'BP.ContractEndorsementElementChangeSet')
  , (@LoadType_Intraday, 'BPStaging.Load_BP_ContractRiskCode', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_BP_ContractRiskCode", "TargetConnection":"PlacementStore"}', 'BP.ContractRiskCode')
  , (@LoadType_Intraday, 'BPStaging.Load_BP_ExpiringResponseElement', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_BP_ExpiringResponseElement", "TargetConnection":"PlacementStore"}', 'BP.ExpiringResponseElement')
  , (@LoadType_Intraday, 'BPStaging.Load_BP_ExpiringResponseGroup', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_BP_ExpiringResponseGroup", "TargetConnection":"PlacementStore"}', 'BP.ExpiringResponseGroup')
  , (@LoadType_Intraday, 'BPStaging.Load_BP_ExposureSummaryElement', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_BP_ExposureSummaryElement", "TargetConnection":"PlacementStore"}', 'BP.ExposureSummaryElement')
  , (@LoadType_Intraday, 'BPStaging.Load_BP_ExposureSummaryElementRiskDefinitionItem', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_BP_ExposureSummaryElementRiskDefinitionItem", "TargetConnection":"PlacementStore"}', 'BP.ExposureSummaryElementRiskDefinitionItem')
  , (@LoadType_Intraday, 'BPStaging.Load_BP_MarketResponseAffirmationQuestion', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_BP_MarketResponseAffirmationQuestion", "TargetConnection":"PlacementStore"}', 'BP.MarketResponseAffirmationQuestion')
  , (@LoadType_Intraday, 'BPStaging.Load_BP_MarketResponseAutoFollowEligibilityRule', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_BP_MarketResponseAutoFollowEligibilityRule", "TargetConnection":"PlacementStore"}', 'BP.MarketResponseAutoFollowEligibilityRule')
  , (@LoadType_Intraday, 'BPStaging.Load_BP_MarketResponseElement', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_BP_MarketResponseElement", "TargetConnection":"PlacementStore"}', 'BP.MarketResponseElement')
  , (@LoadType_Intraday, 'BPStaging.Load_BP_PlacementAffirmationQuestion', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_BP_PlacementAffirmationQuestion", "TargetConnection":"PlacementStore"}', 'BP.PlacementAffirmationQuestion')
  , (@LoadType_Intraday, 'BPStaging.Load_BP_PlacementExposureSummary', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_BP_PlacementExposureSummary", "TargetConnection":"PlacementStore"}', 'BP.PlacementExposureSummary')
  , (@LoadType_Intraday, 'BPStaging.Load_BP_PlacementExposureSummaryGroup', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_BP_PlacementExposureSummaryGroup", "TargetConnection":"PlacementStore"}', 'BP.PlacementExposureSummaryGroup')
  , (@LoadType_Intraday, 'BPStaging.Load_BP_PortalRejectionReason', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_BP_PortalRejectionReason", "TargetConnection":"PlacementStore"}', 'BP.PortalRejectionReason')
  , (@LoadType_Intraday, 'BPStaging.Load_BP_PortalUserDocument', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_BP_PortalUserDocument", "TargetConnection":"PlacementStore"}', 'BP.PortalUserDocument')
  , (@LoadType_Intraday, 'BPStaging.Load_BP_RequestedCoverageElement', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_BP_RequestedCoverageElement", "TargetConnection":"PlacementStore"}', 'BP.RequestedCoverageElement')
  , (@LoadType_Intraday, 'BPStaging.Load_BP_ResponseManagementElement', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_BP_ResponseManagementElement", "TargetConnection":"PlacementStore"}', 'BP.ResponseManagementElement')
  , (@LoadType_Intraday, 'BPStaging.Load_BP_ResponseManagementElementRiskDefinitionElement', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_BP_ResponseManagementElementRiskDefinitionElement", "TargetConnection":"PlacementStore"}', 'BP.ResponseManagementElementRiskDefinitionElement')
  , (@LoadType_Intraday, 'BPStaging.Load_BP_RiskAndAnalyticsRunMapping', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_BP_RiskAndAnalyticsRunMapping", "TargetConnection":"PlacementStore"}', 'BP.RiskAndAnalyticsRunMapping')
  , (@LoadType_Intraday, 'BPStaging.Load_BP_RiskRequest', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_BP_RiskRequest", "TargetConnection":"PlacementStore"}', 'BP.RiskRequest')
  , (@LoadType_Intraday, 'BPStaging.Load_BP_Strategy', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_BP_Strategy", "TargetConnection":"PlacementStore"}', 'BP.Strategy')
  , (@LoadType_Intraday, 'BPStaging.Load_BP_StrategyProposedSignedLine', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_BP_StrategyProposedSignedLine", "TargetConnection":"PlacementStore"}', 'BP.StrategyProposedSignedLine')
  , (@LoadType_Intraday, 'BPStaging.Load_BP_Submission', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_BP_Submission", "TargetConnection":"PlacementStore"}', 'BP.Submission')
  , (@LoadType_Intraday, 'BPStaging.Load_BP_SubmissionContainerCarrier', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_BP_SubmissionContainerCarrier", "TargetConnection":"PlacementStore"}', 'BP.SubmissionContainerCarrier')
  , (@LoadType_Intraday, 'BPStaging.Load_BP_SubmissionContainerFacility', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_BP_SubmissionContainerFacility", "TargetConnection":"PlacementStore"}', 'BP.SubmissionContainerFacility')
  , (@LoadType_Intraday, 'BPStaging.Load_BP_SubmissionContainerMarket', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_BP_SubmissionContainerMarket", "TargetConnection":"PlacementStore"}', 'BP.SubmissionContainerMarket')
  , (@LoadType_Intraday, 'BPStaging.Load_BP_SubmissionContainerPanel', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_BP_SubmissionContainerPanel", "TargetConnection":"PlacementStore"}', 'BP.SubmissionContainerPanel')
  , (@LoadType_Intraday, 'BPStaging.Load_BP_SubmissionContainerPanelMember', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_BP_SubmissionContainerPanelMember", "TargetConnection":"PlacementStore"}', 'BP.SubmissionContainerPanelMember')
  , (@LoadType_Intraday, 'BPStaging.Load_BP_SubmissionContainerRequestedCoverageElement', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_BP_SubmissionContainerRequestedCoverageElement", "TargetConnection":"PlacementStore"}', 'BP.SubmissionContainerRequestedCoverageElement')
  , (@LoadType_Intraday, 'BPStaging.Load_BP_SubmissionContainerRiskDefinitionItem', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_BP_SubmissionContainerRiskDefinitionItem", "TargetConnection":"PlacementStore"}', 'BP.SubmissionContainerRiskDefinitionItem')
  , (@LoadType_Intraday, 'BPStaging.Load_BP_SubmissionContainerThirdPartyMarket', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_BP_SubmissionContainerThirdPartyMarket", "TargetConnection":"PlacementStore"}', 'BP.SubmissionContainerThirdPartyMarket')
  , (@LoadType_Intraday, 'BPStaging.Load_BP_SubmissionDocument', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_BP_SubmissionDocument", "TargetConnection":"PlacementStore"}', 'BP.SubmissionDocument')
  , (@LoadType_Intraday, 'BPStaging.Load_BP_SubmissionMarket', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_BP_SubmissionMarket", "TargetConnection":"PlacementStore"}', 'BP.SubmissionMarket')
  , (@LoadType_Intraday, 'BPStaging.Load_BP_SubmissionMarketRecipient', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_BP_SubmissionMarketRecipient", "TargetConnection":"PlacementStore"}', 'BP.SubmissionMarketRecipient')
  , (@LoadType_Intraday, 'BPStaging.Load_BP_SubmissionPortalUser', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_BP_SubmissionPortalUser", "TargetConnection":"PlacementStore"}', 'BP.SubmissionPortalUser')
  , (@LoadType_Intraday, 'BPStaging.Load_BP_UserEnhancedBrokerDetails', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_BP_UserEnhancedBrokerDetails", "TargetConnection":"PlacementStore"}', 'BP.UserEnhancedBrokerDetails')
  , (@LoadType_Intraday, 'BPStaging.Load_dbo_ElementChangeSet', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_dbo_ElementChangeSet", "TargetConnection":"PlacementStore"}', 'dbo.ElementChangeSet')
  , (@LoadType_Intraday, 'BPStaging.Load_dbo_ElementTagCache', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_dbo_ElementTagCache", "TargetConnection":"PlacementStore"}', 'dbo.ElementTagCache')
  , (@LoadType_Intraday, 'BPStaging.Load_dbo_PlacementMarketValidation', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_dbo_PlacementMarketValidation", "TargetConnection":"PlacementStore"}', 'dbo.PlacementMarketValidation')
  , (@LoadType_Intraday, 'BPStaging.Load_dbo_SubmissionDistribution', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_dbo_SubmissionDistribution", "TargetConnection":"PlacementStore"}', 'dbo.SubmissionDistribution')
  , (@LoadType_Intraday, 'BPStaging.Load_dbo_SubmissionMarketDocuments', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_dbo_SubmissionMarketDocuments", "TargetConnection":"PlacementStore"}', 'dbo.SubmissionMarketDocuments')
  , (@LoadType_Intraday, 'BPStaging.Load_dbo_SubmissionRecipient', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_dbo_SubmissionRecipient", "TargetConnection":"PlacementStore"}', 'dbo.SubmissionRecipient')
  , (@LoadType_Intraday, 'BPStaging.Load_PS_ActionValidationRule', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_PS_ActionValidationRule", "TargetConnection":"PlacementStore"}', 'PS.ActionValidationRule')
  , (@LoadType_Intraday, 'BPStaging.Load_PS_AdditionalDataItem', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_PS_AdditionalDataItem", "TargetConnection":"PlacementStore"}', 'PS.AdditionalDataItem')
  , (@LoadType_Intraday, 'BPStaging.Load_PS_AppetiteNarrative', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_PS_AppetiteNarrative", "TargetConnection":"PlacementStore"}', 'PS.AppetiteNarrative')
  , (@LoadType_Intraday, 'BPStaging.Load_PS_AppetiteResponse', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_PS_AppetiteResponse", "TargetConnection":"PlacementStore"}', 'PS.AppetiteResponse')
  , (@LoadType_Intraday, 'BPStaging.Load_PS_ClientProfile', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_PS_ClientProfile", "TargetConnection":"PlacementStore"}', 'PS.ClientProfile')
  , (@LoadType_Intraday, 'BPStaging.Load_PS_ClientProposal', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_PS_ClientProposal", "TargetConnection":"PlacementStore"}', 'PS.ClientProposal')
  , (@LoadType_Intraday, 'BPStaging.Load_PS_ClientProposalCoverage', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_PS_ClientProposalCoverage", "TargetConnection":"PlacementStore"}', 'PS.ClientProposalCoverage')
  , (@LoadType_Intraday, 'BPStaging.Load_PS_ClientProposalCoverageNotice', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_PS_ClientProposalCoverageNotice", "TargetConnection":"PlacementStore"}', 'PS.ClientProposalCoverageNotice')
  , (@LoadType_Intraday, 'BPStaging.Load_PS_ClientProposalPlacement', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_PS_ClientProposalPlacement", "TargetConnection":"PlacementStore"}', 'PS.ClientProposalPlacement')
  , (@LoadType_Intraday, 'BPStaging.Load_PS_ClientProposalQuoteComparison', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_PS_ClientProposalQuoteComparison", "TargetConnection":"PlacementStore"}', 'PS.ClientProposalQuoteComparison')
  , (@LoadType_Intraday, 'BPStaging.Load_PS_Contract', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_PS_Contract", "TargetConnection":"PlacementStore"}', 'PS.Contract')
  , (@LoadType_Intraday, 'BPStaging.Load_PS_ContractAttribute', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_PS_ContractAttribute", "TargetConnection":"PlacementStore"}', 'PS.ContractAttribute')
  , (@LoadType_Intraday, 'BPStaging.Load_PS_ContractDocumentBase', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_PS_ContractDocumentBase", "TargetConnection":"PlacementStore"}', 'PS.ContractDocumentBase')
  , (@LoadType_Intraday, 'BPStaging.Load_PS_ContractDocumentVersion', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_PS_ContractDocumentVersion", "TargetConnection":"PlacementStore"}', 'PS.ContractDocumentVersion')
  , (@LoadType_Intraday, 'BPStaging.Load_PS_ContractEndorsement', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_PS_ContractEndorsement", "TargetConnection":"PlacementStore"}', 'PS.ContractEndorsement')
  , (@LoadType_Intraday, 'BPStaging.Load_PS_ContractEndorsementAttribute', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_PS_ContractEndorsementAttribute", "TargetConnection":"PlacementStore"}', 'PS.ContractEndorsementAttribute')
  , (@LoadType_Intraday, 'BPStaging.Load_PS_ContractProgrammeAttribute', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_PS_ContractProgrammeAttribute", "TargetConnection":"PlacementStore"}', 'PS.ContractProgrammeAttribute')
  , (@LoadType_Intraday, 'BPStaging.Load_PS_ContractStatusHistory', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_PS_ContractStatusHistory", "TargetConnection":"PlacementStore"}', 'PS.ContractStatusHistory')
  , (@LoadType_Intraday, 'BPStaging.Load_PS_ContractVersionAttribute', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_PS_ContractVersionAttribute", "TargetConnection":"PlacementStore"}', 'PS.ContractVersionAttribute')
  , (@LoadType_Intraday, 'BPStaging.Load_PS_Element', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_PS_Element", "TargetConnection":"PlacementStore"}', 'PS.Element')
  , (@LoadType_Intraday, 'BPStaging.Load_PS_ElementCompositionLinkedElement', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_PS_ElementCompositionLinkedElement", "TargetConnection":"PlacementStore"}', 'PS.ElementCompositionLinkedElement')
  , (@LoadType_Intraday, 'BPStaging.Load_PS_ElementCompositionLinkedElementBranch', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_PS_ElementCompositionLinkedElementBranch", "TargetConnection":"PlacementStore"}', 'PS.ElementCompositionLinkedElementBranch')
  , (@LoadType_Intraday, 'BPStaging.Load_PS_ElementTag', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_PS_ElementTag", "TargetConnection":"PlacementStore"}', 'PS.ElementTag')
  , (@LoadType_Intraday, 'BPStaging.Load_PS_ElementTagSummary', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_PS_ElementTagSummary", "TargetConnection":"PlacementStore"}', 'PS.ElementTagSummary')
  , (@LoadType_Intraday, 'BPStaging.Load_PS_ExposureElement', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_PS_ExposureElement", "TargetConnection":"PlacementStore"}', 'PS.ExposureElement')
  , (@LoadType_Intraday, 'BPStaging.Load_PS_ExposureElementAttribute', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_PS_ExposureElementAttribute", "TargetConnection":"PlacementStore"}', 'PS.ExposureElementAttribute')
  , (@LoadType_Intraday, 'BPStaging.Load_PS_LibAdditionalDataItem', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_PS_LibAdditionalDataItem", "TargetConnection":"PlacementStore"}', 'PS.LibAdditionalDataItem')
  , (@LoadType_Intraday, 'BPStaging.Load_PS_MarketQuoteResponse', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_PS_MarketQuoteResponse", "TargetConnection":"PlacementStore"}', 'PS.MarketQuoteResponse')
  , (@LoadType_Intraday, 'BPStaging.Load_PS_MarketQuoteResponseHistoric', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_PS_MarketQuoteResponseHistoric", "TargetConnection":"PlacementStore"}', 'PS.MarketQuoteResponse')
  , (@LoadType_Intraday, 'BPStaging.Load_PS_MarketQuoteResponseLegacy', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_PS_MarketQuoteResponseLegacy", "TargetConnection":"PlacementStore"}', 'PS.MarketQuoteResponse')
  , (@LoadType_Intraday, 'BPStaging.Load_PS_MarketQuoteResponsePolicyRef', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_PS_MarketQuoteResponsePolicyRef", "TargetConnection":"PlacementStore"}', 'PS.MarketQuoteResponsePolicyRef')
  , (@LoadType_Intraday, 'BPStaging.Load_PS_MarketResponse', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_PS_MarketResponse", "TargetConnection":"PlacementStore", "ResultType":"DummyRow"}', 'PS.MarketResponse')
  , (@LoadType_Intraday, 'BPStaging.Load_PS_MarketResponseBasis', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_PS_MarketResponseBasis", "TargetConnection":"PlacementStore"}', 'PS.MarketResponseBasis')
  , (@LoadType_Intraday, 'BPStaging.Load_PS_MarketResponseElement', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_PS_MarketResponseElement", "TargetConnection":"PlacementStore"}', 'PS.MarketResponseElement')
  , (@LoadType_Intraday, 'BPStaging.Load_PS_MarketResponseElementAttribute_Adjustment', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_PS_MarketResponseElementAttribute_Adjustment", "TargetConnection":"PlacementStore"}', 'PS.MarketResponseElementAttribute')
  , (@LoadType_Intraday, 'BPStaging.Load_PS_MarketResponseElementAttribute_Adjustment_ElementTag', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_PS_MarketResponseElementAttribute_Adjustment_ElementTag", "TargetConnection":"PlacementStore"}', 'PS.MarketResponseElementAttribute')
  , (@LoadType_Intraday, 'BPStaging.Load_PS_MarketResponseElementAttribute_Current', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_PS_MarketResponseElementAttribute_Current", "TargetConnection":"PlacementStore"}', 'PS.MarketResponseElementAttribute')
  , (@LoadType_Intraday, 'BPStaging.Load_PS_MarketResponseElementAttribute_Current_ElementTag', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_PS_MarketResponseElementAttribute_Current_ElementTag", "TargetConnection":"PlacementStore"}', 'PS.MarketResponseElementAttribute')
  , (@LoadType_Intraday, 'BPStaging.Load_PS_MarketResponseElementAttribute_Expiring', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_PS_MarketResponseElementAttribute_Expiring", "TargetConnection":"PlacementStore"}', 'PS.MarketResponseElementAttribute')
  , (@LoadType_Intraday, 'BPStaging.Load_PS_MarketResponseElementAttribute_Expiring_ElementTag', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_PS_MarketResponseElementAttribute_Expiring_ElementTag", "TargetConnection":"PlacementStore"}', 'PS.MarketResponseElementAttribute')
  , (@LoadType_Intraday, 'BPStaging.Load_PS_Negotiation', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_PS_Negotiation", "TargetConnection":"PlacementStore", "ResultType":"DummyRow"}', 'PS.Negotiation')
  , (@LoadType_Intraday, 'BPStaging.Load_PS_NegotiationContract', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_PS_NegotiationContract", "TargetConnection":"PlacementStore"}', 'PS.NegotiationContract')
  , (@LoadType_Intraday, 'BPStaging.Load_PS_NegotiationMarket', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_PS_NegotiationMarket", "TargetConnection":"PlacementStore"}', 'PS.NegotiationMarket')
  , (@LoadType_Intraday, 'BPStaging.Load_PS_NegotiationMarketContract', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_PS_NegotiationMarketContract", "TargetConnection":"PlacementStore"}', 'PS.NegotiationMarketContract')
  , (@LoadType_Intraday, 'BPStaging.Load_PS_NegotiationRiskProfile', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_PS_NegotiationRiskProfile", "TargetConnection":"PlacementStore", "ResultType":"DummyRow"}', 'PS.NegotiationRiskProfile')
  , (@LoadType_Intraday, 'BPStaging.Load_PS_NegotiationSpecification', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_PS_NegotiationSpecification", "TargetConnection":"PlacementStore"}', 'PS.NegotiationSpecification')
  , (@LoadType_Intraday, 'BPStaging.Load_PS_PlacementExposureGroup', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_PS_PlacementExposureGroup", "TargetConnection":"PlacementStore"}', 'PS.PlacementExposureGroup')
  , (@LoadType_Intraday, 'BPStaging.Load_PS_PlacementRuleValidation', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_PS_PlacementRuleValidation", "TargetConnection":"PlacementStore"}', 'PS.PlacementRuleValidation')
  , (@LoadType_Intraday, 'BPStaging.Load_PS_PlacementServiceSatisfactionValidation', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_PS_PlacementServiceSatisfactionValidation", "TargetConnection":"PlacementStore"}', 'PS.PlacementServiceSatisfactionValidation')
  , (@LoadType_Intraday, 'BPStaging.Load_PS_PlacementStatusHistory', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_PS_PlacementStatusHistory", "TargetConnection":"PlacementStore"}', NULL)
  , (@LoadType_Intraday, 'BPStaging.Load_PS_QuoteComparison', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_PS_QuoteComparison", "TargetConnection":"PlacementStore"}', 'PS.QuoteComparison')
  , (@LoadType_Intraday, 'BPStaging.Load_PS_QuoteComparisonQuote', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_PS_QuoteComparisonQuote", "TargetConnection":"PlacementStore"}', 'PS.QuoteComparisonQuote')
  , (@LoadType_Intraday, 'BPStaging.Load_PS_RiskProfile', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_PS_RiskProfile", "TargetConnection":"PlacementStore", "ResultType":"DummyRow"}', 'PS.RiskProfile')
  , (@LoadType_Intraday, 'BPStaging.Load_PS_RiskProfilePlacementExposureGroup', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_PS_RiskProfilePlacementExposureGroup", "TargetConnection":"PlacementStore"}', 'PS.RiskProfilePlacementExposureGroup')
  , (@LoadType_Intraday, 'BPStaging.Load_PS_RiskStructure', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_PS_RiskStructure", "TargetConnection":"PlacementStore", "ResultType":"DummyRow"}', 'PS.RiskStructure')
  , (@LoadType_Intraday, 'BPStaging.Load_PS_RiskStructureContract', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_PS_RiskStructureContract", "TargetConnection":"PlacementStore", "ResultType":"DummyRow"}', 'PS.RiskStructureContract')
  , (@LoadType_Intraday, 'BPStaging.Load_PS_RiskStructureMarketResponse', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_PS_RiskStructureMarketResponse", "TargetConnection":"PlacementStore", "ResultType":"DummyRow"}', 'PS.RiskStructureMarketResponse')
  , (@LoadType_Intraday, 'BPStaging.Load_PS_RiskStructurePolicy', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_PS_RiskStructurePolicy", "TargetConnection":"PlacementStore", "ResultType":"DummyRow"}', 'PS.RiskStructurePolicy')
  , (@LoadType_Intraday, 'BPStaging.Load_PS_RiskStructureSpecification', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_PS_RiskStructureSpecification", "TargetConnection":"PlacementStore"}', 'PS.RiskStructureSpecification')
  , (@LoadType_Intraday, 'BPStaging.Load_PS_ServiceSatisfactionIssueOutcome', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_PS_ServiceSatisfactionIssueOutcome", "TargetConnection":"PlacementStore"}', 'PS.ServiceSatisfactionIssueOutcome')
  , (@LoadType_Intraday, 'BPStaging.Load_PS_Specification', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_PS_Specification", "TargetConnection":"PlacementStore"}', 'PS.Specification')
  , (@LoadType_Intraday, 'BPStaging.Load_PS_SpecificationElement', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_PS_SpecificationElement", "TargetConnection":"PlacementStore"}', 'PS.SpecificationElement')
  , (@LoadType_Intraday, 'BPStaging.Load_PS_SpecificationElementAttribute', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_PS_SpecificationElementAttribute", "TargetConnection":"PlacementStore"}', 'PS.SpecificationElementAttribute')
  , (@LoadType_Intraday, 'BPStaging.Load_PS_Staging_RiskStructurePolicy', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_PS_Staging_RiskStructurePolicy", "TargetConnection":"PlacementStore", "ResultType":"DummyRow"}', 'BPStaging.PS_Staging_RiskStructurePolicy')
  , (@LoadType_Intraday, 'BPStaging.Load_PS_SubmissionPortalSummary', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_PS_SubmissionPortalSummary", "TargetConnection":"PlacementStore"}', 'PS.SubmissionPortalSummary')
  , (@LoadType_Intraday, 'BPStaging.Load_PS_ValidationRuleOutcome', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_PS_ValidationRuleOutcome", "TargetConnection":"PlacementStore"}', 'PS.ValidationRuleOutcome')
  , (@LoadType_Intraday, 'BPStaging.Load_ref_AppetiteLevel', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_ref_AppetiteLevel", "TargetConnection":"PlacementStore"}', 'ref.AppetiteLevel')
  , (@LoadType_Intraday, 'BPStaging.Load_ref_BasisOfCover', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_ref_BasisOfCover", "TargetConnection":"PlacementStore"}', 'ref.BasisOfCover')
  , (@LoadType_Intraday, 'BPStaging.Load_ref_BoundPositionType', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_ref_BoundPositionType", "TargetConnection":"PlacementStore"}', 'ref.BoundPositionType')
  , (@LoadType_Intraday, 'BPStaging.Load_ref_CancellationReason', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_ref_CancellationReason", "TargetConnection":"PlacementStore"}', 'ref.CancellationReason')
  , (@LoadType_Intraday, 'BPStaging.Load_ref_ElementAttributeReferenceOption', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_ref_ElementAttributeReferenceOption", "TargetConnection":"PlacementStore"}', 'ref.ElementAttributeReferenceOption')
  , (@LoadType_Intraday, 'BPStaging.Load_ref_ElementAttributeType', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_ref_ElementAttributeType", "TargetConnection":"PlacementStore"}', 'ref.ElementAttributeType')
  , (@LoadType_Intraday, 'BPStaging.Load_ref_ElementStructureContext', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_ref_ElementStructureContext", "TargetConnection":"PlacementStore"}', 'ref.ElementStructureContext')
  , (@LoadType_Intraday, 'BPStaging.Load_ref_ElementType', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_ref_ElementType", "TargetConnection":"PlacementStore"}', 'ref.ElementType')
  , (@LoadType_Intraday, 'BPStaging.Load_ref_ElementTypeMetaTag', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_ref_ElementTypeMetaTag", "TargetConnection":"PlacementStore"}', 'ref.ElementTypeMetaTag')
  , (@LoadType_Intraday, 'BPStaging.Load_ref_ElementTypeParentType', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_ref_ElementTypeParentType", "TargetConnection":"PlacementStore"}', 'ref.ElementTypeParentType')
  , (@LoadType_Intraday, 'BPStaging.Load_ref_EligibilityRule', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_ref_EligibilityRule", "TargetConnection":"PlacementStore"}', 'ref.EligibilityRule')
  , (@LoadType_Intraday, 'BPStaging.Load_ref_ExposurePeriod', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_ref_ExposurePeriod", "TargetConnection":"PlacementStore"}', 'ref.ExposurePeriod')
  , (@LoadType_Intraday, 'BPStaging.Load_ref_ExposureType', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_ref_ExposureType", "TargetConnection":"PlacementStore"}', 'ref.ExposureType')
  , (@LoadType_Intraday, 'BPStaging.Load_ref_FollowType', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_ref_FollowType", "TargetConnection":"PlacementStore"}', 'ref.FollowType')
  , (@LoadType_Intraday, 'BPStaging.Load_ref_Industry', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_ref_Industry", "TargetConnection":"PlacementStore", "ResultType":"DummyRow"}', 'ref.Industry')
  , (@LoadType_Intraday, 'BPStaging.Load_ref_InsuredType', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_ref_InsuredType", "TargetConnection":"PlacementStore"}', 'ref.InsuredType')
  , (@LoadType_Intraday, 'BPStaging.Load_ref_MarketKind', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_ref_MarketKind", "TargetConnection":"PlacementStore"}', 'ref.MarketKind')
  , (@LoadType_Intraday, 'BPStaging.Load_ref_MetaTag', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_ref_MetaTag", "TargetConnection":"PlacementStore"}', 'ref.MetaTag')
  , (@LoadType_Intraday, 'BPStaging.Load_ref_MTAType', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_ref_MTAType", "TargetConnection":"PlacementStore"}', 'ref.MTAType')
  , (@LoadType_Intraday, 'BPStaging.Load_ref_OpportunityType', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_ref_OpportunityType", "TargetConnection":"PlacementStore"}', 'ref.OpportunityType')
  , (@LoadType_Intraday, 'BPStaging.Load_ref_PolicyRefType', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_ref_PolicyRefType", "TargetConnection":"PlacementStore"}', 'ref.PolicyRefType')
  , (@LoadType_Intraday, 'BPStaging.Load_ref_PricingFactor', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_ref_PricingFactor", "TargetConnection":"PlacementStore"}', 'ref.PricingFactor')
  , (@LoadType_Intraday, 'BPStaging.Load_ref_ProgramStructureType', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_ref_ProgramStructureType", "TargetConnection":"PlacementStore"}', 'ref.ProgramStructureType')
  , (@LoadType_Intraday, 'BPStaging.Load_ref_RiskAndAnalyticsModelType', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_ref_RiskAndAnalyticsModelType", "TargetConnection":"PlacementStore"}', 'ref.RiskAndAnalyticsModelType')
  , (@LoadType_Intraday, 'BPStaging.Load_ref_Team', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_ref_Team", "TargetConnection":"PlacementStore"}', 'ref.Team')
  , (@LoadType_Intraday, 'BPStaging.Load_Reference_VerticalIndustry', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_Reference_VerticalIndustry", "TargetConnection":"PlacementStore"}', 'Reference.VerticalIndustry')
  , (@LoadType_Intraday, 'BPStaging.Load_rpt_ContractTimeline', @ProcessType_SP, '{"StoredProcedure":"BPStaging.Load_rpt_ContractTimeline", "TargetConnection":"PlacementStore"}', 'rpt.ContractTimeline')
  , (@LoadType_Intraday, 'BPStaging.LoadAppraisalType', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadAppraisalType", "TargetConnection":"PlacementStore"}', 'ref.AppraisalType')
  , (@LoadType_Intraday, 'BPStaging.LoadBespokeHeadingReason', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadBespokeHeadingReason", "TargetConnection":"PlacementStore"}', 'ref.BespokeHeadingReason')
  , (@LoadType_Intraday, 'BPStaging.LoadBrokingSegment', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadBrokingSegment", "TargetConnection":"PlacementStore"}', 'ref.BrokingSegment')
  , (@LoadType_Intraday, 'BPStaging.LoadBrokingSubSegment', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadBrokingSubSegment", "TargetConnection":"PlacementStore"}', 'ref.BrokingSubSegment')
  , (@LoadType_Intraday, 'BPStaging.LoadCarrierResponse', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadCarrierResponse", "TargetConnection":"PlacementStore"}', 'dbo.MarketSelection')
  , (@LoadType_Intraday, 'BPStaging.LoadContractDocumentElement', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadContractDocumentElement", "TargetConnection":"PlacementStore"}', 'dbo.ContractDocumentElement')
  , (@LoadType_Intraday, 'BPStaging.LoadContractEndorsementDocumentElement', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadContractEndorsementDocumentElement", "TargetConnection":"PlacementStore"}', 'dbo.ContractEndorsementDocumentElement')
  , (@LoadType_Intraday, 'BPStaging.LoadContractPolicy', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadContractPolicy", "TargetConnection":"PlacementStore"}', 'dbo.ContractPolicy')
  , (@LoadType_Intraday, 'BPStaging.LoadContractRisk', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadContractRisk", "TargetConnection":"PlacementStore"}', 'PS.ContractRisk')
  , (@LoadType_Intraday, 'BPStaging.LoadContractSection', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadContractSection", "TargetConnection":"PlacementStore"}', 'dbo.ContractSection')
  , (@LoadType_Intraday, 'BPStaging.LoadContractSectionBasis', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadContractSectionBasis", "TargetConnection":"PlacementStore"}', 'dbo.ContractSectionBasis')
  , (@LoadType_Intraday, 'BPStaging.LoadContractSectionBasisType', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadContractSectionBasisType", "TargetConnection":"PlacementStore"}', 'ref.ContractSectionBasisType')
  , (@LoadType_Intraday, 'BPStaging.LoadContractSectionRiskCode', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadContractSectionRiskCode", "TargetConnection":"PlacementStore"}', 'dbo.ContractSectionRiskCode')
  , (@LoadType_Intraday, 'BPStaging.LoadContractStatus', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadContractStatus", "TargetConnection":"PlacementStore"}', 'ref.ContractStatus')
  , (@LoadType_Intraday, 'BPStaging.LoadContractType', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadContractType", "TargetConnection":"PlacementStore"}', 'ref.ContractType')
  , (@LoadType_Intraday, 'BPStaging.LoadCoverage', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadCoverage", "TargetConnection":"PlacementStore"}', 'dbo.Coverage')
  , (@LoadType_Intraday, 'BPStaging.LoadCoverageBasis', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadCoverageBasis", "TargetConnection":"PlacementStore"}', 'ref.CoverageBasis')
  , (@LoadType_Intraday, 'BPStaging.LoadCoverageGroup', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadCoverageGroup", "TargetConnection":"PlacementStore"}', 'dbo.CoverageGroup')
  , (@LoadType_Intraday, 'BPStaging.LoadCoverageGroupSection', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadCoverageGroupSection", "TargetConnection":"PlacementStore"}', 'dbo.CoverageGroupSection')
  , (@LoadType_Intraday, 'BPStaging.LoadCoverageType', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadCoverageType", "TargetConnection":"PlacementStore"}', 'dbo.CoverageType')
  , (@LoadType_Intraday, 'BPStaging.LoadDeclinationReason', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadDeclinationReason", "TargetConnection":"PlacementStore"}', 'ref.DeclinationReason')
  , (@LoadType_Intraday, 'BPStaging.LoadDeductibleBasis', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadDeductibleBasis", "TargetConnection":"PlacementStore"}', 'ref.DeductibleBasis')
  , (@LoadType_Intraday, 'BPStaging.LoadDeltaType', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadDeltaType", "TargetConnection":"PlacementStore"}', 'ref.DeltaType')
  , (@LoadType_Intraday, 'BPStaging.LoadDocumentElement', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadDocumentElement", "TargetConnection":"PlacementStore"}', 'dbo.DocumentElement')
  , (@LoadType_Intraday, 'BPStaging.LoadDocumentTemplateElement', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadDocumentTemplateElement", "TargetConnection":"PlacementStore"}', 'dbo.DocumentTemplateElement')
  , (@LoadType_Intraday, 'BPStaging.LoadElementAttribute', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadElementAttribute", "TargetConnection":"PlacementStore"}', 'dbo.ElementAttribute')
  , (@LoadType_Intraday, 'BPStaging.LoadElementAttributeCache', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadElementAttributeCache", "TargetConnection":"PlacementStore"}', 'dbo.ElementAttributeCache')
  , (@LoadType_Intraday, 'BPStaging.LoadElementAttributeDelta', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadElementAttributeDelta", "TargetConnection":"PlacementStore"}', 'dbo.ElementAttributeDelta')
  , (@LoadType_Intraday, 'BPStaging.LoadElementBranch', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadElementBranch", "TargetConnection":"PlacementStore"}', 'dbo.ElementBranch')
  , (@LoadType_Intraday, 'BPStaging.LoadElementCache', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadElementCache", "TargetConnection":"PlacementStore"}', 'dbo.ElementCache')
  , (@LoadType_Intraday, 'BPStaging.LoadElementDelta', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadElementDelta", "TargetConnection":"PlacementStore"}', 'dbo.ElementDelta')
  , (@LoadType_Intraday, 'BPStaging.LoadElementTagDelta', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadElementTagDelta", "TargetConnection":"PlacementStore"}', 'dbo.ElementTagDelta')
  , (@LoadType_Intraday, 'BPStaging.LoadElementTagGroup', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadElementTagGroup", "TargetConnection":"PlacementStore"}', 'ref.ElementTagGroup')
  , (@LoadType_Intraday, 'BPStaging.LoadElementTagType', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadElementTagType", "TargetConnection":"PlacementStore"}', 'ref.ElementTagType')
  , (@LoadType_Intraday, 'BPStaging.LoadEndorsementStatus', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadEndorsementStatus", "TargetConnection":"PlacementStore"}', 'ref.EndorsementStatus')
  , (@LoadType_Intraday, 'BPStaging.LoadExtendedReportingPeriod', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadExtendedReportingPeriod", "TargetConnection":"PlacementStore"}', 'dbo.ExtendedReportingPeriod')
  , (@LoadType_Intraday, 'BPStaging.LoadJustificationReason', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadJustificationReason", "TargetConnection":"PlacementStore"}', 'dbo.JustificationReason')
  , (@LoadType_Intraday, 'BPStaging.LoadJustificationReasonType', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadJustificationReasonType", "TargetConnection":"PlacementStore"}', 'dbo.JustificationReasonType')
  , (@LoadType_Intraday, 'BPStaging.LoadLayer', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadLayer", "TargetConnection":"PlacementStore", "SystemVersioned":true, "AlwaysFullLoad":true }', 'dbo.Layer')
  , (@LoadType_Intraday, 'BPStaging.LoadLayerType', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadLayerType", "TargetConnection":"PlacementStore"}', 'ref.LayerType')
  , (@LoadType_Intraday, 'BPStaging.LoadLegalEntity', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadLegalEntity", "TargetConnection":"PlacementStore"}', 'ref.LegalEntity')
  , (@LoadType_Intraday, 'BPStaging.LoadLibraryElement', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadLibraryElement", "TargetConnection":"PlacementStore"}', 'dbo.LibraryElement')
  , (@LoadType_Intraday, 'BPStaging.LoadLinePercentageBasis', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadLinePercentageBasis", "TargetConnection":"PlacementStore"}', 'ref.LinePercentageBasis')
  , (@LoadType_Intraday, 'BPStaging.LoadMarketInteractionFacility', @ProcessType_SP, '{"StoredProcedure":" BPStaging.LoadMarketInteractionFacility", "TargetConnection":"PlacementStore"}', 'dbo.MarketInteractionFacilities')
  , (@LoadType_Intraday, 'BPStaging.LoadMarketResponseSecurity', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadMarketResponseSecurity", "TargetConnection":"PlacementStore"}', 'dbo.MarketResponseSecurity')
  , (@LoadType_Intraday, 'BPStaging.LoadMarketSelection', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadMarketSelection", "TargetConnection":"PlacementStore"}', 'dbo.MarketSelection')
  , (@LoadType_Intraday, 'BPStaging.LoadMergedPlacements', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadMergedPlacements", "TargetConnection":"PlacementStore"}', 'dbo.MergedPlacements')
  , (@LoadType_Intraday, 'BPStaging.LoadNewClient', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadNewClient", "TargetConnection":"PlacementStore"}', 'dbo.NewClient')
  , (@LoadType_Intraday, 'BPStaging.LoadOutcomeReason', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadOutcomeReason", "TargetConnection":"PlacementStore"}', 'ref.OutcomeReason')
  , (@LoadType_Intraday, 'BPStaging.LoadOutcomeStatus', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadOutcomeStatus", "TargetConnection":"PlacementStore"}', 'ref.OutcomeStatus')
  , (@LoadType_Intraday, 'BPStaging.LoadParticipantFunction', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadParticipantFunction", "TargetConnection":"PlacementStore"}', 'ref.ParticipantFunction')
  , (@LoadType_Intraday, 'BPStaging.LoadPaymentPeriod', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadPaymentPeriod", "TargetConnection":"PlacementStore"}', 'dbo.PaymentPeriod')
  , (@LoadType_Intraday, 'BPStaging.LoadPendingActionReason', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadPendingActionReason", "TargetConnection":"PlacementStore"}', 'ref.PendingActionReason')
  , (@LoadType_Intraday, 'BPStaging.LoadPlacement', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadPlacement", "TargetConnection":"PlacementStore"}', 'dbo.Placement')
  , (@LoadType_Intraday, 'BPStaging.LoadPlacementExtension', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadPlacementExtension", "TargetConnection":"PlacementStore"}', 'dbo.PlacementExtension')
  , (@LoadType_Intraday, 'BPStaging.LoadPlacementOpportunity', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadPlacementOpportunity", "TargetConnection":"PlacementStore"}', 'dbo.PlacementOpportunity')
  , (@LoadType_Intraday, 'BPStaging.LoadPlacementPartyRole', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadPlacementPartyRole", "TargetConnection":"PlacementStore"}', 'dbo.PlacementPartyRole')
  , (@LoadType_Intraday, 'BPStaging.LoadPlacementPolicies', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadPlacementPolicies", "TargetConnection":"PlacementStore"}', 'dbo.PlacementPolicy')
  , (@LoadType_Intraday, 'BPStaging.LoadPlacementPolicyRelationshipType', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadPlacementPolicyRelationshipType", "TargetConnection":"PlacementStore"}', 'dbo.PlacementPolicyRelationshipType')
  , (@LoadType_Intraday, 'BPStaging.LoadPlacementPremium', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadPlacementPremium", "TargetConnection":"PlacementStore"}', 'dbo.PlacementPremium')
  , (@LoadType_Intraday, 'BPStaging.LoadPlacementProduct', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadPlacementProduct", "TargetConnection":"PlacementStore"}', 'dbo.PlacementProduct')
  , (@LoadType_Intraday, 'BPStaging.LoadPlacementRiskDefinitionElement', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadPlacementRiskDefinitionElement", "TargetConnection":"PlacementStore"}', 'dbo.PlacementRiskDefinitionElement')
  , (@LoadType_Intraday, 'BPStaging.LoadPlacementStatus', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadPlacementStatus", "TargetConnection":"PlacementStore"}', 'PS.PlacementStatus')
  , (@LoadType_Intraday, 'BPStaging.LoadPlacementStructure', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadPlacementStructure", "TargetConnection":"PlacementStore"}', 'dbo.PlacementStructure')
  , (@LoadType_Intraday, 'BPStaging.LoadPlacementSystemAudit', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadPlacementSystemAudit", "TargetConnection":"PlacementStore"}', 'dbo.PlacementSystemAudit')
  , (@LoadType_Intraday, 'BPStaging.LoadPlacementsystemtable', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadPlacementsystemtable", "TargetConnection":"PlacementStore"}', 'dbo.PlacementSystemTable')
  , (@LoadType_Intraday, 'BPStaging.LoadPlacementSystemUser', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadPlacementSystemUser", "TargetConnection":"PlacementStore"}', 'dbo.PlacementSystemUser')
  , (@LoadType_Intraday, 'BPStaging.LoadPlacementTasks', @ProcessType_SP, '{"StoredProcedure":" BPStaging.LoadPlacementTasks", "TargetConnection":"PlacementStore"}', 'dbo.PlacementTasks')
  , (@LoadType_Intraday, 'BPStaging.LoadPlacementTeamMember', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadPlacementTeamMember", "TargetConnection":"PlacementStore"}', 'dbo.PlacementTeamMember')
  , (@LoadType_Intraday, 'BPStaging.LoadPlacementTeams', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadPlacementTeams", "TargetConnection":"PlacementStore"}', 'dbo.PlacementTeams')
  , (@LoadType_Intraday, 'BPStaging.LoadPremiumAdjustableIndicator', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadPremiumAdjustableIndicator", "TargetConnection":"PlacementStore"}', 'ref.PremiumAdjustableIndicator')
  , (@LoadType_Intraday, 'BPStaging.LoadPremiumType', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadPremiumType", "TargetConnection":"PlacementStore"}', 'ref.PremiumType')
  , (@LoadType_Intraday, 'BPStaging.LoadProductsFromElements', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadProductsFromElements", "TargetConnection":"PlacementStore"}', 'dbo.Product')
  , (@LoadType_Intraday, 'BPStaging.LoadRegion', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadRegion", "TargetConnection":"PlacementStore"}', 'ref.Region')
  , (@LoadType_Intraday, 'BPStaging.LoadRegulatoryClientClassification', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadRegulatoryClientClassification", "TargetConnection":"PlacementStore"}', 'ref.RegulatoryClientClassification')
  , (@LoadType_Intraday, 'BPStaging.LoadResponseType', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadResponseType", "TargetConnection":"PlacementStore"}', 'ref.ResponseType')
  , (@LoadType_Intraday, 'BPStaging.LoadRiskClassification', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadRiskClassification", "TargetConnection":"PlacementStore"}', 'ref.RiskClassification')
  , (@LoadType_Intraday, 'BPStaging.LoadRiskCode', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadRiskCode", "TargetConnection":"PlacementStore"}', 'ref.RiskCode')
  , (@LoadType_Intraday, 'BPStaging.LoadRiskLocation', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadRiskLocation", "TargetConnection":"PlacementStore"}', 'dbo.RiskLocation')
  , (@LoadType_Intraday, 'BPStaging.LoadScope', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadScope", "TargetConnection":"PlacementStore"}', 'dbo.Scope')
  , (@LoadType_Intraday, 'BPStaging.LoadSelectedReason', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadSelectedReason", "TargetConnection":"PlacementStore"}', 'dbo.SelectedReason')
  , (@LoadType_Intraday, 'BPStaging.LoadServiceLevel', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadServiceLevel", "TargetConnection":"PlacementStore"}', 'ref.ServiceLevel')
  , (@LoadType_Intraday, 'BPStaging.LoadServiceLevelIssue', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadServiceLevelIssue", "TargetConnection":"PlacementStore"}', 'ref.ServiceLevelIssue')
  , (@LoadType_Intraday, 'BPStaging.LoadServicingRole', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadServicingRole", "TargetConnection":"PlacementStore"}', 'ref.ServicingRole')
  , (@LoadType_Intraday, 'BPStaging.LoadSpecification', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadSpecification", "TargetConnection":"PlacementStore"}', 'dbo.Specification')
  , (@LoadType_Intraday, 'BPStaging.LoadSubmissionCoverageGroup', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadSubmissionCoverageGroup", "TargetConnection":"PlacementStore"}', 'dbo.SubmissionCoverageGroup')
  , (@LoadType_Intraday, 'BPStaging.LoadSubmissionPortalDetails', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadSubmissionPortalDetails", "TargetConnection":"PlacementStore"}', 'dbo.SubmissionPortalDetails')
  , (@LoadType_Intraday, 'BPStaging.LoadTaxCode', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadTaxCode", "TargetConnection":"PlacementStore"}', 'ref.TaxCode')
  , (@LoadType_Intraday, 'BPStaging.LoadTaxRateBasis', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadTaxRateBasis", "TargetConnection":"PlacementStore"}', 'ref.TaxRateBasis')
  , (@LoadType_Intraday, 'BPStaging.LoadTeamMember', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadTeamMember", "TargetConnection":"PlacementStore"}', 'dbo.TeamMember')
  , (@LoadType_Intraday, 'BPStaging.LoadTerritory', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadTerritory", "TargetConnection":"PlacementStore"}', 'ref.Territory')
  , (@LoadType_Intraday, 'BPStaging.LoadTerritoryCountry', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadTerritoryCountry", "TargetConnection":"PlacementStore"}', 'dbo.TerritoryCountry')
  , (@LoadType_Intraday, 'BPStaging.LoadTimeZoneRepresention', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadTimeZoneRepresention", "TargetConnection":"PlacementStore"}', 'ref.TimeZoneRepresention')
  , (@LoadType_Intraday, 'BPStaging.LoadUserScope', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadUserScope", "TargetConnection":"PlacementStore"}', 'dbo.UserScope')
  , (@LoadType_Intraday, 'BPStaging.LoadValue', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadValue", "TargetConnection":"PlacementStore"}', 'dbo.Value')
  , (@LoadType_Intraday, 'BPStaging.LoadValueType', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadValueType", "TargetConnection":"PlacementStore"}', 'dbo.ValueType')
  , (@LoadType_Intraday, 'BPStaging.LoadValueTypeLookupValue', @ProcessType_SP, '{"StoredProcedure":"BPStaging.LoadValueTypeLookupValue", "TargetConnection":"PlacementStore"}', 'dbo.ValueTypeLookupValue')
  , (@LoadType_Intraday, 'BPStaging.MarketInteractionFacility', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_FacilityCarrierResponse_Export", "SourceSQL":"", "TargetTable":"BPStaging.MarketInteractionFacility", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":true }', 'BPStaging.MarketInteractionFacility')
  , (@LoadType_Intraday, 'BPStaging.MarketKind', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_MarketKind_Export", "SourceSQL":"", "TargetTable":"BPStaging.MarketKind", "TruncateTargetTable": true, "AlwaysFullLoad":true, "TargetConnection":"PlacementStore"}', 'BPStaging.MarketKind')
  , (@LoadType_Intraday, 'BPStaging.MarketQuoteResponse', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_MarketQuoteResponse_export", "SourceSQL":"", "TargetTable":"BPStaging.MarketQuoteResponse", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "SystemVersioned":true, "IncrementalUpdatedDateColumn":"ValidFrom"}', 'BPStaging.MarketQuoteResponse')
  , (@LoadType_Intraday, 'BPStaging.MarketQuoteResponseAcceptedDates', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_MarketQuoteResponse_export", "SourceSQL":"WITH previousOutcomeStatus AS ( SELECT Id , OutcomeStatusId , ValidFrom , PreviousOutcomeStatusId = LAG(OutcomeStatusId, 1) OVER (PARTITION BY Id ORDER BY ValidFrom ASC) FROM export.vw_MarketQuoteResponse_Export FOR SYSTEM_TIME ALL ) SELECT Id , FirstResponseAcceptedDate = MIN(ValidFrom) , LastResponseAcceptedDate = MAX(ValidFrom) FROM previousOutcomeStatus WHERE ISNULL(OutcomeStatusId, -1) = 1 AND ISNULL(PreviousOutcomeStatusId, -1) <> 1 GROUP BY Id;", "TargetTable":"BPStaging.MarketQuoteResponseAcceptedDates", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":true }', 'BPStaging.MarketQuoteResponseAcceptedDates')
  , (@LoadType_Intraday, 'BPStaging.MarketQuoteResponsePolicyRef', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_MarketQuoteResponsePolicyRef_Export", "SourceSQL":"", "TargetTable":"BPStaging.MarketQuoteResponsePolicyRef", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "SystemVersioned":true, "IncrementalUpdatedDateColumn":"ValidFrom", "AlwaysFullLoad":false }', 'BPStaging.MarketQuoteResponsePolicyRef')
  , (@LoadType_Intraday, 'BPStaging.MarketResponse', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_MarketResponse_Export", "SourceSQL":"", "TargetTable":"BPStaging.MarketResponse", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'BPStaging.MarketResponse')
  , (@LoadType_Intraday, 'BPStaging.MarketResponseAffirmationQuestion', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_MarketResponseAffirmationQuestion_Export", "SourceSQL":"", "TargetTable":"BPStaging.MarketResponseAffirmationQuestion", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "SystemVersioned":true, "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ValidFrom"}', 'BPStaging.MarketResponseAffirmationQuestion')
  , (@LoadType_Intraday, 'BPStaging.MarketResponseAutoFollowEligibilityRule', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_MarketResponseAutoFollowEligibilityRule_Export", "SourceSQL":"", "TargetTable":"BPStaging.MarketResponseAutoFollowEligibilityRule", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "SystemVersioned":true, "IncrementalUpdatedDateColumn":"ValidFrom", "AlwaysFullLoad":false}', 'BPStaging.MarketResponseAutoFollowEligibilityRule')
  , (@LoadType_Intraday, 'BPStaging.MarketResponseBasis', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_MarketResponseBasis_Export_v2", "SourceSQL":"", "TargetTable":"BPStaging.MarketResponseBasis", "TruncateTargetTable": true, "TargetConnection":"PlacementStore" ,"SystemVersioned":true,"AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ValidFrom"}', 'BPStaging.MarketResponseBasis')
  , (@LoadType_Intraday, 'BPStaging.MarketResponseElement', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_MarketResponseElement_Export", "SourceSQL":"", "TargetTable":"BPStaging.MarketResponseElement", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "SystemVersioned":true, "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ValidFrom"}', 'BPStaging.MarketResponseElement')
  , (@LoadType_Intraday, 'BPStaging.MarketResponseSecurity', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_MarketSecurity_Export", "SourceSQL":"", "TargetTable":"BPStaging.MarketResponseSecurity", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'BPStaging.MarketResponseSecurity')
  , (@LoadType_Intraday, 'BPStaging.MarketSelection', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_MarketSelection_Export", "SourceSQL":"SELECT DISTINCT Id , PlacementId , CarrierId , Incumbent , ReasonForSelectionId , IsDeleted , JustificationReasonId , JustificationReasonTypeId , ValidFrom , ValidTo , FacilityId , MarketKindId , NotApplicable FROM export.vw_MarketSelection_Export ms WHERE NotApplicable = 0 UNION SELECT DISTINCT Id , PlacementId , CarrierId , Incumbent , ReasonForSelectionId , IsDeleted , JustificationReasonId , JustificationReasonTypeId , ValidFrom , ValidTo , FacilityId , MarketKindId , NotApplicable FROM export.vw_MarketSelection_Export ms2 WHERE EXISTS ( SELECT 1 FROM export.vw_CarrierResponse_Export ce WHERE ce.PlacementId = ms2.PlacementId AND ce.FacilityId = ms2.FacilityId ); /* included NA Facility if has response 3rd time suggestion */", "TargetTable":"BPStaging.MarketSelection", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":true }', 'BPStaging.MarketSelection')
  , (@LoadType_Intraday, 'BPStaging.MergedPlacements', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_PlacementMergedFrom_Export", "SourceSQL":"", "TargetTable":"BPStaging.MergedPlacements", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":true }', 'BPStaging.MergedPlacements')
  , (@LoadType_Intraday, 'BPStaging.MetaTag', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_MetaTag_Export", "SourceSQL":"", "TargetTable":"BPStaging.MetaTag", "TruncateTargetTable": true, "AlwaysFullLoad":true, "TargetConnection":"PlacementStore"}', 'BPStaging.MetaTag')
  , (@LoadType_Intraday, 'BPStaging.MTAType', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_MTAType_Export", "SourceSQL":"", "TargetTable":"BPStaging.MTAType", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "IncrementalUpdatedDateColumn":"ValidFrom", "AlwaysFullLoad":false }', 'BPStaging.MTAType')
  , (@LoadType_Intraday, 'BPStaging.NewClient', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_NewClient_Export", "SourceSQL":"", "TargetTable":"BPStaging.NewClient", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "SystemVersioned":true, "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ValidFrom"}', 'BPStaging.NewClient')
  , (@LoadType_Intraday, 'BPStaging.OpportunityPlacement', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_OpportunityPlacement_Export", "SourceSQL":"", "TargetTable":"BPStaging.OpportunityPlacement", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "SystemVersioned":true, "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ValidFrom"}', 'BPStaging.OpportunityPlacement')
  , (@LoadType_Intraday, 'BPStaging.OpportunityType', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_OpportunityType_Export", "SourceSQL":"", "TargetTable":"BPStaging.OpportunityType", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":true }', 'BPStaging.OpportunityType')
  , (@LoadType_Intraday, 'BPStaging.OutcomeReason', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_OutcomeReason_Export", "SourceSQL":"", "TargetTable":"BPStaging.OutcomeReason", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'BPStaging.OutcomeReason')
  , (@LoadType_Intraday, 'BPStaging.OutcomeStatus', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_OutcomeStatus_Export", "SourceSQL":"", "TargetTable":"BPStaging.OutcomeStatus", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'BPStaging.OutcomeStatus')
  , (@LoadType_Intraday, 'BPStaging.ParticipantFunction', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_ParticipantFunction_Export", "SourceSQL":"", "TargetTable":"BPStaging.ParticipantFunction", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'BPStaging.ParticipantFunction')
  , (@LoadType_Intraday, 'BPStaging.PaymentPeriod', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_PaymentPeriod", "SourceSQL":"", "TargetTable":"BPStaging.PaymentPeriod", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'BPStaging.PaymentPeriod')
  , (@LoadType_Intraday, 'BPStaging.PendingActionReason', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_PendingActionReason_Export", "SourceSQL":"", "TargetTable":"BPStaging.PendingActionReason", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'BPStaging.PendingActionReason')
  , (@LoadType_Intraday, 'BPStaging.Placement', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_Placement_v3_Export", "SourceSQL":"", "TargetTable":"BPStaging.Placement", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "SystemVersioned":true, "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ValidFrom"}', 'BPStaging.Placement')
  , (@LoadType_Intraday, 'BPStaging.PlacementAffirmationQuestion', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_PlacementAffirmationQuestion_Export", "SourceSQL":"", "TargetTable":"BPStaging.PlacementAffirmationQuestion", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "SystemVersioned":true, "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ValidFrom"}', 'BPStaging.PlacementAffirmationQuestion')
  , (@LoadType_Intraday, 'BPStaging.PlacementCancellationReason', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_PlacementCancellationReason_Export", "SourceSQL":"", "TargetTable":"BPStaging.PlacementCancellationReason", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ValidFrom"}', 'BPStaging.PlacementCancellationReason')
  , (@LoadType_Intraday, 'BPStaging.PlacementExposureSummary', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_PlacementExposureSummary", "SourceSQL":"", "TargetTable":"BPStaging.PlacementExposureSummary", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "SystemVersioned":true, "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ValidFrom"}', 'BPStaging.PlacementExposureSummary')
  , (@LoadType_Intraday, 'BPStaging.PlacementExposureSummaryGroup', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_PlacementExposureSummaryGroup", "SourceSQL":"", "TargetTable":"BPStaging.PlacementExposureSummaryGroup", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "SystemVersioned":true, "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ValidFrom"}', 'BPStaging.PlacementExposureSummaryGroup')
  , (@LoadType_Intraday, 'BPStaging.PlacementExtension', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_AdditionalData_Export", "SourceSQL":"", "TargetTable":"BPStaging.PlacementExtension", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'BPStaging.PlacementExtension')
  , (@LoadType_Intraday, 'BPStaging.PlacementMarketValidation', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_PlacementMarketValidation_Export", "SourceSQL":"", "TargetTable":"BPStaging.PlacementMarketValidation", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'BPStaging.PlacementMarketValidation')
  , (@LoadType_Intraday, 'BPStaging.PlacementPartyRelationship', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_Insured_Export", "SourceSQL":"", "TargetTable":"BPStaging.PlacementPartyRelationship", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":true }', 'BPStaging.PlacementPartyRelationship')
  , (@LoadType_Intraday, 'BPStaging.PlacementPolicy', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_PlacementPolicy_Export", "SourceSQL":"", "TargetTable":"BPStaging.PlacementPolicy", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "SystemVersioned":true, "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ValidFrom"}', 'BPStaging.PlacementPolicy')
  , (@LoadType_Intraday, 'BPStaging.PlacementRuleValidation', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_PlacementRuleValidation_Export", "SourceSQL":"", "TargetTable":"BPStaging.PlacementRuleValidation", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'BPStaging.PlacementRuleValidation')
  , (@LoadType_Intraday, 'BPStaging.PlacementServiceSatisfactionValidation', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_PlacementServiceSatisfactionValidation_Export", "SourceSQL":"", "TargetTable":"BPStaging.PlacementServiceSatisfactionValidation", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'BPStaging.PlacementServiceSatisfactionValidation')
  , (@LoadType_Intraday, 'BPStaging.PlacementStatus', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_PlacementStatus_Export", "SourceSQL":"", "TargetTable":"BPStaging.PlacementStatus", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ValidFrom"}', 'BPStaging.PlacementStatus')
  , (@LoadType_Intraday, 'BPStaging.PlacementSystemAudit', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.Placement_Timeline_Reporting", "SourceSQL":"EXEC export.Placement_Timeline_Reporting @StartDate = @ETLUpdatedDate WITH RESULT SETS ((      ModifiedById     INT NULL, PlacementId      INT NULL, TableId          INT NULL, TableKey         INT NULL, DateOfEvent      DATETIME2 (7)  NULL, Description      NVARCHAR (MAX) NULL, UserPrincipalName NVARCHAR (MAX) NULL, Detail            NVARCHAR (MAX) NULL, UserId            INT NULL, GroupingSection   NVARCHAR (MAX) NULL, GroupingSectionType INT NULL, EventType    NVARCHAR (255)  NULL     ))", "TargetTable":"BPStaging.PlacementSystemAudit", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"DateOfEvent"}', 'BPStaging.PlacementSystemAudit')
  , (@LoadType_Intraday, 'BPStaging.PlacementSystemTable', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_AuditedTemporalTable_Export", "SourceSQL":"", "TargetTable":"BPStaging.PlacementSystemTable", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'BPStaging.PlacementSystemTable')
  , (@LoadType_Intraday, 'BPStaging.PlacementSystemUser', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_User_Export", "SourceSQL":"", "TargetTable":"BPStaging.PlacementSystemUser", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'BPStaging.PlacementSystemUser')
  , (@LoadType_Intraday, 'BPStaging.PlacementTasks', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_PlacementTasks", "SourceSQL":"", "TargetTable":"BPStaging.PlacementTasks", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":true }', 'BPStaging.PlacementTasks')
  , (@LoadType_Intraday, 'BPStaging.PlacementTeamMember', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_PlacementTeamMember_Export", "SourceSQL":"", "TargetTable":"BPStaging.PlacementTeamMember", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'BPStaging.PlacementTeamMember')
  , (@LoadType_Intraday, 'BPStaging.PlacementTeams', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_PlacementUserGroup_Export", "SourceSQL":"", "TargetTable":"BPStaging.PlacementTeams", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":true }', 'BPStaging.PlacementTeams')
  , (@LoadType_Intraday, 'BPStaging.PolicyRefType', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_PolicyRefType_Export", "SourceSQL":"", "TargetTable":"BPStaging.PolicyRefType", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "SystemVersioned":true, "IncrementalUpdatedDateColumn":"ValidFrom", "AlwaysFullLoad":false }', 'BPStaging.PolicyRefType')
  , (@LoadType_Intraday, 'BPStaging.PolicyType', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_PolicyType_Export", "SourceSQL":"", "TargetTable":"BPStaging.PolicyType", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'BPStaging.PolicyType')
  , (@LoadType_Intraday, 'BPStaging.POPlacementMarketResponseBasis', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_POPlacementMarketResponseBasis_Export", "SourceSQL":"", "TargetTable":"BPStaging.POPlacementMarketResponseBasis", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "SystemVersioned":true, "AlwaysFullLoad":true }', 'BPStaging.POPlacementMarketResponseBasis')
  , (@LoadType_Intraday, 'BPStaging.PortalRejectionReason', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_PortalRejectionReason_Export", "SourceSQL":"", "TargetTable":"BPStaging.PortalRejectionReason", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "SystemVersioned":true, "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ValidFrom"}', 'BPStaging.PortalRejectionReason')
  , (@LoadType_Intraday, 'BPStaging.PortalUserDocument', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_PortalUserDocument_export", "SourceSQL":"", "TargetTable":"BPStaging.PortalUserDocument", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "SystemVersioned":true, "IncrementalUpdatedDateColumn":"ValidFrom"}', 'BPStaging.PortalUserDocument')
  , (@LoadType_Intraday, 'BPStaging.PremiumAdjustableIndicator', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_PremiumAdjustableIndicator_Export", "SourceSQL":"", "TargetTable":"BPStaging.PremiumAdjustableIndicator", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'BPStaging.PremiumAdjustableIndicator')
  , (@LoadType_Intraday, 'BPStaging.PremiumType', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_PremiumType_Export", "SourceSQL":"", "TargetTable":"BPStaging.PremiumType", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'BPStaging.PremiumType')
  , (@LoadType_Intraday, 'BPStaging.PricingFactor', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_PricingFactor_Export", "SourceSQL":"", "TargetTable":"BPStaging.PricingFactor", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'BPStaging.PricingFactor')
  , (@LoadType_Intraday, 'BPStaging.ProducingOfficeHubPlacement', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_ProducingOfficeHubPlacement_Export", "SourceSQL":"", "TargetTable":"BPStaging.ProducingOfficeHubPlacement", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "SystemVersioned":true, "AlwaysFullLoad":true }', 'BPStaging.ProducingOfficeHubPlacement')
  , (@LoadType_Intraday, 'BPStaging.ProgramStructureType', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_ProgramStructureType_Export", "SourceSQL":"", "TargetTable":"BPStaging.ProgramStructureType", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'BPStaging.ProgramStructureType')
  , (@LoadType_Intraday, 'BPStaging.QuoteComparison', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_QuoteComparison_Export_v2", "SourceSQL":"", "TargetTable":"BPStaging.QuoteComparison", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "SystemVersioned":true, "AlwaysFullLoad":false,"IncrementalUpdatedDateColumn":"ValidFrom"}', 'BPStaging.QuoteComparison')
  , (@LoadType_Intraday, 'BPStaging.QuoteComparisonQuote', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_QuoteComparisonQuote_Export_v2", "SourceSQL":"", "TargetTable":"BPStaging.QuoteComparisonQuote", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "SystemVersioned":true, "AlwaysFullLoad":false , "IncrementalUpdatedDateColumn":"ValidFrom"}', 'BPStaging.QuoteComparisonQuote')
  , (@LoadType_Intraday, 'BPStaging.Region', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_Region_Export", "SourceSQL":"", "TargetTable":"BPStaging.Region", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":true }', 'BPStaging.Region')
  , (@LoadType_Intraday, 'BPStaging.RegulatoryClientClassification', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_RegulatoryClientClassification_Export", "SourceSQL":"", "TargetTable":"BPStaging.RegulatoryClientClassification", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'BPStaging.RegulatoryClientClassification')
  , (@LoadType_Intraday, 'BPStaging.RequestedCoverageElement', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_RequestedCoverageElement_Export", "SourceSQL":"", "TargetTable":"BPStaging.RequestedCoverageElement", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "SystemVersioned":true, "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ValidFrom"}', 'BPStaging.RequestedCoverageElement')
  , (@LoadType_Intraday, 'BPStaging.ResponseManagementElement', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_ResponseManagementElement_Export", "SourceSQL":"", "TargetTable":"BPStaging.ResponseManagementElement", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "SystemVersioned":true, "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ValidFrom"}', 'BPStaging.ResponseManagementElement')
  , (@LoadType_Intraday, 'BPStaging.ResponseManagementElementRiskDefinitionElement', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_ResponseManagementElementRiskDefinitionElement_Export", "SourceSQL":"", "TargetTable":"BPStaging.ResponseManagementElementRiskDefinitionElement", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "SystemVersioned":true, "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ValidFrom"}', 'BPStaging.ResponseManagementElementRiskDefinitionElement')
  , (@LoadType_Intraday, 'BPStaging.ResponseType', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_ResponseType_Export", "SourceSQL":"", "TargetTable":"BPStaging.ResponseType", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'BPStaging.ResponseType')
  , (@LoadType_Intraday, 'BPStaging.RiskAndAnalyticsModelType', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_RiskAndAnalyticsModelType", "SourceSQL":"", "TargetTable":"BPStaging.RiskAndAnalyticsModelType", "TruncateTargetTable": true, "AlwaysFullLoad":true, "TargetConnection":"PlacementStore"}', 'BPStaging.RiskAndAnalyticsRunMapping')
  , (@LoadType_Intraday, 'BPStaging.RiskAndAnalyticsRunMapping', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_RiskAndAnalyticsRunMapping_Export", "SourceSQL":"", "TargetTable":"BPStaging.RiskAndAnalyticsRunMapping", "TruncateTargetTable": true, "SystemVersioned":true, "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ValidFrom", "TargetConnection":"PlacementStore"}', 'BPStaging.RiskAndAnalyticsRunMapping')
  , (@LoadType_Intraday, 'BPStaging.RiskClassification', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_RiskClassification_Export", "SourceSQL":"", "TargetTable":"BPStaging.RiskClassification", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'BPStaging.RiskClassification')
  , (@LoadType_Intraday, 'BPStaging.RiskCode', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_RiskCode_Export", "SourceSQL":"", "TargetTable":"BPStaging.RiskCode", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'BPStaging.RiskCode')
  , (@LoadType_Intraday, 'BPStaging.RiskLocation', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_RiskLocation_Export", "SourceSQL":"", "TargetTable":"BPStaging.RiskLocation", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'BPStaging.RiskLocation')
  , (@LoadType_Intraday, 'BPStaging.RiskRequest', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_RiskRequest_Export", "SourceSQL":"", "TargetTable":"BPStaging.RiskRequest", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "SystemVersioned":true, "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ValidFrom"}', 'BPStaging.RiskRequest')
  , (@LoadType_Intraday, 'BPStaging.RiskStructure', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_RiskStructure_Export", "SourceSQL":"", "TargetTable":"BPStaging.RiskStructure", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":true }', 'BPStaging.RiskStructure')
  , (@LoadType_Intraday, 'BPStaging.Role', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_Roles_Export", "SourceSQL":"", "TargetTable":"BPStaging.Role", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'BPStaging.Role')
  , (@LoadType_Intraday, 'BPStaging.Scope', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_Scope_Export", "SourceSQL":"", "TargetTable":"BPStaging.Scope", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'BPStaging.Scope')
  , (@LoadType_Intraday, 'BPStaging.SelectedReason', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_SelectionReason_Export", "SourceSQL":"", "TargetTable":"BPStaging.SelectedReason", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'BPStaging.SelectedReason')
  , (@LoadType_Intraday, 'BPStaging.ServiceLevel', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_ServiceLevel_Export", "SourceSQL":"", "TargetTable":"BPStaging.ServiceLevel", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'BPStaging.ServiceLevel')
  , (@LoadType_Intraday, 'BPStaging.ServiceLevelIssue', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_ServiceLevelIssue_Export", "SourceSQL":"", "TargetTable":"BPStaging.ServiceLevelIssue", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'BPStaging.ServiceLevelIssue')
  , (@LoadType_Intraday, 'BPStaging.ServiceSatisfactionIssueOutcome', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_ServiceSatisfactionIssueOutcome_Export", "SourceSQL":"", "TargetTable":"BPStaging.ServiceSatisfactionIssueOutcome", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'BPStaging.ServiceSatisfactionIssueOutcome')
  , (@LoadType_Intraday, 'BPStaging.Specification', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_Specification_Export", "SourceSQL":"", "TargetTable":"BPStaging.Specification", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'BPStaging.Specification')
  , (@LoadType_Intraday, 'BPStaging.Strategy', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_Strategy_Export", "SourceSQL":"", "TargetTable":"BPStaging.Strategy", "TruncateTargetTable": true,"SystemVersioned":true,"IncrementalUpdatedDateColumn":"ValidFrom", "TargetConnection":"PlacementStore", "AlwaysFullLoad":false}', 'BPStaging.Strategy')
  , (@LoadType_Intraday, 'BPStaging.StrategyProposedSignedLine', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_StrategyProposedSignedLine_Export", "SourceSQL":"", "TargetTable":"BPStaging.StrategyProposedSignedLine", "TruncateTargetTable": true,"SystemVersioned":true,"IncrementalUpdatedDateColumn":"ValidFrom", "TargetConnection":"PlacementStore", "AlwaysFullLoad":false}', 'BPStaging.StrategyProposedSignedLine')
  , (@LoadType_Intraday, 'BPStaging.Submission', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_Submission_export", "SourceSQL":"", "TargetTable":"BPStaging.Submission", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "SystemVersioned":false, "IncrementalUpdatedDateColumn":"Sent"}', 'BPStaging.Submission')
  , (@LoadType_Intraday, 'BPStaging.SubmissionContainer', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_SubmissionContainer_Export", "SourceSQL":"", "TargetTable":"BPStaging.SubmissionContainer", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'BPStaging.SubmissionContainer')
  , (@LoadType_Intraday, 'BPStaging.SubmissionContainerCarrier', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_SubmissionContainerCarrier_export", "SourceSQL":"", "TargetTable":"BPStaging.SubmissionContainerCarrier", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "SystemVersioned":true, "IncrementalUpdatedDateColumn":"ValidFrom"}', 'BPStaging.SubmissionContainerCarrier')
  , (@LoadType_Intraday, 'BPStaging.SubmissionContainerContract', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_SubmissionContainerContract_Export", "SourceSQL":"", "TargetTable":"BPStaging.SubmissionContainerContract", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "SystemVersioned":true, "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ValidFrom"}', 'BPStaging.SubmissionContainerContract')
  , (@LoadType_Intraday, 'BPStaging.SubmissionContainerFacility', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_SubmissionContainerFacility_export", "SourceSQL":"", "TargetTable":"BPStaging.SubmissionContainerFacility", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "SystemVersioned":true, "IncrementalUpdatedDateColumn":"ValidFrom"}', 'BPStaging.SubmissionContainerFacility')
  , (@LoadType_Intraday, 'BPStaging.SubmissionContainerMarket', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_SubmissionContainerMarket_export", "SourceSQL":"", "TargetTable":"BPStaging.SubmissionContainerMarket", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "SystemVersioned":true, "IncrementalUpdatedDateColumn":"ValidFrom"}', 'BPStaging.SubmissionContainerMarket')
  , (@LoadType_Intraday, 'BPStaging.SubmissionContainerMarketContract', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_SubmissionContainerMarketContract_Export", "SourceSQL":"", "TargetTable":"BPStaging.SubmissionContainerMarketContract","TruncateTargetTable": true, "SystemVersioned":true,"TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ValidFrom"}', 'BPStaging.SubmissionContainerMarketContract')
  , (@LoadType_Intraday, 'BPStaging.SubmissionContainerPanel', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_SubmissionContainerPanel_export", "SourceSQL":"", "TargetTable":"BPStaging.SubmissionContainerPanel", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "SystemVersioned":true, "IncrementalUpdatedDateColumn":"ValidFrom"}', 'BPStaging.SubmissionContainerPanel')
  , (@LoadType_Intraday, 'BPStaging.SubmissionContainerPanelMember', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_SubmissionContainerPanelMember_export", "SourceSQL":"", "TargetTable":"BPStaging.SubmissionContainerPanelMember", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "SystemVersioned":true, "IncrementalUpdatedDateColumn":"ValidFrom"}', 'BPStaging.SubmissionContainerPanelMember')
  , (@LoadType_Intraday, 'BPStaging.SubmissionContainerRequestedCoverageElement', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_SubmissionContainerRequestedCoverageElement_Export", "SourceSQL":"", "TargetTable":"BPStaging.SubmissionContainerRequestedCoverageElement", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "SystemVersioned":true, "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ValidFrom"}', 'BPStaging.SubmissionContainerRequestedCoverageElement')
  , (@LoadType_Intraday, 'BPStaging.SubmissionContainerRiskDefinitionItem', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_SubmissionContainerRiskDefinitionItem_Export", "SourceSQL":"", "TargetTable":"BPStaging.SubmissionContainerRiskDefinitionItem", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "SystemVersioned":true,"AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ValidFrom"}', 'BPStaging.SubmissionContainerRiskDefinitionItem')
  , (@LoadType_Intraday, 'BPStaging.SubmissionContainerThirdPartyMarket', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_SubmissionContainerThirdPartyMarket_export", "SourceSQL":"", "TargetTable":"BPStaging.SubmissionContainerThirdPartyMarket", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "SystemVersioned":true, "IncrementalUpdatedDateColumn":"ValidFrom"}', 'BPStaging.SubmissionContainerThirdPartyMarket')
  , (@LoadType_Intraday, 'BPStaging.SubmissionCoverageGroup', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_SubmissionCoverageGroup_Export", "SourceSQL":"", "TargetTable":"BPStaging.SubmissionCoverageGroup", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'BPStaging.SubmissionCoverageGroup')
  , (@LoadType_Intraday, 'BPStaging.SubmissionDocument', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_SubmissionDocument_export", "SourceSQL":"", "TargetTable":"BPStaging.SubmissionDocument", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "SystemVersioned":true, "IncrementalUpdatedDateColumn":"ValidFrom"}', 'BPStaging.SubmissionDocument')
  , (@LoadType_Intraday, 'BPStaging.SubmissionMarket_v2', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_SubmissionMarket_Export_v2", "SourceSQL":"", "TargetTable":"BPStaging.SubmissionMarket_v2", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "SystemVersioned":true, "IncrementalUpdatedDateColumn":"ValidFrom"}', 'BPStaging.SubmissionMarket_v2')
  , (@LoadType_Intraday, 'BPStaging.SubmissionMarketRecipient', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_SubmissionMarketRecipient_export", "SourceSQL":"", "TargetTable":"BPStaging.SubmissionMarketRecipient", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "SystemVersioned":true, "IncrementalUpdatedDateColumn":"ValidFrom"}', 'BPStaging.SubmissionMarketRecipient')
  , (@LoadType_Intraday, 'BPStaging.SubmissionPortalDetails', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_SubmissionPortalDetails", "SourceSQL":"", "TargetTable":"BPStaging.SubmissionPortalDetails", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'BPStaging.SubmissionPortalDetails')
  , (@LoadType_Intraday, 'BPStaging.SubmissionPortalUser', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_SubmissionPortalUser_export", "SourceSQL":"", "TargetTable":"BPStaging.SubmissionPortalUser", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "SystemVersioned":true, "IncrementalUpdatedDateColumn":"ValidFrom"}', 'BPStaging.SubmissionPortalUser')
  , (@LoadType_Intraday, 'BPStaging.TaxCode', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_TaxCode_Export", "SourceSQL":"", "TargetTable":"BPStaging.TaxCode", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'BPStaging.TaxCode')
  , (@LoadType_Intraday, 'BPStaging.TaxRateBasis', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_TaxRateBasis_Export", "SourceSQL":"", "TargetTable":"BPStaging.TaxRateBasis", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'BPStaging.TaxRateBasis')
  , (@LoadType_Intraday, 'BPStaging.TeamMember', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_UserGroupMember_Export", "SourceSQL":"", "TargetTable":"BPStaging.TeamMember", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'BPStaging.TeamMember')
  , (@LoadType_Intraday, 'BPStaging.Territory', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_Territory_Export", "SourceSQL":"", "TargetTable":"BPStaging.Territory", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'BPStaging.Territory')
  , (@LoadType_Intraday, 'BPStaging.TerritoryCountry', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_TerritoryCountry_Export", "SourceSQL":"", "TargetTable":"BPStaging.TerritoryCountry", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'BPStaging.TerritoryCountry')
  , (@LoadType_Intraday, 'BPStaging.TimeZoneRepresention', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_TimeZoneRepresention_Export", "SourceSQL":"", "TargetTable":"BPStaging.TimeZoneRepresention", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'BPStaging.TimeZoneRepresention')
  , (@LoadType_Intraday, 'BPStaging.UpdateMarketSelectionIsLeadFlag', @ProcessType_SP, '{"StoredProcedure":"BPStaging.UpdateMarketSelectionIsLeadFlag", "TargetConnection":"PlacementStore"}', 'dbo.MarketSelection')
  , (@LoadType_Intraday, 'BPStaging.UpdatePlacement', @ProcessType_SP, '{"StoredProcedure":"BPStaging.UpdatePlacement", "TargetConnection":"PlacementStore"}', 'dbo.Placement')
  , (@LoadType_Intraday, 'BPStaging.UpdatePlacementDatesAndStatus', @ProcessType_SP, '{"StoredProcedure":"BPStaging.UpdatePlacementDatesAndStatus", "TargetConnection":"PlacementStore"}', 'dbo.Placement')
  , (@LoadType_Intraday, 'BPStaging.UpdatePlacementRiskDefinitionElement', @ProcessType_SP, '{"StoredProcedure":"BPStaging.UpdatePlacementRiskDefinitionElement", "TargetConnection":"PlacementStore"}', 'dbo.PlacementRiskDefinitionElement')
  , (@LoadType_Intraday, 'BPStaging.UserEnhancedBrokerDetails', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_UserEnhancedBrokerDetails_Export", "SourceSQL":"", "TargetTable":"BPStaging.UserEnhancedBrokerDetails", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":true }', 'BPStaging.UserEnhancedBrokerDetails')
  , (@LoadType_Intraday, 'BPStaging.UserGroup', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_UserGroup_Export", "SourceSQL":"", "TargetTable":"BPStaging.UserGroup", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "SystemVersioned":true, "IncrementalUpdatedDateColumn":"ValidFrom", "AlwaysFullLoad":false }', 'BPStaging.UserGroup')
  , (@LoadType_Intraday, 'BPStaging.UserScope', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_UserScope_Export", "SourceSQL":"", "TargetTable":"BPStaging.UserScope", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'BPStaging.UserScope')
  , (@LoadType_Intraday, 'BPStaging.ValidationRuleOutcome', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_ValidationRuleOutcome_Export", "SourceSQL":"", "TargetTable":"BPStaging.ValidationRuleOutcome", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'BPStaging.ValidationRuleOutcome')
  , (@LoadType_Intraday, 'BPStaging.Value', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_Value_Export", "SourceSQL":"", "TargetTable":"BPStaging.Value", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'BPStaging.Value')
  , (@LoadType_Intraday, 'BPStaging.ValueType', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_ValueType_Export", "SourceSQL":"", "TargetTable":"BPStaging.ValueType", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'BPStaging.ValueType')
  , (@LoadType_Intraday, 'BPStaging.ValueTypeLookupValue', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_ValueTypeLookupValue_Export", "SourceSQL":"", "TargetTable":"BPStaging.ValueTypeLookupValue", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "SystemVersioned":true, "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ValidFrom"}', 'BPStaging.ValueTypeLookupValue')
  , (@LoadType_Intraday, 'BPStaging.VerticalIndustry', @ProcessType_Copy, '{"SourceConnection":"BrokingPlatformRead", "SourceTable":"export.vw_VerticalIndustry_Export", "SourceSQL":"", "TargetTable":"BPStaging.VerticalIndustry", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":true }', 'BPStaging.VerticalIndustry')
  , (@LoadType_Intraday, 'CAMStaging.Load_ref_Panel', @ProcessType_SP, '{"StoredProcedure":"CAMStaging.Load_ref_Panel", "TargetConnection":"PlacementStore"}', 'ref.Panel')
  , (@LoadType_Intraday, 'CAMStaging.Load_ref_PanelMember', @ProcessType_SP, '{"StoredProcedure":"CAMStaging.Load_ref_PanelMember", "TargetConnection":"PlacementStore"}', 'ref.PanelMember')
  , (@LoadType_Intraday, 'CAMStaging.Load_ref_PanelMemberCarrier', @ProcessType_SP, '{"StoredProcedure":"CAMStaging.Load_ref_PanelMemberCarrier", "TargetConnection":"PlacementStore"}', 'ref.PanelMemberCarrier')
  , (@LoadType_Intraday, 'CAMStaging.Load_ref_PanelMemberContact', @ProcessType_SP, '{"StoredProcedure":"CAMStaging.Load_ref_PanelMemberContact", "TargetConnection":"PlacementStore"}', 'ref.PanelMemberContact')
  , (@LoadType_Intraday, 'CAMStaging.Load_ref_PanelMemberFacility', @ProcessType_SP, '{"StoredProcedure":"CAMStaging.Load_ref_PanelMemberFacility", "TargetConnection":"PlacementStore"}', 'ref.PanelMemberFacility')
  , (@LoadType_Intraday, 'CAMStaging.Panel', @ProcessType_Copy, '{"SourceConnection":"CAM", "SourceTable":"export.vw_Panel_Export", "SourceSQL":"", "TargetTable":"CAMStaging.Panel", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "DataSourceInstanceIdLookupSql":"SELECT Value = ADF.GetDataSourceInstanceList();", "DataSourceInstanceIdLookupConnection":"PlacementStore"}', 'CAMStaging.Panel')
  , (@LoadType_Intraday, 'CAMStaging.PanelMember', @ProcessType_Copy, '{"SourceConnection":"CAM", "SourceTable":"export.vw_PanelMember_Export", "SourceSQL":"", "TargetTable":"CAMStaging.PanelMember", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "DataSourceInstanceIdLookupSql":"SELECT Value = ADF.GetDataSourceInstanceList();", "DataSourceInstanceIdLookupConnection":"PlacementStore"}', 'CAMStaging.PanelMember')
  , (@LoadType_Intraday, 'CAMStaging.PanelMemberCarrier', @ProcessType_Copy, '{"SourceConnection":"CAM", "SourceTable":"export.vw_PanelMemberCarrier_Export", "SourceSQL":"", "TargetTable":"CAMStaging.PanelMemberCarrier", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'CAMStaging.PanelMemberCarrier')
  , (@LoadType_Intraday, 'CAMStaging.PanelMemberContact', @ProcessType_Copy, '{"SourceConnection":"CAM", "SourceTable":"export.vw_PanelMemberContact_Export", "SourceSQL":"", "TargetTable":"CAMStaging.PanelMemberContact", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'CAMStaging.PanelMemberContact')
  , (@LoadType_Intraday, 'CAMStaging.PanelMemberFacility', @ProcessType_Copy, '{"SourceConnection":"CAM", "SourceTable":"export.vw_PanelMemberFacility_Export", "SourceSQL":"SELECT pmf.Id, pmf.PanelMemberId, fac.RefFacilityId AS FacilityId, pmf.ApplicableFrom, pmf.ApplicableUntil, pmf.ValidFrom, pmf.ValidTo, pmf.IsExported, pmf.ExportedDate FROM export.vw_PanelMemberFacility_Export pmf INNER JOIN dbo.Facility fac ON fac.Id = pmf.FacilityId", "TargetTable":"CAMStaging.PanelMemberFacility", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'CAMStaging.PanelMemberFacility')
  , (@LoadType_Intraday, 'CMAStaging.ExecTable', @ProcessType_Copy, '{"SourceConnection":"ExcelFile", "FileShare":"datasource-cam-analytics", "FolderName":"CMAQI", "FileName":"ExecTable.xlsx", "SheetName":"Exec Data", "TargetTable":"CMAStaging.ExecTable", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "FailIfFileNotFound":false}', 'CMAStaging.ExecTable')
  , (@LoadType_Intraday, 'CMAStaging.LoadCarrierQIData', @ProcessType_SP, '{"TargetConnection":"PlacementStore", "StoredProcedure":"CMAStaging.LoadCarrierQIData"}', 'PS.CarrierQIData')
  , (@LoadType_Intraday, 'CMAStaging.LoadQITeamMapping', @ProcessType_SP, '{"TargetConnection":"PlacementStore", "StoredProcedure":"CMAStaging.LoadQITeamMapping"}', 'dbo.QITeamMapping')
  , (@LoadType_Intraday, 'CMAStaging.QITeamMapping', @ProcessType_Copy, '{"SourceConnection":"ExcelFile", "FileShare":"datasource-cam-analytics", "FolderName":"CMAQI", "FileName":"TeamMapping.xlsx", "SheetName":"Sheet1", "TargetTable":"CMAStaging.QITeamMapping", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "FailIfFileNotFound":false }', 'CMAStaging.ExecTable')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ps_AdditionalDataItem', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ps_AdditionalDataItem", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ps_AdditionalDataItem.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ps_AdditionalDataItem/"}', 'vw_ps_AdditionalDataItem.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ps_AppetiteNarrative', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ps_AppetiteNarrative", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ps_AppetiteNarrative.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ps_AppetiteNarrative/"}', 'vw_ps_AppetiteNarrative.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ps_AppetiteResponse', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ps_AppetiteResponse", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ps_AppetiteResponse.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ps_AppetiteResponse/"}', 'vw_ps_AppetiteResponse.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ps_ClientProfile', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ps_ClientProfile", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ps_ClientProfile.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ps_ClientProfile/"}', 'vw_ps_ClientProfile.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ps_Contract', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ps_Contract", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ps_Contract.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ps_Contract/"}', 'vw_ps_Contract.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ps_ContractAttribute', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ps_ContractAttribute", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ps_ContractAttribute.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ps_ContractAttribute/"}', 'vw_ps_ContractAttribute.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ps_ContractEndorsement', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ps_ContractEndorsement", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ps_ContractEndorsement.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ps_ContractEndorsement/"}', 'vw_ps_ContractEndorsement.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ps_ContractEndorsementAttribute', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ps_ContractEndorsementAttribute", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ps_ContractEndorsementAttribute.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ps_ContractEndorsementAttribute/"}', 'vw_ps_ContractEndorsementAttribute.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ps_ContractProgramme', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ps_ContractProgramme", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ps_ContractProgramme.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ps_ContractProgramme/"}', 'vw_ps_ContractProgramme.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ps_ContractProgrammeAttribute', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ps_ContractProgrammeAttribute", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ps_ContractProgrammeAttribute.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ps_ContractProgrammeAttribute/"}', 'vw_ps_ContractProgrammeAttribute.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ps_ContractRiskProfile', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ps_ContractRiskProfile", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ps_ContractRiskProfile.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ps_ContractRiskProfile/"}', 'vw_ps_ContractRiskProfile.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ps_ContractsClausesAndConditions', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ps_ContractsClausesAndConditions", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ps_ContractsClausesAndConditions.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ps_ContractsClausesAndConditions/"}', 'vw_ps_ContractsClausesAndConditions.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ps_ContractVersion', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ps_ContractVersion", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ps_ContractVersion.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ps_ContractVersion/"}', 'vw_ps_ContractVersion.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ps_ContractVersionAttribute', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ps_ContractVersionAttribute", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ps_ContractVersionAttribute.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ps_ContractVersionAttribute/"}', 'vw_ps_ContractVersionAttribute.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ps_ElementTagInclusionRule', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ps_ElementTagInclusionRule", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ps_ElementTagInclusionRule.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ps_ElementTagInclusionRule/"}', 'vw_ps_ElementTagInclusionRule.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ps_ExposureAttribute', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ps_ExposureAttribute", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ps_ExposureAttribute.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ps_ExposureAttribute/"}', 'vw_ps_ExposureAttribute.snappy.parquet')
  --, (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ps_GlobalPartySecurity', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ps_GlobalPartySecurity", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ps_GlobalPartySecurity.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ps_GlobalPartySecurity/"}', 'vw_ps_GlobalPartySecurity.snappy.parquet')
  --, (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ps_LookUp', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ps_LookUp", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ps_LookUp.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ps_LookUp/"}', 'vw_ps_LookUp.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ps_MarketQuoteResponsePolicyRef', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ps_MarketQuoteResponsePolicyRef", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ps_MarketQuoteResponsePolicyRef.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ps_MarketQuoteResponsePolicyRef/"}', 'vw_ps_MarketQuoteResponsePolicyRef.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ps_MarketResponse', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ps_MarketResponse", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ps_MarketResponse.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ps_MarketResponse/"}', 'vw_ps_MarketResponse.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ps_MarketResponseAttribute', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ps_MarketResponseAttribute", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ps_MarketResponseAttribute.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ps_MarketResponseAttribute/"}', 'vw_ps_MarketResponseAttribute.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ps_MarketResponseBasis', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ps_MarketResponseBasis", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ps_MarketResponseBasis.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ps_MarketResponseBasis/"}', 'vw_ps_MarketResponseBasis.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ps_MarketResponsePlacementPolicy', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ps_MarketResponsePlacementPolicy", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ps_MarketResponsePlacementPolicy.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ps_MarketResponsePlacementPolicy/"}', 'vw_ps_MarketResponsePlacementPolicy.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ps_MarketResponseSplit', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ps_MarketResponseSplit", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ps_MarketResponseSplit.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ps_MarketResponseSplit/"}', 'vw_ps_MarketResponseSplit.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ps_Negotiation', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ps_Negotiation", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ps_Negotiation.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ps_Negotiation/"}', 'vw_ps_Negotiation.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ps_NegotiationContract', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ps_NegotiationContract", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ps_NegotiationContract.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ps_NegotiationContract/"}', 'vw_ps_NegotiationContract.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ps_NegotiationMarket', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ps_NegotiationMarket", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ps_NegotiationMarket.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ps_NegotiationMarket/"}', 'vw_ps_NegotiationMarket.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ps_NegotiationMarketContract', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ps_NegotiationMarketContract", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ps_NegotiationMarketContract.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ps_NegotiationMarketContract/"}', 'vw_ps_NegotiationMarketContract.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ps_NegotiationRiskProfile', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ps_NegotiationRiskProfile", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ps_NegotiationRiskProfile.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ps_NegotiationRiskProfile/"}', 'vw_ps_NegotiationRiskProfile.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ps_NegotiationSpecification', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ps_NegotiationSpecification", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ps_NegotiationSpecification.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ps_NegotiationSpecification/"}', 'vw_ps_NegotiationSpecification.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ps_Placement', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ps_Placement", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ps_Placement.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ps_Placement/"}', 'vw_ps_Placement.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ps_PlacementExposure', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ps_PlacementExposure", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ps_PlacementExposure.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ps_PlacementExposure/"}', 'vw_ps_PlacementExposure.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ps_PlacementExposureGroup', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ps_PlacementExposureGroup", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ps_PlacementExposureGroup.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ps_PlacementExposureGroup/"}', 'vw_ps_PlacementExposureGroup.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ps_PlacementMarketValidation', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ps_PlacementMarketValidation", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ps_PlacementMarketValidation.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ps_PlacementMarketValidation/"}', 'vw_ps_PlacementMarketValidation.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ps_PlacementPartyRole', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ps_PlacementPartyRole", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ps_PlacementPartyRole.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ps_PlacementPartyRole/"}', 'vw_ps_PlacementPartyRole.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ps_PlacementPolicy', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ps_PlacementPolicy", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ps_PlacementPolicy.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ps_PlacementPolicy/"}', 'vw_ps_PlacementPolicy.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ps_PlacementRuleValidation', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ps_PlacementRuleValidation", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ps_PlacementRuleValidation.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ps_PlacementRuleValidation/"}', 'vw_ps_PlacementRuleValidation.snappy.parquet')
  --, (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ps_PlacementSecurity', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ps_PlacementSecurity", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ps_PlacementSecurity.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ps_PlacementSecurity/"}', 'vw_ps_PlacementSecurity.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ps_PlacementServiceSatisfactionValidation', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ps_PlacementServiceSatisfactionValidation", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ps_PlacementServiceSatisfactionValidation.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ps_PlacementServiceSatisfactionValidation/"}', 'vw_ps_PlacementServiceSatisfactionValidation.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ps_PlacementServicingRole', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ps_PlacementServicingRole", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ps_PlacementServicingRole.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ps_PlacementServicingRole/"}', 'vw_ps_PlacementServicingRole.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ps_RiskProfile', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ps_RiskProfile", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ps_RiskProfile.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ps_RiskProfile/"}', 'vw_ps_RiskProfile.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ps_RiskProfileAttribute', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ps_RiskProfileAttribute", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ps_RiskProfileAttribute.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ps_RiskProfileAttribute/"}', 'vw_ps_RiskProfileAttribute.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ps_RiskProfilePlacementExposureGroup', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ps_RiskProfilePlacementExposureGroup", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ps_RiskProfilePlacementExposureGroup.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ps_RiskProfilePlacementExposureGroup/"}', 'vw_ps_RiskProfilePlacementExposureGroup.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ps_RiskStructure', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ps_RiskStructure", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ps_RiskStructure.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ps_RiskStructure/"}', 'vw_ps_RiskStructure.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ps_RiskStructureContract', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ps_RiskStructureContract", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ps_RiskStructureContract.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ps_RiskStructureContract/"}', 'vw_ps_RiskStructureContract.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ps_RiskStructureMarketResponse', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ps_RiskStructureMarketResponse", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ps_RiskStructureMarketResponse.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ps_RiskStructureMarketResponse/"}', 'vw_ps_RiskStructureMarketResponse.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ps_RiskStructurePolicy', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ps_RiskStructurePolicy", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ps_RiskStructurePolicy.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ps_RiskStructurePolicy/"}', 'vw_ps_RiskStructurePolicy.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ps_RiskStructureSpecification', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ps_RiskStructureSpecification", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ps_RiskStructureSpecification.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ps_RiskStructureSpecification/"}', 'vw_ps_RiskStructureSpecification.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ps_ServiceSatisfactionIssueOutcome', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ps_ServiceSatisfactionIssueOutcome", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ps_ServiceSatisfactionIssueOutcome.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ps_ServiceSatisfactionIssueOutcome/"}', 'vw_ps_ServiceSatisfactionIssueOutcome.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ps_Specification', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ps_Specification", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ps_Specification.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ps_Specification/"}', 'vw_ps_Specification.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ps_SpecificationAttribute', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ps_SpecificationAttribute", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ps_SpecificationAttribute.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ps_SpecificationAttribute/"}', 'vw_ps_SpecificationAttribute.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ps_ValidationRuleOutcome', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ps_ValidationRuleOutcome", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ps_ValidationRuleOutcome.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ps_ValidationRuleOutcome/"}', 'vw_ps_ValidationRuleOutcome.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ref_AppetiteLevel', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ref_AppetiteLevel", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ref_AppetiteLevel.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ref_AppetiteLevel/"}', 'vw_ref_AppetiteLevel.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ref_AppraisalType', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ref_AppraisalType", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ref_AppraisalType.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ref_AppraisalType/"}', 'vw_ref_AppraisalType.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ref_BrokingRegion', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ref_BrokingRegion", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ref_BrokingRegion.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ref_BrokingRegion/"}', 'vw_ref_BrokingRegion.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ref_BrokingSegment', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ref_BrokingSegment", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ref_BrokingSegment.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ref_BrokingSegment/"}', 'vw_ref_BrokingSegment.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ref_BrokingSubsegment', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ref_BrokingSubsegment", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ref_BrokingSubsegment.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ref_BrokingSubsegment/"}', 'vw_ref_BrokingSubsegment.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ref_CancellationReason', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ref_CancellationReason", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ref_CancellationReason.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ref_CancellationReason/"}', 'vw_ref_CancellationReason.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ref_ClassOfBusiness', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ref_ClassOfBusiness", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ref_ClassOfBusiness.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ref_ClassOfBusiness/"}', 'vw_ref_ClassOfBusiness.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ref_ContractStatus', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ref_ContractStatus", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ref_ContractStatus.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ref_ContractStatus/"}', 'vw_ref_ContractStatus.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ref_CoverageType', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ref_CoverageType", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ref_CoverageType.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ref_CoverageType/"}', 'vw_ref_CoverageType.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ref_DeclinationReason', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ref_DeclinationReason", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ref_DeclinationReason.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ref_DeclinationReason/"}', 'vw_ref_DeclinationReason.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ref_ElementAttributeType', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ref_ElementAttributeType", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ref_ElementAttributeType.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ref_ElementAttributeType/"}', 'vw_ref_ElementAttributeType.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ref_ExposurePeriod', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ref_ExposurePeriod", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ref_ExposurePeriod.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ref_ExposurePeriod/"}', 'vw_ref_ExposurePeriod.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ref_ExposureType', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ref_ExposureType", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ref_ExposureType.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ref_ExposureType/"}', 'vw_ref_ExposureType.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ref_FollowType', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ref_FollowType", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ref_FollowType.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ref_FollowType/"}', 'vw_ref_FollowType.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ref_Industry', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ref_Industry", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ref_Industry.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ref_Industry/"}', 'vw_ref_Industry.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ref_JustificationReason', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ref_JustificationReason", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ref_JustificationReason.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ref_JustificationReason/"}', 'vw_ref_JustificationReason.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ref_JustificationReasonType', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ref_JustificationReasonType", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ref_JustificationReasonType.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ref_JustificationReasonType/"}', 'vw_ref_JustificationReasonType.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ref_LayerType', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ref_LayerType", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ref_LayerType.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ref_LayerType/"}', 'vw_ref_LayerType.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ref_LegalEntity', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ref_LegalEntity", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ref_LegalEntity.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ref_LegalEntity/"}', 'vw_ref_LegalEntity.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ref_LineOfBusiness', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ref_LineOfBusiness", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ref_LineOfBusiness.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ref_LineOfBusiness/"}', 'vw_ref_LineOfBusiness.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ref_MarketingDecision', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ref_MarketingDecision", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ref_MarketingDecision.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ref_MarketingDecision/"}', 'vw_ref_MarketingDecision.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ref_MarketKind', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ref_MarketKind", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ref_MarketKind.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ref_MarketKind/"}', 'vw_ref_MarketKind.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ref_MTAType', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ref_MTAType", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ref_MTAType.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ref_MTAType/"}', 'vw_ref_MTAType.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ref_OpportunityType', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ref_OpportunityType", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ref_OpportunityType.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ref_OpportunityType/"}', 'vw_ref_OpportunityType.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ref_OptionReference', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ref_OptionReference", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ref_OptionReference.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ref_OptionReference/"}', 'vw_ref_OptionReference.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ref_OutcomeReason', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ref_OutcomeReason", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ref_OutcomeReason.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ref_OutcomeReason/"}', 'vw_ref_OutcomeReason.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ref_OutcomeStatus', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ref_OutcomeStatus", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ref_OutcomeStatus.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ref_OutcomeStatus/"}', 'vw_ref_OutcomeStatus.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ref_Panel', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ref_Panel", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ref_Panel.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ref_Panel/"}', 'vw_ref_Panel.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ref_PanelMember', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ref_PanelMember", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ref_PanelMember.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ref_PanelMember/"}', 'vw_ref_PanelMember.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ref_PanelMemberCarrier', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ref_PanelMemberCarrier", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ref_PanelMemberCarrier.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ref_PanelMemberCarrier/"}', 'vw_ref_PanelMemberCarrier.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ref_PanelMemberFacility', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ref_PanelMemberFacility", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ref_PanelMemberFacility.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ref_PanelMemberFacility/"}', 'vw_ref_PanelMemberFacility.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ref_PendingActionReason', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ref_PendingActionReason", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ref_PendingActionReason.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ref_PendingActionReason/"}', 'vw_ref_PendingActionReason.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ref_PlacementPolicyRelationshipType', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ref_PlacementPolicyRelationshipType", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ref_PlacementPolicyRelationshipType.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ref_PlacementPolicyRelationshipType/"}', 'vw_ref_PlacementPolicyRelationshipType.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ref_PlacementStatus', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ref_PlacementStatus", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ref_PlacementStatus.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ref_PlacementStatus/"}', 'vw_ref_PlacementStatus.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ref_PolicyRefType', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ref_PolicyRefType", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ref_PolicyRefType.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ref_PolicyRefType/"}', 'vw_ref_PolicyRefType.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ref_PricingFactor', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ref_PricingFactor", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ref_PricingFactor.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ref_PricingFactor/"}', 'vw_ref_PricingFactor.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ref_ProgramStructureType', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ref_ProgramStructureType", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ref_ProgramStructureType.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ref_ProgramStructureType/"}', 'vw_ref_ProgramStructureType.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ref_ProgramType', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ref_ProgramType", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ref_ProgramType.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ref_ProgramType/"}', 'vw_ref_ProgramType.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ref_ResponseType', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ref_ResponseType", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ref_ResponseType.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ref_ResponseType/"}', 'vw_ref_ResponseType.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ref_ServiceLevel', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ref_ServiceLevel", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ref_ServiceLevel.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ref_ServiceLevel/"}', 'vw_ref_ServiceLevel.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ref_ServiceLevelIssue', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ref_ServiceLevelIssue", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ref_ServiceLevelIssue.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ref_ServiceLevelIssue/"}', 'vw_ref_ServiceLevelIssue.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ref_ServicingRole', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ref_ServicingRole", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ref_ServicingRole.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ref_ServicingRole/"}', 'vw_ref_ServicingRole.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ref_Team', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT TeamId , DataSourceInstanceId , TeamName , ParentId , ServicingPlatformId , LevelNum , FullPath , OrgLevel1 , OrgLevel2 , OrgLevel3 , OrgLevel4 , OrgLevel5 , OrgLevel6 , OrganisationRole , ETLCreatedDate , ETLUpdatedDate , SourceUpdatedDate , IsDeprecated FROM ods.vw_ref_Team", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ref_Team.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ref_Team/"}', 'vw_ref_Team.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ref_TimeZone', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ref_TimeZone", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ref_TimeZone.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ref_TimeZone/"}', 'vw_ref_TimeZone.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ref_Trigger', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ref_Trigger", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ref_Trigger.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ref_Trigger/"}', 'vw_ref_Trigger.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ref_VerticalIndustry', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ref_VerticalIndustry", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ref_VerticalIndustry.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ref_VerticalIndustry/"}', 'vw_ref_VerticalIndustry.snappy.parquet')
  , (@LoadType_Intraday, 'CRB Data Lake Export - ods.vw_ref_Worker', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"SELECT * FROM ods.vw_ref_Worker", "TargetConnection":"CRBDataLakeEU", "TargetFile":"vw_ref_Worker.snappy.parquet", "CompressionType":"Snappy", "Container":"placementstore", "Path":"[env]/curated/ods/vw_ref_Worker/"}', 'vw_ref_Worker.snappy.parquet')
  , (@LoadType_Intraday, 'eGlobalStaging.PLH_POOLHEADER', @ProcessType_Copy, '{"SourceConnection":"PASStaging", "SourceTable":"eGlobal.PLH_POOLHEADER", "SourceSQL":"", "TargetTable":"eGlobalStaging.PLH_POOLHEADER", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":true, "IncrementalUpdatedDateColumn":"ETLUpdatedDate"}', 'eGlobalStaging.PLH_POOLHEADER')
  , (@LoadType_Intraday, 'eGlobalStaging.POL_POOLS', @ProcessType_Copy, '{"SourceConnection":"PASStaging", "SourceTable":"eGlobal.POL_POOLS", "SourceSQL":"", "TargetTable":"eGlobalStaging.POL_POOLS", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":true, "IncrementalUpdatedDateColumn":"ETLUpdatedDate"}', 'eGlobalStaging.POL_POOLS')
  , (@LoadType_Intraday, 'ERD Mapping dbo.spMappingUpdateMDS_Br', @ProcessType_SP, '{"StoredProcedure" : "dbo.spMappingUpdateMDS_Br", "TargetConnection":"ERDMDS", "ResultType":"DummyRow"}', 'MDS Mapping Table')
  , (@LoadType_Intraday, 'ERD Mapping ReferenceStaging.ServiceBusMDSInterimTable_Br', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceSQL":"", "SourceTable":"ReferenceStaging.ServiceBusMDSInterimTable_Br", "TargetTable":"dbo.ServiceBusMDSInterimTable_Br", "TruncateTargetTable": false, "TargetConnection":"ERDMDS"}', 'ReferenceStaging.ServiceBusMDSInterimTable_Br')
  , (@LoadType_Intraday, 'ERD Mapping ReferenceStaging.StageServiceBusMDSInterimTable_Br', @ProcessType_SP, '{"StoredProcedure" : "ReferenceStaging.StageServiceBusMDSInterimTable_Br", "TargetConnection":"PlacementStore"}', 'ReferenceStaging.ServiceBusMDSInterimTable_Br')
  , (@LoadType_Intraday, 'ERD Mapping ReferenceStaging.UpdateSentForMappingDateOnProduct', @ProcessType_SP, '{"StoredProcedure" : "ReferenceStaging.UpdateSentForMappingDateOnProduct", "TargetConnection":"PlacementStore"}', 'dbo.Product')
  , (@LoadType_Intraday, 'FMATemp.StageAdditionalDataItem', @ProcessType_SP, '{"StoredProcedure":"FMATemp.StageAdditionalDataItem", "TargetConnection":"PlacementStore"}', '[FMATemp].[AdditionalDataItem]')
  , (@LoadType_Intraday, 'FMATemp.StageAgency', @ProcessType_SP, '{"StoredProcedure":"FMATemp.StageAgency", "TargetConnection":"PlacementStore"}', 'FMATemp.staging_vw_Agency')
  , (@LoadType_Intraday, 'FMATemp.StageAppPlacementPolicy', @ProcessType_SP, '{"StoredProcedure":"FMATemp.StageAppPlacementPolicy", "TargetConnection":"PlacementStore"}', '[FMATemp].[AppPlacementPolicy]')
  , (@LoadType_Intraday, 'FMATemp.StageCarrierAdditionalData', @ProcessType_SP, '{"StoredProcedure":"FMATemp.StageCarrierAdditionalData", "TargetConnection":"PlacementStore"}', 'FMATemp.staging_vw_CarrierAdditionalData')
  , (@LoadType_Intraday, 'FMATemp.StageCarrierAgency', @ProcessType_SP, '{"StoredProcedure":"FMATemp.StageCarrierAgency", "TargetConnection":"PlacementStore"}', 'FMATemp.staging_vw_CarrierAgency')
  , (@LoadType_Intraday, 'FMATemp.StageCarrierAgencyRating', @ProcessType_SP, '{"StoredProcedure":"FMATemp.StageCarrierAgencyRating", "TargetConnection":"PlacementStore"}', 'FMATemp.staging_vw_CarrierAgencyRating')
  , (@LoadType_Intraday, 'FMATemp.StageCarrierExtended', @ProcessType_SP, '{"StoredProcedure":"FMATemp.StageCarrierExtended", "TargetConnection":"PlacementStore"}', 'FMATemp.staging_vw_CarrierExtended')
  , (@LoadType_Intraday, 'FMATemp.StageCarrierQIRating', @ProcessType_SP, '{"StoredProcedure":"FMATemp.StageCarrierQIRating", "TargetConnection":"PlacementStore"}', 'FMATemp.staging_vw_CarrierQIRating')
  , (@LoadType_Intraday, 'FMATemp.StageCarrierRating', @ProcessType_SP, '{"StoredProcedure":"FMATemp.StageCarrierRating", "TargetConnection":"PlacementStore"}', 'FMATemp.staging_vw_CarrierRating')
  , (@LoadType_Intraday, 'FMATemp.StageCarrierRatingOutlookDefinition', @ProcessType_SP, '{"StoredProcedure":"FMATemp.StageCarrierRatingOutlookDefinition", "TargetConnection":"PlacementStore"}', 'FMATemp.staging_vw_CarrierRatingOutlookDefinition')
  , (@LoadType_Intraday, 'FMATemp.StageCarrierRelationship', @ProcessType_SP, '{"StoredProcedure":"FMATemp.StageCarrierRelationship", "TargetConnection":"PlacementStore"}', 'FMATemp.staging_vw_CarrierRelationship')
  , (@LoadType_Intraday, 'FMATemp.StageCarrierRestriction', @ProcessType_SP, '{"StoredProcedure":"FMATemp.StageCarrierRestriction", "TargetConnection":"PlacementStore"}', 'FMATemp.staging_vw_CarrierRestriction')
  , (@LoadType_Intraday, 'FMATemp.StageCarrierRestrictionDefinition', @ProcessType_SP, '{"StoredProcedure":"FMATemp.StageCarrierRestrictionDefinition", "TargetConnection":"PlacementStore"}', 'FMATemp.staging_vw_CarrierRestrictionDefinition')
  , (@LoadType_Intraday, 'FMATemp.StageCarrierStatus', @ProcessType_SP, '{"StoredProcedure":"FMATemp.StageCarrierStatus", "TargetConnection":"PlacementStore"}', 'FMATemp.staging_vw_CarrierStatus')
  , (@LoadType_Intraday, 'FMATemp.StageCarrierStatusOverride', @ProcessType_SP, '{"StoredProcedure":"FMATemp.StageCarrierStatusOverride", "TargetConnection":"PlacementStore"}', 'FMATemp.staging_vw_CarrierStatusOverride')
  , (@LoadType_Intraday, 'FMATemp.StageCarrierTOBA', @ProcessType_SP, '{"StoredProcedure":"FMATemp.StageCarrierTOBA", "TargetConnection":"PlacementStore"}', 'FMATemp.staging_vw_CarrierTOBA')
  , (@LoadType_Intraday, 'FMATemp.StageCarrierType', @ProcessType_SP, '{"StoredProcedure":"FMATemp.StageCarrierType", "TargetConnection":"PlacementStore"}', 'FMATemp.staging_vw_CarrierType')
  , (@LoadType_Intraday, 'FMATemp.StageCountry', @ProcessType_SP, '{"StoredProcedure":"FMATemp.StageCountry", "TargetConnection":"PlacementStore"}', 'FMATemp.staging_vw_Country')
  , (@LoadType_Intraday, 'FMATemp.StageCountrySubdivision', @ProcessType_SP, '{"StoredProcedure":"FMATemp.StageCountrySubdivision", "TargetConnection":"PlacementStore"}', 'FMATemp.staging_vw_CountrySubdivision')
  , (@LoadType_Intraday, 'FMATemp.StageCurrency', @ProcessType_SP, '{"StoredProcedure":"FMATemp.StageCurrency", "TargetConnection":"PlacementStore"}', 'FMATemp.staging_vw_Currency')
  , (@LoadType_Intraday, 'FMATemp.StageFacility', @ProcessType_SP, '{"StoredProcedure":"FMATemp.StageFacility", "TargetConnection":"PlacementStore"}', 'FMATemp.staging_vw_Facility')
  , (@LoadType_Intraday, 'FMATemp.StageFacilityCarrier', @ProcessType_SP, '{"StoredProcedure":"FMATemp.StageFacilityCarrier", "TargetConnection":"PlacementStore"}', 'FMATemp.staging_vw_FacilityCarrier')
  , (@LoadType_Intraday, 'FMATemp.StageFacilitySection', @ProcessType_SP, '{"StoredProcedure":"FMATemp.StageFacilitySection", "TargetConnection":"PlacementStore"}', 'FMATemp.staging_vw_FacilitySection')
  , (@LoadType_Intraday, 'FMATemp.StageFacilitySectionCarrier', @ProcessType_SP, '{"StoredProcedure":"FMATemp.StageFacilitySectionCarrier", "TargetConnection":"PlacementStore"}', 'FMATemp.staging_vw_FacilitySectionCarrier')
  , (@LoadType_Intraday, 'FMATemp.StageIndustry', @ProcessType_SP, '{"StoredProcedure":"FMATemp.StageIndustry", "TargetConnection":"PlacementStore"}', 'FMATemp.staging_vw_Industry')
  , (@LoadType_Intraday, 'FMATemp.StageInsured', @ProcessType_SP, '{"StoredProcedure":"FMATemp.StageInsured", "TargetConnection":"PlacementStore"}', '[FMATemp].[Insured]')
  , (@LoadType_Intraday, 'FMATemp.StageLookup', @ProcessType_SP, '{"StoredProcedure":"FMATemp.StageLookup", "TargetConnection":"PlacementStore"}', 'FMATemp.staging_vw_Lookup')
  , (@LoadType_Intraday, 'FMATemp.StageLookupGroup', @ProcessType_SP, '{"StoredProcedure":"FMATemp.StageLookupGroup", "TargetConnection":"PlacementStore"}', 'FMATemp.staging_vw_LookupGroup')
  , (@LoadType_Intraday, 'FMATemp.StageOffice', @ProcessType_SP, '{"StoredProcedure":"FMATemp.StageOffice", "TargetConnection":"PlacementStore"}', 'FMATemp.staging_vw_Office')
  , (@LoadType_Intraday, 'FMATemp.StageOfficeAddress', @ProcessType_SP, '{"StoredProcedure":"FMATemp.StageOfficeAddress", "TargetConnection":"PlacementStore"}', 'FMATemp.staging_vw_OfficeAddress')
  , (@LoadType_Intraday, 'FMATemp.StageParty', @ProcessType_SP, '{"StoredProcedure":"FMATemp.StageParty", "TargetConnection":"PlacementStore"}', 'FMATemp.staging_vw_Party')
  , (@LoadType_Intraday, 'FMATemp.StagePartyAddress', @ProcessType_SP, '{"StoredProcedure":"FMATemp.StagePartyAddress", "TargetConnection":"PlacementStore"}', 'FMATemp.staging_vw_PartyAddress')
  , (@LoadType_Intraday, 'FMATemp.StagePlacement', @ProcessType_SP, '{"StoredProcedure":"FMATemp.StagePlacement", "TargetConnection":"PlacementStore"}', '[FMATemp].[Placement]')
  , (@LoadType_Intraday, 'FMATemp.StagePlacementCarrier', @ProcessType_SP, '{"StoredProcedure":"FMATemp.StagePlacementCarrier", "TargetConnection":"PlacementStore"}', '[FMATemp].[PlacementCarrier]')
  , (@LoadType_Intraday, 'FMATemp.StagePlacementPolicy', @ProcessType_SP, '{"StoredProcedure":"FMATemp.StagePlacementPolicy", "TargetConnection":"PlacementStore"}', '[FMATemp].[PlacementPolicy]')
  , (@LoadType_Intraday, 'FMATemp.StagePlacementProducts', @ProcessType_SP, '{"StoredProcedure":"FMATemp.StagePlacementProducts", "TargetConnection":"PlacementStore"}', '[FMATemp].[PlacementProducts]')
  , (@LoadType_Intraday, 'FMATemp.StagePlacementTeams', @ProcessType_SP, '{"StoredProcedure":"FMATemp.StagePlacementTeams", "TargetConnection":"PlacementStore"}', '[FMATemp].[PlacementTeams]')
  , (@LoadType_Intraday, 'FMATemp.StagePlacementUpdates', @ProcessType_SP, '{"StoredProcedure":"FMATemp.StagePlacementUpdates", "TargetConnection":"PlacementStore"}', '[FMATemp].[PlacementUpdates]')
  , (@LoadType_Intraday, 'FMATemp.StagePlacementWorkers', @ProcessType_SP, '{"StoredProcedure":"FMATemp.StagePlacementWorkers", "TargetConnection":"PlacementStore"}', '[FMATemp].[PlacementWorkers]')
  , (@LoadType_Intraday, 'FMATemp.StagePolicy', @ProcessType_SP, '{"StoredProcedure":"FMATemp.StagePolicy", "TargetConnection":"PlacementStore"}', '[FMATemp].[Policy]')
  , (@LoadType_Intraday, 'FMATemp.StagePolicyCarrier', @ProcessType_SP, '{"StoredProcedure":"FMATemp.StagePolicyCarrier", "TargetConnection":"PlacementStore"}', '[FMATemp].[PolicyCarrier]')
  , (@LoadType_Intraday, 'FMATemp.StagePolicyProduct', @ProcessType_SP, '{"StoredProcedure":"FMATemp.StagePolicyProduct", "TargetConnection":"PlacementStore"}', '[FMATemp].[PolicyProduct]')
  , (@LoadType_Intraday, 'FMATemp.StagePolicyStatus', @ProcessType_SP, '{"StoredProcedure":"FMATemp.StagePolicyStatus", "TargetConnection":"PlacementStore"}', 'FMATemp.staging_vw_PolicyStatus')
  , (@LoadType_Intraday, 'FMATemp.StageProduct', @ProcessType_SP, '{"StoredProcedure":"FMATemp.StageProduct", "TargetConnection":"PlacementStore"}', 'FMATemp.staging_vw_Product')
  , (@LoadType_Intraday, 'FMATemp.StageReferenceDataProductMapping', @ProcessType_SP, '{"StoredProcedure":"FMATemp.StageReferenceDataProductMapping", "TargetConnection":"PlacementStore"}', 'FMATemp.staging_vw_ReferenceDataProductMapping')
  , (@LoadType_Intraday, 'FMATemp.StageServicingPlatform', @ProcessType_SP, '{"StoredProcedure":"FMATemp.StageServicingPlatform", "TargetConnection":"PlacementStore"}', 'FMATemp.staging_vw_ServicingPlatform')
  , (@LoadType_Intraday, 'FMATemp.StageTranslation', @ProcessType_SP, '{"StoredProcedure":"FMATemp.StageTranslation", "TargetConnection":"PlacementStore"}', 'FMATemp.staging_vw_Translation')
  , (@LoadType_Intraday, 'FMATemp.StageUser', @ProcessType_SP, '{"StoredProcedure":"FMATemp.StageUser", "TargetConnection":"PlacementStore"}', 'FMATemp.staging_vw_User')
  , (@LoadType_Intraday, 'MarineMar.CalculateContractEPI', @ProcessType_SP, '{"StoredProcedure":"MarineMar.CalculateContractEPI", "TargetConnection":"PlacementStore"}', 'MarineMar.ContractEPI')
  , (@LoadType_Intraday, 'MarineMar.Load_MarineMar_NegotiationMarket', @ProcessType_SP, '{"StoredProcedure":"MarineMar.Load_MarineMar_NegotiationMarket", "TargetConnection":"PlacementStore"}', 'MarineMar.NegotiationMarket')
  , (@LoadType_Intraday, 'MarineMar.LoadContract', @ProcessType_SP, '{"StoredProcedure":"MarineMar.LoadContract", "TargetConnection":"PlacementStore"}', 'MarineMar.Contract')
  , (@LoadType_Intraday, 'MarineMar.LoadMarketResponse', @ProcessType_SP, '{"StoredProcedure":"MarineMar.LoadMarketResponse", "TargetConnection":"PlacementStore"}', 'MarineMar.MarketResponse')
  , (@LoadType_Intraday, 'MarineMar.LoadPlacement', @ProcessType_SP, '{"StoredProcedure":"MarineMar.LoadPlacement", "TargetConnection":"PlacementStore"}', 'MarineMar.Placement')
  , (@LoadType_Intraday, 'MarineMar.LoadPlacementBroker', @ProcessType_SP, '{"StoredProcedure":"MarineMar.LoadPlacementBroker", "TargetConnection":"PlacementStore"}', 'MarineMar.PlacementBroker')
  , (@LoadType_Intraday, 'MarineMar.LoadPlacementClient', @ProcessType_SP, '{"StoredProcedure":"MarineMar.LoadPlacementClient", "TargetConnection":"PlacementStore"}', 'MarineMar.PlacementClient')
  , (@LoadType_Intraday, 'MarineMar.LoadPlacementClientExec', @ProcessType_SP, '{"StoredProcedure":"MarineMar.LoadPlacementClientExec", "TargetConnection":"PlacementStore"}', 'MarineMar.PlacementClientExec')
  , (@LoadType_Intraday, 'MarineMar.LoadPlacementInsured', @ProcessType_SP, '{"StoredProcedure":"MarineMar.LoadPlacementInsured", "TargetConnection":"PlacementStore"}', 'MarineMar.PlacementInsured')
  , (@LoadType_Intraday, 'MarineMar.LoadPlacementReinsured', @ProcessType_SP, '{"StoredProcedure":"MarineMar.LoadPlacementReinsured", "TargetConnection":"PlacementStore"}', 'MarineMar.PlacementReinsured')
  , (@LoadType_Intraday, 'MarineMar.LoadPolicy', @ProcessType_SP, '{"StoredProcedure":"MarineMar.LoadPolicy", "TargetConnection":"PlacementStore"}', 'MarineMar.Policy')
  , (@LoadType_Intraday, 'MarineMar.LoadPolicyAccountHandler', @ProcessType_SP, '{"StoredProcedure":"MarineMar.LoadPolicyAccountHandler", "TargetConnection":"PlacementStore"}', 'MarineMar.PolicyAccountHandler')
  , (@LoadType_Intraday, 'MarineMar.LoadRiskProfileClassOfBusiness', @ProcessType_SP, '{"StoredProcedure":"MarineMar.LoadRiskProfileClassOfBusiness", "TargetConnection":"PlacementStore"}', 'MarineMar.RiskProfileClassOfBusiness')
  , (@LoadType_Intraday, 'MarineMar.LoadRiskProfileCoverType', @ProcessType_SP, '{"StoredProcedure":"MarineMar.LoadRiskProfileCoverType", "TargetConnection":"PlacementStore"}', 'MarineMar.RiskProfileCoverType')
  , (@LoadType_Intraday, 'MarineMar.LoadRiskProfileIndustrySectorType', @ProcessType_SP, '{"StoredProcedure":"MarineMar.LoadRiskProfileIndustrySectorType", "TargetConnection":"PlacementStore"}', 'MarineMar.RiskProfileIndustrySectorType')
  , (@LoadType_Intraday, 'MarineMar.LoadRiskProfileLineOfBusiness', @ProcessType_SP, '{"StoredProcedure":"MarineMar.LoadRiskProfileLineOfBusiness", "TargetConnection":"PlacementStore"}', 'MarineMar.RiskProfileLineOfBusiness')
  , (@LoadType_Intraday, 'MarineMar.LoadRiskProfileNatureOfOperationInterest', @ProcessType_SP, '{"StoredProcedure":"MarineMar.LoadRiskProfileNatureOfOperationInterest", "TargetConnection":"PlacementStore"}', 'MarineMar.RiskProfileNatureOfOperationInterest')
  , (@LoadType_Intraday, 'MarineMar.LoadRiskProfileVesselType', @ProcessType_SP, '{"StoredProcedure":"MarineMar.LoadRiskProfileVesselType", "TargetConnection":"PlacementStore"}', 'MarineMar.RiskProfileVesselType')
  , (@LoadType_Intraday, 'PS.Load_PS_ContractClauseAndCondition', @ProcessType_SP, '{"StoredProcedure":"PS.Load_PS_ContractClauseAndCondition", "TargetConnection":"PlacementStore"}', 'PS.ContractClauseAndCondition')
  , (@LoadType_Intraday, 'PS.Load_PS_ContractEndorsementClauseAndCondition', @ProcessType_SP, '{"StoredProcedure":"PS.Load_PS_ContractEndorsementClauseAndCondition", "TargetConnection":"PlacementStore"}', 'PS.ContractEndorsementClauseAndCondition')
  , (@LoadType_Intraday, 'PS.Load_PS_ContractProgrammeClauseAndCondition', @ProcessType_SP, '{"StoredProcedure":"PS.Load_PS_ContractProgrammeClauseAndCondition", "TargetConnection":"PlacementStore"}', 'PS.ContractProgrammeClauseAndCondition')
  , (@LoadType_Intraday, 'PS.Load_PS_ContractVersionClauseAndCondition', @ProcessType_SP, '{"StoredProcedure":"PS.Load_PS_ContractVersionClauseAndCondition", "TargetConnection":"PlacementStore"}', 'PS.ContractVersionClauseAndCondition')
  , (@LoadType_Intraday, 'PS.LoadCarrierHierarchyExtended', @ProcessType_SP, '{"StoredProcedure":"PS.LoadCarrierHierarchyExtended", "TargetConnection":"PlacementStore"}', 'PS.CarrierHierarchyExtended')
  , (@LoadType_Intraday, 'PS.LoadElementTimeline', @ProcessType_SP, '{"StoredProcedure":"PS.LoadElementTimeline", "TargetConnection":"PlacementStore"}', 'PS.ElementTimeline')
  , (@LoadType_Intraday, 'PS.LoadGlobalPartySecurity', @ProcessType_SP, '{"StoredProcedure":"PS.LoadGlobalPartySecurity", "TargetConnection":"PlacementStore"}', 'PS.GlobalPartySecurity')
  , (@LoadType_Intraday, 'PS.LoadPlacementSecurity', @ProcessType_SP, '{"StoredProcedure":"PS.LoadPlacementSecurity", "Parameters":"@Success", "TargetConnection":"PlacementStore"}', 'PS.PlacementSecurity')
  , (@LoadType_Intraday, 'PS.UpdateTranslationLanguage', @ProcessType_SP, '{"StoredProcedure":"PS.UpdateTranslationLanguage", "TargetConnection":"PlacementStore"}', NULL)
  , (@LoadType_Intraday, 'ReferenceStaging.MergeIntoAuthorizationStatus', @ProcessType_SP, '{"StoredProcedure":"ReferenceStaging.MergeIntoAuthorizationStatus", "TargetConnection":"PlacementStore"}', 'Reference.AuthorizationStatus')
  , (@LoadType_Intraday, 'ReferenceStaging.MergeIntoCheckType', @ProcessType_SP, '{"StoredProcedure":"ReferenceStaging.MergeIntoCheckType", "TargetConnection":"PlacementStore"}', 'Reference.CheckType')
  , (@LoadType_Intraday, 'ReferenceStaging.MergeIntoPartyVerificationStatus', @ProcessType_SP, '{"StoredProcedure":"ReferenceStaging.MergeIntoPartyVerificationStatus", "TargetConnection":"PlacementStore"}', 'Reference.PartyVerificationStatus')
  , (@LoadType_Intraday, 'ReferenceStaging.MergeIntoProductMapping', @ProcessType_SP, '{"StoredProcedure":"ReferenceStaging.MergeIntoProductMapping", "TargetConnection":"PlacementStore"}', 'ReferenceStaging.rpt_vwProductMapping')
  , (@LoadType_Intraday, 'ReferenceStaging.MergeProductMappingIntoProduct', @ProcessType_SP, '{"StoredProcedure":"ReferenceStaging.MergeProductMappingIntoProduct", "TargetConnection":"PlacementStore", "ResultType":"DummyRow"}', 'dbo.Product')
  , (@LoadType_Intraday, 'ReferenceStaging.rpt_vwAuthorizationStatus', @ProcessType_Copy, '{"SourceConnection":"WillisReference", "SourceTable":"rpt.vwAuthorizationStatus", "SourceSQL":"", "TargetTable":"ReferenceStaging.rpt_vwAuthorizationStatus", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate"}', 'ReferenceStaging.rpt_vwAuthorizationStatus')
  , (@LoadType_Intraday, 'ReferenceStaging.rpt_vwCheckType', @ProcessType_Copy, '{"SourceConnection":"WillisReference", "SourceTable":"rpt.vwCheckType", "SourceSQL":"", "TargetTable":"ReferenceStaging.rpt_vwCheckType", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate"}', 'ReferenceStaging.rpt_vwCheckType')
  , (@LoadType_Intraday, 'ReferenceStaging.rpt_vwPartyVerificationStatus', @ProcessType_Copy, '{"SourceConnection":"WillisReference", "SourceTable":"rpt.vwPartyVerificationStatus", "SourceSQL":"", "TargetTable":"ReferenceStaging.rpt_vwPartyVerificationStatus", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate"}', 'ReferenceStaging.rpt_vwPartyVerificationStatus')
  , (@LoadType_Intraday, 'ReferenceStaging.rpt_vwProductMapping', @ProcessType_Copy, '{"SourceConnection":"WillisReferenceMapping", "SourceTable":"rpt.vwProductMapping", "TargetTable":"ReferenceStaging.rpt_vwProductMapping", "TruncateTargetTable": true, "TargetConnection":"PlacementStore", "AlwaysFullLoad":false, "IncrementalUpdatedDateColumn":"ETLUpdatedDate", "DataSourceInstanceIdLookupSql":"SELECT Value = ADF.GetDataSourceInstanceList();", "DataSourceInstanceIdLookupConnection":"PlacementStore"}', 'ReferenceStaging.rpt_vwProductMapping')
  , (@LoadType_Intraday, 'rpt.Load_rpt_CarrierMatching', @ProcessType_SP, '{"StoredProcedure":"rpt.Load_rpt_CarrierMatching", "TargetConnection":"PlacementStore"}', 'rpt.CarrierMatching')
  , (@LoadType_Intraday, 'rpt.Load_rpt_Contract', @ProcessType_SP, '{"StoredProcedure":"rpt.Load_rpt_Contract", "TargetConnection":"PlacementStore"}', 'rpt.Contract')
  , (@LoadType_Intraday, 'rpt.Load_rpt_ContractElementAttribute', @ProcessType_SP, '{"StoredProcedure":"rpt.Load_rpt_ContractElementAttribute", "TargetConnection":"PlacementStore"}', 'rpt.ContractElementAttribute')
  , (@LoadType_Intraday, 'rpt.Load_rpt_ContractEndorsementLimit', @ProcessType_SP, '{"StoredProcedure":"rpt.Load_rpt_ContractEndorsementLimit", "TargetConnection":"PlacementStore"}', 'rpt.ContractLimit')
  , (@LoadType_Intraday, 'rpt.Load_rpt_ContractLimit', @ProcessType_SP, '{"StoredProcedure":"rpt.Load_rpt_ContractLimit", "TargetConnection":"PlacementStore"}', 'rpt.ContractLimit')
  , (@LoadType_Intraday, 'rpt.Load_rpt_ContractVersionLimit', @ProcessType_SP, '{"StoredProcedure":"rpt.Load_rpt_ContractVersionLimit", "TargetConnection":"PlacementStore"}', 'rpt.ContractLimit')
  , (@LoadType_Intraday, 'rpt.Load_rpt_Market', @ProcessType_SP, '{"StoredProcedure":"rpt.Load_rpt_Market", "TargetConnection":"PlacementStore"}', 'rpt.Market')
  , (@LoadType_Intraday, 'rpt.Load_rpt_MarketActivity', @ProcessType_SP, '{"StoredProcedure":"rpt.Load_rpt_MarketActivity", "TargetConnection":"PlacementStore"}', 'rpt.MarketActivity')
  , (@LoadType_Intraday, 'rpt.Load_rpt_MarketInteractionResponse', @ProcessType_SP, '{"StoredProcedure":"rpt.Load_rpt_MarketInteractionResponse", "TargetConnection":"PlacementStore"}', 'rpt.MarketInteractionResponse')
  , (@LoadType_Intraday, 'rpt.Load_rpt_MarketResponseElementAttribute', @ProcessType_SP, '{"StoredProcedure":"rpt.Load_rpt_MarketResponseElementAttribute", "TargetConnection":"PlacementStore"}', 'rpt.MarketResponseElementAttribute')
  , (@LoadType_Intraday, 'rpt.Load_rpt_PlacementExposure', @ProcessType_SP, '{"StoredProcedure":"rpt.Load_rpt_PlacementExposure", "TargetConnection":"PlacementStore"}', 'rpt.PlacementExposure')
  , (@LoadType_Intraday, 'rpt.Load_rpt_PlacementPrimaryRole', @ProcessType_SP, '{"StoredProcedure":"rpt.Load_rpt_PlacementPrimaryRole", "TargetConnection":"PlacementStore"}', 'rpt.PlacementPrimaryRole')
  , (@LoadType_Intraday, 'rpt.Load_rpt_PlacementProductSummary', @ProcessType_SP, '{"StoredProcedure":"rpt.Load_rpt_PlacementProductSummary", "TargetConnection":"PlacementStore"}', 'rpt.PlacementProductSummary')
  , (@LoadType_Intraday, 'rpt.Load_rpt_PlacementSecurity', @ProcessType_SP, '{"StoredProcedure":"rpt.Load_rpt_PlacementSecurity", "TargetConnection":"PlacementStore"}', 'rpt.PlacementSecurity')
  , (@LoadType_Intraday, 'rpt.Load_rpt_PolicyAudit', @ProcessType_SP, '{"StoredProcedure":"rpt.Load_rpt_PolicyAudit", "TargetConnection":"PlacementStore"}', 'rpt.PolicyAudit')
  , (@LoadType_Intraday, 'rpt.Load_rpt_ProductHierarchy', @ProcessType_SP, '{"StoredProcedure":"rpt.Load_rpt_ProductHierarchy", "TargetConnection":"PlacementStore"}', 'rpt.ProductHierarchy')
  , (@LoadType_Intraday, 'rpt.Load_rpt_ProgrammeContractLimit', @ProcessType_SP, '{"StoredProcedure":"rpt.Load_rpt_ProgrammeContractLimit", "TargetConnection":"PlacementStore"}', 'rpt.ContractLimit')
  , (@LoadType_Intraday, 'rpt.Load_rpt_RiskAttribute', @ProcessType_SP, '{"StoredProcedure":"rpt.Load_rpt_RiskAttribute", "TargetConnection":"PlacementStore"}', 'rpt.RiskAttribute')
  , (@LoadType_Intraday, 'rpt.Load_rpt_SubmissionDistribution', @ProcessType_SP, '{"StoredProcedure":"rpt.Load_rpt_SubmissionDistribution", "TargetConnection":"PlacementStore"}', 'rpt.SubmissionDistribution')
  , (@LoadType_Intraday, 'rpt.Load_rpt_SubmissionMarketSelection', @ProcessType_SP, '{"StoredProcedure":"rpt.Load_rpt_SubmissionMarketSelection", "TargetConnection":"PlacementStore"}', 'rpt.SubmissionMarketSelection')
  , (@LoadType_Intraday, 'rpt.Load_rpt_UserSecurity', @ProcessType_SP, '{"StoredProcedure":"rpt.Load_rpt_UserSecurity", "TargetConnection":"PlacementStore"}', 'rpt.UserSecurity')
  , (@LoadType_Intraday, 'rpt.LoadAuditUser', @ProcessType_SP, '{"StoredProcedure":"rpt.LoadAuditUser", "TargetConnection":"PlacementStore", "ResultType":"DummyRow"}', 'rpt.AuditUser')
  , (@LoadType_Intraday, 'rpt.LoadContractAuditUser', @ProcessType_SP, '{"StoredProcedure":"rpt.LoadContractAuditUser", "TargetConnection":"PlacementStore", "ResultType":"DummyRow"}', 'rpt.ContractAuditUser')
  , (@LoadType_Intraday, 'rpt.LoadContractDocument', @ProcessType_SP, '{"StoredProcedure":"rpt.LoadContractDocument", "TargetConnection":"PlacementStore", "ResultType":"DummyRow"}', 'rpt.ContractDocument')
  , (@LoadType_Intraday, 'Send Data Refresh Message', @ProcessType_Event, '{"TopicName":"psapi-to-consumer-event/messages","TargetConnection":"AzureServiceBus", "Action":"RefreshComplete","BodyTemplate":"{\"RunStartDateTime\":\"@InstanceStartDate\", \"MessageCreatedUTCDateTime\":\"@GetUtcDate\", \"StartStep\":\"Broking Platform only (Step 3)\"}"}', '')
  , (@LoadType_Intraday, 'SHPACT.Carrier', @ProcessType_Event, '{"TopicName":"psadf-to-psfuncapp-event/messages","TargetConnection":"AzureServiceBus","BodyTemplate":"{\"SourceDatabase\":\"ServiceHubMetadata\", \"SourceTable\":\"ETL.CarrierServiceHub\", \"SourceQuery\":\"SELECT PartyId,DataSourceInstanceId,PartyKey,SourceQueryId,CompCode,DUNSNumber,Email,GeographyId,ParentId,Party,PhoneNumber,CAST(CASE WHEN Attributes NOT LIKE ''%&amp;%'' THEN REPLACE(Attributes, ''&'', ''&amp;'') ELSE Attributes END AS XML) AS Attributes,SourceLastUpdateDate,CAST(IsDeleted AS BIT) AS IsDeleted,SourcePartyId,GlobalPartyId,PartyId AS DataSourcePartyId FROM ETL.CarrierServiceHub;\", \"TargetDatabase\":\"ServiceHub\", \"TargetTable\":\"Staging.vw_Carrier\",\"ProcessId\":@ProcessId,\"SessionGuId\":\"@SessionGuid\",\"RunGuid\":\"@RunGuid\",\"InstanceLogId\":@InstanceLogId,\"ProcessSessionId\":\"@ProcessSessionId\",\"TriggerType\":\"Event\"}","HandlerUpdatesStatus":true}', 'Staging.vw_Carrier')
  , (@LoadType_Intraday, 'SHPACT.Client', @ProcessType_Event, '{"TopicName":"psadf-to-psfuncapp-event/messages","TargetConnection":"AzureServiceBus","BodyTemplate":"{\"SourceDatabase\":\"ServiceHubMetadata\", \"SourceTable\":\"ETL.ClientServiceHub\", \"SourceQuery\":\"SELECT PartyId,DataSourceInstanceId,PartyKey,SourceQueryId,CompCode,DUNSNumber,Email,GeographyId,ParentId,Party,PhoneNumber,CAST(CASE WHEN Attributes NOT LIKE ''%&amp;%'' THEN REPLACE(Attributes, ''&'', ''&amp;'') ELSE Attributes END AS XML) AS Attributes,SourceLastUpdateDate,CAST(IsDeleted AS BIT) AS IsDeleted,SourcePartyId,GlobalPartyId,PartyId AS DataSourcePartyId FROM ETL.ClientServiceHub;\", \"TargetDatabase\":\"ServiceHub\", \"TargetTable\":\"Staging.vw_Client\",\"ProcessId\":@ProcessId,\"SessionGuId\":\"@SessionGuid\",\"RunGuid\":\"@RunGuid\",\"InstanceLogId\":@InstanceLogId,\"ProcessSessionId\":\"@ProcessSessionId\",\"TriggerType\":\"Event\"}","HandlerUpdatesStatus":true}', 'Staging.vw_Client')
  , (@LoadType_Intraday, 'SHStaging.Carrier_Ireland', @ProcessType_Copy, '{"SourceConnection":"PACTAzure", "SourceTable":"rpt.vwParty", "SourceSQL":"SELECT DISTINCT pty.PartyId, pty.DataSourceInstanceId, pty.[PartyKey], pty.[SourceQueryId], ISNULL(pty.[GeographyId], -1) AS [GeographyId], pty.[ParentId], pty.[Party], pty.[Attributes], pty.[ETLUpdatedDate] AS [SourceLastUpdateDate], CAST(pty.[isdeleted] AS INT) AS [isdeleted], pty.[sourcepartyid], pty.[globalpartyid] FROM rpt.vwPolicyPartyRole AS ppr INNER JOIN rpt.vwPartyRole AS pr ON pr.PartyRoleId = ppr.PartyRoleId AND pr.GlobalPartyRoleId = 102 INNER JOIN rpt.vwParty AS pty ON pty.PartyId = ppr.PartyId WHERE pr.DataSourceInstanceId IN (SELECT [value] FROM STRING_SPLIT(@DataSourceInstanceId, '','')) AND PTY.ETLUpdatedDate > @ETLUpdatedDate AND pty.PartyKey IS NOT NULL ORDER BY PartyId, DataSourceInstanceId;", "TargetTable":"ETL.CarrierServiceHub", "TargetConnection":"ServiceHubMetadata", "DontCheckForDataSourceInstanceId":false, "IncrementalUpdatedDateColumn":"SourceLastUpdateDate", "TruncateTargetTable": false, "DataSourceInstanceIdLookupSql":"SELECT STRING_AGG([DataSourceInstanceId], '','') AS [Value] FROM [ETL].[ClientCarrierETLConfig] WHERE EtlType IN (''Carrier_102_ireland'') AND IsDeleted = 0 AND IsActive = ''Y'';", "DataSourceInstanceIdLookupConnection":"ServiceHubMetadata"}', 'ETL.CarrierServiceHub')
  , (@LoadType_Intraday, 'SHStaging.Carrier_Like', @ProcessType_Copy, '{"SourceConnection":"PACTAzure", "SourceTable":"rpt.vwParty", "SourceSQL":"SELECT pty.[PartyId], pty.[DataSourceInstanceId], pty.[PartyKey], -1 AS [SourceQueryId], ISNULL(pty.[GeographyId], -1) AS [GeographyId], pty.[ParentId], pty.[Party], pty.[Attributes], pty.[ETLUpdatedDate] AS [SourceLastUpdateDate], CAST(pty.[isdeleted] AS INT) AS [isdeleted], pty.[sourcepartyid], pty.[globalpartyid] FROM (SELECT p.*, ROW_NUMBER() OVER (PARTITION BY p.PartyId, p.DataSourceInstanceId ORDER BY p.IsDeleted ASC) AS [Rank] FROM [rpt].[vwParty] AS p WHERE p.ETLUpdatedDate > @ETLUpdatedDate AND p.PartyKey like ''CARR|%'' AND p.PartyId IS NOT NULL AND p.DataSourceInstanceId IN (SELECT [value] FROM STRING_SPLIT(@DataSourceInstanceId, '',''))) AS pty WHERE [Rank] = 1;", "TargetTable":"ETL.CarrierServiceHub", "TargetConnection":"ServiceHubMetadata", "DontCheckForDataSourceInstanceId":false, "IncrementalUpdatedDateColumn":"SourceLastUpdateDate", "TruncateTargetTable": false, "DataSourceInstanceIdLookupSql":"SELECT STRING_AGG([datasourceinstanceid], '','') AS [Value]  FROM [ETL].[ClientCarrierETLConfig] WHERE etltype IN (''Carrier_Chile_like'', ''Carrier_Spain_like'', ''Carrier_Germany_Austria_like'',''Carrier_Like'') AND isdeleted = 0 AND isactive = ''Y'';", "DataSourceInstanceIdLookupConnection":"ServiceHubMetadata"}', 'ETL.CarrierServiceHub')
  , (@LoadType_Intraday, 'SHStaging.CarrierAtrrole', @ProcessType_Copy, '{"SourceConnection":"PAS", "SourceTable":"rpt.vwParty", "SourceSQL":"SELECT pty.[PartyId], pty.[DataSourceInstanceId], pty.[PartyKey], -1 AS [SourceQueryId], ISNULL(pty.[GeographyId], -1) AS [GeographyId], pty.[ParentId], pty.PartyName As Party, CAST(NULL AS NVARCHAR(MAX)) AS Attributes, pty.[ETLUpdatedDate] AS SourceLastUpdateDate, CAST(pty.[isdeleted] AS INT) AS [isdeleted], pty.[sourcepartyid], pty.[globalpartyid] FROM (SELECT p.*, CONVERT(NVARCHAR(500), CASE WHEN p.IsDeleted = 0 THEN '''' ELSE ''INACTIVE '' END  + ISNULL(pa.EclipseRoleDsc + ISNULL('' ('' + pa.EclipseLedgerAccounts + '')'', ''''), P.[Party])) AS [PartyName], ROW_NUMBER() OVER (partition BY p.PartyId, p.DataSourceInstanceId ORDER BY p.IsDeleted ASC) AS [Rank] FROM [rpt].[vwParty] AS p INNER JOIN rpt.vwPartyAttribute AS pa ON pa.PartyId = p.PartyId INNER JOIN (SELECT DISTINCT CAST(SUBSTRING([value], 1, 5) AS INT) AS DataSourceInstanceId, SUBSTRING([value], 7, 50) AS RoleKey FROM STRING_SPLIT(@DataSourceInstanceId, '','')) AS c ON c.DataSourceInstanceId = p.DataSourceInstanceId AND pa.EclipseRole = c.RoleKey WHERE p.ETLUpdatedDate > @ETLUpdatedDate AND p.PartyKey IS NOT NULL) AS pty WHERE [Rank] = 1;", "TargetTable":"ETL.CarrierServiceHub", "TargetConnection":"ServiceHubMetadata", "DontCheckForDataSourceInstanceId":false, "TruncateTargetTable": false, "IncrementalUpdatedDateColumn":"SourceLastUpdateDate", "DataSourceInstanceIdLookupSql":"SELECT STRING_AGG(CONCAT([DataSourceInstanceId], ''|'', RoleKey), '','') AS [Value]  FROM [ETL].[ClientCarrierETLConfig] WHERE EtlType IN (''CarrierAtrrole'') AND IsDeleted = 0 AND IsActive = ''Y'';", "DataSourceInstanceIdLookupConnection":"ServiceHubMetadata"}', 'ETL.CarrierServiceHub')
  , (@LoadType_Intraday, 'SHStaging.CarrierReferenceData', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceTable":"PS.CarrierHierarchyExtended", "SourceSQL":"SELECT  PartyId = (100000000) + CarrierID, DataSourceInstanceId = 50355, PartyKey = CarrierID, SourceQueryId = -1, GeographyId = -1, ParentId = CarrierGroupID, Party = CarrierName, Attributes = NULL, SourceLastUpdateDate = LastUpdatedUTCDate, isdeleted = CAST(0 AS INT), sourcepartyid = NULL, globalpartyid = NULL FROM PS.CarrierHierarchyExtended WHERE DataSourceInstanceId IN ( 50276, 50369 ) AND RoleDescription IN ( ''SignetCarrierGroup'', ''IMOperatingEntity'', ''SignetCarrier'' );", "TargetTable":"ETL.CarrierServiceHub", "TargetConnection":"ServiceHubMetadata", "TruncateTargetTable": false}', 'ETL.CarrierServiceHub')
  , (@LoadType_Intraday, 'SHStaging.CarrierSqk', @ProcessType_Copy, '{"SourceConnection":"PACTAzure", "SourceTable":"rpt.vwParty", "SourceSQL":"SELECT pty.[PartyId], pty.[DataSourceInstanceId], pty.[PartyKey], -1 AS [SourceQueryId], ISNULL(pty.[GeographyId], -1) AS [GeographyId], pty.[ParentId], pty.[Party], CAST(pty.[Attributes] AS NVARCHAR(MAX)) AS [Attributes], pty.[ETLUpdatedDate] AS [SourceLastUpdateDate], CAST(pty.[isdeleted] AS INT) AS [isdeleted], pty.[sourcepartyid], pty.[globalpartyid] FROM (SELECT p.*, ROW_NUMBER() OVER (PARTITION BY p.PartyId, p.DataSourceInstanceId ORDER BY p.IsDeleted ASC) AS [Rank] FROM (SELECT DISTINCT CAST(SUBSTRING(value, 1, 5) AS INT) AS DataSourceInstanceId, CAST(SUBSTRING(value, 7, 50) AS NVARCHAR(50)) AS SourceQuery FROM STRING_SPLIT(@DataSourceInstanceId, '','')) AS c INNER JOIN [rpt].[vwParty] AS p ON c.SourceQuery = p.SourceQuery AND c.DataSourceInstanceId = p.DataSourceInstanceId WHERE p.ETLUpdatedDate > @ETLUpdatedDate AND p.PartyId IS NOT NULL AND p.PartyKey IS NOT NULL) AS pty WHERE [Rank] = 1;", "TargetTable":"ETL.CarrierServiceHub", "TargetConnection":"ServiceHubMetadata", "DontCheckForDataSourceInstanceId":false, "IncrementalUpdatedDateColumn":"SourceLastUpdateDate", "TruncateTargetTable": true, "DataSourceInstanceIdLookupSql":"SELECT STRING_AGG(CONCAT([DataSourceInstanceId], ''|'', SourceQuery), '','') AS [Value]  FROM [ETL].[ClientCarrierETLConfig] WHERE EtlType IN (''Carriersqk'') AND IsDeleted = 0 AND IsActive = ''Y'';", "DataSourceInstanceIdLookupConnection":"ServiceHubMetadata"}', 'ETL.CarrierServiceHub')
  , (@LoadType_Intraday, 'SHStaging.CarrierSqk_Brazil', @ProcessType_Copy, '{"SourceConnection":"PACTAzure", "SourceTable":"rpt.vwParty", "SourceSQL":"SELECT pty.[PartyId], pty.[DataSourceInstanceId], pty.[PartyKey], -1 AS [SourceQueryId], ISNULL(pty.[GeographyId], -1) AS [GeographyId], pty.[ParentId], pty.[Party], pty.[Attributes], pty.[ETLUpdatedDate] AS [SourceLastUpdateDate], CAST(pty.[isdeleted] AS INT) AS [isdeleted], pty.[sourcepartyid], pty.[globalpartyid] FROM (SELECT p.*, ROW_NUMBER() OVER (PARTITION BY p.PartyId, p.DataSourceInstanceId ORDER BY p.IsDeleted ASC) AS [Rank] FROM (SELECT DISTINCT CAST(SUBSTRING([value], 1, 5) AS INT) AS DataSourceInstanceId, CAST(SUBSTRING([value], 7, 50) AS NVARCHAR(50)) AS SourceQuery FROM STRING_SPLIT(@DataSourceInstanceId, '','')) AS c INNER JOIN [rpt].[vwParty] AS p ON c.sourcequery = p.SourceQuery AND c.DataSourceInstanceId = p.DataSourceInstanceId WHERE p.ETLUpdatedDate > @ETLUpdatedDate AND p.PartyId IS NOT NULL AND p.PartyKey IS NOT NULL) AS pty WHERE [Rank] = 1;", "TargetTable":"ETL.CarrierServiceHub", "TargetConnection":"ServiceHubMetadata", "DontCheckForDataSourceInstanceId":false, "IncrementalUpdatedDateColumn":"SourceLastUpdateDate", "TruncateTargetTable": false, "DataSourceInstanceIdLookupSql":"SELECT STRING_AGG(CONCAT([DataSourceInstanceId], ''|'', SourceQuery), '','') AS [Value] FROM [ETL].[ClientCarrierETLConfig] WHERE EtlType IN (''Carriersqk_brazil'') AND IsDeleted = 0 AND IsActive = ''Y'';", "DataSourceInstanceIdLookupConnection":"ServiceHubMetadata"}', 'ETL.CarrierServiceHub')
  , (@LoadType_Intraday, 'SHStaging.CarrierTransactionLedger', @ProcessType_Copy, '{"SourceConnection":"PACTAzure", "SourceTable":"rpt.vwParty", "SourceSQL":"SELECT pty.PartyId, pty.DataSourceInstanceId, pty.[PartyKey], pty.[SourceQueryId], ISNULL(pty.[GeographyId], -1) AS [GeographyId], pty.[ParentId], pty.[Party], pty.[Attributes], pty.[ETLUpdatedDate] AS [SourceLastUpdateDate], CAST(pty.[isdeleted] AS INT) AS [isdeleted], pty.[sourcepartyid], pty.[globalpartyid] FROM rpt.vwTransactionLedger AS tl INNER JOIN rpt.vwParty AS pty ON pty.PartyId = tl.AccountPartyId INNER JOIN rpt.vwPartyRole AS pr ON pr.PartyRoleId = tl.AccountPartyRoleId AND pr.GlobalPartyRoleId = 102 WHERE tl.DataSourceInstanceId IN (SELECT [value] FROM STRING_SPLIT(@DataSourceInstanceId, '','')) AND tl.ETLUpdatedDate > @ETLUpdatedDate UNION SELECT pty.PartyId, pty.DataSourceInstanceId, pty.[PartyKey], pty.[SourceQueryId], ISNULL(pty.[GeographyId], -1) AS [GeographyId], pty.[ParentId], pty.[Party], pty.[Attributes], pty.[ETLUpdatedDate] AS [SourceLastUpdateDate], pty.[IsDeleted], pty.[SourcePartyId], pty.[GlobalPartyId] FROM rpt.vwTransactionLedgerBreakdown AS tlb INNER JOIN rpt.vwParty AS pty ON pty.PartyId = tlb.PartyId INNER JOIN rpt.vwPartyRole AS pr ON pr.PartyRoleId = tlb.PartyRoleId AND pr.DataSourceInstanceId = tlb.DataSourceInstanceId AND pr.GlobalPartyRoleId = 102 WHERE tlb.DataSourceInstanceId IN (SELECT [value] FROM STRING_SPLIT(@DataSourceInstanceId, '','')) AND tlb.ETLUpdatedDate > @ETLUpdatedDate AND pty.PartyKey IS NOT NULL;", "TargetTable":"ETL.CarrierServiceHub", "TargetConnection":"ServiceHubMetadata", "DontCheckForDataSourceInstanceId":false, "TruncateTargetTable": false, "IncrementalUpdatedDateColumn":"SourceLastUpdateDate", "DataSourceInstanceIdLookupSql":"SELECT STRING_AGG([DataSourceInstanceId], '','') AS [Value] FROM [ETL].[ClientCarrierETLConfig] WHERE EtlType IN (''CarrierTransactionledger'') AND IsDeleted = 0 AND IsActive = ''Y'';", "DataSourceInstanceIdLookupConnection":"ServiceHubMetadata"}', 'ETL.CarrierServiceHub')
  , (@LoadType_Intraday, 'SHStaging.Client_Like', @ProcessType_Copy, '{"SourceConnection":"PACTAzure", "SourceTable":"rpt.vwParty", "SourceSQL":"SELECT pty.[PartyId], pty.[DataSourceInstanceId], pty.[PartyKey], -1 AS [SourceQueryId], pty.[SourceQuery], ISNULL(pty.[GeographyId], -1) AS [GeographyId], pty.[ParentId], pty.[Party], CAST(pty.[Attributes] AS NVARCHAR(MAX)) AS [Attributes], pty.[ETLUpdatedDate] AS [SourceLastUpdateDate], CAST(CASE WHEN pty.[IsActive] = 0 THEN 1 ELSE pty.[IsDeleted] END AS INT) AS [isdeleted], pty.[sourcepartyid], pty.[globalpartyid] FROM (SELECT p.*, ROW_NUMBER() OVER (PARTITION BY p.PartyId, p.DataSourceInstanceId ORDER BY p.IsDeleted ASC) AS [Rank] FROM [rpt].[vwParty] AS p WHERE p.ETLUpdatedDate > @ETLUpdatedDate AND p.PartyId IS NOT NULL AND p.PartyKey LIKE ''CLT|%'' AND p.DataSourceInstanceId IN (SELECT [value] FROM STRING_SPLIT(@DataSourceInstanceId, '',''))) AS pty WHERE [Rank] = 1;", "TargetTable":"ETL.ClientServiceHub", "TargetConnection":"ServiceHubMetadata", "DontCheckForDataSourceInstanceId":false, "IncrementalUpdatedDateColumn":"SourceLastUpdateDate", "TruncateTargetTable": false, "DataSourceInstanceIdLookupSql":"SELECT STRING_AGG([DataSourceInstanceId], '','') AS [Value]  FROM [ETL].[ClientCarrierETLConfig] WHERE EtlType IN (''Client_Chile_like'', ''Client_Spain_like'', ''Client_Germany_Austria_like'',''Client_Like'') AND IsDeleted = 0 AND IsActive = ''Y'';", "DataSourceInstanceIdLookupConnection":"ServiceHubMetadata", "AlwaysFullLoad":false }', 'ETL.ClientServiceHub')
  , (@LoadType_Intraday, 'SHStaging.Client_PartyRoleId', @ProcessType_Copy, '{"SourceConnection":"PACTAzure", "SourceTable":"rpt.vwParty", "SourceSQL":"SELECT pty.[PartyId], pty.[DataSourceInstanceId], pty.[PartyKey], -1 AS [SourceQueryId], pty.[SourceQuery], ISNULL(pty.[GeographyId], -1) AS [GeographyId], pty.[ParentId], pty.Party, CAST(pty.[Attributes] AS NVARCHAR(MAX)) AS [Attributes], pty.[ETLUpdatedDate] AS [SourceLastUpdateDate], CAST(CASE WHEN pty.[IsActive] = 0 THEN 1 ELSE pty.[IsDeleted] END AS INT) AS [isdeleted], pty.[sourcepartyid], pty.[globalpartyid] FROM (SELECT p.*, ROW_NUMBER() OVER (PARTITION BY p.PartyId, p.DataSourceInstanceId ORDER BY p.IsDeleted ASC) AS [Rank] FROM (SELECT DISTINCT ppr.PartyId, pty.DataSourceInstanceId FROM rpt.vwPolicyPartyRole AS ppr INNER JOIN rpt.vwPartyRole AS pr ON pr.PartyRoleId = ppr.PartyRoleId AND pr.GlobalPartyRoleId IN (100, 101) INNER JOIN rpt.vwParty AS PTY ON pty.PartyId = ppr.PartyId WHERE  pr.DataSourceInstanceId IN (SELECT [value] FROM STRING_SPLIT(@DataSourceInstanceId , '','')) AND PTY.ETLUpdatedDate > @ETLUpdatedDate AND PTY.PartyKey IS NOT NULL) AS c INNER JOIN [rpt].[vwparty] AS p ON c.DataSourceInstanceId = p.DataSourceInstanceId AND c.PartyId = p.PartyId) AS pty WHERE [Rank] = 1;", "TargetTable":"ETL.ClientServiceHub", "TargetConnection":"ServiceHubMetadata", "DontCheckForDataSourceInstanceId":false, "TruncateTargetTable": false, "IncrementalUpdatedDateColumn":"SourceLastUpdateDate", "DataSourceInstanceIdLookupSql":"SELECT STRING_AGG([DataSourceInstanceId], '','') AS [Value]  FROM [ETL].[ClientCarrierETLConfig] WHERE EtlType IN (''ClientPolicyparyrole'', ''Client_50030'', ''Client_100'', ''Client_100_ireland'') AND IsDeleted = 0 AND IsActive = ''Y'';", "DataSourceInstanceIdLookupConnection":"ServiceHubMetadata", "AlwaysFullLoad":false }', 'ETL.ClientServiceHub')
  , (@LoadType_Intraday, 'SHStaging.ClientAtrrole', @ProcessType_Copy, '{"SourceConnection":"PAS", "SourceTable":"rpt.vwParty", "SourceSQL":"SELECT pty.[PartyId], pty.[DataSourceInstanceId], pty.[PartyKey], -1 AS [SourceQueryId], pty.[SourceQuery], ISNULL(pty.[GeographyId], -1) AS [GeographyId], pty.[ParentId], pty.PartyName As Party, CAST(NULL AS NVARCHAR(MAX)) AS Attributes, pty.[ETLUpdatedDate] AS SourceLastUpdateDate, CAST(CASE WHEN pty.[IsActive] = 0 THEN 1 ELSE pty.[IsDeleted] END AS INT) AS [isdeleted], pty.[sourcepartyid], pty.[globalpartyid] FROM (SELECT p.*, CONVERT(NVARCHAR(500), CASE WHEN p.IsDeleted = 0 THEN '''' ELSE ''INACTIVE '' END  + ISNULL(pa.EclipseRoleDsc + ISNULL('' ('' + pa.EclipseLedgerAccounts + '')'', ''''), P.[Party])) AS [PartyName], CONVERT(NVARCHAR(50), LOWER(pa.EclipseRole)) AS RoleKey, ROW_NUMBER() OVER (PARTITION BY p.PartyId, p.DataSourceInstanceId ORDER BY p.IsDeleted ASC) AS [Rank] FROM [rpt].[vwParty] AS P INNER JOIN rpt.vwPartyAttribute AS pa ON pa.PartyId = P.PartyId INNER JOIN (SELECT DISTINCT CAST(SUBSTRING([value], 1, 5) AS INT) AS DataSourceInstanceId, SUBSTRING([value], 7, 50) AS RoleKey FROM STRING_SPLIT(@DataSourceInstanceId, '','')) AS c ON c.DataSourceInstanceId = p.DataSourceInstanceId AND pa.EclipseRole = c.RoleKey WHERE P.ETLUpdatedDate > @ETLUpdatedDate AND P.PartyId IS NOT NULL AND P.PartyKey IS NOT NULL) AS pty WHERE [Rank] = 1;", "TargetTable":"ETL.ClientServiceHub", "TargetConnection":"ServiceHubMetadata", "DontCheckForDataSourceInstanceId":false, "IncrementalUpdatedDateColumn":"SourceLastUpdateDate", "TruncateTargetTable": false, "DataSourceInstanceIdLookupSql":"SELECT STRING_AGG(CONCAT([DataSourceInstanceId], ''|'', RoleKey), '','') AS [Value]  FROM [ETL].[ClientCarrierETLConfig] WHERE EtlType IN (''ClientAtrrole'') AND IsDeleted = 0 AND IsActive = ''Y'';", "DataSourceInstanceIdLookupConnection":"ServiceHubMetadata", "AlwaysFullLoad":false }', 'ETL.ClientServiceHub')
  , (@LoadType_Intraday, 'SHStaging.ClientReference', @ProcessType_Copy, '{"SourceConnection":"PlacementStore", "SourceTable":"ods.vw_ref_Party", "SourceSQL":"SELECT DataSourceInstanceId, PartyKey, -10 as SourceQueryId, -10 as GeographyId, NULL As ParentId, Partyname As Party, CAST(''<Attributes><Role>Reference Party</Role></Attributes>'' AS XML) AS Attributes, ETLUpdatedDate as SourceLastUpdateDate, CAST(IsDeprecated AS INT) AS isdeleted, PactPartyId As sourcepartyid, globalpartyid, PartyId FROM [ods].[vw_ref_Party](nolock) WHERE DataSourceInstanceID = 50355 AND ETLUpdatedDate > @ETLUpdatedDate", "TargetTable":"ETL.ClientServiceHub", "TargetConnection":"ServiceHubMetadata", "DontCheckForDataSourceInstanceId":false, "TruncateTargetTable": false, "IncrementalUpdatedDateColumn":"SourceLastUpdateDate"}', 'ETL.ClientServiceHub')
  , (@LoadType_Intraday, 'SHStaging.ClientSqk', @ProcessType_Copy, '{"SourceConnection":"PACTAzure", "SourceTable":"rpt.vwParty", "SourceSQL":"SELECT pty.[PartyId], pty.[DataSourceInstanceId], pty.[PartyKey], -1 AS [SourceQueryId], pty.[SourceQuery], ISNULL(pty.[GeographyId], -1) AS [GeographyId], pty.[ParentId], pty.[Party], CAST(pty.[Attributes] AS NVARCHAR(MAX)) AS [Attributes], pty.[ETLUpdatedDate] AS SourceLastUpdateDate, CAST(CASE WHEN pty.[IsActive] = 0 THEN 1 ELSE pty.[IsDeleted] END AS INT) AS [isdeleted], pty.[sourcepartyid], pty.[globalpartyid] FROM (SELECT p.*, ROW_NUMBER() OVER (PARTITION BY p.PartyId, p.DataSourceInstanceId ORDER BY p.IsDeleted ASC) AS [Rank] FROM (SELECT DISTINCT CAST(SUBSTRING([value], 1, 5) AS INT) AS DataSourceInstanceId, CAST(SUBSTRING([value], 7, 50) AS NVARCHAR(50)) AS SourceQuery FROM STRING_SPLIT(@DataSourceInstanceId, '','')) AS c INNER JOIN [rpt].[vwParty] AS p ON c.SourceQuery = p.SourceQuery AND c.DataSourceInstanceId = p.DataSourceInstanceId WHERE P.ETLUpdatedDate > @ETLUpdatedDate AND p.PartyID IS NOT NULL AND p.PartyKey IS NOT NULL) AS pty WHERE [Rank] = 1;", "TargetTable":"ETL.ClientServiceHub", "TargetConnection":"ServiceHubMetadata", "DontCheckForDataSourceInstanceId":false, "TruncateTargetTable": true, "IncrementalUpdatedDateColumn":"SourceLastUpdateDate", "DataSourceInstanceIdLookupSql":"SELECT STRING_AGG(CONCAT([DataSourceInstanceId], ''|'', SourceQuery), '','') AS [Value] FROM [ETL].[ClientCarrierETLConfig] WHERE EtlType IN (''Clientsqk'') AND IsDeleted = 0 AND IsActive = ''Y'';", "DataSourceInstanceIdLookupConnection":"ServiceHubMetadata", "AlwaysFullLoad":false }', 'ETL.ClientServiceHub')
  , (@LoadType_Intraday, 'SHStaging.ClientSqk_Brazil', @ProcessType_Copy, '{"SourceConnection":"PACTAzure", "SourceTable":"rpt.vwParty", "SourceSQL":"SELECT pty.[PartyId], pty.[DataSourceInstanceId], pty.[PartyKey], -1 AS [SourceQueryId], pty.[SourceQuery], ISNULL(pty.[GeographyId], -1) AS [GeographyId], pty.[ParentId], pty.PartyName As Party, CAST(pty.[Attributes] AS NVARCHAR(MAX)) AS [Attributes], pty.[ETLUpdatedDate] AS [SourceLastUpdateDate], CAST(CASE WHEN pty.[IsActive] = 0 THEN 1 ELSE pty.[IsDeleted] END AS INT) AS [isdeleted], pty.[sourcepartyid], pty.[globalpartyid] FROM (SELECT p.*, CASE WHEN p.IsDeleted = 1 THEN ''INACTIVE '' ELSE '''' END + ISNULL(p.Party + '' ('' + CONVERT(NVARCHAR(60), a.[Value]) + '')'', P.[Party]) AS [PartyName], ROW_NUMBER() OVER (partition BY p.PartyId, p.DataSourceInstanceId ORDER BY p.IsDeleted ASC) AS [Rank] FROM (SELECT DISTINCT CAST(SUBSTRING([value], 1, 5) AS INT) AS DataSourceInstanceId, SUBSTRING([value], 7, 50) AS SourceQuery FROM STRING_SPLIT(@DataSourceInstanceId, '','')) AS c INNER JOIN [rpt].[vwParty] AS p ON c.SourceQuery = p.SourceQuery AND c.DataSourceInstanceId = p.DataSourceInstanceId LEFT JOIN rpt.vwAttribute AS a ON p.PartyId = a.ObjectId AND a.ObjectType = ''Cgc_cpf'' AND a.IsDeleted = 0 WHERE p.ETLUpdatedDate > @ETLUpdatedDate AND p.PartyId IS NOT NULL AND p.PartyKey IS NOT NULL) AS pty WHERE [Rank] = 1;", "TargetTable":"ETL.ClientServiceHub", "TargetConnection":"ServiceHubMetadata", "DontCheckForDataSourceInstanceId":false, "IncrementalUpdatedDateColumn":"SourceLastUpdateDate", "TruncateTargetTable": false, "DataSourceInstanceIdLookupSql":"SELECT STRING_AGG(CONCAT([DataSourceInstanceId], ''|'', SourceQuery), '','') AS [Value] FROM [ETL].[ClientCarrierETLConfig] WHERE EtlType IN (''Clientsqk_brazil'') AND IsDeleted = 0 AND IsActive = ''Y'';", "DataSourceInstanceIdLookupConnection":"ServiceHubMetadata", "AlwaysFullLoad":false }', 'ETL.ClientServiceHub')
  , (@LoadType_Intraday, 'SignetStaging.AssociatedCarriersRIMS', @ProcessType_Copy, '{"SourceConnection":"Signet", "SourceTable":"dbo.vwAssociatedCarriersRIMS", "SourceSQL":"SELECT [CompCode],[CompanyType],[CountryName],[Underlying CompCode] AS UnderlyingCompCode,[Underlying Relationship] as UnderlyingRelationship,[Begindate] AS BeginDate,[End Date] AS EndDate FROM [signet].[dbo].[vwAssociatedCarriersRIMS]", "TargetTable":"SignetStaging.AssociatedCarriersRIMS", "TruncateTargetTable": true, "TargetConnection":"PlacementStore"}', 'SignetStaging.AssociatedCarriersRIMS')
  , (@LoadType_Intraday, 'SignetStaging.LoadApprovedCarrier', @ProcessType_SP, '{"StoredProcedure":"SignetStaging.LoadApprovedCarrier", "TargetConnection":"PlacementStore"}', 'Reference.ApprovedCarrier')
  , (@LoadType_Intraday, 'Task.StartRun', @ProcessType_SP, '{"StoredProcedure":"Task.StartRun", "TargetConnection":"PlacementStore"}', 'Task.PolicyRenewalTask, Task.PolicyRenewal, Task.PolicyAudit')
  , (@LoadType_Intraday, 'Truncate FMATemp.TruncateTranslation', @ProcessType_SP, '{"StoredProcedure" : "FMATemp.TruncateTranslation", "TargetConnection":"PlacementStore"}', 'FMATemp.staging_vw_Translation')
  , (@LoadType_Model, 'AzureProcessSSAS-Carrier', @ProcessType_Event, '{"TopicName":"psadf-to-psfuncapp-ssas-refresh-event/messages","TargetConnection":"AzureServiceBus","TargetDatabase":"CarrierAnalytics_AS_TAB","BodyTemplate":"{\"RefreshType\":\"Full\",\"DatabaseName\":\"CarrierAnalytics_AS_TAB\",\"InstanceLogId\":@InstanceLogId,\"ProcessSessionId\":@ProcessSessionId}"}', '')
  , (@LoadType_Model, 'AzureProcessSSAS-General', @ProcessType_Event, '{"TopicName":"psadf-to-psfuncapp-ssas-refresh-event/messages","TargetConnection":"AzureServiceBus", "TargetDatabase":"GeneralAnalytics_AS_TAB","BodyTemplate":"{\"RefreshType\":\"Full\",\"DatabaseName\":\"GeneralAnalytics_AS_TAB\",\"InstanceLogId\":@InstanceLogId,\"ProcessSessionId\":@ProcessSessionId}"}', '')
  , (@LoadType_Model, 'AzureProcessSSAS-Usage', @ProcessType_Event, '{"TopicName":"psadf-to-psfuncapp-ssas-refresh-event/messages","TargetConnection":"AzureServiceBus", "TargetDatabase":"BrokingPlatformUsage_AS_TAB","BodyTemplate":"{\"RefreshType\":\"Full\",\"DatabaseName\":\"BrokingPlatformUsage_AS_TAB\",\"InstanceLogId\":@InstanceLogId,\"ProcessSessionId\":@ProcessSessionId}"}', '');

IF EXISTS (SELECT Name FROM @Process GROUP BY Name HAVING COUNT(*) > 1)
BEGIN
    SELECT Name
    FROM
        @Process
    GROUP BY
        Name
    HAVING
        COUNT(*) > 1;

    THROW 50000, 'PopulateProcess.sql: Duplicate process names exist!', 1;

    RETURN;
END;

MERGE ADF.Process T
USING
    (SELECT LoadTypeId, Name, ProcessTypeId, JSONConfig, TargetTable, IsDeleted = 0 FROM @Process) S
ON T.Name = S.Name
WHEN NOT MATCHED BY TARGET
    THEN INSERT
             (Name, ProcessTypeId, JSONConfig, TargetTable, IsDeleted, ETLCreatedDate, ETLUpdatedDate, LoadTypeId)
         VALUES
             (S.Name, S.ProcessTypeId, S.JSONConfig, S.TargetTable, S.IsDeleted, GETUTCDATE(), GETUTCDATE(), S.LoadTypeId)
WHEN MATCHED AND NOT EXISTS
                         (SELECT S.ProcessTypeId, S.JSONConfig, S.LoadTypeId, S.TargetTable, S.IsDeleted
                          INTERSECT
                          SELECT T.ProcessTypeId, T.JSONConfig, T.LoadTypeId, T.TargetTable, T.IsDeleted)
    THEN UPDATE SET T.ProcessTypeId = S.ProcessTypeId, T.JSONConfig = S.JSONConfig, T.TargetTable = S.TargetTable, T.IsDeleted = S.IsDeleted, T.ETLUpdatedDate = GETUTCDATE(), T.LoadTypeId = S.LoadTypeId
WHEN NOT MATCHED BY SOURCE AND T.IsDeleted = 0
    THEN UPDATE SET T.IsDeleted = 1, T.ETLUpdatedDate = GETUTCDATE();
