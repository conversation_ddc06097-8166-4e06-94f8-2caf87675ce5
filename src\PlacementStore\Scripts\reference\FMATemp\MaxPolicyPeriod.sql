DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'FMATemp.staging_vw_MaxPolicyPeriod';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET @SprocName = 'FMATemp.stageMaxPolicyPeriod';

EXEC ADF.StoredProcStartLog @SprocName;

DROP TABLE IF EXISTS #GeminiProduct;

CREATE TABLE #GeminiProduct (
    ClassOfBusiness              NVARCHAR(50)
  , SubClass                     NVARCHAR(200)
  , LineBasedOn                  NVARCHAR(50)
  , MaxUSDLimit                  DECIMAL(21, 2)
  , MAXLinePct                   DECIMAL(9, 6)
  , PeriodOfInsuranceDescription NVARCHAR(100)
  , MaxPolicyPeriodInMonths      INT
);

INSERT INTO #GeminiProduct
    (ClassOfBusiness, SubClass, LineBasedOn, MaxUSDLimit, MAXLinePct, PeriodOfInsuranceDescription, MaxPolicyPeriodInMonths)
VALUES
    ('Property', 'International Property', 'Per risk', 75000000, 0.125, '18 months', 18)
  , ('Property', 'International Property - Critical CAT', 'Per risk', 35000000, 0.125, '18 months', 18)
  , ('Property', 'North America Property (US & Canada)', 'Per risk', 75000000, 0.125, '18 months', 18)
  , ('Property', 'North America Property  (US & Canada) - Critical CAT', 'Per risk', 35000000, 0.125, '18 months', 18)
  , ('Casualty / Liability', 'Casualty', 'Per risk', 35000000, 0.125, '18 months', 18)
  , ('Casualty / Liability', 'Construction', 'Per risk', 20000000, 0.125, '5 years plus 10 years completed operations', 60)
  , ('Casualty / Liability', 'Healthcare', 'Per risk', 20000000, 0.125, '18 months', 18)
  , ('Casualty / Liability', 'Environmental', 'Per risk', 20000000, 0.125, '18 months', 18)
  , ('Casualty / Liability', 'Product Recall', 'Per risk', 20000000, 0.125, '18 months', 18)
  , ('Construction', 'Construction (line part of ECV)', 'Per risk based on ECV', 35000000, 0.125, '60 months + 24 months Maintenance and Discovery', 60)
  , ('Construction', 'Construction (line part of risk engineered PML)', 'PML where available', 35000000, 0.125, '60 months + 24 months Maintenance and Discovery', 60)
  , ('Natural Resources', 'Downstream Energy', 'Per risk', 75000000, 0.125, '18 months', 18)
  , ('Natural Resources', 'Downstream Energy - Critical CAT', 'Per risk', 35000000, 0.125, '18 months', 18)
  , ('Natural Resources', 'Upstream Energy', 'Per risk / Per complex', 75000000, 0.125, '18 months', 18)
  , ('Natural Resources', 'Upstream Energy - Critical CAT', 'Per risk / Per complex', 35000000, 0.125, '18 months', 18)
  , ('Natural Resources', 'Midstream Energy', 'Per risk', 75000000, 0.125, '18 months', 18)
  , ('Natural Resources', 'Midstream Energy - Critical CAT', 'Per risk', 35000000, 0.125, '18 months', 18)
  , ('Natural Resources', 'Energy Liabilities', 'Per risk', 35000000, 0.125, '18 months', 18)
  , ('Natural Resources', 'Energy Liabilities - Onshore Construction', 'Per risk', 35000000, 0.125, '60 months + 24 months Maintenance and Discovery', 60)
  , ('Natural Resources', 'Offshore Energy - Construction', 'Per Risk based on FCV', 35000000, 0.125, '60 months + 24 months Maintenance and Discovery', 60)
  , ('Natural Resources', 'Power - Operational', 'Per risk', 75000000, 0.125, '18 months', 18)
  , ('Natural Resources', 'Power - Operational - Critical CAT', 'Per risk', 35000000, 0.125, '18 months', 18)
  , ('Natural Resources', 'Renewables', 'Per risk', 35000000, 0.125, '18 months', 18)
  , ('Natural Resources', 'Renewables Construction', 'Per risk', 35000000, 0.125, '36 months + 24 months Operational', 36)
  , ('Natural Resources', 'Renewables - TPL', 'Per risk', 35000000, 0.125, '18 months', 18)
  , ('FINEX', 'D&O - Liabilities excl Financial Institutions', 'Per risk', 25000000, 0.125, '18 months (+ 6 Years Run off for all risks)', 18)
  , ('FINEX', 'Professional Indemnity', 'Per risk', 20000000, 0.125, '18 months', 18)
  , ('FINEX', 'FI Placement - FIPI (F2 or F3) & / or  Crime (BB) & / or D&O (D4 or D5)  ', 'Per risk', 20000000, 0.125, '18 months', 18)
  , ('FINEX', 'Cyber', 'Per risk', 20000000, 0.125, '18 months', 18)
  , ('FINEX', 'Crime / Fidelity', 'Per risk', 20000000, 0.125, '18 months (+ 6 Years Run off for all risks)', 18)
  , ('FINEX', 'Credit Solutions', 'Per risk', 20000000, 0.125, '18 months', 18)
  , ('FINEX', 'Warranty & Indemnity', 'Per risk', 10000000, 0.125, '18 months', 18)
  , ('FINEX', 'Public Offering of Securities Insurance / Transaction Liability', 'Per risk', 20000000, 0.125, '18 months (+ 6 Years Run off for all risks)', 18)
  , ('Marine', 'Cargo', 'Per risk', 35000000, 0.125, '18 months', 18)
  , ('Marine', 'Fine Art & Specie', 'Per risk', 35000000, 0.125, '18 months', 18)
  , ('Marine', 'Marine Construction', 'Per risk', 35000000, 0.125, '36 months', 36)
  , ('Marine', 'Ports & Terminals Property', 'Per risk', 35000000, 0.125, '18 months', 18)
  , ('Marine', 'Ports & Terminals Liabs', 'Per risk', 35000000, 0.125, '18 months', 18)
  , ('Marine', 'Hull', 'Per risk', 35000000, 0.125, '18 months', 18)
  , ('Marine', 'War', 'Per risk', 35000000, 0.125, '18 months', 18)
  , ('Marine', 'Marine Liabs', 'Per risk', 35000000, 0.125, '18 months', 18)
  , ('Marine', 'Logistics (Cargo)', 'Per risk', 35000000, 0.125, '18 months', 18)
  , ('Marine', 'Logistics (Liabs)', 'Per risk', 35000000, 0.125, '18 months', 18)
  , ('Aviation & Space', 'Airline Hull + Liabs Combined', 'Per risk', 35000000, 0.125, '18 months', 18)
  , ('Aviation & Space', 'Third Party War Liabilities (AVN52) - Aerospace', 'Per risk', 20000000, 0.125, '18 months', 18)
  , ('Aviation & Space', 'Third Party War Liabilities (AVN52) - Airline', 'Per risk', 20000000, 0.125, '18 months', 18)
  , ('Aviation & Space', 'Third Party War Liabilities (AVN52) - General Aviation', 'Per risk', 20000000, 0.125, '18 months', 18)
  , ('Aviation & Space', 'War Hull –  Aerospace', 'Per risk', 20000000, 0.125, '18 months', 18)
  , ('Aviation & Space', 'War Hull –  General Aviation', 'Per risk', 20000000, 0.125, '18 months', 18)
  , ('Aviation & Space', 'War Hull –  Airline', 'Per risk', 20000000, 0.125, '18 months', 18)
  , ('Aviation & Space', 'General Aviation Hull and Liability', 'Hull and Liability combined', 35000000, 0.125, '18 months', 18)
  , ('Aviation & Space', 'Crew Liability', 'Per risk', 20000000, 0.125, '18 months', 18)
  , ('Aviation & Space', 'Space - Orbit', 'Per risk', 35000000, 0.125, '18 months', 18)
  , ('Aviation & Space', 'Space - Launch', 'Per risk', 10000000, 0.125, '24 months, launch must be within policy period', 24)
  , ('Financial & Political Risks', 'Active Assailant', 'Per risk', 20000000, 0.125, '18 months', 18)
  , ('Financial & Political Risks', 'Contingency', 'Per risk', 20000000, 0.125, '18 months', 18)
  , ('Financial & Political Risks', 'Political Risk (CF)', 'Per risk', 20000000, 0.125, '60 months', 60)
  , ('Financial & Political Risks', 'Political Risk (CR)', 'Per risk', 20000000, 0.125, '60 months', 60)
  , ('Financial & Political Risks', 'Political Risk (PR) ex Political Violence', 'Per risk', 20000000, 0.125, '60 months', 60)
  , ('Financial & Political Risks', 'Political Risk (CEND - inc Political Violence)', 'Per risk', 20000000, 0.125, '36 months', 36)
  , ('Financial & Political Risks', 'Full Political Violence (PV)', 'Per risk', 35000000, 0.125, '18 months', 18)
  , ('Financial & Political Risks', 'S&T (Sabotage & Terrorism)', 'Per risk', 35000000, 0.125, '18 months', 18)
  , ('Financial & Political Risks', 'SRCCMD (Strikes, Riots, Civil Commotion and/or Malicious Damage)', 'Per risk', 35000000, 0.125, '18 months', 18)
  , ('Financial & Political Risks', 'Terrorism Liability', 'Per risk', 35000000, 0.125, '18 months', 18)
  , ('Financial & Political Risks', 'Trade Credit', 'Per risk', 20000000, 0.125, '36 months', 36);

DROP TABLE IF EXISTS #GeminiProductMapping;

CREATE TABLE #GeminiProductMapping (
    ProductId            INT
  , DataSourceInstanceId INT
  , ClassOfBusiness      NVARCHAR(50)
  , SubClass             NVARCHAR(200)
);

INSERT INTO #GeminiProductMapping
    (ProductId, DataSourceInstanceId, ClassOfBusiness, SubClass)
VALUES
    (320001, 50355, 'Casualty / Liability', 'Healthcare')
  , (320002, 50355, 'Casualty / Liability', 'Healthcare')
  , (320015, 50355, 'Aviation & Space', 'Space - Orbit')
  , (320018, 50355, 'Casualty / Liability', 'Environmental')
  , (320021, 50355, 'Casualty / Liability', 'Environmental')
  , (320024, 50355, 'Casualty / Liability', 'Environmental')
  , (320026, 50355, 'Casualty / Liability', 'Environmental')
  , (320036, 50355, 'Financial & Political Risks', 'Trade Credit')
  , (320040, 50355, 'Casualty / Liability', 'Casualty')
  , (320041, 50355, 'Casualty / Liability', 'Casualty')
  , (320042, 50355, 'Casualty / Liability', 'Casualty')
  , (320043, 50355, 'Casualty / Liability', 'Casualty')
  , (320045, 50355, 'Construction', 'Construction (line part of ECV)')
  , (320047, 50355, 'FINEX', 'Cyber')
  , (320048, 50355, 'Natural Resources', 'Renewables')
  , (320049, 50355, 'Natural Resources', 'Downstream Energy')
  , (320053, 50355, 'Natural Resources', 'Downstream Energy')
  , (320061, 50355, 'FINEX', 'Crime / Fidelity')
  , (320064, 50355, 'Marine', 'Marine Liabs')
  , (320065, 50355, 'Property', 'International Property')
  , (320066, 50355, 'Marine', 'Hull')
  , (320069, 50355, 'Financial & Political Risks', 'Terrorism Liability')
  , (320072, 50355, 'Financial & Political Risks', 'Political Risk (CF)')
  , (320075, 50355, 'Property', 'International Property')
  , (320077, 50355, 'Casualty / Liability', 'Casualty')
  , (320092, 50355, 'Property', 'International Property')
  , (320094, 50355, 'Property', 'International Property')
  , (320096, 50355, 'Property', 'International Property')
  , (320100, 50355, 'Property', 'International Property')
  , (320101, 50355, 'Property', 'International Property')
  , (320103, 50355, 'Marine', 'Fine Art & Specie')
  , (320104, 50355, 'Casualty / Liability', 'Healthcare')
  , (320106, 50355, 'Casualty / Liability', 'Healthcare')
  , (330001, 50355, 'Casualty / Liability', 'Healthcare')
  , (330002, 50355, 'FINEX', 'Professional Indemnity')
  , (330003, 50355, 'Property', 'International Property')
  , (330008, 50355, 'Aviation & Space', 'Third Party War Liabilities (AVN52) - Aerospace')
  , (330012, 50355, 'Aviation & Space', 'Third Party War Liabilities (AVN52) - Airline')
  , (330013, 50355, 'Aviation & Space', 'Airline Hull + Liabs Combined')
  , (330014, 50355, 'Aviation & Space', 'War Hull –  Airline')
  , (330015, 50355, 'Aviation & Space', 'General Aviation Hull and Liability')
  , (330016, 50355, 'Aviation & Space', 'Airline Hull + Liabs Combined')
  , (330021, 50355, 'FINEX', 'Professional Indemnity')
  , (330023, 50355, 'Aviation & Space', 'Airline Hull + Liabs Combined')
  , (330024, 50355, 'Aviation & Space', 'War Hull –  General Aviation')
  , (330025, 50355, 'Aviation & Space', 'General Aviation Hull and Liability')
  , (330026, 50355, 'Aviation & Space', 'General Aviation Hull and Liability')
  , (330032, 50355, 'FINEX', 'Professional Indemnity')
  , (330033, 50355, 'Property', 'International Property')
  , (330036, 50355, 'Property', 'International Property')
  , (330041, 50355, 'Casualty / Liability', 'Casualty')
  , (330042, 50355, 'Casualty / Liability', 'Casualty')
  , (330050, 50355, 'FINEX', 'Crime / Fidelity')
  , (330054, 50355, 'Casualty / Liability', 'Construction')
  , (330055, 50355, 'Casualty / Liability', 'Construction')
  , (330056, 50355, 'Construction', 'Construction (line part of ECV)')
  , (330057, 50355, 'Construction', 'Construction (line part of ECV)')
  , (330075, 50355, 'FINEX', 'Cyber')
  , (330076, 50355, 'FINEX', 'Cyber')
  , (330080, 50355, 'Property', 'International Property')
  , (330081, 50355, 'FINEX', 'D&O - Liabilities excl Financial Institutions')
  , (330083, 50355, 'Natural Resources', 'Downstream Energy')
  , (330084, 50355, 'Natural Resources', 'Downstream Energy')
  , (330085, 50355, 'Natural Resources', 'Downstream Energy')
  , (330089, 50355, 'FINEX', 'Professional Indemnity')
  , (330094, 50355, 'Casualty / Liability', 'Environmental')
  , (330095, 50355, 'Casualty / Liability', 'Environmental')
  , (330096, 50355, 'Casualty / Liability', 'Environmental')
  , (330097, 50355, 'Casualty / Liability', 'Environmental')
  , (330108, 50355, 'FINEX', 'FI Placement - FIPI (F2 or F3) & / or  Crime (BB) & / or D&O (D4 or D5)  ')
  , (330113, 50355, 'FINEX', 'Professional Indemnity')
  , (330116, 50355, 'Marine', 'Fine Art & Specie')
  , (330117, 50355, 'Property', 'International Property')
  , (330123, 50355, 'Marine', 'Cargo')
  , (330125, 50355, 'Property', 'International Property')
  , (330127, 50355, 'Casualty / Liability', 'Healthcare')
  , (330129, 50355, 'Casualty / Liability', 'Healthcare')
  , (330148, 50355, 'Casualty / Liability', 'Healthcare')
  , (330153, 50355, 'Marine', 'Hull')
  , (330163, 50355, 'Aviation & Space', 'Space - Orbit')
  , (330165, 50355, 'Property', 'International Property')
  , (330169, 50355, 'Aviation & Space', 'Space - Launch')
  , (330170, 50355, 'Aviation & Space', 'Space - Launch')
  , (330171, 50355, 'FINEX', 'Professional Indemnity')
  , (330181, 50355, 'Marine', 'Hull')
  , (330182, 50355, 'Marine', 'War')
  , (330183, 50355, 'Marine', 'Marine Liabs')
  , (330185, 50355, 'FINEX', 'Professional Indemnity')
  , (330186, 50355, 'Casualty / Liability', 'Healthcare')
  , (330187, 50355, 'Natural Resources', 'Midstream Energy')
  , (330188, 50355, 'Natural Resources', 'Midstream Energy')
  , (330195, 50355, 'Marine', 'Cargo')
  , (330202, 50355, 'Natural Resources', 'Offshore Energy - Construction')
  , (330227, 50355, 'Financial & Political Risks', 'Political Risk (CF)')
  , (330228, 50355, 'Marine', 'Ports & Terminals Liabs')
  , (330232, 50355, 'Casualty / Liability', 'Product Recall')
  , (330236, 50355, 'Property', 'International Property')
  , (330237, 50355, 'Property', 'International Property')
  , (330238, 50355, 'FINEX', 'Public Offering of Securities Insurance / Transaction Liability')
  , (330240, 50355, 'FINEX', 'Professional Indemnity')
  , (330244, 50355, 'Natural Resources', 'Renewables Construction')
  , (330251, 50355, 'FINEX', 'D&O - Liabilities excl Financial Institutions')
  , (330261, 50355, 'FINEX', 'Cyber')
  , (330264, 50355, 'Financial & Political Risks', 'Terrorism Liability')
  , (330266, 50355, 'Financial & Political Risks', 'Trade Credit')
  , (330270, 50355, 'Natural Resources', 'Upstream Energy')
  , (330271, 50355, 'Natural Resources', 'Upstream Energy')
  , (330272, 50355, 'Natural Resources', 'Upstream Energy')
  , (330283, 50355, 'FINEX', 'Warranty & Indemnity')
  , (330288, 50355, 'Marine', 'Hull')
  , (330295, 50355, 'Casualty / Liability', 'Casualty')
  , (330296, 50355, 'Casualty / Liability', 'Casualty')
  , (330297, 50355, 'Construction', 'Construction (line part of ECV)')
  , (330298, 50355, 'Casualty / Liability', 'Construction')
  , (330299, 50355, 'Construction', 'Construction (line part of ECV)')
  , (330300, 50355, 'Casualty / Liability', 'Environmental')
  , (330304, 50355, 'Marine', 'Cargo')
  , (330309, 50355, 'Property', 'International Property')
  , (330316, 50355, 'FINEX', 'Cyber')
  , (330334, 50355, 'Financial & Political Risks', 'Terrorism Liability')
  , (330340, 50355, 'Aviation & Space', 'General Aviation Hull and Liability')
  , (330341, 50355, 'Aviation & Space', 'General Aviation Hull and Liability')
  , (330342, 50355, 'Aviation & Space', 'General Aviation Hull and Liability')
  , (330343, 50355, 'Aviation & Space', 'General Aviation Hull and Liability')
  , (330344, 50355, 'Aviation & Space', 'General Aviation Hull and Liability')
  , (330345, 50355, 'Aviation & Space', 'General Aviation Hull and Liability')
  , (330346, 50355, 'Aviation & Space', 'General Aviation Hull and Liability')
  , (330347, 50355, 'Aviation & Space', 'General Aviation Hull and Liability')
  , (330349, 50355, 'Aviation & Space', 'General Aviation Hull and Liability')
  , (330350, 50355, 'Aviation & Space', 'General Aviation Hull and Liability')
  , (330351, 50355, 'Aviation & Space', 'Airline Hull + Liabs Combined')
  , (330352, 50355, 'Aviation & Space', 'Airline Hull + Liabs Combined')
  , (330353, 50355, 'Aviation & Space', 'General Aviation Hull and Liability')
  , (330354, 50355, 'Aviation & Space', 'General Aviation Hull and Liability')
  , (330355, 50355, 'Aviation & Space', 'General Aviation Hull and Liability')
  , (330356, 50355, 'Aviation & Space', 'General Aviation Hull and Liability')
  , (330357, 50355, 'Aviation & Space', 'General Aviation Hull and Liability')
  , (330358, 50355, 'Aviation & Space', 'General Aviation Hull and Liability')
  , (330359, 50355, 'Aviation & Space', 'General Aviation Hull and Liability')
  , (330360, 50355, 'Aviation & Space', 'General Aviation Hull and Liability')
  , (330362, 50355, 'Aviation & Space', 'Third Party War Liabilities (AVN52) - Airline')
  , (330367, 50355, 'FINEX', 'FI Placement - FIPI (F2 or F3) & / or  Crime (BB) & / or D&O (D4 or D5)  ')
  , (330368, 50355, 'Property', 'International Property')
  , (330369, 50355, 'Aviation & Space', 'Airline Hull + Liabs Combined')
  , (330378, 50355, 'Property', 'International Property')
  , (330379, 50355, 'Aviation & Space', 'Airline Hull + Liabs Combined')
  , (330380, 50355, 'Aviation & Space', 'Airline Hull + Liabs Combined')
  , (330381, 50355, 'Aviation & Space', 'Airline Hull + Liabs Combined')
  , (330382, 50355, 'Aviation & Space', 'War Hull –  Aerospace')
  , (330383, 50355, 'Aviation & Space', 'Airline Hull + Liabs Combined')
  , (330391, 50355, 'Aviation & Space', 'Airline Hull + Liabs Combined')
  , (330392, 50355, 'Aviation & Space', 'Airline Hull + Liabs Combined')
  , (330393, 50355, 'Aviation & Space', 'War Hull –  Aerospace')
  , (330395, 50355, 'Aviation & Space', 'Third Party War Liabilities (AVN52) - Aerospace')
  , (330400, 50355, 'Property', 'International Property')
  , (330402, 50355, 'Property', 'International Property')
  , (330404, 50355, 'Natural Resources', 'Power - Operational')
  , (330405, 50355, 'Natural Resources', 'Renewables')
  , (330406, 50355, 'Natural Resources', 'Renewables - TPL')
  , (330407, 50355, 'Natural Resources', 'Energy Liabilities');

DROP TABLE IF EXISTS #FMATemp_MaxPolicyPeriod;

CREATE TABLE #FMATemp_MaxPolicyPeriod (
    Id                      INT          NOT NULL
  , AutoFollowKey           VARCHAR(100) NOT NULL
  , ProductId               INT          NOT NULL
  , MaxPolicyPeriodInMonths INT          NOT NULL
  , IsDeprecated            BIT          NOT NULL
);

INSERT INTO #FMATemp_MaxPolicyPeriod
    (Id, AutoFollowKey, ProductId, MaxPolicyPeriodInMonths, IsDeprecated)
SELECT Id = ROW_NUMBER() OVER (ORDER BY rph.ProductId), AutoFollowKey = 'Gemini', rph.ProductId, gp.MaxPolicyPeriodInMonths, IsDeprecated = 0
FROM
    #GeminiProductMapping gpm
    INNER JOIN #GeminiProduct gp
        ON gp.ClassOfBusiness = gpm.ClassOfBusiness
           AND gp.SubClass = gpm.SubClass

    INNER JOIN rpt.ProductHierarchy rph
        ON rph.ProductKey = CAST(gpm.ProductId AS NVARCHAR(50))
           AND rph.DataSourceInstanceId = gpm.DataSourceInstanceId;

IF EXISTS (SELECT * FROM #FMATemp_MaxPolicyPeriod)
BEGIN
    BEGIN TRY
        MERGE FMATemp.staging_vw_MaxPolicyPeriod t
        USING
            (SELECT Id, AutoFollowKey, ProductId, MaxPolicyPeriodInMonths, IsDeprecated FROM #FMATemp_MaxPolicyPeriod) s
        ON t.Id = s.Id
        WHEN NOT MATCHED
            THEN INSERT
                     (Id, AutoFollowKey, ProductId, MaxPolicyPeriodInMonths, IsDeprecated)
                 VALUES
                     (s.Id, s.AutoFollowKey, s.ProductId, s.MaxPolicyPeriodInMonths, s.IsDeprecated)
        WHEN MATCHED AND NOT EXISTS
    (SELECT t.AutoFollowKey, t.ProductId, t.MaxPolicyPeriodInMonths, t.IsDeprecated
     INTERSECT
     SELECT s.AutoFollowKey, s.ProductId, s.MaxPolicyPeriodInMonths, s.IsDeprecated)
            THEN UPDATE SET t.AutoFollowKey = s.AutoFollowKey, t.ProductId = s.ProductId, t.MaxPolicyPeriodInMonths = s.MaxPolicyPeriodInMonths, t.IsDeprecated = s.IsDeprecated
        WHEN NOT MATCHED BY SOURCE AND t.IsDeprecated = 0
            THEN UPDATE SET t.IsDeprecated = 1
        OUTPUT $ACTION
        INTO @Actions;

        SELECT @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                             THEN 1
                                         ELSE 0 END), @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                                                                   THEN 1
                                                                               ELSE 0 END), @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                                                                                                         THEN 1
                                                                                                                     ELSE 0 END)
        FROM
            @Actions;
    END TRY
    BEGIN CATCH
        DECLARE @ErrorMessage NVARCHAR(MAX);

        SET @ErrorMessage = ERROR_MESSAGE();

        EXEC ADF.StoredProcErrorLog @SprocName, @ErrorMessage;

        SET @RejectedCount = 1;
    END CATCH;
END;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog @SprocName, @InsertedCount, @UpdatedCount, @DeletedCount, @RejectedCount, @Action, NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT InsertedCount = ISNULL(@InsertedCount, 0), UpdatedCount = ISNULL(@UpdatedCount, 0), DeletedCount = ISNULL(@DeletedCount, 0), RejectedCount = ISNULL(@RejectedCount, 0);
