/*
    This script populates the MarineMar tables.
    It also handle mapping to between MarineMar references 
        MMS ID in spreadsheet, table primary key in tables.
    Where possible it tries to show what each table is linked to.
    Currently this script only inserts or updates values. It does not delete anything from the tables.
    See spreadsheet "MMS BP Ref Data Mapping".
*/

PRINT 'In Script.PostDeployment_PopulateSpecieMarData (for SpecieMar)';

/* Load [ProductClassScope]-ClassofBusiness data        */
/* Related to MMS Risk Code to group into product Class */
DROP TABLE IF EXISTS #RefProductClass;
GO

CREATE TABLE #RefProductClass (
    ProductClassId INT           NOT NULL
  , ProductClass   NVARCHAR(250) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL
  , Scope          BIT           NOT NULL
        DEFAULT 1
  , StartDate      DATE          NOT NULL
  , EndDate        DATE          NULL
  , IsDeleted      BIT           NOT NULL
        DEFAULT (0),
);

INSERT INTO #RefProductClass
    (ProductClassId, ProductClass, Scope, StartDate, EndDate, IsDeleted)
VALUES
    (1, 'Fine Art', 1, '2015-01-01', NULL, 0)
  , (2, 'Jewellers Block', 1, '2015-01-01', NULL, 0)
  , (3, 'Cash In Transit', 1, '2015-01-01', NULL, 0)
  , (4, 'General Specie', 1, '2015-01-01', NULL, 0);

MERGE INTO MarineMar.SpecieRefProductClass T
USING #RefProductClass S
ON T.ProductClassId = S.ProductClassId
WHEN NOT MATCHED BY TARGET
    THEN INSERT
             (ProductClassId, ProductClass, Scope, StartDate, EndDate, IsDeleted)
         VALUES
             (S.ProductClassId, S.ProductClass, S.Scope, S.StartDate, S.EndDate, S.IsDeleted)
WHEN MATCHED AND NOT EXISTS
                         (SELECT T.ProductClassId, T.ProductClass, T.Scope, T.StartDate, T.EndDate, T.IsDeleted
                          INTERSECT
                          SELECT S.ProductClassId, S.ProductClass, S.Scope, S.StartDate, S.EndDate, S.IsDeleted)
    THEN UPDATE SET T.ProductClassId = S.ProductClassId, T.ProductClass = S.ProductClass, T.Scope = S.Scope, T.StartDate = S.StartDate, T.EndDate = S.EndDate, T.ETLUpdatedDate = GETUTCDATE(), T.IsDeleted = S.IsDeleted
WHEN NOT MATCHED BY SOURCE
    THEN UPDATE SET T.ETLUpdatedDate = GETUTCDATE(), T.IsDeleted = 1;

/* Risk Code */
DROP TABLE IF EXISTS #RefRiskCode;
GO

CREATE TABLE #RefRiskCode (
    RiskCodeId     INT           NOT NULL PRIMARY KEY
  , RiskCodeName   NVARCHAR(200) COLLATE SQL_Latin1_General_CP1_CI_AS
  , RiskCodeDesc   INT
  , ProductClassId INT           NOT NULL
  , IsDeleted      BIT           NULL
        DEFAULT 0
);

INSERT INTO #RefRiskCode
    (RiskCodeId, RiskCodeName, RiskCodeDesc, ProductClassId, IsDeleted)
VALUES
    (92, '1. Auction House', NULL, 1, 0)
  , (93, '2. Car Collection', NULL, 1, 0)
  , (94, '3. Collateralised Art', NULL, 1, 0)
  , (95, '4. Corporate Collection', NULL, 1, 0)
  , (96, '5. Dealers', NULL, 1, 0)
  , (97, '6. Exhibition', NULL, 1, 0)
  , (98, '7. Gallery', NULL, 1, 0)
  , (99, '8. Museum', NULL, 1, 0)
  , (100, '9. Private Collection', NULL, 1, 0)
  , (101, '10. Public Entity', NULL, 1, 0)
  , (102, '11. Shipper & Packer', NULL, 1, 0)
  , (103, '12. Wine Collections', NULL, 1, 0)
  , (104, '13. Digital Artwork', NULL, 1, 0)
  , (105, '14. Antique & Library Collections', NULL, 1, 0)
  , (106, '15. Living Artist Cover', NULL, 1, 0)
  , (107, '16. Reinsurance of Captives', NULL, 1, 0)
  , (108, '17. Conservation / Restoration Costs', NULL, 1, 0)
  , (109, '18. Defective Titles  (and or defence costs)', NULL, 1, 0)
  , (110, '19. Employee Liability', NULL, 1, 0)
  , (111, '20. All Risks', NULL, 1, 0)
  , (112, '21. Legal Liability', NULL, 1, 0)
  , (113, '1. Personal Jewellery', NULL, 2, 0)
  , (114, '2. Watch Dealers / Retailers', NULL, 2, 0)
  , (115, '3. Jewellery Retailer', NULL, 2, 0)
  , (116, '4. Jewellery Wholesaler', NULL, 2, 0)
  , (117, '5. Jewellery Manufacturer', NULL, 2, 0)
  , (118, '6. Diamond Processing', NULL, 2, 0)
  , (119, '7. Rough Stone & Metals from Mines', NULL, 2, 0)
  , (120, '8. Exhibitions and Events', NULL, 2, 0)
  , (121, '9. Third Party Insolvency', NULL, 2, 0)
  , (122, '10. Third Party Dishonesty', NULL, 2, 0)
  , (123, '1. ATM', NULL, 3, 0)
  , (124, '2. Cash Management Company', NULL, 3, 0)
  , (125, '3. Cheque Cashiers', NULL, 3, 0)
  , (126, '4. Precious Metals', NULL, 3, 0)
  , (127, '5. Vault', NULL, 3, 0)
  , (128, '6. Electronic Funds Transfer', NULL, 3, 0)
  , (129, '7. Foreign Exchange Bureaux', NULL, 3, 0)
  , (130, '8. Bitcoin', NULL, 3, 0)
  , (131, '1. Diamond Processor', NULL, 4, 0)
  , (132, '2. Metal Refinery', NULL, 4, 0)
  , (133, '3. Mining', NULL, 4, 0)
  , (134, '4. Precious Metals', NULL, 4, 0)
  , (135, '5. Precious Metal Dealers', NULL, 4, 0)
  , (136, '6. Movement of Precious Metals', NULL, 4, 0)
  , (137, '7. Storage of Precious Metals', NULL, 4, 0)
  , (138, '8. Safety Deposit Facilities', NULL, 4, 0)
  , (139, '9. Numismatics', NULL, 4, 0)
  , (140, '10. Cash In Vault', NULL, 4, 0)
  , (141, '22. Quake', NULL, 1, 0);

MERGE INTO MarineMar.SpecieRefRiskCode T
USING #RefRiskCode S
ON T.RiskCodeId = S.RiskCodeId
WHEN NOT MATCHED BY TARGET
    THEN INSERT
             (RiskCodeId, RiskCodeName, RiskCodeDesc, ProductClassId, IsDeleted)
         VALUES
             (S.RiskCodeId, S.RiskCodeName, S.RiskCodeDesc, S.ProductClassId, S.IsDeleted)
WHEN MATCHED AND NOT EXISTS
                         (SELECT T.RiskCodeId, T.RiskCodeName, T.RiskCodeDesc, T.ProductClassId, T.IsDeleted
                          INTERSECT
                          SELECT S.RiskCodeId, S.RiskCodeName, S.RiskCodeDesc, S.ProductClassId, S.IsDeleted)
    THEN UPDATE SET T.RiskCodeId = S.RiskCodeId, T.RiskCodeName = S.RiskCodeName, T.RiskCodeDesc = S.RiskCodeDesc, T.ProductClassId = S.ProductClassId, T.ETLUpdatedDate = GETUTCDATE(), T.IsDeleted = S.IsDeleted
WHEN NOT MATCHED BY SOURCE
    THEN UPDATE SET T.ETLUpdatedDate = GETUTCDATE(), T.IsDeleted = 1;

/* RefCountries */
DROP TABLE IF EXISTS #RefCountries;
GO

CREATE TABLE #RefCountries (
    CountryId INT           NOT NULL PRIMARY KEY
  , CountryUK NVARCHAR(200) COLLATE SQL_Latin1_General_CP1_CI_AS
  , GeoUK     NVARCHAR(200) COLLATE SQL_Latin1_General_CP1_CI_AS
  , ISOCode   NVARCHAR(3)   COLLATE SQL_Latin1_General_CP1_CI_AS
  , IsDeleted BIT           NULL
        DEFAULT 0
);

INSERT INTO #RefCountries
    (CountryId, CountryUK, GeoUK, ISOCode, IsDeleted)
VALUES
    (1, 'United Kingdom', 'United Kingdom', 'GB', 0)
  , (2, 'United States', 'North America', 'US', 0)
  , (3, 'Italy', 'Europe', 'IT', 0)
  , (4, 'Spain', 'Europe', 'ES', 0)
  , (5, 'Switzerland', 'Europe', 'CH', 0)
  , (6, 'Afghanistan', 'Asia', 'AF', 0)
  , (7, 'Albania', 'Europe', 'AL', 0)
  , (8, 'Algeria', 'Africa', 'DZ', 0)
  , (9, 'American Samoa', 'Oceania', 'AS', 0)
  , (10, 'Andorra', 'Europe', 'AD', 0)
  , (11, 'Angola', 'Africa', 'AO', 0)
  , (12, 'Anguilla', 'Caribbean', 'AI', 0)
  , (13, 'Antigua and Barbuda', 'Caribbean', 'AG', 0)
  , (14, 'Argentina', 'Central and South America', 'AR', 0)
  , (15, 'Armenia', 'Europe', 'AM', 0)
  , (16, 'Aruba', 'Caribbean', 'AW', 0)
  , (17, 'Australia', 'Oceania', 'AU', 0)
  , (18, 'Austria', 'Europe', 'AT', 0)
  , (19, 'Azerbaijan', 'Europe', 'AZ', 0)
  , (20, 'Azores', 'Africa', 'AZS', 0)
  , (21, 'Bahamas', 'Caribbean', 'BS', 0)
  , (22, 'Bahrain', 'Middle East', 'BH', 0)
  , (23, 'Bangladesh', 'Asia', 'BD', 0)
  , (24, 'Barbados', 'Caribbean', 'BB', 0)
  , (25, 'Belarus', 'Europe', 'BY', 0)
  , (26, 'Belgium', 'Europe', 'BE', 0)
  , (27, 'Belize', 'Central and South America', 'BZ', 0)
  , (28, 'Benin', 'Africa', 'BJ', 0)
  , (29, 'Bermuda', 'North America', 'BM', 0)
  , (30, 'Bhutan', 'Asia', 'BT', 0)
  , (31, 'Bolivia', 'Central and South America', 'BO', 0)
  , (32, 'Bosnia and Herzegovina', 'Europe', 'BA', 0)
  , (33, 'Botswana', 'Africa', 'BW', 0)
  , (34, 'Brazil', 'Central and South America', 'BR', 0)
  , (35, 'British Indian Ocean Territory', 'Africa', 'IO', 0)
  , (36, 'British Virgin Islands', 'Caribbean', 'VG', 0)
  , (37, 'Brunei Darussalam', 'Asia', 'BN', 0)
  , (38, 'Bulgaria', 'Europe', 'BG', 0)
  , (39, 'Burkina Faso', 'Africa', 'BF', 0)
  , (40, 'Burundi', 'Africa', 'BI', 0)
  , (41, 'Cambodia', 'Asia', 'KH', 0)
  , (42, 'Cameroon', 'Africa', 'CM', 0)
  , (43, 'Canada', 'North America', 'CA', 0)
  , (44, 'Cape Verde', 'Africa', 'CV', 0)
  , (45, 'Cayman Islands', 'Caribbean', 'KY', 0)
  , (46, 'Central African Republic', 'Africa', 'CF', 0)
  , (47, 'Chad', 'Africa', 'TD', 0)
  , (48, 'Channel Islands', 'Europe', 'CHA', 0)
  , (49, 'Chile', 'Central and South America', 'CL', 0)
  , (50, 'China', 'Asia', 'CN', 0)
  , (51, 'Christmas Island', 'Oceania', 'CX', 0)
  , (52, 'Cocos (Keeling) Islands', 'Asia', 'CC', 0)
  , (53, 'Colombia', 'Central and South America', 'CO', 0)
  , (54, 'Comoros', 'Africa', 'KM', 0)
  , (55, 'Congo', 'Africa', 'CG', 0)
  , (56, 'Cook Islands', 'Oceania', 'CK', 0)
  , (57, 'Costa Rica', 'Central and South America', 'CR', 0)
  , (58, 'Cote D''Ivoire', 'Africa', 'CI', 0)
  , (59, 'Croatia', 'Europe', 'HR', 0)
  , (60, 'Cuba', 'Caribbean', 'CU', 0)
  , (61, 'Cyprus', 'Europe', 'CY', 0)
  , (62, 'Czech Republic', 'Europe', 'CZ', 0)
  , (63, 'Democratic People''s Republic Of Korea', 'Asia', 'KP', 0)
  , (64, 'Denmark', 'Europe', 'DK', 0)
  , (65, 'Djibouti', 'Africa', 'DJ', 0)
  , (66, 'Dominica', 'Central and South America', 'DM', 0)
  , (67, 'Dominican Republic', 'Caribbean', 'DO', 0)
  , (68, 'Ecuador', 'Central and South America', 'EC', 0)
  , (69, 'Egypt', 'Africa', 'EG', 0)
  , (70, 'El Salvador', 'Central and South America', 'SV', 0)
  , (71, 'Equatorial Guinea', 'Africa', 'GQ', 0)
  , (72, 'Eritrea', 'Africa', 'ER', 0)
  , (73, 'Estonia', 'Europe', 'EE', 0)
  , (74, 'Ethiopia', 'Africa', 'ET', 0)
  , (75, 'Falkland Islands (Malvinas)', 'Central and South America', 'FK', 0)
  , (76, 'Faroe Islands', 'Europe', 'FO', 0)
  , (77, 'Fiji', 'Oceania', 'FJ', 0)
  , (78, 'Finland', 'Europe', 'FI', 0)
  , (79, 'France', 'Europe', 'FR', 0)
  , (80, 'French Guiana', 'Central and South America', 'GF', 0)
  , (81, 'French Polynesia', 'Oceania', 'PF', 0)
  , (82, 'Gabon', 'Africa', 'GA', 0)
  , (83, 'Gambia', 'Africa', 'GM', 0)
  , (84, 'Georgia', 'Europe', 'GE', 0)
  , (85, 'Germany', 'Europe', 'DE', 0)
  , (86, 'Ghana', 'Africa', 'GH', 0)
  , (87, 'Gibraltar', 'Europe', 'GI', 0)
  , (88, 'Greece', 'Europe', 'GR', 0)
  , (89, 'Greenland', 'Europe', 'GL', 0)
  , (90, 'Grenada', 'Caribbean', 'GD', 0)
  , (91, 'Guadeloupe', 'Central and South America', 'GP', 0)
  , (92, 'Guam', 'Asia', 'GU', 0)
  , (93, 'Guatemala', 'Central and South America', 'GT', 0)
  , (94, 'Guinea', 'Africa', 'GN', 0)
  , (95, 'Guinea-Bissau', 'Africa', 'GW', 0)
  , (96, 'Guyana', 'Central and South America', 'GY', 0)
  , (97, 'Haiti', 'Caribbean', 'HT', 0)
  , (98, 'Holy See (Vatican City State)', 'Europe', 'VA', 0)
  , (99, 'Honduras', 'Central and South America', 'HN', 0)
  , (100, 'Hong Kong', 'Asia', 'HK', 0)
  , (101, 'Hungary', 'Europe', 'HU', 0)
  , (102, 'Iceland', 'Europe', 'IS', 0)
  , (103, 'India', 'Asia', 'IN', 0)
  , (104, 'Indonesia', 'Asia', 'ID', 0)
  , (105, 'Iran', 'Middle East', 'IR', 0)
  , (106, 'Iraq', 'Middle East', 'IQ', 0)
  , (107, 'Isle of Man', 'Europe', 'IM', 0)
  , (108, 'Israel', 'Middle East', 'IL', 0)
  , (109, 'Jamaica', 'Caribbean', 'JM', 0)
  , (110, 'Japan', 'Asia', 'JP', 0)
  , (111, 'Jordan', 'Middle East', 'JO', 0)
  , (112, 'Kazakhstan', 'Europe', 'KZ', 0)
  , (113, 'Kenya', 'Africa', 'KE', 0)
  , (114, 'Kiribati', 'Oceania', 'KI', 0)
  , (115, 'Kuwait', 'Middle East', 'KW', 0)
  , (116, 'Kyrgyzstan', 'Asia', 'KG', 0)
  , (117, 'Lao People''s Democratic Republic', 'Asia', 'LA', 0)
  , (118, 'Latvia', 'Europe', 'LV', 0)
  , (119, 'Lebanon', 'Middle East', 'LB', 0)
  , (120, 'Lesotho', 'Africa', 'LS', 0)
  , (121, 'Liberia', 'Africa', 'LR', 0)
  , (122, 'Libyan Arab Jamahiriya', 'Africa', 'LY', 0)
  , (123, 'Liechtenstein', 'Europe', 'LI', 0)
  , (124, 'Lithuania', 'Europe', 'LT', 0)
  , (125, 'Luxembourg', 'Europe', 'LU', 0)
  , (126, 'Macao', 'Asia', 'MO', 0)
  , (127, 'Macedonia', 'Europe', 'MK', 0)
  , (128, 'Madagascar', 'Africa', 'MG', 0)
  , (129, 'Malawi', 'Africa', 'MW', 0)
  , (130, 'Malaysia', 'Asia', 'MY', 0)
  , (131, 'Maldives', 'Asia', 'MV', 0)
  , (132, 'Mali', 'Africa', 'ML', 0)
  , (133, 'Malta', 'Europe', 'MT', 0)
  , (134, 'Marshall Islands', 'Asia', 'MH', 0)
  , (135, 'Martinique', 'Central and South America', 'MQ', 0)
  , (136, 'Mauritania', 'Africa', 'MR', 0)
  , (137, 'Mauritius', 'Africa', 'MU', 0)
  , (138, 'Mexico', 'Central and South America', 'MX', 0)
  , (139, 'Micronesia', 'Oceania', 'FM', 0)
  , (140, 'Moldova', 'Europe', 'MD', 0)
  , (141, 'Monaco', 'Europe', 'MC', 0)
  , (142, 'Mongolia', 'Asia', 'MN', 0)
  , (143, 'Montenegro', 'Europe', 'ME', 0)
  , (144, 'Montserrat', 'Caribbean', 'MS', 0)
  , (145, 'Morocco', 'Africa', 'MA', 0)
  , (146, 'Mozambique', 'Africa', 'MZ', 0)
  , (147, 'Myanmar', 'Asia', 'MM', 0)
  , (148, 'Namibia', 'Africa', 'NA', 0)
  , (149, 'Nauru', 'Oceania', 'NR', 0)
  , (150, 'Nepal', 'Asia', 'NP', 0)
  , (151, 'Netherlands', 'Europe', 'NL', 0)
  , (152, 'Netherlands Antilles', 'Caribbean', 'AN', 0)
  , (153, 'New Caledonia', 'Oceania', 'NC', 0)
  , (154, 'New Zealand', 'Oceania', 'NZ', 0)
  , (155, 'Nicaragua', 'Central and South America', 'NI', 0)
  , (156, 'Niger', 'Africa', 'NE', 0)
  , (157, 'Nigeria', 'Africa', 'NG', 0)
  , (158, 'Niue', 'Oceania', 'NU', 0)
  , (159, 'Norfolk Island', 'Oceania', 'NF', 0)
  , (160, 'Norway', 'Europe', 'NO', 0)
  , (161, 'Occupied Palestinian Territory', 'Middle East', 'PS', 0)
  , (162, 'Oman', 'Middle East', 'OM', 0)
  , (163, 'Pakistan', 'Asia', 'PK', 0)
  , (164, 'Palau', 'Oceania', 'PW', 0)
  , (165, 'Panama', 'Central and South America', 'PA', 0)
  , (166, 'Papua New Guinea', 'Oceania', 'PG', 0)
  , (167, 'Paraguay', 'Central and South America', 'PY', 0)
  , (168, 'Peru', 'Central and South America', 'PE', 0)
  , (169, 'Philippines', 'Asia', 'PH', 0)
  , (170, 'Pitcairn', 'Caribbean', 'PN', 0)
  , (171, 'Poland', 'Europe', 'PL', 0)
  , (172, 'Portugal', 'Europe', 'PT', 0)
  , (173, 'Puerto Rico', 'Caribbean', 'PR', 0)
  , (174, 'Qatar', 'Middle East', 'QA', 0)
  , (175, 'Republic of Ireland', 'Europe', 'IE', 0)
  , (176, 'Republic Of Korea', 'Asia', 'KR', 0)
  , (177, 'Reunion', 'Africa', 'RE', 0)
  , (178, 'Romania', 'Europe', 'RO', 0)
  , (179, 'Russian Federation', 'Europe', 'RU', 0)
  , (180, 'Rwanda', 'Africa', 'RW', 0)
  , (181, 'Saint Helena', 'Central and South America', 'SH', 0)
  , (182, 'Saint Kitts and Nevis', 'Caribbean', 'KN', 0)
  , (183, 'Saint Lucia', 'Caribbean', 'LC', 0)
  , (184, 'Saint Pierre and Miquelon', 'Caribbean', 'PM', 0)
  , (185, 'Saint Vincent and The Grenadines', 'Caribbean', 'VC', 0)
  , (186, 'Samoa', 'Asia', 'WS', 0)
  , (187, 'San Marino', 'Europe', 'SM', 0)
  , (188, 'Sao Tome and Principe', 'Africa', 'ST', 0)
  , (189, 'Saudi Arabia', 'Middle East', 'SA', 0)
  , (190, 'Senegal', 'Africa', 'SN', 0)
  , (191, 'Serbia', 'Europe', 'RS', 0)
  , (192, 'Seychelles', 'Africa', 'SC', 0)
  , (193, 'Sierra Leone', 'Africa', 'SL', 0)
  , (194, 'Singapore', 'Asia', 'SG', 0)
  , (195, 'Slovakia', 'Europe', 'SK', 0)
  , (196, 'Slovenia', 'Europe', 'SI', 0)
  , (197, 'Solomon Islands', 'Oceania', 'SB', 0)
  , (198, 'Somalia', 'Africa', 'SO', 0)
  , (199, 'South Africa', 'Africa', 'ZA', 0)
  , (200, 'Sri Lanka', 'Asia', 'LK', 0)
  , (201, 'Sudan', 'Middle East', 'SD', 0)
  , (202, 'Suriname', 'Central and South America', 'SR', 0)
  , (203, 'Svalbard and Jan Mayen Islands', 'Europe', 'SJ', 0)
  , (204, 'Swaziland', 'Africa', 'SZ', 0)
  , (205, 'Sweden', 'Europe', 'SE', 0)
  , (206, 'Syrian Arab Republic', 'Middle East', 'SY', 0)
  , (207, 'Taiwan', 'Asia', 'TW', 0)
  , (208, 'Tajikistan', 'Asia', 'TJ', 0)
  , (209, 'Tanzania', 'Africa', 'TZ', 0)
  , (210, 'Thailand', 'Asia', 'TH', 0)
  , (211, 'The Democratic Republic Of The Congo', 'Africa', 'CD', 0)
  , (212, 'Timor-Leste', 'Asia', 'TL', 0)
  , (213, 'Togo', 'Africa', 'TG', 0)
  , (214, 'Tokelau', 'Asia', 'TK', 0)
  , (215, 'Tonga', 'Oceania', 'TO', 0)
  , (216, 'Trinidad and Tobago', 'Caribbean', 'TT', 0)
  , (217, 'Tunisia', 'Africa', 'TN', 0)
  , (218, 'Turkey', 'Europe', 'TR', 0)
  , (219, 'Turkmenistan', 'Asia', 'TM', 0)
  , (220, 'Turks And Caicos Islands', 'Caribbean', 'TC', 0)
  , (221, 'Tuvalu', 'Oceania', 'TV', 0)
  , (222, 'U.S. Virgin Islands', 'North America', 'VI', 0)
  , (223, 'Uganda', 'Africa', 'UG', 0)
  , (224, 'Ukraine', 'Europe', 'UA', 0)
  , (225, 'United Arab Emirates', 'Middle East', 'AE', 0)
  , (226, 'Uruguay', 'Central and South America', 'UY', 0)
  , (227, 'Uzbekistan', 'Asia', 'UZ', 0)
  , (228, 'Vanuatu', 'Oceania', 'VU', 0)
  , (229, 'Venezuela', 'Central and South America', 'VE', 0)
  , (230, 'Vietnam', 'Asia', 'VN', 0)
  , (231, 'Western Sahara', 'Africa', 'EH', 0)
  , (232, 'Yemen', 'Middle East', 'YE', 0)
  , (233, 'Zambia', 'Africa', 'ZM', 0)
  , (234, 'Zimbabwe', 'Africa', 'ZW', 0);

MERGE INTO MarineMar.SpecieRefCountries T
USING #RefCountries S
ON T.CountryId = S.CountryId
WHEN NOT MATCHED BY TARGET
    THEN INSERT
             (CountryId, CountryUK, GeoUK, ISOCode, IsDeleted)
         VALUES
             (S.CountryId, S.CountryUK, S.GeoUK, S.ISOCode, S.IsDeleted)
WHEN MATCHED AND NOT EXISTS
                         (SELECT T.CountryId, T.CountryUK, T.GeoUK, T.ISOCode, T.IsDeleted
                          INTERSECT
                          SELECT S.CountryId, S.CountryUK, S.GeoUK, S.ISOCode, S.IsDeleted)
    THEN UPDATE SET T.CountryId = S.CountryId, T.CountryUK = S.CountryUK, T.GeoUK = S.GeoUK, T.ISOCode = S.ISOCode, T.ETLUpdatedDate = GETUTCDATE(), T.IsDeleted = S.IsDeleted
WHEN NOT MATCHED BY SOURCE
    THEN UPDATE SET T.ETLUpdatedDate = GETUTCDATE(), T.IsDeleted = 1;

/* RefGeneric */
DROP TABLE IF EXISTS #RefGeneric;
GO

CREATE TABLE #RefGeneric (
    FieldId   INT           NOT NULL PRIMARY KEY
  , DescUK    NVARCHAR(200) COLLATE SQL_Latin1_General_CP1_CI_AS
  , RefCol    INT
  , IsDeleted BIT           NULL
        DEFAULT 0
);

INSERT INTO #RefGeneric
    (FieldId, DescUK, RefCol, IsDeleted)
VALUES
    (121, 'January', 2, 0)
  , (122, 'February', 2, 0)
  , (123, 'March', 2, 0)
  , (124, 'April', 2, 0)
  , (125, 'May', 2, 0)
  , (126, 'June', 2, 0)
  , (127, 'July', 2, 0)
  , (128, 'August', 2, 0)
  , (129, 'September', 2, 0)
  , (130, 'October', 2, 0)
  , (131, 'November', 2, 0)
  , (132, 'December', 2, 0)
  , (133, 'Renewal (non tacit)', 3, 1)
  , (134, 'Renewal', 3, 0)
  , (135, 'New', 3, 0)
  , (136, 'Direct', 4, 1)
  , (137, 'Wholesale Willis', 4, 1)
  , (138, 'Wholesale Non Willis', 4, 1)
  , (139, '1. General Cargo Vessels', 5, 0)
  , (140, '2. Oil Tankers', 5, 0)
  , (141, 'Primary', 6, 1)
  , (142, 'Excess', 6, 1)
  , (143, 'Stand Alone', 6, 1)
  , (144, 'I', 7, 0)
  , (145, 'II', 7, 0)
  , (146, 'III', 7, 0)
  , (147, 'I', 8, 0)
  , (148, 'II & III', 8, 0)
  , (149, 'Non-FINMAR360° market', 8, 0)
  , (150, 'Request quote from Insurer', 9, 0)
  , (152, 'Insurer: Appetite Restriction Criteria Met', 9, 0)
  , (153, 'Insurer: Legal, regulatory or licencing restriction', 9, 0)
  , (154, 'Insurer: Other (Details in Comments)', 9, 0)
  , (155, 'Client Not Tendering: Performance of incumbent', 9, 0)
  , (156, 'Client Not Tendering: Claims Loyalty / History', 9, 0)
  , (157, 'Client Not Tendering: Account Now In Run-Off', 9, 0)
  , (158, 'Client Not Tendering: Other (Details in Comments)', 9, 0)
  , (159, 'Client Direction: Perceived Insurer Performance', 9, 0)
  , (160, 'Client Direction: Aggregate Counterparty Exposure', 9, 0)
  , (161, 'Client Direction: Other (Details in Comments)', 9, 0)
  , (162, 'Request quote from Insurer', 10, 0)
  , (164, 'Insurer: Appetite Restriction Criteria Met', 10, 0)
  , (165, 'Insurer: Legal, regulatory or licencing restriction', 10, 0)
  , (166, 'Insurer: Other (Details in Comments)', 10, 0)
  , (167, 'Client Not Tendering: Performance of incumbent', 10, 0)
  , (168, 'Client Not Tendering: Claims Loyalty / History', 10, 0)
  , (169, 'Client Not Tendering: Account Now In Run-Off', 10, 0)
  , (170, 'Client Not Tendering: Other (Details in Comments)', 10, 0)
  , (171, 'Client Direction: Perceived Insurer Performance', 10, 0)
  , (172, 'Client Direction: Aggregate Counterparty Exposure', 10, 0)
  , (173, 'Client Direction: Other (Details in Comments)', 10, 0)
  , (174, 'Broker Decision: Insurer not selected', 10, 0)
  , (175, 'Request quote from Insurer', 11, 0)
  , (177, 'Insurer: Appetite Restriction Criteria Met', 11, 0)
  , (178, 'Insurer: Legal, regulatory or licencing restriction', 11, 0)
  , (179, 'Insurer: Other (Details in Comments)', 11, 0)
  , (180, 'Client Not Tendering: Performance of incumbent', 11, 0)
  , (181, 'Client Not Tendering: Claims Loyalty / History', 11, 0)
  , (182, 'Client Not Tendering: Account Now In Run-Off', 11, 0)
  , (183, 'Client Not Tendering: Other (Details in Comments)', 11, 0)
  , (184, 'Client Direction: Perceived Insurer Performance', 11, 0)
  , (185, 'Client Direction: Aggregate Counterparty Exposure', 11, 0)
  , (186, 'Client Direction: Other (Details in Comments)', 11, 0)
  , (187, 'Request quote from Insurer', 12, 0)
  , (189, 'Insurer: Appetite Restriction Criteria Met', 12, 0)
  , (190, 'Insurer: Legal, regulatory or licencing restriction', 12, 0)
  , (191, 'Insurer: Other (Details in Comments)', 12, 0)
  , (192, 'Client Not Tendering: Performance of incumbent', 12, 0)
  , (193, 'Client Not Tendering: Claims Loyalty / History', 12, 0)
  , (194, 'Client Not Tendering: Account Now In Run-Off', 12, 0)
  , (195, 'Client Not Tendering: Other (Details in Comments)', 12, 0)
  , (196, 'Client Direction: Perceived Insurer Performance', 12, 0)
  , (197, 'Client Direction: Aggregate Counterparty Exposure', 12, 0)
  , (198, 'Client Direction: Other (Details in Comments)', 12, 0)
  , (199, 'Broker Decision: Insurer not selected', 12, 0)
  , (200, 'Yes', 13, 1)
  , (201, 'No - One Off Policy', 13, 1)
  , (202, 'No - Run Off', 13, 1)
  , (203, 'No - Other', 13, 1)
  , (204, 'Unable to meet Quote Deadline', 14, 0)
  , (205, 'Obligation to another Broker', 14, 0)
  , (206, 'Licensing issues', 14, 0)
  , (207, 'Insurer committed to another layer', 14, 0)
  , (208, 'Claims / Loss history', 14, 0)
  , (209, 'Financials/Solvency', 14, 0)
  , (210, 'Activity / Industry', 14, 0)
  , (211, 'Pricing / Program Cost', 14, 0)
  , (212, 'Structure / Retention', 14, 0)
  , (213, 'Coverages / Wordings', 14, 0)
  , (214, 'Limits or capacity', 14, 0)
  , (215, 'Class / Line / Product', 14, 0)
  , (216, 'Risk Location', 14, 0)
  , (217, 'Not enough info for Insurer to quote', 14, 0)
  , (218, 'Insurer wishes to follow only, if required', 14, 0)
  , (219, 'Existing Lines with another Insurer', 15, 0)
  , (220, 'Experience of Claims handling', 15, 0)
  , (221, 'Experience of Service Quality', 15, 0)
  , (222, 'Perceived Claims handling Quality', 15, 1)
  , (223, 'Perceived Service Quality', 15, 1)
  , (224, 'Financial Strength of Insurer', 15, 0)
  , (225, 'Limits or Capacity ', 15, 0)
  , (226, 'Payment terms', 15, 0)
  , (227, 'Policy Conditions / Warranties', 15, 0)
  , (228, 'Pricing / Program Cost', 15, 0)
  , (229, 'Structure / Retention ', 15, 0)
  , (230, 'Sub-Limits', 15, 0)
  , (231, 'Coverages / Wordings', 15, 0)
  , (232, 'Late Receipt of Quote', 15, 0)
  , (233, 'Placed with another broker', 15, 0)
  , (234, 'Product not purchased', 15, 0)
  , (235, '1-5% ', 16, 0)
  , (236, '6-10%', 16, 0)
  , (237, '11-15%', 16, 0)
  , (238, '16%+', 16, 0)
  , (239, 'Unable to meet Quote Deadline', 17, 0)
  , (240, 'Obligation to another Broker', 17, 0)
  , (241, 'Licensing issues', 17, 0)
  , (242, 'Claims / Loss history', 17, 0)
  , (243, 'Financials/Solvency', 17, 0)
  , (244, 'Activity / Industry', 17, 0)
  , (245, 'Pricing / Program Cost', 17, 0)
  , (246, 'Structure / Retention', 17, 0)
  , (247, 'Coverages / Wordings', 17, 0)
  , (248, 'Limits or capacity', 17, 0)
  , (249, 'Class / Line / Product', 17, 0)
  , (251, 'Risk Location', 17, 0)
  , (252, 'Yes', 18, 0)
  , (253, 'No', 18, 0)
  , (254, 'Auto Renewal (tacit)', 3, 1)
  , (255, 'Performance of incumbant', 19, 0)
  , (256, 'Claim History', 19, 0)
  , (257, 'Other (Details in Comments)', 19, 0)
  , (258, '1. Only Carrier / Facility to quote is the incumbent', 20, 1)
  , (259, '2. Global program required', 20, 1)
  , (269, 'Non Tacit', 21, 1)
  , (270, 'Tacit: Seek Alternative Quotes', 21, 1)
  , (271, 'Tacit: Auto Renew (No alternative quotes)', 21, 1)
  , (272, 'Tacit: Non Auto Renew', 21, 1)
  , (273, 'Request quote', 22, 1)
  , (274, 'Alternative quote not required', 22, 1)
  , (276, 'Request quote from Insurer', 22, 0)
  , (277, 'Insurer: Appetite Restriction Criteria Met', 22, 0)
  , (278, 'Insurer: Legal, regulatory or licencing restriction', 22, 0)
  , (279, 'Insurer: Other (Details in Comments)', 22, 0)
  , (280, 'Client Not Tendering: Performance of incumbent', 22, 0)
  , (281, 'Client Not Tendering: Claims Loyalty / History', 22, 0)
  , (282, 'Client Not Tendering: Account Now In Run-Off', 22, 0)
  , (283, 'Client Not Tendering: Other (Details in Comments)', 22, 0)
  , (284, 'Client Direction: Perceived Insurer Performance', 22, 0)
  , (285, 'Client Direction: Aggregate Counterparty Exposure', 22, 0)
  , (286, 'Client Direction: Other (Details in Comments)', 22, 0)
  , (287, '3. Committed to another broker / Willis Office', 20, 0)
  , (288, '4. Client would not allow us to approach carrier / Client Dictated markets used', 20, 0)
  , (289, '5. Business placed with Tier 1 facility', 20, 0)
  , (290, '6. Carrier / Facility percieved to have low appetite for this risk', 20, 0)
  , (291, '7. Regulatory / licensing constraint', 20, 0)
  , (292, '8. Contractual penalties if moved', 20, 0)
  , (293, '9. Client would be disadvantaged by use of G360 (G360 only)', 20, 0)
  , (294, 'Client Not Tendering: Other (Details in Comments)', 20, 1)
  , (295, 'Client Direction: Perceived Insurer Performance', 20, 1)
  , (296, 'Client Direction: Aggregate Counterparty Exposure', 20, 1)
  , (297, 'Client Direction: Other (Details in Comments)', 20, 1)
  , (298, 'Layered Program Primary', 6, 1)
  , (299, 'Layered Program Excess', 6, 1)
  , (300, 'Perceived Quality of Service', 15, 0)
  , (301, 'Perceived Quality of Claims Handling', 15, 0)
  , (304, '1. Hull & Machinery', 6, 1)
  , (305, '2. IV & Disbursements', 6, 1)
  , (306, '3. War', 6, 1)
  , (307, '4. Loss of Hire', 6, 1)
  , (308, '5. Mortgagees Interest', 6, 1)
  , (309, '1. Legal & Contractual TPL', 6, 1)
  , (310, '2. General Liability', 6, 1)
  , (311, '3. Excess Liability', 6, 1)
  , (312, '4. Ship Builders Risks', 6, 1)
  , (313, '5. Ship Repairers', 6, 1)
  , (314, '6. Shipyard packages', 6, 1)
  , (315, '7. Ports & Terminals', 6, 1)
  , (316, '8. Ports packages', 6, 1)
  , (317, '9. Ports / Shipyard Property & Equipment', 6, 1)
  , (318, '10. Charterers Liability', 6, 1)
  , (319, '11. Cargo Owners Liability', 6, 1)
  , (320, '12. Marine PI / E&O', 6, 1)
  , (321, '13. P&I', 6, 1)
  , (322, '14. Specialist Operations', 6, 1)
  , (323, '15. Social Responsibility', 6, 1)
  , (324, '16. Maritime Employers Liability', 6, 1)
  , (325, 'Remarketed', 21, 1)
  , (326, 'Restructured', 21, 1)
  , (327, 'Not Remarketed', 21, 1)
  , (328, '1. Specific Client Written Instruction', 4, 0)
  , (329, '2. 100% Single Carrier', 4, 0)
  , (330, '3. Contractual Penalties if moved / Contenious Claim', 4, 0)
  , (331, '4. Exisiting markets are SpecieMar Carriers', 4, 0)
  , (332, '5. Niche markets, no alternatives', 4, 0)
  , (333, '3. Dry Cargo', 5, 0)
  , (334, '4. Bulkers', 5, 0)
  , (335, '5. Reefers', 5, 0)
  , (336, '6. LNG', 5, 0)
  , (337, '7. Heavy Lift', 5, 0)
  , (338, '8. Tugs', 5, 0)
  , (339, '9. Cruise', 5, 0)
  , (340, '10. Ferries', 5, 0)
  , (341, '11. Container', 5, 0)
  , (342, '1. Price', 23, 0)
  , (343, '2. Breadth of coverage / wording / overall solution', 23, 0)
  , (344, '3. Claims run off', 23, 0)
  , (345, '4. Contractual relationship with penalties if moved', 23, 0)
  , (346, '5. Specialist for this class / risk or recognised leader', 23, 0)
  , (347, '6. Written client instruction', 23, 0)
  , (348, '7. Carrier Perceived to have a higher appetite for risk', 23, 0)
  , (349, '8. No SpecieMar markets prepared to quote / follow', 23, 0)
  , (350, '9. Carrier is the incumbant leader', 23, 0)
  , (351, '1. Hull & Machinery', 24, 0)
  , (352, '2. IV & Disbursements', 24, 0)
  , (353, '3. War', 24, 0)
  , (354, '4. Loss of Hire', 24, 0)
  , (355, '5. Mortgagees Interest', 24, 0)
  , (356, '1. Legal & Contractual TPL', 25, 0)
  , (357, '2. General Liability', 25, 0)
  , (358, '3. Excess Liability', 25, 0)
  , (359, '4. Ship Builders Risks', 25, 0)
  , (360, '5. Ship Repairers', 25, 0)
  , (361, '6. Shipyard packages', 25, 0)
  , (362, '7. Ports & Terminals', 25, 0)
  , (363, '8. Ports packages', 25, 0)
  , (364, '9. Ports / Shipyard Property & Equipment', 25, 0)
  , (365, '10. Charterers Liability', 25, 0)
  , (366, '11. Cargo Owners Liability', 25, 0)
  , (367, '12. Marine PI / E&O', 25, 0)
  , (368, '13. P&I', 25, 0)
  , (369, '14. Specialist Operations', 25, 0)
  , (370, '15. Social Responsibility', 25, 0)
  , (371, '16. Maritime Employers Liability', 25, 0)
  , (372, '1. General Cargo', 26, 0)
  , (373, '2. Freight Liability', 26, 0)
  , (374, '3. Commodities', 26, 0)
  , (375, '4. Political Violence', 26, 0)
  , (376, '5. Soft Commodities', 26, 1)
  , (377, '6. Stock Excess', 26, 0)
  , (378, '7. Stock Primary', 26, 0)
  , (379, '8. Stock Retail', 26, 0)
  , (380, '9. Temp Sensitive', 26, 0)
  , (381, '10. Vehicle Excess', 26, 0)
  , (382, '11. Vehicle Primary', 26, 0)
  , (383, '12. War on Land', 26, 0)
  , (384, 'Submitted to Quote', 27, 0)
  , (385, 'Submitted to Follow', 27, 0)
  , (386, 'Bound', 28, 0)
  , (387, 'Declined to quote', 28, 0)
  , (390, 'Bound', 30, 0)
  , (391, 'Not Bound', 30, 0)
  , (392, 'Bound -  1. Price', 31, 0)
  , (393, 'Bound -  2. Breadth of coverage / wording / overall solution', 31, 0)
  , (394, 'Quote and Follow', 27, 0)
  , (395, 'Quote not taken up', 28, 0)
  , (396, 'BOUND - 1. Price', 29, 0)
  , (397, 'BOUND - 2. Breadth of coverage / wording / overall solution', 29, 0)
  , (398, 'BOUND - 3. Claims run off', 29, 0)
  , (399, 'BOUND - 4. Contractual relationship with penalties if moved', 29, 0)
  , (400, 'BOUND - 5. Specialist for this class / risk or recognised leader', 29, 0)
  , (401, 'BOUND - 6. Written client instruction', 29, 0)
  , (402, 'BOUND - 7. Following incumbent leader', 29, 0)
  , (403, 'BOUND - 8. Carrier quoted terms but not accepted however, carrier agreed to follow alternative quote', 29, 0)
  , (404, 'BOUND - 9.  Carrier declined to quote but agreed to follow alternative quote', 29, 0)
  , (405, 'QUOTE NOT TAKEN UP - 1. Price within 10%', 29, 0)
  , (406, 'QUOTE NOT TAKEN UP - 2. Price over 10%', 29, 0)
  , (407, 'QUOTE NOT TAKEN UP - 3. Breadth of coverage / wording / overall solution', 29, 0)
  , (408, 'QUOTE NOT TAKEN UP - 4. Regulatory / licensing constraint ', 29, 0)
  , (409, 'QUOTE NOT TAKEN UP - 5. Financial Strength', 29, 0)
  , (410, 'QUOTE NOT TAKEN UP - 6. Claim Handling / Service Quality', 29, 0)
  , (411, 'QUOTE NOT TAKEN UP - 7. Existing lines with another Carrier', 29, 0)
  , (412, 'QUOTE NOT TAKEN UP - 8. Placed exclusively with other SpecieMar markets*', 29, 0)
  , (413, 'QUOTE NOT TAKEN UP - 9. Placed with SpecieMar and other markets*', 29, 0)
  , (414, 'DECLINED TO QUOTE - 1. Price', 29, 0)
  , (415, 'DECLINED TO QUOTE - 2. Class of business / Product', 29, 0)
  , (416, 'DECLINED TO QUOTE - 3. Risk Location', 29, 0)
  , (417, 'DECLINED TO QUOTE - 4. Claims / Loss History', 29, 0)
  , (418, 'DECLINED TO QUOTE - 5.Breadth of coverage / wording / overall solution', 29, 0)
  , (419, 'DECLINED TO QUOTE - 6. Already write / quoted', 29, 0)
  , (420, 'DECLINED TO QUOTE - 7. Aggregate Counterparty Exposure', 29, 0)
  , (421, 'DECLINED TO QUOTE - 8. Regulatory / Licensing constraints', 29, 0)
  , (422, 'DECLINED TO QUOTE - 9. Unable to meet quote deadline / No Response', 29, 0)
  , (423, 'Submitted to Quote', 32, 0)
  , (424, 'Submitted to Follow', 32, 0)
  , (425, '1. Only Carrier / Facility to quote is the incumbent', 33, 0)
  , (426, '2. Global program required', 33, 0)
  , (427, '3. Committed to another broker / Willis Office', 33, 0)
  , (428, '4. Client would not allow us to approach carrier / Client Dictated markets used', 33, 0)
  , (429, '5. Business placed with Tier 1 facility', 33, 0)
  , (430, '6. Carrier / Facility percieved to have low appetite for this risk', 33, 0)
  , (431, '7. Regulatory / licensing constraint', 33, 0)
  , (432, '8. Contractual penalties if moved', 33, 0)
  , (433, '9. Quoted by other SpecieMar markets', 33, 0)
  , (434, '11. Client Opt Out after exemption approval process', 34, 0)
  , (435, '1. Expectation of a competitive quote', 35, 0)
  , (436, '2. Breadth of coverage / wording / overall solution', 35, 0)
  , (437, '3. Claims run off', 35, 0)
  , (438, '4. Contractual relationship with penalties if moved', 35, 0)
  , (439, '5. Specialist for this class / risk or recognised leader', 35, 0)
  , (440, '6. Written client instruction', 35, 0)
  , (441, '7. No SpecieMar Tier 2 markets prepared to quote', 35, 0)
  , (442, '8. Carrier is the incumbant leader', 35, 0)
  , (443, '1. Capacity still required after approaching all SpecieMar Carriers and G360', 36, 0)
  , (444, '2. Written client instruction to specifically use this carrier', 36, 0)
  , (445, 'New Business', 21, 0)
  , (446, 'Fully Remarketed', 21, 0)
  , (447, 'Rollover Renewal', 21, 0)
  , (448, 'Bound -  3. Claims run off', 31, 0)
  , (449, 'Bound -  4. Contractual relationship with penalties if moved', 31, 0)
  , (450, 'Bound -  5. Specialist for this class / risk or recognised leader', 31, 0)
  , (451, 'Bound -  6. Written client instruction', 31, 0)
  , (452, 'Bound -  7. Following incumbent leader', 31, 0)
  , (453, 'Bound -  8. Carrier quoted terms but not accepted however, carrier agreed to follow alternative quote', 31, 0)
  , (454, 'Bound -  9.  Carrier declined to quote but agreed to follow alternative quote', 31, 0)
  , (455, 'Not Submitted', 27, 0)
  , (456, '10. N/A', 33, 0)
  , (457, '9. N/A', 35, 0)
  , (458, 'Not Submitted', 32, 0)
  , (459, '12. Fish Boats', 5, 0)
  , (460, '13. Semi-Submersible / Jack-Up Vessels', 5, 0)
  , (461, '13. Subsea', 26, 0)
  , (462, '1. Primary', 37, 0)
  , (463, '2. Excess', 37, 0)
  , (464, '1. Fertiliser', 38, 0)
  , (465, '2. Metals', 38, 0)
  , (466, '3. Oils / Products', 38, 0)
  , (467, '4. Soft Commodities', 38, 0)
  , (468, '5. Cotton', 38, 0)
  , (469, '12. 100% Single Carrier / Facility', 34, 0)
  , (470, '14. Barges', 5, 0)
  , (471, '15. Offshore Support', 5, 0)
  , (472, '16. Seismic Research', 5, 0)
  , (473, '13. Quote Not Taken Up (NTU)', 34, 0)
  , (474, 'QUOTE NOT TAKEN UP - 10. Quote competitive, but no firm order (reason in comments)', 29, 0)
  , (475, '14. No Lloyds Lead', 34, 0)
  , (476, '3. Claims run-off', 36, 0)
  , (477, '4. Contractual relationship with penalties', 36, 0)
  , (478, '5. Incumbent', 36, 0)
  , (479, 'DECLINED TO QUOTE - 10. Poor / deficient risk quality', 29, 0)
  , (480, 'DECLINED TO QUOTE - 11. Insufficient risk information', 29, 0)
  , (481, 'DECLINED TO QUOTE - 12. Appetite', 29, 0)
  , (482, 'DECLINED TO QUOTE- 13. Limits or capacity', 29, 0)
  , (483, 'QUOTE NOT TAKEN UP - 11. Limits or capacity', 29, 0);

MERGE INTO MarineMar.SpecieRefGeneric T
USING #RefGeneric S
ON T.FieldId = S.FieldId
WHEN NOT MATCHED BY TARGET
    THEN INSERT
             (FieldId, DescUK, RefCol, IsDeleted)
         VALUES
             (S.FieldId, S.DescUK, S.RefCol, S.IsDeleted)
WHEN MATCHED AND NOT EXISTS
                         (SELECT T.DescUK, T.RefCol, T.IsDeleted INTERSECT SELECT S.DescUK, S.RefCol, S.IsDeleted)
    THEN UPDATE SET T.DescUK = S.DescUK, T.RefCol = S.RefCol, T.ETLUpdatedDate = GETUTCDATE(), T.IsDeleted = S.IsDeleted
WHEN NOT MATCHED BY SOURCE
    THEN UPDATE SET T.ETLUpdatedDate = GETUTCDATE(), T.IsDeleted = 1;

/* Mappings */

/* Load RiskRenewal data */
/* Mapped to ref.RenewableOption           */
DROP TABLE IF EXISTS #RefRiskRenewalMapping;
GO

CREATE TABLE #RefRiskRenewalMapping (
    RenewableOptionId INT NOT NULL
  , FieldId           INT NOT NULL
  , RefCol            INT NOT NULL,
);

INSERT INTO #RefRiskRenewalMapping
    (RenewableOptionId, FieldId, RefCol)
VALUES
    (1, 252, 18) --Yes 
  , (2, 253, 18); --No  

MERGE INTO MarineMar.SpecieRiskRenewalMapping T
USING #RefRiskRenewalMapping S
ON T.RenewableOptionId = S.RenewableOptionId
WHEN NOT MATCHED BY TARGET
    THEN INSERT
             (RenewableOptionId, FieldId, RefCol)
         VALUES
             (S.RenewableOptionId, S.FieldId, S.RefCol)
WHEN MATCHED AND NOT EXISTS
                         (SELECT T.RenewableOptionId, T.FieldId, T.RefCol INTERSECT SELECT S.RenewableOptionId, S.FieldId, S.RefCol)
    THEN UPDATE SET T.RenewableOptionId = S.RenewableOptionId, T.FieldId = S.FieldId, T.RefCol = S.RefCol, T.ETLUpdatedDate = GETUTCDATE()
WHEN NOT MATCHED BY SOURCE
    THEN UPDATE SET T.ETLUpdatedDate = GETUTCDATE(), T.IsDeleted = 1;

/* Load NotMarketingReason data (Reason For Rollover) */
/* Mapped to Reference.NotRemarketingReason           */
DROP TABLE IF EXISTS #RefNotRemarketingReasonMapping;
GO

CREATE TABLE #RefNotRemarketingReasonMapping (
    NotRemarketingReasonId INT NOT NULL
  , FieldId                INT NOT NULL
  , RefCol                 INT NOT NULL,
);

INSERT INTO #RefNotRemarketingReasonMapping
    (NotRemarketingReasonId, FieldId, RefCol)
VALUES
    (1, 328, 4) --1. Specific Client Written Instruction
  , (2, 329, 4) --2. 100% Single Carrier
  , (3, 330, 4) --3. Contractual Penalties if moved / Contentious Claim
  , (4, 331, 4) --4. Existing markets are MarineMar Carriers
  , (5, 332, 4); --5. Niche markets, no alternatives

MERGE INTO MarineMar.SpecieNotRemarketingReasonMapping T
USING #RefNotRemarketingReasonMapping S
ON T.NotRemarketingReasonId = S.NotRemarketingReasonId
WHEN NOT MATCHED BY TARGET
    THEN INSERT
             (NotRemarketingReasonId, FieldId, RefCol)
         VALUES
             (S.NotRemarketingReasonId, S.FieldId, S.RefCol)
WHEN MATCHED AND NOT EXISTS
                         (SELECT T.NotRemarketingReasonId, T.FieldId, T.RefCol INTERSECT SELECT S.NotRemarketingReasonId, S.FieldId, S.RefCol)
    THEN UPDATE SET T.NotRemarketingReasonId = S.NotRemarketingReasonId, T.FieldId = S.FieldId, T.RefCol = S.RefCol, T.ETLUpdatedDate = GETUTCDATE()
WHEN NOT MATCHED BY SOURCE
    THEN UPDATE SET T.ETLUpdatedDate = GETUTCDATE(), T.IsDeleted = 1;

/* RequestQuoteInsurer (Request Quote Insurer) */
/* Linked to dbo.JustifcationReason            */
DROP TABLE IF EXISTS #RefRequestQuoteInsurerMapping;
GO

CREATE TABLE #RefRequestQuoteInsurerMapping (
    JustificationReasonId INT NOT NULL
  , FieldId               INT NOT NULL
  , RefCol                INT NOT NULL,
);

INSERT INTO #RefRequestQuoteInsurerMapping
    (JustificationReasonId, FieldId, RefCol)
VALUES
    (23, 425, 33) --1. Only Carrier / Facility to quote is the incumbent
  , (24, 426, 33) --2. Global program required
  , (25, 427, 33) --3. Committed to another broker / Willis Office
  , (26, 428, 33) --4. Client would not allow us to approach carrier / Client Dictated markets used
  , (27, 429, 33) --5. Business placed with Tier 1 facility
  , (28, 430, 33) --6. Carrier / Facility percieved to have low appetite for this risk
  , (29, 431, 33) --7. Regulatory / licensing constraint
  , (30, 432, 33) --8. Contractual penalties if moved
  , (31, 433, 33) --9. Quoted by other SpecieMar markets
                  --, (NULL, 456, 33) --10. N/A
  , (32, 435, 35) --1. Expectation of a competitive quote
  , (33, 436, 35) --2. Breadth of coverage / wording / overall solution
  , (34, 437, 35) --3. Claims run off
  , (35, 438, 35) --4. Contractual relationship with penalties if moved
  , (36, 439, 35) --5. Specialist for this class / risk or recognised leader
  , (37, 440, 35) --6. Written client instruction
  , (38, 441, 35) --7. No SpecieMar Tier 2 markets prepared to quote
  , (39, 442, 35) --8. Carrier is the incumbant leader
                  --, (NULL, 457, 35) --9. N/A
  , (40, 443, 36) --1. Capacity still required after approaching all SpecieMar Carriers and G360
  , (41, 444, 36);

--2. Written client instruction to specifically use this carrier
--, (NULL, 476, 36) --3. Claims run-off
--, (NULL, 477, 36) --4. Contractual relationship with penalties
--, (NULL, 478, 36) --5. Incumbent
MERGE INTO MarineMar.SpecieRequestQuoteInsurerMapping T
USING #RefRequestQuoteInsurerMapping S
ON T.JustificationReasonId = S.JustificationReasonId
WHEN NOT MATCHED BY TARGET
    THEN INSERT
             (JustificationReasonId, FieldId, RefCol)
         VALUES
             (S.JustificationReasonId, S.FieldId, S.RefCol)
WHEN MATCHED AND NOT EXISTS
                         (SELECT T.JustificationReasonId, T.FieldId, T.RefCol INTERSECT SELECT S.JustificationReasonId, S.FieldId, S.RefCol)
    THEN UPDATE SET T.JustificationReasonId = S.JustificationReasonId, T.FieldId = S.FieldId, T.RefCol = S.RefCol, T.ETLUpdatedDate = GETUTCDATE()
WHEN NOT MATCHED BY SOURCE
    THEN UPDATE SET T.ETLUpdatedDate = GETUTCDATE(), T.IsDeleted = 1;

/* Mapping between MarineMar mapping and ref.OutcomeReason */
DROP TABLE IF EXISTS #RefOutcomeReasonMapping;
GO

CREATE TABLE #RefOutcomeReasonMapping (
    OutcomeReasonId     INT          NOT NULL
  , LabelTranslationKey NVARCHAR(250)
  , FieldId             INT          NOT NULL
  , RefCol              INT          NOT NULL,
);

INSERT INTO #RefOutcomeReasonMapping
    (OutcomeReasonId, LabelTranslationKey, FieldId, RefCol)
VALUES
    --Client Accepted
    (1, 'REF_OUTCOME_REASON_ACCEPTED_FINANCIAL_STRENGTH_OF_CARRIER', 400, 29) --BOUND - 5. Specialist for this class / risk or recognised leader
  , (2, 'REF_OUTCOME_REASON_ACCEPTED_ONGOING_CLAIM', 398, 29) --BOUND - 3. Claims run off
  , (3, 'REF_OUTCOME_REASON_ACCEPTED_PRICING_PROGRAM_COST', 396, 29) --BOUND - 1. Price
  , (4, 'REF_OUTCOME_REASON_ACCEPTED_COVERAGE_STRUCTURE', 397, 29) --BOUND - 2. Breadth of coverage / wording / overall solution
  , (5, 'REF_OUTCOME_REASON_ACCEPTED_EXISTING_LINES', 401, 29) --BOUND - 6. Written client instruction
  , (6, 'REF_OUTCOME_REASON_ACCEPTED_CLAIMS_HANDLING', 400, 29) --BOUND - 5. Specialist for this class / risk or recognised leader
  , (21, 'REF_OUTCOME_REASON_ACCEPTED_REQUIRED_CAPACITY_LIMITS', 397, 29) --BOUND - 2. Breadth of coverage / wording / overall solution
  , (22, 'REF_OUTCOME_REASON_ACCEPTED_DOES_NOT_FIT_PROGRAMME', 397, 29) --BOUND - 2. Breadth of coverage / wording / overall solution
  , (23, 'REF_OUTCOME_REASON_ACCEPTED_UNDERWRITING_SERVICE_LEVELS', 400, 29) --BOUND - 5. Specialist for this class / risk or recognised leader
  , (36, 'REF_OUTCOME_REASON_ACCEPTED_UNDERWRITING_REPUTATION', 400, 29) --BOUND - 5. Specialist for this class / risk or recognised leader
                                                                         --Client Declinature
  , (8, 'REF_OUTCOME_REASON_NOT_ACCEPTED_FINANCIAL_STRENGTH_OF_CARRIER', 409, 29) --QUOTE NOT TAKEN UP - 5. Financial Strength
  , (9, 'REF_OUTCOME_REASON_NOT_ACCEPTED_ONGOING_CLAIM', 411, 29) --QUOTE NOT TAKEN UP - 7. Existing lines with another Carrier
  , (10, 'REF_OUTCOME_REASON_NOT_ACCEPTED_PRICING_PROGRAM_COST', 405, 29) --QUOTE NOT TAKEN UP - 1. Price within 10%
  , (11, 'REF_OUTCOME_REASON_NOT_ACCEPTED_COVERAGE_STRUCTURE', 407, 29) --QUOTE NOT TAKEN UP - 3. Breadth of coverage / wording / overall solution
  , (12, 'REF_OUTCOME_REASON_NOT_ACCEPTED_EXISTING_LINES', 474, 29) --QUOTE NOT TAKEN UP - 10. Quote competitive, but no firm order (reason in comments)
  , (13, 'REF_OUTCOME_REASON_NOT_ACCEPTED_CLAIMS_HANDLING', 410, 29) --QUOTE NOT TAKEN UP - 6. Claim Handling / Service Quality
  , (20, 'REF_OUTCOME_REASON_NOT_ACCEPTED_DOES_NOT_FIT_PROGRAMME', 407, 29) --QUOTE NOT TAKEN UP - 3. Breadth of coverage / wording / overall solution
  , (24, 'REF_OUTCOME_REASON_NOT_ACCEPTED_REQUIRED_CAPACITY_LIMITS', 407, 29) --QUOTE NOT TAKEN UP - 3. Breadth of coverage / wording / overall solution
  , (25, 'REF_OUTCOME_REASON_NOT_ACCEPTED_UNDERWRITING_SERVICE_LEVELS', 410, 29) --QUOTE NOT TAKEN UP - 6. Claim Handling / Service Quality
  , (26, 'REF_OUTCOME_REASON_NOT_ACCEPTED_OPTION_NOT_REQUIRED', 474, 29) --QUOTE NOT TAKEN UP - 10. Quote competitive, but no firm order (reason in comments)
  , (37, 'REF_OUTCOME_REASON_NOT_ACCEPTED_UNDERWRITING_REPUTATION', 409, 29) --QUOTE NOT TAKEN UP - 5. Financial Strength
                                                                             --Broker Not Offered to Client
  , (18, 'REF_OUTCOME_REASON_NOT_OFFERED_TERMS', 405, 29) --QUOTE NOT TAKEN UP - 1. Price within 10%
  , (19, 'REF_OUTCOME_REASON_NOT_OFFERED_STRUCTURE', 407, 29) --QUOTE NOT TAKEN UP - 3. Breadth of coverage / wording / overall solution
  , (27, 'REF_OUTCOME_REASON_NOT_OFFERED_ONGOING_CLAIM', 411, 29) --QUOTE NOT TAKEN UP - 7. Existing lines with another Carrier
  , (28, 'REF_OUTCOME_REASON_NOT_OFFERED_REQUIRED_CAPACITY_LIMITS', 407, 29) --QUOTE NOT TAKEN UP - 3. Breadth of coverage / wording / overall solution
  , (29, 'REF_OUTCOME_REASON_NOT_OFFERED_COVERAGE_STRUCTURE', 407, 29) --QUOTE NOT TAKEN UP - 3. Breadth of coverage / wording / overall solution
  , (30, 'REF_OUTCOME_REASON_NOT_OFFERED_UNDERWRITING_SERVICE_LEVELS', 410, 29) --QUOTE NOT TAKEN UP - 6. Claim Handling / Service Quality
  , (31, 'REF_OUTCOME_REASON_NOT_OFFERED_EXISTING_LINES', 474, 29) --QUOTE NOT TAKEN UP - 10. Quote competitive, but no firm order (reason in comments)
  , (32, 'REF_OUTCOME_REASON_NOT_OFFERED_CLAIMS_HANDLING', 410, 29) --QUOTE NOT TAKEN UP - 6. Claim Handling / Service Quality
  , (33, 'REF_OUTCOME_REASON_NOT_OFFERED_QUOTE_LATE', 474, 29) --QUOTE NOT TAKEN UP - 10. Quote competitive, but no firm order (reason in comments)
  , (34, 'REF_OUTCOME_REASON_NOT_OFFERED_OPTION_NOT_REQUIRED', 474, 29) --QUOTE NOT TAKEN UP - 10. Quote competitive, but no firm order (reason in comments)
  , (35, 'REF_OUTCOME_REASON_NOT_OFFERED_FINANCIAL_STRENGTH_OF_CARRIER', 409, 29); --QUOTE NOT TAKEN UP - 5. Financial Strength

MERGE INTO MarineMar.SpecieOutcomeReasonMapping T
USING #RefOutcomeReasonMapping S
ON T.OutcomeReasonId = S.OutcomeReasonId
WHEN NOT MATCHED BY TARGET
    THEN INSERT
             (OutcomeReasonId, FieldId, RefCol)
         VALUES
             (S.OutcomeReasonId, S.FieldId, S.RefCol)
WHEN MATCHED AND NOT EXISTS
                         (SELECT T.OutcomeReasonId, T.FieldId, T.RefCol INTERSECT SELECT S.OutcomeReasonId, S.FieldId, S.RefCol)
    THEN UPDATE SET T.OutcomeReasonId = S.OutcomeReasonId, T.FieldId = S.FieldId, T.RefCol = S.RefCol, T.ETLUpdatedDate = GETUTCDATE()
WHEN NOT MATCHED BY SOURCE
    THEN UPDATE SET T.ETLUpdatedDate = GETUTCDATE(), T.IsDeleted = 1;

--/* Mapping between MarineMar mapping and ref.DeclinationReason */
DROP TABLE IF EXISTS #RefDeclinationReasonMapping;
GO

CREATE TABLE #RefDeclinationReasonMapping (
    DeclinationReasonId INT          NOT NULL
  , LabelTranslationKey NVARCHAR(250)
  , FieldId             INT          NOT NULL
  , RefCol              INT          NOT NULL,
);

INSERT INTO #RefDeclinationReasonMapping
    (DeclinationReasonId, LabelTranslationKey, FieldId, RefCol)
VALUES
    (1, 'REF_DECLINATION_REASON_CLAIMS_HISTORY', 417, 29) --DECLINED TO QUOTE - 4. Claims / Loss History
  , (2, 'REF_DECLINATION_REASON_CONTINUITY', 415, 29) --DECLINED TO QUOTE - 2. Class of business / Product
  , (3, 'REF_DECLINATION_REASON_BLOCKED', 419, 29) --DECLINED TO QUOTE - 6. Already write / quoted
  , (4, 'REF_DECLINATION_REASON_LICENSING_ISSUE', 421, 29) --DECLINED TO QUOTE - 8. Regulatory / Licensing constraints
  , (5, 'REF_DECLINATION_REASON_CANNOT_MEET_QUOTE_DEADLINE', 422, 29) --DECLINED TO QUOTE - 9. Unable to meet quote deadline / No Response
  , (6, 'REF_DECLINATION_REASON_UNABLE_TO_MEET_COVERAGE', 418, 29) --DECLINED TO QUOTE - 5.Breadth of coverage / wording / overall solution
  , (7, 'REF_DECLINATION_REASON_INADEQUATE', 482, 29) --DECLINED TO QUOTE- 13. Limits or capacity
  , (8, 'REF_DECLINATION_REASON_NOT_COMPETITIVE', 414, 29) --DECLINED TO QUOTE - 1. Price
  , (9, 'REF_DECLINATION_REASON_INSUFFICIENT_INFORMATION', 422, 29) --DECLINED TO QUOTE - 9. Unable to meet quote deadline / No Response
  , (10, 'REF_DECLINATION_REASON_EXPOSURE_PROFILE', 416, 29) --DECLINED TO QUOTE - 3. Risk Location
  , (11, 'REF_DECLINATION_REASON_PROGRAM_STRUCTURE', 418, 29) --DECLINED TO QUOTE - 5.Breadth of coverage / wording / overall solution
  , (12, 'REF_DECLINATION_REASON_WISHES_TO_LEAD_FOLLOW', 418, 29);

--DECLINED TO QUOTE - 5.Breadth of coverage / wording / overall solution
--, (NULL, NULL, 420, 29) --DECLINED TO QUOTE - 7. Aggregate Counterparty Exposure
--, (NULL, NULL, 479, 29) --DECLINED TO QUOTE - 10. Poor / deficient risk quality
--, (NULL, NULL, 480, 29) --DECLINED TO QUOTE - 11. Insufficient risk information
--, (NULL, NULL, 481, 29) --DECLINED TO QUOTE - 12. Appetite
MERGE INTO MarineMar.SpecieDeclinationReasonMapping T
USING #RefDeclinationReasonMapping S
ON T.DeclinationReasonId = S.DeclinationReasonId
WHEN NOT MATCHED BY TARGET
    THEN INSERT
             (DeclinationReasonId, FieldId, RefCol)
         VALUES
             (S.DeclinationReasonId, S.FieldId, S.RefCol)
WHEN MATCHED AND NOT EXISTS
                         (SELECT T.DeclinationReasonId, T.FieldId, T.RefCol INTERSECT SELECT S.DeclinationReasonId, S.FieldId, S.RefCol)
    THEN UPDATE SET T.DeclinationReasonId = S.DeclinationReasonId, T.FieldId = S.FieldId, T.RefCol = S.RefCol, T.ETLUpdatedDate = GETUTCDATE()
WHEN NOT MATCHED BY SOURCE
    THEN UPDATE SET T.ETLUpdatedDate = GETUTCDATE(), T.IsDeleted = 1;

/* IndustrySectorType mapping (Interest Type for Specie (all lines)) */
/* Mapped to SpecieRefRiskCode                    */
DROP TABLE IF EXISTS #IndustrySectorTypeMapping;
GO

CREATE TABLE #IndustrySectorTypeMapping (
    IndustrySectorTypeElementTagTypeKey NVARCHAR(100) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL
  , RiskCodeId                          INT           NOT NULL
  , ProductClassId                      INT           NOT NULL,
);

/* Fine Art */
INSERT INTO #IndustrySectorTypeMapping
    (IndustrySectorTypeElementTagTypeKey, RiskCodeId, ProductClassId)
VALUES
    ('ETT_INDUSTRYSECTORTYPE_AUCTION_HOUSE', 92, 1) --1. Auction House
  , ('ETT_INDUSTRYSECTORTYPE_CAR_COLLECTION', 93, 1) --2. Car Collection
  , ('ETT_INDUSTRYSECTORTYPE_COLLATERALISED_ART', 94, 1) --3. Collateralised Art
  , ('ETT_INDUSTRYSECTORTYPE_CORPORATE_COLLECTION', 95, 1) --4. Corporate Collection
  , ('ETT_INDUSTRYSECTORTYPE_DEALERS', 96, 1) --5. Dealers
  , ('ETT_INDUSTRYSECTORTYPE_EXHIBITION', 97, 1) --6. Exhibition
  , ('ETT_INDUSTRYSECTORTYPE_GALLERY', 98, 1) --7. Gallery
  , ('ETT_INDUSTRYSECTORTYPE_MUSEUM', 99, 1) --8. Museum
  , ('ETT_INDUSTRYSECTORTYPE_PRIVATE_COLLECTION', 100, 1) --9. Private Collection
  , ('ETT_INDUSTRYSECTORTYPE_PUBLIC_ENTITY', 101, 1) --10. Public Entity
  , ('ETT_INDUSTRYSECTORTYPE_SHIPPER_AND_PACKER', 102, 1) --11. Shipper & Packer
  , ('ETT_INDUSTRYSECTORTYPE_WINE_COLLECTIONS', 103, 1) --12. Wine Collections
  , ('ETT_INDUSTRYSECTORTYPE_DIGITAL_ARTWORK', 104, 1) --13. Digital Artwork
  , ('ETT_INDUSTRYSECTORTYPE_ANTIQUE_AND_LIBRARY_COLLECTIONS', 105, 1) --14. Antique & Library Collections
  , ('ETT_INDUSTRYSECTORTYPE_LIVING_ARTIST_COVER', 106, 1) --15. Living Artist Cover
  , ('ETT_INDUSTRYSECTORTYPE_REINSURANCE_OF_CAPTIVES', 107, 1) --16. Reinsurance of Captives
  , ('ETT_INDUSTRYSECTORTYPE_CONSERVATION_RESTORATION_COSTS', 108, 1) --17. Conservation / Restoration Costs
  , ('ETT_INDUSTRYSECTORTYPE_DEFECTIVE_TITLES', 109, 1) --18. Defective Titles  (and or defence costs)
  , ('ETT_INDUSTRYSECTORTYPE_EMPLOYEE_LIABILITY', 110, 1) --19. Employee Liability
  , ('ETT_INDUSTRYSECTORTYPE_ALL_RISKS', 111, 1) --20. All Risks
  , ('ETT_INDUSTRYSECTORTYPE_LEGAL_LIABILITY', 112, 1) --21. Legal Liability
  , ('ETT_INDUSTRYSECTORTYPE_QUAKE', 141, 1) --22. Quake
  , ('industrySectorType_auctionHouse', 92, 1) --1. Auction House
  , ('industrySectorType_carCollection', 93, 1) --2. Car Collection
  , ('industrySectorType_collateralisedArt', 94, 1) --3. Collateralised Art
  , ('industrySectorType_corporateCollection', 95, 1) --4. Corporate Collection
  , ('industrySectorType_dealers', 96, 1) --5. Dealers
  , ('industrySectorType_exhibition', 97, 1) --6. Exhibition
  , ('industrySectorType_gallery', 98, 1) --7. Gallery
  , ('industrySectorType_museum', 99, 1) --8. Museum
  , ('industrySectorType_privateCollection', 100, 1) --9. Private Collection
  , ('industrySectorType_publicEntity', 101, 1) --10. Public Entity
  , ('industrySectorType_shipperAndPacker', 102, 1) --11. Shipper & Packer
  , ('industrySectorType_wineCollections', 103, 1) --12. Wine Collections
  , ('industrySectorType_digitalArtwork', 104, 1) --13. Digital Artwork
  , ('industrySectorType_antiqueAndLibraryCollections', 105, 1) --14. Antique & Library Collections
  , ('industrySectorType_livingArtistCover', 106, 1) --15. Living Artist Cover
  , ('industrySectorType_reinsuranceOfCaptives', 107, 1) --16. Reinsurance of Captives
  , ('industrySectorType_conservationRestorationCosts', 108, 1) --17. Conservation / Restoration Costs
  , ('industrySectorType_defectiveTitles', 109, 1) --18. Defective Titles  (and or defence costs)
  , ('industrySectorType_employeeLiability', 110, 1) --19. Employee Liability
  , ('industrySectorType_allRisks', 111, 1) --20. All Risks
  , ('industrySectorType_legalLiability', 112, 1) --21. Legal Liability
  , ('industrySectorType_quake', 141, 1);

--22. Quake

/* Jewellers Block */
INSERT INTO #IndustrySectorTypeMapping
    (IndustrySectorTypeElementTagTypeKey, RiskCodeId, ProductClassId)
VALUES
    ('ETT_INDUSTRYSECTORTYPE_PERSONAL_JEWELLERY', 113, 2) --1. Personal Jewellery
  , ('ETT_INDUSTRYSECTORTYPE_WATCH_DEALERS_RETAILERS', 114, 2) --2. Watch Dealers / Retailers
  , ('ETT_INDUSTRYSECTORTYPE_JEWELLERY_RETAILER', 115, 2) --3. Jewellery Retailer
  , ('ETT_INDUSTRYSECTORTYPE_JEWELLERY_WHOLESALER', 116, 2) --4. Jewellery Wholesaler
  , ('ETT_INDUSTRYSECTORTYPE_JEWELLERY_MANUFACTURER', 117, 2) --5. Jewellery Manufacturer
  , ('ETT_INDUSTRYSECTORTYPE_DIAMOND_PROCESSING', 118, 2) --6. Diamond Processing
  , ('ETT_INDUSTRYSECTORTYPE_ROUGH_STONE_AND_METALS_FROM_MINES', 119, 2) --7. Rough Stone & Metals from Mines
  , ('ETT_INDUSTRYSECTORTYPE_EXHIBITIONS_AND_EVENTS', 120, 2) --8. Exhibitions and Events
  , ('ETT_INDUSTRYSECTORTYPE_THIRD_PARTY_INSOLVENCY', 121, 2) --9. Third Party Insolvency
  , ('ETT_INDUSTRYSECTORTYPE_THIRD_PARTY_DISHONESTY', 122, 2) --10. Third Party Dishonesty
  , ('industrySectorType_personalJewellery', 113, 2) --1. Personal Jewellery
  , ('industrySectorType_watchDealersRetailers', 114, 2) --2. Watch Dealers / Retailers
  , ('industrySectorType_jewelleryRetailer', 115, 2) --3. Jewellery Retailer
  , ('industrySectorType_jewelleryWholesaler', 116, 2) --4. Jewellery Wholesaler
  , ('industrySectorType_jewelleryManufacturer', 117, 2) --5. Jewellery Manufacturer
  , ('industrySectorType_diamondProcessing', 118, 2) --6. Diamond Processing
  , ('industrySectorType_roughStoneAndMetalsFromMines', 119, 2) --7. Rough Stone & Metals from Mines
  , ('industrySectorType_exhibitionsAndEvents', 120, 2) --8. Exhibitions and Events
  , ('industrySectorType_thirdPartyInsolvency', 121, 2) --9. Third Party Insolvency
  , ('industrySectorType_thirdPartyDishonesty', 122, 2);

--10. Third Party Dishonesty

/* Cash In Transit */
INSERT INTO #IndustrySectorTypeMapping
    (IndustrySectorTypeElementTagTypeKey, RiskCodeId, ProductClassId)
VALUES
    ('ETT_INDUSTRYSECTORTYPE_ATM', 123, 3) --1. ATM
  , ('ETT_INDUSTRYSECTORTYPE_CASH_MANAGEMENT_COMPANY', 124, 3) --2. Cash Management Company
  , ('ETT_INDUSTRYSECTORTYPE_CHEQUE_CASHIERS', 125, 3) --3. Cheque Cashiers
  , ('ETT_INDUSTRYSECTORTYPE_PRECIOUS_METALS', 126, 3) --4. Precious Metals
  , ('ETT_INDUSTRYSECTORTYPE_VAULT', 127, 3) --5. Vault
  , ('ETT_INDUSTRYSECTORTYPE_ELECTRONIC_FUNDS_TRANSFER', 128, 3) --6. Electronic Funds Transfer
  , ('ETT_INDUSTRYSECTORTYPE_FOREIGN_EXCHANGE_BUREAUX', 129, 3) --7. Foreign Exchange Bureaux
  , ('ETT_INDUSTRYSECTORTYPE_BITCOIN', 130, 3) --8. Bitcoin
  , ('industrySectorType_ATM', 123, 3) --1. ATM
  , ('industrySectorType_cashManagementCompany', 124, 3) --2. Cash Management Company
  , ('industrySectorType_chequeCashiers', 125, 3) --3. Cheque Cashiers
  , ('industrySectorType_preciousMetals', 126, 3) --4. Precious Metals
  , ('industrySectorType_vault', 127, 3) --5. Vault
  , ('industrySectorType_electronicFundsTransfer', 128, 3) --6. Electronic Funds Transfer
  , ('industrySectorType_foreignExchangeBureaux', 129, 3) --7. Foreign Exchange Bureaux
  , ('industrySectorType_bitcoin', 130, 3);

--8. Bitcoin

/* General Specie */
INSERT INTO #IndustrySectorTypeMapping
    (IndustrySectorTypeElementTagTypeKey, RiskCodeId, ProductClassId)
VALUES
    ('ETT_INDUSTRYSECTORTYPE_DIAMOND_PROCESSOR', 131, 4) --1. Diamond Processor
  , ('ETT_INDUSTRYSECTORTYPE_METAL_REFINERY', 132, 4) --2. Metal Refinery
  , ('ETT_INDUSTRYSECTORTYPE_MINING', 133, 4) --3. Mining
  , ('ETT_INDUSTRYSECTORTYPE_PRECIOUS_METALS', 134, 4) --4. Precious Metals
  , ('ETT_INDUSTRYSECTORTYPE_PRECIOUS_METAL_DEALERS', 135, 4) --5. Precious Metal Dealers
  , ('ETT_INDUSTRYSECTORTYPE_MOVEMENT_OF_PRECIOUS_METALS', 136, 4) --6. Movement of Precious Metals
  , ('ETT_INDUSTRYSECTORTYPE_STORAGE_OF_PRECIOUS_METALS', 137, 4) --7. Storage of Precious Metals
  , ('ETT_INDUSTRYSECTORTYPE_SAFETY_DEPOSIT_FACILITIES', 138, 4) --8. Safety Deposit Facilities
  , ('ETT_INDUSTRYSECTORTYPE_NUMISMATICS', 139, 4) --9. Numismatics
  , ('ETT_INDUSTRYSECTORTYPE_CASH_IN_VAULT', 140, 4) --10. Cash In Vault
  , ('industrySectorType_diamondProcessor', 131, 4) --1. Diamond Processor
  , ('industrySectorType_metalRefinery', 132, 4) --2. Metal Refinery
  , ('industrySectorType_mining', 133, 4) --3. Mining
  , ('industrySectorType_preciousMetals', 134, 4) --4. Precious Metals
  , ('industrySectorType_preciousMetalDealers', 135, 4) --5. Precious Metal Dealers
  , ('industrySectorType_movementOfPreciousMetals', 136, 4) --6. Movement of Precious Metals
  , ('industrySectorType_storageOfPreciousMetals', 137, 4) --7. Storage of Precious Metals
  , ('industrySectorType_safetyDepositFacilities', 138, 4) --8. Safety Deposit Facilities
  , ('industrySectorType_numismatics', 139, 4) --9. Numismatics
  , ('industrySectorType_cashInVault', 140, 4); --10. Cash In Vault

MERGE INTO MarineMar.SpecieIndustrySectorTypeMapping T
USING #IndustrySectorTypeMapping S
ON T.IndustrySectorTypeElementTagTypeKey = S.IndustrySectorTypeElementTagTypeKey
WHEN NOT MATCHED BY TARGET
    THEN INSERT
             (IndustrySectorTypeElementTagTypeKey, RiskCodeId, ProductClassId)
         VALUES
             (S.IndustrySectorTypeElementTagTypeKey, S.RiskCodeId, S.ProductClassId)
WHEN MATCHED AND NOT EXISTS
                         (SELECT T.IndustrySectorTypeElementTagTypeKey, T.RiskCodeId, T.ProductClassId
                          INTERSECT
                          SELECT S.IndustrySectorTypeElementTagTypeKey, S.RiskCodeId, S.ProductClassId)
    THEN UPDATE SET T.IndustrySectorTypeElementTagTypeKey = S.IndustrySectorTypeElementTagTypeKey, T.RiskCodeId = S.RiskCodeId, T.ProductClassId = S.ProductClassId, T.ETLUpdatedDate = GETUTCDATE()
WHEN NOT MATCHED BY SOURCE
    THEN UPDATE SET T.ETLUpdatedDate = GETUTCDATE(), T.IsDeleted = 1;

/* Currency                                                         */
/* These map exactly to the CurrencyAlphaCode in Reference.Currency */
DROP TABLE IF EXISTS #RefCurrencyMapping;
GO

CREATE TABLE #RefCurrencyMapping (
    ISOCurrencyCode NCHAR(3) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL
  , CurrencyCodeId  INT      NOT NULL
);

INSERT INTO #RefCurrencyMapping
    (CurrencyCodeId, ISOCurrencyCode)
VALUES
    (0, 'AED')
  , (1, 'AFN')
  , (2, 'ALL')
  , (3, 'AMD')
  , (4, 'ANG')
  , (5, 'AOA')
  , (6, 'ARS')
  , (7, 'AUD')
  , (8, 'AWG')
  , (9, 'BBD')
  , (10, 'BDT')
  , (11, 'BGN')
  , (12, 'BHD')
  , (13, 'BIF')
  , (14, 'BMD')
  , (15, 'BND')
  , (16, 'BOB')
  , (17, 'BRL')
  , (18, 'BSD')
  , (19, 'BTN')
  , (20, 'BWP')
  , (21, 'BZD')
  , (22, 'CAD')
  , (23, 'CDF')
  , (24, 'CHF')
  , (25, 'CLF')
  , (26, 'CLP')
  , (27, 'CNY')
  , (28, 'COP')
  , (29, 'CRC')
  , (30, 'CUP')
  , (31, 'CVE')
  , (32, 'CZK')
  , (33, 'DJF')
  , (34, 'DKK')
  , (35, 'DOP')
  , (36, 'DZD')
  , (37, 'EGP')
  , (38, 'ETB')
  , (39, 'EUR')
  , (40, 'FJD')
  , (41, 'FKP')
  , (42, 'GBP')
  , (43, 'GHS')
  , (44, 'GIP')
  , (45, 'GMD')
  , (46, 'GNF')
  , (47, 'GTQ')
  , (48, 'GYD')
  , (49, 'HKD')
  , (50, 'HNL')
  , (51, 'HRK')
  , (52, 'HTG')
  , (53, 'HUF')
  , (54, 'IDR')
  , (55, 'ILS')
  , (56, 'INR')
  , (57, 'IRR')
  , (58, 'ISK')
  , (59, 'JMD')
  , (60, 'JOD')
  , (61, 'JPY')
  , (62, 'KES')
  , (63, 'KHR')
  , (64, 'KPW')
  , (65, 'KRW')
  , (66, 'KWD')
  , (67, 'KYD')
  , (68, 'LAK')
  , (69, 'LBP')
  , (70, 'LKR')
  , (71, 'LRD')
  , (72, 'LSL')
  , (73, 'LTL')
  , (74, 'LVL')
  , (75, 'LYD')
  , (76, 'MAD')
  , (77, 'MDL')
  , (78, 'MGA')
  , (79, 'MKD')
  , (80, 'MMK')
  , (81, 'MOP')
  , (82, 'MRO')
  , (83, 'MUR')
  , (84, 'MVR')
  , (85, 'MWK')
  , (86, 'MXN')
  , (87, 'MYR')
  , (88, 'MZN')
  , (89, 'NAD')
  , (90, 'NGN')
  , (91, 'NIO')
  , (92, 'NOK')
  , (93, 'NPR')
  , (94, 'NZD')
  , (95, 'OMR')
  , (96, 'PAB')
  , (97, 'PEN')
  , (98, 'PGK')
  , (99, 'PHP')
  , (100, 'PKR')
  , (101, 'PLN')
  , (102, 'PYG')
  , (103, 'QAR')
  , (104, 'RON')
  , (105, 'RSD')
  , (106, 'RUB')
  , (107, 'RWF')
  , (108, 'SAR')
  , (109, 'SCR')
  , (110, 'SEK')
  , (111, 'SGD')
  , (112, 'SLL')
  , (113, 'SOS')
  , (114, 'SRD')
  , (115, 'SYP')
  , (116, 'SZL')
  , (117, 'THB')
  , (118, 'TND')
  , (119, 'TOP')
  , (120, 'TRY')
  , (121, 'TTD')
  , (122, 'TWD')
  , (123, 'TZS')
  , (124, 'UAH')
  , (125, 'UGX')
  , (126, 'USD')
  , (127, 'UYU')
  , (128, 'VEF')
  , (129, 'VND')
  , (130, 'WST')
  , (131, 'XCD')
  , (132, 'XOF')
  , (133, 'XPF')
  , (134, 'YER')
  , (135, 'ZAR')
  , (136, 'ZMK');

MERGE INTO MarineMar.SpecieCurrencyMapping T
USING #RefCurrencyMapping S
ON T.ISOCurrencyCode = S.ISOCurrencyCode
WHEN NOT MATCHED BY TARGET
    THEN INSERT
             (ISOCurrencyCode, CurrencyCodeId)
         VALUES
             (S.ISOCurrencyCode, S.CurrencyCodeId)
WHEN MATCHED AND NOT EXISTS
                         (SELECT T.ISOCurrencyCode, T.CurrencyCodeId INTERSECT SELECT S.ISOCurrencyCode, S.CurrencyCodeId)
    THEN UPDATE SET T.ISOCurrencyCode = S.ISOCurrencyCode, T.CurrencyCodeId = S.CurrencyCodeId, T.ETLUpdatedDate = GETUTCDATE()
WHEN NOT MATCHED BY SOURCE
    THEN UPDATE SET T.ETLUpdatedDate = GETUTCDATE(), T.IsDeleted = 1;

/* Mapping between Panel Members and InsurerSubscription */
DROP TABLE IF EXISTS #PanelMemberMapping;
GO

CREATE TABLE #PanelMemberMapping (
    PanelMemberId         INT NOT NULL
  , PanelId               INT NOT NULL
  , insurerSubscriptionId INT NOT NULL
  , SubscriptionTier      INT NOT NULL,
);

INSERT INTO #PanelMemberMapping
    (PanelMemberId, PanelId, insurerSubscriptionId, SubscriptionTier)
VALUES

    --SpecieMar - Fine Art - Tier 2
    (228, 39, 1, 2) --Hamilton Insurance
  , (229, 39, 4, 4) --Travelers
  , (390, 39, 44, 2) --Aviva
  , (419, 39, 45, 2) --Hartford
  , (424, 39, 28, 2) --Markel
  , (964, 39, 907, 2) --Chubb

  --SpecieMar - Fine Art - Tier 3
  , (230, 40, 10, 3) --Aegis
  , (231, 40, 15, 3) --Argenta
  , (232, 40, 21, 3) --Brit
  , (233, 40, 12, 3) --Canopius
  , (234, 40, 43, 3) --Ki
  , (235, 40, 31, 3) --Zurich

  --SpecieMar - Jewellers Block - Tier 2
  , (241, 42, 21, 2) --Brit

  --SpecieMar - Jewellers Block - Tier 3
  , (242, 43, 10, 3) --Aegis
  , (243, 43, 15, 3) --Argenta
  , (244, 43, 12, 3) --Canopius
  , (245, 43, 43, 3) --Ki

  --SpecieMar - Cash In Transit - Tier 3
  , (223, 38, 10, 3) --Aegis
  , (224, 38, 15, 3) --Argenta
  , (225, 38, 21, 3) --Brit
  , (226, 38, 43, 3) --Ki
  , (227, 38, 31, 3) --Zurich

  --SpecieMar - General Specie - Tier 3
  , (236, 41, 10, 3) --Aegis
  , (237, 41, 15, 3) --Argenta
  , (238, 41, 21, 3) --Brit
  , (239, 41, 43, 3) --Ki
  , (240, 41, 31, 3); --Zurich

MERGE INTO MarineMar.SpeciePanelMemberMapping T
USING
    (SELECT PanelMemberId, PanelId, MarketKey = 'PMEM|' + CAST(PanelMemberId AS NVARCHAR(10)), insurerSubscriptionId, SubscriptionTier
     FROM
         #PanelMemberMapping) S
ON T.PanelMemberId = S.PanelMemberId
WHEN NOT MATCHED BY TARGET
    THEN INSERT
             (PanelMemberId, PanelId, MarketKey, insurerSubscriptionId, SubscriptionTier)
         VALUES
             (S.PanelMemberId, S.PanelId, S.MarketKey, S.insurerSubscriptionId, S.SubscriptionTier)
WHEN MATCHED AND NOT EXISTS
                         (SELECT T.PanelMemberId, T.PanelId, T.MarketKey, T.InsurerSubscriptionId, T.SubscriptionTier
                          INTERSECT
                          SELECT S.PanelMemberId, S.PanelId, S.MarketKey, S.InsurerSubscriptionId, S.SubscriptionTier)
    THEN UPDATE SET T.PanelMemberId = S.PanelMemberId, T.PanelId = S.PanelId, T.MarketKey = S.MarketKey, T.InsurerSubscriptionId = S.insurerSubscriptionId, T.SubscriptionTier = S.SubscriptionTier, T.ETLUpdatedDate = GETUTCDATE()
WHEN NOT MATCHED BY SOURCE
    THEN UPDATE SET T.ETLUpdatedDate = GETUTCDATE(), T.IsDeleted = 1;
