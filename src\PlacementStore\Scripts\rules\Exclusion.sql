/*
--------------------------------------------------------------------------------------
    exclusion rules
--------------------------------------------------------------------------------------
*/
DECLARE @ExclusionRules TABLE (
    ruleid                        INT            NOT NULL
  , ruletypeid                    INT            NOT NULL
  , rulekey                       NVARCHAR(50)   NOT NULL
  , ruleversion                   INT            NOT NULL
  , rulename                      NVARCHAR(255)  NOT NULL
  , ruledescription               NVARCHAR(4000) NULL
  , enabled                       BIT            NULL
  , [order]                       INT            NULL
  , datasourceinstanceid          INT            NULL
  , wheresql                      NVARCHAR(MAX)  NULL
  , joinsql                       NVARCHAR(MAX)  NULL
  , idsql                         NVARCHAR(MAX)  NULL
  , datesql                       NVARCHAR(MAX)  NULL
  , placementdatasourceinstanceid INT            NULL
  , parentruleid                  INT            NULL
  , enabledDev                    BIT            NULL
  , enabledQA                     BIT            NULL
  , enabledIAT                    BIT            NULL
  , enabledProd                   BIT            NULL
  , TeamID                        INT            NULL
  , ResultID                      INT            NULL
  , UsePlacementValueYr2          BIT
        DEFAULT 0
);

/* start of Exclusion rules */
INSERT @ExclusionRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID)
VALUES
    (-1, 3, N'Eclipse_ExcludeFeeOnly', 1, N'Eclipse-Exclude Fee Only Policies', N'Eclipse Excluding Fee Only Policies', 1, 1, 1, 1, 1, 50000, N'PM.PartyName LIKE ''%Fee Only U/W%'' AND pms.MarketCount = 1 AND CONCAT(OrgLevel1,''-'',OrgLevel2,''-'',OrgLevel3,''-'',OrgLevel4,''-'',OrgLevel5) NOT IN (''Facultative-Faber WL-Ownership-Casualty-F C - Europe'',''Facultative-Faber WL-Ownership-Casualty-F C - Latam/Carribean'',''Facultative-Faber WL-Ownership-Casualty-F C - MEA'',''Facultative-Faber WL-Ownership-Casualty-F C - North America'',''Facultative-Faber WL-Ownership-Casualty-F C - UK'',''Facultative-Faber WL-Ownership-Property-F P - Asia Pac'',''Facultative-Faber WL-Ownership-Property-F P - Australasia'',''Facultative-Faber WL-Ownership-Property-F P - Europe'',''Facultative-Faber WL-Ownership-Property-F P - Latam/Carribean'',''Facultative-Faber WL-Ownership-Property-F P - MEA'',''Facultative-Faber WL-Ownership-Property-F P - North America'',''Facultative-Faber WL-Ownership-Property-F P - UK'',''Facultative-Faber WL-Ownership-Management-Retro'',''Willis Towers Watson SA NV-Faber WL-Ownership-Casualty-F C - Europe'',''Willis Towers Watson SA NV-Faber WL-Ownership-Property-F P - Europe'',''Willis Towers Watson SA NV-Faber WL-Ownership-Property-F P - Latam/Carribean'',''Willis Towers Watson SA NV-Faber WL-Ownership-Property-F P - North America'')', N'LEFT JOIN dbo.PolicyMarket AS PM WITH (NOLOCK) ON PM.PolicyId = Pol.PolicyId AND PM.IsDeleted = 0 LEFT JOIN (SELECT PolicyID, COUNT(distinct PartyKey) AS MarketCount FROM dbo.PolicyMarket WHERE PartyKey IS NOT NULL AND IsDeleted = 0 GROUP BY PolicyID) PMS	ON pol.PolicyID = PMS.PolicyID', N'MIN(CONVERT(NVARCHAR(50), ISNULL(DSI.DataSourceInstanceName,''None'')) + N''/''+ CONVERT(NVARCHAR(50), pol.[PolicyID]) + N''/''+ CONVERT(NVARCHAR(8), pol.[InceptionDate], 112))', NULL, 50366, NULL, NULL);

INSERT @ExclusionRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID)
VALUES
    (-2, 3, N'Eclipse_ExcludeBonds', 1, N'Eclipse-Exclude Bond Products', N'Eclipse Excluding Policies with Bond Products', 1, 1, 1, 1, 2, 50000, N'(PROD.SourceProductName in (''Bond'',''Bond.'',''Bond SG''))', N'LEFT JOIN dbo.PolicySection AS POLS WITH (nolock) ON POLS.policyid = Pol.policyid and POLS.isdeleted = 0  LEFT JOIN dbo.PolicySectionProduct AS PSP WITH (nolock) ON PSP.PolicySectionID = POLS.PolicySectionID AND PSP.IsDeleted = 0 LEFT JOIN dbo.Product AS PROD WITH (nolock) ON PROD.ProductID = PSP.ProductID and Prod.IsDeleted = 0', N'Min(CONVERT(NVARCHAR(50), ISNULL(DSI.DataSourceInstanceName,''None'')) + N''/''+ CONVERT(NVARCHAR(50), pol.[PolicyID]) + N''/''+ CONVERT(NVARCHAR(8), pol.[InceptionDate], 112))', NULL, 50366, NULL, NULL);

INSERT @ExclusionRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID)
VALUES
    (-3, 3, N'Eclipse_ExcludeEngineeringInspection', 1, N'Eclipse-Exclude Engineering Inspection Policies', N'Eclipse Excluding Policies with Engineering Inspection Products', 1, 1, 1, 1, 3, 50000, N'(PROD.SourceProductName  in (''Engineering Inspec'',''Eng. Insp'',''Eng Inspect''))', N'LEFT JOIN dbo.PolicySection AS POLS WITH (nolock) ON POLS.policyid = Pol.policyid and POLS.isdeleted = 0  LEFT JOIN dbo.PolicySectionProduct AS PSP WITH (nolock) ON PSP.PolicySectionID = POLS.PolicySectionID AND PSP.IsDeleted = 0 LEFT JOIN dbo.Product AS PROD WITH (nolock) ON PROD.ProductID = PSP.ProductID and Prod.IsDeleted = 0', N'Min(CONVERT(NVARCHAR(50), ISNULL(DSI.DataSourceInstanceName,''None'')) + N''/''+ CONVERT(NVARCHAR(50), pol.[PolicyID]) + N''/''+ CONVERT(NVARCHAR(8), pol.[InceptionDate], 112))', NULL, 50366, NULL, NULL);

INSERT @ExclusionRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID)
VALUES
    (-4, 3, N'Eclipse_ExcludeReverseFlow', 1, N'Eclipse-Exclude Reverse Flow Policies', N'Eclipse Excluding Policies which are Reverse Flow', 1, 1, 1, 1, 4, 50000, N'(ISNULL (PROD.ProductLine, ''test'')  in(''Trans Reverse Flow'', ''Reverse Flow''))', N'LEFT JOIN dbo.PolicySection AS POLS WITH (nolock) ON POLS.policyid = Pol.policyid and POLS.isdeleted = 0  LEFT JOIN dbo.PolicySectionProduct AS PSP WITH (nolock) ON PSP.PolicySectionID = POLS.PolicySectionID AND PSP.IsDeleted = 0 LEFT JOIN dbo.Product AS PROD WITH (nolock) ON PROD.ProductID = PSP.ProductID and Prod.IsDeleted = 0', N'Min(CONVERT(NVARCHAR(50), ISNULL(DSI.DataSourceInstanceName,''None'')) + N''/''+ CONVERT(NVARCHAR(50), pol.[PolicyID]) + N''/''+ CONVERT(NVARCHAR(8), pol.[InceptionDate], 112))', NULL, 50366, NULL, NULL);

INSERT @ExclusionRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID)
VALUES
    (-5, 3, N'eGlobal_Australia_IsCurrent', 1, N'eGlobal_Australia_IsCurrent Policies', N'eGlobal_Australia_IsCurrent Excluding Policies which are not the current version', 0, 0, 0, 0, 5, 50004, N'pol.PolicyAttributes.value(''(/Attributes/IsCurrent)[1]'',''nvarchar(max)'') = ''0''', NULL, N'Min(CONVERT(NVARCHAR(50), ISNULL(DSI.DataSourceInstanceName,''None'')) + N''/''+ CONVERT(NVARCHAR(50), pol.[PolicyID]) + N''/''+ CONVERT(NVARCHAR(8), pol.[InceptionDate], 112))', NULL, 50366, NULL, NULL);

INSERT @ExclusionRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID)
VALUES
    (-6, 3, N'eGlobal_Colombia_IsCurrent', 1, N'eGlobal_Colombia_IsCurrent Policies', N'eGlobal_Colombia_IsCurrent Excluding Policies which are not the current version', 0, 0, 0, 0, 6, 50020, N'pol.PolicyAttributes.value(''(/Attributes/IsCurrent)[1]'',''nvarchar(max)'') = ''0''', NULL, N'Min(CONVERT(NVARCHAR(50), ISNULL(DSI.DataSourceInstanceName,''None'')) + N''/''+ CONVERT(NVARCHAR(50), pol.[PolicyID]) + N''/''+ CONVERT(NVARCHAR(8), pol.[InceptionDate], 112))', NULL, 50366, NULL, NULL);

INSERT @ExclusionRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID)
VALUES
    (-7, 3, N'eGlobal_Netherlands_IsCurrent', 1, N'eGlobal_Netherlands_IsCurrent Policies', N'eGlobal_Netherlands_IsCurrent Excluding Policies which are not the current version', 0, 0, 0, 0, 7, 50010, N'pol.PolicyAttributes.value(''(/Attributes/IsCurrent)[1]'',''nvarchar(max)'') = ''0''', NULL, N'Min(CONVERT(NVARCHAR(50), ISNULL(DSI.DataSourceInstanceName,''None'')) + N''/''+ CONVERT(NVARCHAR(50), pol.[PolicyID]) + N''/''+ CONVERT(NVARCHAR(8), pol.[InceptionDate], 112))', NULL, 50366, NULL, NULL);

INSERT @ExclusionRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID)
VALUES
    (-8, 3, N'NA_Divested_Business', 1, N'eNA_Divested_Business Policies', N'NA_Divested_Business Excluding Policies which have been sold to another organisation', 1, 1, 1, 1, 8, 50001, N'pol.[IsPrimaryParty] = 1 	AND EpicCategoryCode.[ObjectType] = ''Sold Business''', N'INNER JOIN dbo.PolicySection AS PS ON PS.PolicyID = Pol.PolicyID AND PS.SourceQueryID = 3684 INNER JOIN dbo.PolicySectionAttribute AS EpicCategoryCode ON EpicCategoryCode.PolicySectionID = PS.PolicySectionID AND EpicCategoryCode.IsDeleted = 0 AND EpicCategoryCode.DataSourceInstanceID = PS.DataSourceInstanceID', N'Min(CONVERT(NVARCHAR(50), ISNULL(DSI.DataSourceInstanceName,''None'')) + N''/''+ CONVERT(NVARCHAR(50), pol.[PolicyID]) + N''/''+ CONVERT(NVARCHAR(8), pol.[InceptionDate], 112))', NULL, 50366, NULL, NULL);

INSERT @ExclusionRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID)
VALUES
    (-9, 3, N'eGlobal_AustraliaAffinityInsolvencyPrivateClients', 1, N'eGlobal_Australia_Affinity_Insolvency_PrivateClients Policies', N'Excluding Policies associated to Affinity_Insolvency_PrivateClients ', 1, 1, 1, 1, 9, 50004, N'P.Department in (''AAF'',''BAF'',''HAF'',''MAF'',''PAF'',''SAF'',''AIF'',''BIF'',''MIF'',''PIF'',''SIF'',''MPC'')', N'LEFT JOIN [Policy] p ON pol.PolicyID = p.PolicyID', N'Min(CONVERT(NVARCHAR(50), ISNULL(DSI.DataSourceInstanceName,''None'')) + N''/''+ CONVERT(NVARCHAR(50), pol.[PolicyID]) + N''/''+ CONVERT(NVARCHAR(8), pol.[InceptionDate], 112))', NULL, 50366, NULL, NULL);

INSERT @ExclusionRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID)
VALUES
    (-10, 3, N'eGlobal_SouthAfrica_IsCurrent', 1, N'eGlobal_SouthAfrica_IsCurrent Policies', N'eGlobal_SouthAfrica_IsCurrent Excluding Policies which are not the current version', 0, 0, 0, 0, 10, 50015, N'pol.PolicyAttributes.value(''(/Attributes/IsCurrent)[1]'',''nvarchar(max)'') = ''0''', NULL, N'Min(CONVERT(NVARCHAR(50), ISNULL(DSI.DataSourceInstanceName,''None'')) + N''/''+ CONVERT(NVARCHAR(50), pol.[PolicyID]) + N''/''+ CONVERT(NVARCHAR(8), pol.[InceptionDate], 112))', NULL, 50366, NULL, NULL);

INSERT @ExclusionRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID)
VALUES
    (-11, 3, N'eGlobal_SouthAfrica', 1, N'eGlobal_SouthAfrica_FEE_SASRIA_GRMS Policies', N'Excluding Policies associated to Fee_SASRIA_GRMS', 1, 1, 1, 1, 11, 50015, N'((substring(pol1.ply_branch, CHARINDEX(''|'', pol1.ply_branch) + 1, LEN(pol1.ply_branch)) = ''GRM'') OR (pa.eGlobalRI_ABBRNAME like ''%FEE%'') OR (pa.eGlobalRI_ABBRNAME like ''%SASRIA%''))', N'LEFT JOIN [policy] pol1 on pol.Policyid = pol1.PolicyID LEFT JOIN dbo.PolicySection AS POLS WITH (nolock) ON POLS.policyid = Pol.policyid and POLS.isdeleted = 0  LEFT JOIN dbo.PolicySectionProduct AS PSP WITH (nolock) ON PSP.PolicySectionID = POLS.PolicySectionID AND PSP.IsDeleted = 0 LEFT JOIN dbo.Product AS PROD WITH (nolock) ON PROD.ProductID = PSP.ProductID and Prod.IsDeleted = 0
    LEFT JOIN PAS.Product pasp ON pasp.PASProductId = PROD.SourceProductId AND pasp.DataSourceInstanceId = PROD.DataSourceInstanceId
    LEFT JOIN PAS.ProductAttribute pa ON pa.ProductKey = pasp.ProductKey AND pa.DataSourceInstanceId = pasp.DataSourceInstanceId
		AND pa.IsDeleted = 0', N'Min(CONVERT(NVARCHAR(50), ISNULL(DSI.DataSourceInstanceName,''None'')) + N''/''+ CONVERT(NVARCHAR(50), pol.[PolicyID]) + N''/''+ CONVERT(NVARCHAR(8), pol.[InceptionDate], 112))', NULL, 50366, NULL, NULL);

INSERT @ExclusionRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID)
VALUES
    (-12, 3, N'Eclipse_InsolvencyPlacements', 1, N'Eclipse-Exclude Insolvency Placements', N'Eclipse Excluding Insolvency Placements', 1, 1, 1, 1, 12, 50000, N'(pol.OrgLevel1 IN (''Willis Limited'',''Willis Towers Watson SA NV'') AND pol.OrgLevel2 = ''Insolvency'')', NULL, N'Min(CONVERT(NVARCHAR(50), ISNULL(DSI.DataSourceInstanceName,''None'')) + N''/''+ CONVERT(NVARCHAR(50), pol.[PolicyID]) + N''/''+ CONVERT(NVARCHAR(8), pol.[InceptionDate], 112))', NULL, 50366, NULL, NULL);

INSERT @ExclusionRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID)
VALUES
    (-13, 3, N'Eclipse_WCNA_Retail', 1, N'Eclipse-Exclude WCNA Retail', N'Eclipse Excluding WCNA Retail', 1, 1, 1, 1, 13, 50000, N'(pol.OrgLevel1 IN (''Willis Limited'',''Willis Towers Watson SA NV'') AND pol.OrgLevel2 = ''Retail Network'' AND pol.OrgLevel4 IN (''WCNA'',''WCN''))', NULL, N'Min(CONVERT(NVARCHAR(50), ISNULL(DSI.DataSourceInstanceName,''None'')) + N''/''+ CONVERT(NVARCHAR(50), pol.[PolicyID]) + N''/''+ CONVERT(NVARCHAR(8), pol.[InceptionDate], 112))', NULL, 50366, NULL, NULL);

INSERT @ExclusionRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID)
VALUES
    (-14, 3, N'Eclipse_Non-IBA', 1, N'Eclipse-Exclude Non-IBA', N'Eclipse Excluding Non-IBA', 1, 1, 1, 1, 14, 50000, N'(pol.OrgLevel1 IN (''Willis Non-IBA''))', NULL, N'Min(CONVERT(NVARCHAR(50), ISNULL(DSI.DataSourceInstanceName,''None'')) + N''/''+ CONVERT(NVARCHAR(50), pol.[PolicyID]) + N''/''+ CONVERT(NVARCHAR(8), pol.[InceptionDate], 112))', NULL, 50366, NULL, NULL);

INSERT @ExclusionRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID)
VALUES
    (-15, 3, N'EclipseGB_Willis_Re', 1, N'Eclipse-Exclude Willis Re', N'Eclipse Excluding Willis Re', 1, 1, 1, 1, 15, 50000, N'(pol.OrgLevel1 IN (''Willis Limited'',''Willis Towers Watson SA NV'') AND pol.OrgLevel2 IN (''Willis Re Specialty'',''Willis Re NAUK'',''Willis Re International''))', NULL, N'Min(CONVERT(NVARCHAR(50), ISNULL(DSI.DataSourceInstanceName,''None'')) + N''/''+ CONVERT(NVARCHAR(50), pol.[PolicyID]) + N''/''+ CONVERT(NVARCHAR(8), pol.[InceptionDate], 112))', NULL, 50366, NULL, NULL);

INSERT @ExclusionRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID)
VALUES
    (-16, 3, N'eGlobal_HongKong', 1, N'eGlobal_HongKong_Exclude_FEE_Products', N'Excluding Policies with product names like Fee', 1, 1, 1, 1, 16, 50007, N'prod.SourceProductName like ''%FEE%''', N'LEFT JOIN [policy] pol1 on pol.Policyid = pol1.PolicyID LEFT JOIN dbo.PolicySection AS POLS WITH (nolock) ON POLS.policyid = Pol.policyid and POLS.isdeleted = 0  LEFT JOIN dbo.PolicySectionProduct AS PSP WITH (nolock) ON PSP.PolicySectionID = POLS.PolicySectionID AND PSP.IsDeleted = 0 LEFT JOIN dbo.Product AS PROD WITH (nolock) ON PROD.ProductID = PSP.ProductID and Prod.IsDeleted = 0', N'Min(CONVERT(NVARCHAR(50), ISNULL(DSI.DataSourceInstanceName,''None'')) + N''/''+ CONVERT(NVARCHAR(50), pol.[PolicyID]) + N''/''+ CONVERT(NVARCHAR(8), pol.[InceptionDate], 112))', NULL, 50366, NULL, NULL);

INSERT @ExclusionRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID)
VALUES
    (-17, 3, N'eGlobal_HongKong', 1, N'eGlobal_HongKong_Exclude_Individuals', N'Excluding Policies with Individual Clients', 1, 1, 1, 1, 17, 50007, N'PTY.IsIndividual = 1', N'LEFT JOIN dbo.PolicyPartyRelationship PPR ON PPR.PolicyID = POL.PolicyID AND PPR.IsDeleted = 0
          INNER JOIN ref.PartyRole PRL ON PRL.PartyRoleID = PPR.PartyRoleID AND PRL.GlobalPartyRoleID = 100 /*CLIENT*/ AND PRL.IsDeprecated = 0', N'Min(CONVERT(NVARCHAR(50), ISNULL(DSI.DataSourceInstanceName,''None'')) + N''/''+ CONVERT(NVARCHAR(50), pol.[PolicyID]) + N''/''+ CONVERT(NVARCHAR(8), pol.[InceptionDate], 112))', NULL, 50366, NULL, NULL);

INSERT @ExclusionRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID)
VALUES
    (-18, 3, N'eGlobal_HongKong', 1, N'eGlobal_HongKong_Exclude_Various_Policies', N'Excluding Global Policies, T1 & HKCA Facility Policies and Minibus Program Policies', 1, 1, 1, 1, 18, 50007, N'W.WorkerName in (''Global Policy'',''T1 Facility'',''HKCA'',''Minibus Program'',''Insolvency Program'')', N'LEFT JOIN dbo.PolicyWorker PW ON PW.PolicyID = POL.PolicyID and PW.IsDeleted = 0
          LEFT JOIN PS.Worker PSW ON PSW.WorkerSK = PW.WorkerId LEFT JOIN PAS.Worker W ON W.WorkerKey = PSW.WorkerKey AND W.DataSourceInstanceId = PSW.DataSourceInstanceId AND W.IsDeleted = 0', N'Min(CONVERT(NVARCHAR(50), ISNULL(DSI.DataSourceInstanceName,''None'')) + N''/''+ CONVERT(NVARCHAR(50), pol.[PolicyID]) + N''/''+ CONVERT(NVARCHAR(8), pol.[InceptionDate], 112))', NULL, 50366, NULL, NULL);

INSERT @ExclusionRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID)
VALUES
    (-19, 3, N'ASYS_Germany', 1, N'ASYS_Germany_Exclude_Individuals', N'Excluding Policies with Individual Clients', 1, 1, 1, 1, 19, 50029, N'PTY.IsIndividual = 1', N'LEFT JOIN dbo.PolicyPartyRelationship PPR ON PPR.PolicyID = POL.PolicyID AND PPR.IsDeleted = 0
          INNER JOIN ref.PartyRole PRL ON PRL.PartyRoleID = PPR.PartyRoleID AND PRL.GlobalPartyRoleID = 100 /*CLIENT*/ AND PRL.IsDeprecated = 0', N'Min(CONVERT(NVARCHAR(50), ISNULL(DSI.DataSourceInstanceName,''None'')) + N''/''+ CONVERT(NVARCHAR(50), pol.[PolicyID]) + N''/''+ CONVERT(NVARCHAR(8), pol.[InceptionDate], 112))', NULL, 50366, NULL, NULL);

INSERT @ExclusionRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID)
VALUES
    (-20, 3, N'Inbroker_Argentina', 1, N'Inbroker_Argentina_Affinity', N'Excluding Policies associated to Affinity Organisation', 1, 1, 1, 1, 20, 50035, N'ORG.OrganisationKey = ''Affinity''', NULL, N'Min(CONVERT(NVARCHAR(50), ISNULL(DSI.DataSourceInstanceName,''None'')) + N''/''+ CONVERT(NVARCHAR(50), pol.[PolicyID]) + N''/''+ CONVERT(NVARCHAR(8), pol.[InceptionDate], 112))', NULL, 50366, NULL, NULL);

INSERT @ExclusionRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID)
VALUES
    (-21, 3, N'Broker.Net_Ireland', 1, N'Broker.Net_Ireland_Exclude_Orgs', N'Excluding Policies linked to Organisations', 1, 1, 1, 1, 21, 50358, N'ORG.OrganisationKey in (''12'',''BP'',''CL'',''CR'', ''T5'',''TW'')', NULL, N'Min(CONVERT(NVARCHAR(50), ISNULL(DSI.DataSourceInstanceName,''None'')) + N''/''+ CONVERT(NVARCHAR(50), pol.[PolicyID]) + N''/''+ CONVERT(NVARCHAR(8), pol.[InceptionDate], 112))', NULL, 50366, NULL, NULL);

INSERT @ExclusionRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID)
VALUES
    (-22, 3, N'Broker.Net_Ireland', 1, N'Broker.Net_Ireland_Fees', N'Excluding Policies with Fee Products', 1, 1, 1, 1, 22, 50358, N'PROD.ProductKey in (''CF'',''FE'',''LM'',''RM'')', N'LEFT JOIN [policy] pol1 on pol.Policyid = pol1.PolicyID LEFT JOIN dbo.PolicySection AS POLS WITH (nolock) ON POLS.policyid = Pol.policyid and POLS.isdeleted = 0  LEFT JOIN dbo.PolicySectionProduct AS PSP WITH (nolock) ON PSP.PolicySectionID = POLS.PolicySectionID AND PSP.IsDeleted = 0 LEFT JOIN dbo.Product AS PROD WITH (nolock) ON PROD.ProductID = PSP.ProductID and Prod.IsDeleted = 0', N'Min(CONVERT(NVARCHAR(50), ISNULL(DSI.DataSourceInstanceName,''None'')) + N''/''+ CONVERT(NVARCHAR(50), pol.[PolicyID]) + N''/''+ CONVERT(NVARCHAR(8), pol.[InceptionDate], 112))', NULL, 50366, NULL, NULL);

INSERT @ExclusionRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID)
VALUES
    (-23, 3, N'Eclipse_REPDirectPolicys', 1, N'Eclipse-Exclude REP Direct Policies', N'Eclipse Exclude REP Direct Policies', 1, 1, 1, 1, 12, 50000, N'(pol.OrgLevel1 IN (''Willis Limited'',''Willis Towers Watson SA NV'') AND pol.OrgLevel2 = ''WREP'' AND PROD.productclass = ''REP Direct'')', N'  LEFT JOIN dbo.policysection AS POLS WITH (nolock)
              ON POLS.policyid = pol.policyid
                 AND POLS.isdeleted = 0
       LEFT JOIN dbo.policysectionproduct AS PSP WITH (nolock)
              ON PSP.policysectionid = POLS.policysectionid
                 AND PSP.isdeleted = 0
       LEFT JOIN dbo.product AS PROD WITH (nolock)
              ON PROD.productid = PSP.productid
                 AND PROD.datasourceinstanceid = pol.datasourceinstanceid
                 AND PROD.isdeleted = 0 ', N'Min(CONVERT(NVARCHAR(50), ISNULL(DSI.DataSourceInstanceName,''None'')) + N''/''+ CONVERT(NVARCHAR(50), pol.[PolicyID]) + N''/''+ CONVERT(NVARCHAR(8), pol.[InceptionDate], 112))', NULL, 50366, NULL, NULL);

INSERT @ExclusionRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID)
VALUES
    (-24, 3, N'Visual_Seg_Spain', 1, N'Visual_Seg_Spain_Exclude_Affinity_Segment', N'Excluding Policies linked to Affinity Organisations', 0, 0, 0, 0, 24, 50044, N'BU.BusinessUnit LIKE ''CRB - AFFINIITY%''', N'INNER JOIN (
				SELECT
				LTRIM(RTRIM(REVERSE(PARSENAME(REPLACE(REVERSE(REPLACE(org.Organisation,''.'','''')), ''|'', ''.''), 2)))) AS BusinessUnit,
				POL.PolicyID,
				ISNULL(ORG.OrganisationSK, PLORG.OrganisationId) OrganisationId
				FROM dbo.[Policy] POL
					LEFT JOIN dbo.PolicyOrganisation PORG
						ON POL.PolicyID = PORG.PolicyId
						AND PORG.OrganisationRoleID < 100000
						AND PORG.DataSourceInstanceID = 50044
                        AND PORG.IsDeleted = 0
					LEFT JOIN dbo.PolicyOrganisation PLORG
						ON POL.PolicyID = PLORG.PolicyId
						AND PLORG.OrganisationRoleID >= 100000
						AND PLORG.DataSourceInstanceID = 50044
                        AND PLORG.IsDeleted = 0
					LEFT JOIN PS.Organisation AS ORG
						ON ISNULL(PORG.OrganisationId, PLORG.OrganisationId) = ORG.OrganisationSK
						AND ORG.IsDeleted = 0
						AND ORG.DataSourceInstanceID = 50044
				) BU
					ON BU.PolicyID = pol.PolicyID', N'Min(CONVERT(NVARCHAR(50), ISNULL(DSI.DataSourceInstanceName,''None'')) + N''/''+ CONVERT(NVARCHAR(50), pol.[PolicyID]) + N''/''+ CONVERT(NVARCHAR(8), pol.[InceptionDate], 112))', NULL, 50366, NULL, NULL);

INSERT @ExclusionRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID)
VALUES
    (-25, 3, N'Visual_Seg_Spain', 1, N'Visual_Seg_Spain_CRB OTHER_Products', N'Excluding Policies with OOS Products in CRB - OTHERS', 0, 0, 0, 0, 25, 50044, N'BU.BusinessUnit LIKE ''CRB - OTHERS%'' AND LTRIM(substring(prod.SourceProductName, CHARINDEX(''|'', prod.SourceProductName) + 1, LEN(prod.SourceProductName))) in (''AFFINITY'',''RECLAMACIONES - SINIESTROS NO INTERMEDIADOS'',''ACCIDENTES'',''SALUD'',''VIDA'',''ASISTENCIA EN VIAJE'',''AHORRO'',''SGC.NO USAR'',''GARANTÍA MECÁNICA'',''CONTINGENCIAS'')', N'INNER JOIN (
				SELECT
				LTRIM(RTRIM(REVERSE(PARSENAME(REPLACE(REVERSE(REPLACE(org.Organisation,''.'','''')), ''|'', ''.''), 2)))) AS BusinessUnit,
				POL.PolicyID,
				ISNULL(ORG.OrganisationSK, PLORG.OrganisationId) OrganisationId
				FROM dbo.[Policy] POL
					LEFT JOIN dbo.PolicyOrganisation PORG
						ON POL.PolicyID = PORG.PolicyId
						AND PORG.OrganisationRoleID < 100000
						AND PORG.DataSourceInstanceID = 50044
                        AND PORG.IsDeleted = 0
					LEFT JOIN dbo.PolicyOrganisation PLORG
						ON POL.PolicyID = PLORG.PolicyId
						AND PLORG.OrganisationRoleID >= 100000
						AND PLORG.DataSourceInstanceID = 50044
                        AND PLORG.IsDeleted = 0
					LEFT JOIN PS.Organisation AS ORG
						ON ISNULL(PORG.OrganisationId, PLORG.OrganisationId) = ORG.OrganisationSK
						AND ORG.IsDeleted = 0
						AND ORG.DataSourceInstanceID = 50044
					  ) BU
						ON BU.PolicyID = pol.PolicyID
		  LEFT JOIN [policy] pol1
			on pol.Policyid = pol1.PolicyID
		  LEFT JOIN dbo.PolicySection AS POLS WITH (nolock)
			ON POLS.policyid = Pol.policyid
			and POLS.isdeleted = 0
		  LEFT JOIN dbo.PolicySectionProduct AS PSP WITH (nolock)
			ON PSP.PolicySectionID = POLS.PolicySectionID
			AND PSP.IsDeleted = 0
		  LEFT JOIN dbo.Product AS PROD WITH (nolock)
			ON PROD.ProductID = PSP.ProductID
			and Prod.IsDeleted = 0', N'Min(CONVERT(NVARCHAR(50), ISNULL(DSI.DataSourceInstanceName,''None'')) + N''/''+ CONVERT(NVARCHAR(50), pol.[PolicyID]) + N''/''+ CONVERT(NVARCHAR(8), pol.[InceptionDate], 112))', NULL, 50366, NULL, NULL);

INSERT @ExclusionRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID)
VALUES
    (-26, 3, N'Visual_Seg_Spain', 1, N'Visual_Seg_Spain_Exclude_Individuals', N'Excluding Policies with Individual Clients', 1, 1, 1, 1, 26, 50044, N'PTY.IsIndividual = 1', N'LEFT JOIN dbo.PolicyPartyRelationship PPR ON PPR.PolicyID = POL.PolicyID AND PPR.IsDeleted = 0
          INNER JOIN ref.PartyRole PRL ON PRL.PartyRoleID = PPR.PartyRoleID AND PRL.GlobalPartyRoleID = 100 /*CLIENT*/ AND PRL.IsDeprecated = 0', N'Min(CONVERT(NVARCHAR(50), ISNULL(DSI.DataSourceInstanceName,''None'')) + N''/''+ CONVERT(NVARCHAR(50), pol.[PolicyID]) + N''/''+ CONVERT(NVARCHAR(8), pol.[InceptionDate], 112))', NULL, 50366, NULL, NULL);

INSERT @ExclusionRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID)
VALUES
    (-27, 3, N'Inbroker_Argentina', 1, N'Inbroker_Argentina_Products', N'Excluding Policies with out of scope Products', 1, 1, 1, 1, 27, 50035, N'prod.SourceProductName in (''012|Accidentes Personales'',''012|Accidentes Personales(Affinidad)'',''012|Accidentes Personales(Corporativo)'',''016|Acc.  Riesgo Trabajo'',
		''016|Accidentes de Trabajo'',''016|Accidentes del Trabajo'',''016|Responsabilidad Civil Patronal'',''021|Caucion'',''025|Personal Domestico'',''025|Seguro Colectivo de Saldo Deudor'',
		''025|Seguro Incapacidad Colectivo'',''025|Seguro Incapacidad Individual'',''025|Seguro Retiro Individual'',''025|Sepelio'',''025|Vida Con Constitucion Reservas Matemat'',
		''025|Vida Sin Constitucion Reservas Matemat'',''025|Vida Colectivo Capital Uniforme'',''025|Vida Colectivio Convenio Mercantil'',''025|Vida Colectivio Convenio San Juan'',
		''025|Vida Collectivo Escala de Capitales'',''025|Vida Colectivo Ley Contrato de Trabajo'',''025|Vida Colectivo Multiplo de Sueldo'',''025|Vida Obligatorio (decreto 1567/74)'',
		''025|Vida Obligatorio (Trabajadores Rurales)'',''114|Accidentes A Pasajeros'',''114|Seguro de Salud'')', N'LEFT JOIN [policy] pol1 on pol.Policyid = pol1.PolicyID LEFT JOIN dbo.PolicySection AS POLS WITH (nolock) ON POLS.policyid = Pol.policyid and POLS.isdeleted = 0  LEFT JOIN dbo.PolicySectionProduct AS PSP WITH (nolock) ON PSP.PolicySectionID = POLS.PolicySectionID AND PSP.IsDeleted = 0 LEFT JOIN dbo.Product AS PROD WITH (nolock) ON PROD.ProductID = PSP.ProductID and Prod.IsDeleted = 0', N'Min(CONVERT(NVARCHAR(50), ISNULL(DSI.DataSourceInstanceName,''None'')) + N''/''+ CONVERT(NVARCHAR(50), pol.[PolicyID]) + N''/''+ CONVERT(NVARCHAR(8), pol.[InceptionDate], 112))', NULL, 50366, NULL, NULL);

INSERT @ExclusionRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID)
VALUES
    (-28, 3, N'Eclipse_CrossBU_and_Intercompany_Facilities', 1, N'Eclipse-Exclude CrossBU and Intercompany Facilities', N'Eclipse Excluding CrossBU and Intercompany Facilities', 1, 1, 1, 1, 28, 50000, N'(pol.OrgLevel1 IN (''Willis Limited'',''Willis Towers Watson SA NV'') AND pol.OrgLevel2 IN (''Cross-BU Facilities'',''Inter-Company Facilities Processing''))', NULL, N'Min(CONVERT(NVARCHAR(50), ISNULL(DSI.DataSourceInstanceName,''None'')) + N''/''+ CONVERT(NVARCHAR(50), pol.[PolicyID]) + N''/''+ CONVERT(NVARCHAR(8), pol.[InceptionDate], 112))', NULL, 50366, NULL, NULL);

INSERT @ExclusionRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID)
VALUES
    (-29, 3, N'eGlobal_China', 1, N'eGlobal_China_IsCurrent_Policies', N'IsCurrent Excluding Policies which are not the current version', 0, 0, 0, 0, 29, 50006, N'pol.PolicyAttributes.value(''(/Attributes/IsCurrent)[1]'',''nvarchar(max)'') = ''0''', NULL, N'Min(CONVERT(NVARCHAR(50), ISNULL(DSI.DataSourceInstanceName,''None'')) + N''/''+ CONVERT(NVARCHAR(50), pol.[PolicyID]) + N''/''+ CONVERT(NVARCHAR(8), pol.[InceptionDate], 112))', NULL, 50366, NULL, NULL);

INSERT @ExclusionRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID)
VALUES
    (-30, 3, N'eGlobal_China', 1, N'eGlobal_China_Exclude_Individuals', N'Excluding Policies with Individual Clients', 1, 1, 1, 1, 30, 50006, N'PTY.IsIndividual = 1', N'LEFT JOIN dbo.PolicyPartyRelationship PPR ON PPR.PolicyID = POL.PolicyID AND PPR.IsDeleted = 0
          INNER JOIN ref.PartyRole PRL ON PRL.PartyRoleID = PPR.PartyRoleID AND PRL.GlobalPartyRoleID = 100 /*CLIENT*/ AND PRL.IsDeprecated = 0', N'Min(CONVERT(NVARCHAR(50), ISNULL(DSI.DataSourceInstanceName,''None'')) + N''/''+ CONVERT(NVARCHAR(50), pol.[PolicyID]) + N''/''+ CONVERT(NVARCHAR(8), pol.[InceptionDate], 112))', NULL, 50366, NULL, NULL);

INSERT @ExclusionRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID)
VALUES
    (-31, 3, N'eGlobal_China', 1, N'eGlobal_China_Exclude_Fee', N'Excluding Policies with Fee named Products', 1, 1, 1, 1, 31, 50006, N'prod.SourceProductName like ''%FEE%''', N'LEFT JOIN [policy] pol1 on pol.Policyid = pol1.PolicyID LEFT JOIN dbo.PolicySection AS POLS WITH (nolock) ON POLS.policyid = Pol.policyid and POLS.isdeleted = 0  LEFT JOIN dbo.PolicySectionProduct AS PSP WITH (nolock) ON PSP.PolicySectionID = POLS.PolicySectionID AND PSP.IsDeleted = 0 LEFT JOIN dbo.Product AS PROD WITH (nolock) ON PROD.ProductID = PSP.ProductID and Prod.IsDeleted = 0', N'Min(CONVERT(NVARCHAR(50), ISNULL(DSI.DataSourceInstanceName,''None'')) + N''/''+ CONVERT(NVARCHAR(50), pol.[PolicyID]) + N''/''+ CONVERT(NVARCHAR(8), pol.[InceptionDate], 112))', NULL, 50366, NULL, NULL);

INSERT @ExclusionRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID)
VALUES
    (-32, 3, N'eGlobal_China', 1, N'eGlobal_China_Exclude_Departments', N'Excluding Policies with Policy Departments', 1, 1, 1, 1, 32, 50006, N'P.Department in (''CAP'',''CBD'',''CGP'',''CHD'',''CHQ'',''CLM'',''CCMP'',''COR'',''FAJ'',''GZ2'',''HAZ'',''HEF'',''HLJ'',''JIN'',''MGT'',''MMU'',''NA'',''RMN'',''SHH'',''SHT'',''SHX'',''TAL'',''TWN'',''WBC'',''XIA'')', N'LEFT JOIN [Policy] p ON pol.PolicyID = p.PolicyID', N'Min(CONVERT(NVARCHAR(50), ISNULL(DSI.DataSourceInstanceName,''None'')) + N''/''+ CONVERT(NVARCHAR(50), pol.[PolicyID]) + N''/''+ CONVERT(NVARCHAR(8), pol.[InceptionDate], 112))', NULL, 50366, NULL, NULL);

INSERT @ExclusionRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID)
VALUES
    (-33, 3, N'eGlobal_China', 1, N'eGlobal_China_Exclude_Affinity_Workers', N'Excluding Affinity Policies where not one of the inscope workers', 1, 1, 1, 1, 33, 50006, N'P.Department = ''AFF'' AND W.WorkerName not in (''Tommy Xie'',''Robert Shi'',''Frank Yan'',''Kelvin Zhong'',''Mo Li'',''Rong Xie'',''Doris Zhou'')', N'LEFT JOIN [Policy] p ON pol.PolicyID = p.PolicyID
		  LEFT JOIN dbo.PolicyWorker PW ON PW.PolicyID = POL.PolicyID and PW.IsDeleted = 0
          LEFT JOIN PS.Worker PSW ON PSW.WorkerSK = PW.WorkerId LEFT JOIN PAS.Worker W ON W.WorkerKey = PSW.WorkerKey AND W.DataSourceInstanceId = PSW.DataSourceInstanceId AND W.IsDeleted = 0', N'Min(CONVERT(NVARCHAR(50), ISNULL(DSI.DataSourceInstanceName,''None'')) + N''/''+ CONVERT(NVARCHAR(50), pol.[PolicyID]) + N''/''+ CONVERT(NVARCHAR(8), pol.[InceptionDate], 112))', NULL, 50366, NULL, NULL);

INSERT @ExclusionRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID)
VALUES
    (-34, 3, N'eGlobal_China', 1, N'eGlobal_China_Exclude_Wholesale_Workers', N'Excluding Wholesale Policies where not one of the inscope workers', 1, 1, 1, 1, 34, 50006, N'P.Department = ''RE2'' AND W.WorkerName not in (''Billy Bo'',''Lois Liu'',''Richard W Nie'',''Yingyi Tan'',''Jessica Zhang'',''Ivy Xu'')', N'LEFT JOIN [Policy] p ON pol.PolicyID = p.PolicyID
		  LEFT JOIN dbo.PolicyWorker PW ON PW.PolicyID = POL.PolicyID and PW.IsDeleted = 0
          LEFT JOIN PS.Worker PSW ON PSW.WorkerSK = PW.WorkerId LEFT JOIN PAS.Worker W ON W.WorkerKey = PSW.WorkerKey AND W.DataSourceInstanceId = PSW.DataSourceInstanceId AND W.IsDeleted = 0', N'Min(CONVERT(NVARCHAR(50), ISNULL(DSI.DataSourceInstanceName,''None'')) + N''/''+ CONVERT(NVARCHAR(50), pol.[PolicyID]) + N''/''+ CONVERT(NVARCHAR(8), pol.[InceptionDate], 112))', NULL, 50366, NULL, NULL);

INSERT @ExclusionRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID)
VALUES
    (-35, 3, N'NA_Capital_Wholesale', 1, N'NA_Capital_Wholesale Policies', N'NA_Capital_Wholesale Excluding Policies which have been sold to another organisation', 1, 1, 1, 1, 35, 50001, N'A.RelatedOrganisationCode IN (''SBT'', ''NHE'', ''MIP'', ''CTP'', ''LPI'', ''FRE'', ''WPO'', ''VRM'')', N'INNER JOIN dbo.PolicyOrganisation AS PO ON PO.PolicyID = Pol.PolicyID AND PO.IsDeleted = 0 LEFT JOIN PS.Organisation PSO ON PSO.OrganisationSK = PO.OrganisationId LEFT JOIN PS.OrganisationRelationship AS A	ON A.OrganisationKey = PSO.OrganisationKey AND A.DataSourceInstanceId = PSO.DataSourceInstanceId	AND A.IsDeleted = 0	AND A.OrganisationRelationshipType = ''Agency''', N'Min(CONVERT(NVARCHAR(50), ISNULL(DSI.DataSourceInstanceName,''None'')) + N''/''+ CONVERT(NVARCHAR(50), pol.[PolicyID]) + N''/''+ CONVERT(NVARCHAR(8), pol.[InceptionDate], 112))', NULL, 50366, NULL, NULL);

INSERT @ExclusionRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID)
VALUES
    (-36, 3, N'NA_Capital_Wholesale', 1, N'NA_Capital_Wholesale Policies', N'NA_Capital_Wholesale Excluding Policies which have been sold to another organisation', 1, 1, 1, 1, 36, 50001, N'A.RelatedOrganisationCode IN (''WNA'', ''WAA'')', N'INNER JOIN dbo.PolicyOrganisation AS PO ON PO.PolicyID = Pol.PolicyID AND PO.IsDeleted = 0 LEFT JOIN PS.Organisation PSO ON PSO.OrganisationSK = PO.OrganisationId LEFT JOIN PS.OrganisationRelationship AS A	ON A.OrganisationKey = PSO.OrganisationKey AND A.DataSourceInstanceId = PSO.DataSourceInstanceId	AND A.IsDeleted = 0	AND A.OrganisationRelationshipType = ''Agency''', N'Min(CONVERT(NVARCHAR(50), ISNULL(DSI.DataSourceInstanceName,''None'')) + N''/''+ CONVERT(NVARCHAR(50), pol.[PolicyID]) + N''/''+ CONVERT(NVARCHAR(8), pol.[InceptionDate], 112))', NULL, 50366, NULL, NULL);

INSERT @ExclusionRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID)
VALUES
    (-37, 3, N'NA_Capital_Wholesale', 1, N'NA_Capital_Wholesale Policies', N'NA_Capital_Wholesale Excluding Policies which have been sold to another organisation', 1, 1, 1, 1, 37, 50001, N'A.RelatedOrganisationCode IN (''BER'', ''CAY'', ''LTD'', ''BEM'', ''WRE'', ''VET'') ', N'INNER JOIN dbo.PolicyOrganisation AS PO ON PO.PolicyID = Pol.PolicyID AND PO.IsDeleted = 0 LEFT JOIN PS.Organisation PSO ON PSO.OrganisationSK = PO.OrganisationId LEFT JOIN PS.OrganisationRelationship AS A	ON A.OrganisationKey = PSO.OrganisationKey AND A.DataSourceInstanceId = PSO.DataSourceInstanceId	AND A.IsDeleted = 0	AND A.OrganisationRelationshipType = ''Agency''', N'Min(CONVERT(NVARCHAR(50), ISNULL(DSI.DataSourceInstanceName,''None'')) + N''/''+ CONVERT(NVARCHAR(50), pol.[PolicyID]) + N''/''+ CONVERT(NVARCHAR(8), pol.[InceptionDate], 112))', NULL, 50366, NULL, NULL);

INSERT @ExclusionRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID)
VALUES
    (-38, 3, N'Canada__Products', 1, N'Canada_Products', N'Canada Excluding Policies which have products which are out of scope', 1, 1, 1, 1, 38, 50354, N'PROD.ProductKey in (''CLCL'',''CFEE'',''FEXT'',''FHCP'',''FILC'',''FPLC'',''FREF'',''FSER'',''FSUR'',''HFEE'',''RFEE'',''UFEE'',''PACL'')', N'LEFT JOIN dbo.PolicySection AS POLS WITH (nolock) ON POLS.policyid = Pol.policyid and POLS.isdeleted = 0  LEFT JOIN dbo.PolicySectionProduct AS PSP WITH (nolock) ON PSP.PolicySectionID = POLS.PolicySectionID
          AND PSP.IsDeleted = 0 LEFT JOIN dbo.Product AS PROD WITH (nolock) ON PROD.ProductID = PSP.ProductID and Prod.IsDeleted = 0', N'Min(CONVERT(NVARCHAR(50), ISNULL(DSI.DataSourceInstanceName,''None'')) + N''/''+ CONVERT(NVARCHAR(50), pol.[PolicyID]) + N''/''+ CONVERT(NVARCHAR(8), pol.[InceptionDate], 112))', NULL, 50366, NULL, NULL);

INSERT @ExclusionRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID)
VALUES
    (-39, 3, N'Canada_IsIndividual', 1, N'Canada_IsIndividual', N'Canada Excluding Policies which have a client who is an Individual', 1, 1, 1, 1, 39, 50354, N'PTY.IsIndividual = 1', N'LEFT JOIN dbo.PolicyPartyRelationship PPR ON PPR.PolicyID = POL.PolicyID AND PPR.IsDeleted = 0
          INNER JOIN ref.PartyRole PRL ON PRL.PartyRoleID = PPR.PartyRoleID AND PRL.GlobalPartyRoleID = 100 /*CLIENT*/ AND PRL.IsDeprecated = 0', N'Min(CONVERT(NVARCHAR(50), ISNULL(DSI.DataSourceInstanceName,''None'')) + N''/''+ CONVERT(NVARCHAR(50), pol.[PolicyID]) + N''/''+ CONVERT(NVARCHAR(8), pol.[InceptionDate], 112))', NULL, 50366, NULL, NULL);

INSERT @ExclusionRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID)
VALUES
    (-40, 3, N'Canada_Branch', 1, N'Canada_Branch', N'Canada Excluding Policies associated with out of scope Branch', 1, 1, 1, 1, 40, 50354, N'ORB.RelatedOrganisationCode in (''FQB'',''HOF'')', N'LEFT JOIN (
						Select P.PolicyId, PLORG.OrganisationId OrganisationId, PLORG.OrganisationId porg, PLORG.OrganisationId plorg
						From dbo.Policy P
						LEFT JOIN dbo.PolicyOrganisation PLORG
							ON P.PolicyID = PLORG.PolicyId
							AND PLORG.IsDeleted = 0

					) PORG
						ON PORG.PolicyID = POL.PolicyID
        LEFT JOIN PS.Organisation PO ON PO.OrganisationSK = PORG.OrganisationID
		LEFT JOIN PS.OrganisationRelationship ORB
			ON ORB.OrganisationKey = PO.OrganisationKey
            AND ORB.DataSourceInstanceId = PO.DataSourceInstanceId
			AND ORB.IsDeleted = 0
			AND ORB.OrganisationRelationshipType = ''Branch''', N'Min(CONVERT(NVARCHAR(50), ISNULL(DSI.DataSourceInstanceName,''None'')) + N''/''+ CONVERT(NVARCHAR(50), pol.[PolicyID]) + N''/''+ CONVERT(NVARCHAR(8), pol.[InceptionDate], 112))', NULL, 50366, NULL, NULL);

INSERT @ExclusionRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID)
VALUES
    (-41, 3, N'Canada_Departments', 1, N'Canada_Departments', N'Canada Excluding Policies associated with out of scope Departments', 1, 1, 1, 1, 41, 50354, N'ORD.RelatedOrganisationCode in (''PL'',''ADM'',''FCC'',''BEN'',''TRA'')', N'LEFT JOIN (
						Select P.PolicyId,  PLORG.OrganisationId OrganisationId, PLORG.OrganisationId porg, PLORG.OrganisationId plorg
						From dbo.Policy P
						LEFT JOIN dbo.PolicyOrganisation PLORG
							ON P.PolicyID = PLORG.PolicyId
							AND PLORG.IsDeleted = 0
					) PORG
						ON PORG.PolicyID = POL.PolicyID
		LEFT JOIN PS.Organisation PO ON PO.OrganisationSK = PORG.OrganisationID
		LEFT JOIN PS.OrganisationRelationship ORD
			ON ORD.OrganisationKey = PO.OrganisationKey
            AND ORD.DataSourceInstanceId = PO.DataSourceInstanceId
			AND ORD.IsDeleted = 0
			AND ORD.OrganisationRelationshipType = ''Department''', N'Min(CONVERT(NVARCHAR(50), ISNULL(DSI.DataSourceInstanceName,''None'')) + N''/''+ CONVERT(NVARCHAR(50), pol.[PolicyID]) + N''/''+ CONVERT(NVARCHAR(8), pol.[InceptionDate], 112))', NULL, 50366, NULL, NULL);

INSERT @ExclusionRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID)
VALUES
    (-42, 3, N'Canada_Client', 1, N'Canada_Client', N'Canada Excluding Policies linked to a test client', 1, 1, 1, 1, 42, 50354, N'PTY.PACTPartyID = 19082052 /*Joe Test*/', N'LEFT JOIN dbo.PolicyPartyRelationship PPR ON PPR.PolicyID = POL.PolicyID AND PPR.IsDeleted = 0
          INNER JOIN ref.PartyRole PRL ON PRL.PartyRoleID = PPR.PartyRoleID AND PRL.GlobalPartyRoleID = 100 /*CLIENT*/ AND PRL.IsDeprecated = 0', N'Min(CONVERT(NVARCHAR(50), ISNULL(DSI.DataSourceInstanceName,''None'')) + N''/''+ CONVERT(NVARCHAR(50), pol.[PolicyID]) + N''/''+ CONVERT(NVARCHAR(8), pol.[InceptionDate], 112))', NULL, 50366, NULL, NULL);

INSERT @ExclusionRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID)
VALUES
    (-43, 3, N'Eclipse_FacilitySegmentation', 1, N'Eclipse-Exclude FacilityType Binder and Lineslip', N'Eclipse Excluding FacilityType Binder and Lineslip', 1, 1, 1, 1, 1, 50000, N'P.FacilityType IN (''Binder'',''LineSlip'')', N'LEFT JOIN dbo.Policy AS P WITH (NOLOCK) ON P.PolicyId = Pol.PolicyId AND P.IsDeleted = 0', N'MIN(CONVERT(NVARCHAR(50), ISNULL(DSI.DataSourceInstanceName,''None'')) + N''/''+ CONVERT(NVARCHAR(50), pol.[PolicyID]) + N''/''+ CONVERT(NVARCHAR(8), pol.[InceptionDate], 112))', NULL, 50366, NULL, NULL);

--Germany Exclusion Rule--
INSERT @ExclusionRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID)
VALUES
    (-44, 3, N'ASYS_Germany', 1, N'ASYS_Germany_Exclude_Organisations', N'Excluding Policies with out of scope Organisations', 0, 0, 0, 0, 44, 50029, N'POLORG.OrganisationKey NOT IN (''10309501'',''10409501'',''10509501'',''10609501'',''10709501'',''10052120'',''10052130'',''10601020'',''10601030'',''10052321'',''10052322'',''10052323'')', N'INNER JOIN
          (
            SELECT
            POL.PolicyID,
            ISNULL(ORG.OrganisationId, PLORG.OrganisationId) OrganisationId,
			ORG.OrganisationKey
            FROM
                dbo.[Policy] POL
                LEFT JOIN dbo.PolicyOrganisation PORG
                    ON POL.PolicyID = PORG.PolicyId
                    AND PORG.OrganisationRoleID < 100000
                    AND PORG.DataSourceInstanceID = 50029
                    AND PORG.IsDeleted = 0
                LEFT JOIN dbo.PolicyOrganisation PLORG
                    ON POL.PolicyID = PLORG.PolicyId
                    AND PLORG.OrganisationRoleID >= 100000
                    AND PLORG.DataSourceInstanceID = 50029
                    AND PLORG.IsDeleted = 0
                LEFT JOIN PS.Organisation PO ON PO.OrganisationSK = ISNULL(PORG.OrganisationId, PLORG.OrganisationId)
                LEFT JOIN PS.Organisation AS ORG
                    ON ORG.OrganisationKey = PO.OrganisationKey
                    AND ORG.DataSourceInstanceId = PO.DataSourceInstanceId
                    AND ORG.IsDeleted = 0
                    AND ORG.DataSourceInstanceID = 50029
           ) POLORG
                    ON POLORG.PolicyID = pol.PolicyID', N'Min(CONVERT(NVARCHAR(50), ISNULL(DSI.DataSourceInstanceName,''None'')) + N''/''+ CONVERT(NVARCHAR(50), pol.[PolicyID]) + N''/''+ CONVERT(NVARCHAR(8), pol.[InceptionDate], 112))', NULL, 50366, NULL, NULL);

INSERT @ExclusionRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID)
VALUES
    (-45, 3, N'GB_ExcludeReverseFlow', 1, N'GB-Exclude NON FMA', N'GB-Exclude NON FMA', 1, 1, 1, 1, 4, 50000, N'(ISNULL (PROD.ProductLine, ''test'')  in(''NON FMA''))', N'LEFT JOIN dbo.PolicySection AS POLS WITH (nolock) ON POLS.policyid = Pol.policyid and POLS.isdeleted = 0  LEFT JOIN dbo.PolicySectionProduct AS PSP WITH (nolock) ON PSP.PolicySectionID = POLS.PolicySectionID AND PSP.IsDeleted = 0 LEFT JOIN dbo.Product AS PROD WITH (nolock) ON PROD.ProductID = PSP.ProductID and Prod.IsDeleted = 0', N'Min(CONVERT(NVARCHAR(50), ISNULL(DSI.DataSourceInstanceName,''None'')) + N''/''+ CONVERT(NVARCHAR(50), pol.[PolicyID]) + N''/''+ CONVERT(NVARCHAR(8), pol.[InceptionDate], 112))', NULL, 50366, NULL, NULL);

INSERT @ExclusionRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID)
VALUES
    (-46, 3, N'GB_ExcludeSCR Alert 24', 1, N'GB-Exclude SCR Alert 24', N'GB-Exclude SCR Alert 24', 1, 1, 1, 1, 4, 50000, N'(pol.OrgLevel1 IN (''Willis Limited'', ''Willis Towers Watson SA NV'') AND pol.OrgLevel2 = ''Special Contingency Risks'' AND pol.OrgLevel5 IN (''SCR Alert 24''))', N'LEFT JOIN dbo.PolicySection AS POLS WITH (nolock) ON POLS.policyid = Pol.policyid and POLS.isdeleted = 0  LEFT JOIN dbo.PolicySectionProduct AS PSP WITH (nolock) ON PSP.PolicySectionID = POLS.PolicySectionID AND PSP.IsDeleted = 0 LEFT JOIN dbo.Product AS PROD WITH (nolock) ON PROD.ProductID = PSP.ProductID and Prod.IsDeleted = 0', N'Min(CONVERT(NVARCHAR(50), ISNULL(DSI.DataSourceInstanceName,''None'')) + N''/''+ CONVERT(NVARCHAR(50), pol.[PolicyID]) + N''/''+ CONVERT(NVARCHAR(8), pol.[InceptionDate], 112))', NULL, 50366, NULL, NULL);

INSERT @ExclusionRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledQA, enabledDev, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID)
VALUES
    (-47, 3, N'eGlobal_Australia_Commercial', 1, N'eGlobal_Australia_Commercial Policies', N'eGlobal_Australia Excluding Commercial Policies', 1, 1, 1, 1, 47, 50004, N'P.Department in (''ACM'',''BCM'',''HCM'',''MCM'',''PCM'',''SCM'')', N'INNER JOIN dbo.[Policy] P WITH (NOLOCK) ON P.PolicyID = pol.PolicyID', N'Min(CONVERT(NVARCHAR(50), ISNULL(DSI.DataSourceInstanceName,''None'')) + N''/''+ CONVERT(NVARCHAR(50), pol.[PolicyID]) + N''/''+ CONVERT(NVARCHAR(8), pol.[InceptionDate], 112))', NULL, 50366, NULL, NULL);

INSERT @ExclusionRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID)
VALUES
    (-51, 3, N'GB_Marine_P&I', 1, N'GB_Marine_P&I', N'GB Marine Excluding Policies under the P&I Org Structure', 1, 1, 1, 1, 51, 50000, N'(pol.OrgLevel1 IN (''Willis Limited'',''Willis Towers Watson SA NV'') AND pol.OrgLevel2 = ''Marine'' AND pol.OrgLevel3 = ''Ownership Marine'' AND pol.OrgLevel4 = ''P&I'')', NULL, N'Min(CONVERT(NVARCHAR(50), ISNULL(DSI.DataSourceInstanceName,''None'')) + N''/''+ CONVERT(NVARCHAR(50), pol.[PolicyID]) + N''/''+ CONVERT(NVARCHAR(8), pol.[InceptionDate], 112))', NULL, 50366, NULL, NULL);

/* NA Exclusion Rules */
INSERT @ExclusionRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID)
VALUES
    (-52, 3, N'NA_Agency', 1, N'NA_Agency', N'NA Excluded Agencies', 0, 0, 0, 0, 52, 50001, N'ORA.RelatedOrganisationCode IN(''SBT'', ''NHE'', ''MIP'', ''CTP'', ''LPI'', ''FRE'', ''WPO'', ''VRM'', ''WNA'', ''WAA'', ''BER'', ''CAY'', ''LTD'', ''BEM'', ''WRE'', ''VET'')', N'INNER JOIN PS.Organisation PO ON PO.OrganisationSK = pol.OrganisationID INNER JOIN PS.OrganisationRelationship AS ORA ON ORA.OrganisationKey = PO.OrganisationKey AND ORA.DataSourceInstanceId = PO.DataSourceInstanceId AND ORA.OrganisationRelationshipType = ''Agency''', N'Min(CONVERT(NVARCHAR(50), ISNULL(DSI.DataSourceInstanceName,''None'')) + N''/''+ CONVERT(NVARCHAR(50), pol.[PolicyID]) + N''/''+ CONVERT(NVARCHAR(8), pol.[InceptionDate], 112))', NULL, 50366, NULL, NULL);

INSERT @ExclusionRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID)
VALUES
    (-53, 3, N'NA_Department', 1, N'NA_Department', N'NA Excluded Departments', 0, 0, 0, 0, 53, 50001, N'ORD.RelatedOrganisationCode  NOT IN(''AER'', ''ALT'', ''CON'', ''FCR'', ''FCY'', ''FFI'', ''FIN'', ''FSL'', ''FWF'', ''FWI'', ''MCA'', ''MFA'', ''MFJ'', ''MSO'', ''MSY'', ''NAR'', ''P&C'', ''TRA'')', N'INNER JOIN PS.Organisation PO ON PO.OrganisationSK = pol.OrganisationID INNER JOIN PS.OrganisationRelationship AS ORA ON ORA.OrganisationKey = PO.OrganisationKey AND ORA.DataSourceInstanceId = PO.DataSourceInstanceId AND ORD.OrganisationRelationshipType = ''DEPARTMENT''', N'Min(CONVERT(NVARCHAR(50), ISNULL(DSI.DataSourceInstanceName,''None'')) + N''/''+ CONVERT(NVARCHAR(50), pol.[PolicyID]) + N''/''+ CONVERT(NVARCHAR(8), pol.[InceptionDate], 112))', NULL, 50366, NULL, NULL);

INSERT @ExclusionRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID)
VALUES
    (-54, 3, N'NA_LineStatus', 1, N'NA_LineStatus', N'NA Excluded Line Status', 0, 0, 0, 0, 54, 50001, N' pol.PolicySectionStatusKey NOT IN(''105'', ''107'', ''111'', ''112'', ''113'', ''114'', ''116'', ''65538'')', NULL, N'Min(CONVERT(NVARCHAR(50), ISNULL(DSI.DataSourceInstanceName,''None'')) + N''/''+ CONVERT(NVARCHAR(50), pol.[PolicyID]) + N''/''+ CONVERT(NVARCHAR(8), pol.[InceptionDate], 112))', NULL, 50366, NULL, NULL);

INSERT @ExclusionRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID)
VALUES
    (-55, 3, N'NA_Products', 1, N'NA_Products', N'NA Excluded Products', 0, 0, 0, 0, 55, 50001, N'pol.[ProductKey] IN(''ACFS'', ''ACNS'', ''ALFC'', ''ALPB'', ''ALPJ'', ''AOTS'', ''ARVS'', ''BKEX'', ''CERT'', ''CFEE'', ''CLCL'', ''CLHC'', ''CNTG'', ''CTF'', ''FACC'', ''FADV'', ''FCYR'', ''FEXT'', ''FHCP'', ''FILC'', ''FPLC'', ''FREF'', ''FRET'', ''FSUR'', ''FSWU'', ''FTPA'', ''FUND'', ''FWPT'', ''FWRP'', ''HFEE'', ''MCOM'', ''MDIF'', ''PASS'', ''PRGR'', ''RFEE'', ''SCR'',''SFEE'', ''TPA'', ''TXCO'', ''TXFL'', ''TXHC'', ''TXHL'', ''UFEE'')', NULL, N'Min(CONVERT(NVARCHAR(50), ISNULL(DSI.DataSourceInstanceName,''None'')) + N''/''+ CONVERT(NVARCHAR(50), pol.[PolicyID]) + N''/''+ CONVERT(NVARCHAR(8), pol.[InceptionDate], 112))', NULL, 50366, NULL, NULL);

/*End of NA Exclusion Rules*/

/*Portugal Exclusion Rules*/
INSERT @ExclusionRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID)
VALUES
    (-57, 3, N'Elevia_Portugal', 1, N'Elevia_Portugal_Exclude_H&B', N'Excluding Policies linked to H&B Organisation', 1, 1, 1, 1, 57, 50041, N'POLORG.Organisation LIKE ''%H&B%''', N'INNER JOIN (
			            SELECT
			            pol.policyid,
			            ORG.Organisation
			            FROM dbo.policy pol
				            LEFT JOIN dbo.PolicyOrganisation PO
					            ON PO.PolicyID = pol.PolicyID
					            AND PO.IsDeleted = 0
					            AND po.DataSourceInstanceID = 50041
                            INNER JOIN PS.Organisation O ON O.OrganisationSK = PO.OrganisationID
				            INNER JOIN PAS.Organisation ORG
					            ON ORG.OrganisationKey = O.OrganisationKey
                                AND ORG.DataSourceInstanceId = O.DataSourceInstanceId
					            AND ORG.IsDeleted = 0
					            AND ORG.DataSourceInstanceID = 50041
			         ) POLORG
				                ON POLORG.PolicyID = pol.PolicyID', N'Min(CONVERT(NVARCHAR(50), ISNULL(DSI.DataSourceInstanceName,''None'')) + N''/''+ CONVERT(NVARCHAR(50), pol.[PolicyID]) + N''/''+ CONVERT(NVARCHAR(8), pol.[InceptionDate], 112))', NULL, 50366, NULL, NULL);

INSERT @ExclusionRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID)
VALUES
    (-58, 3, N'Visual_Seg_Spain', 1, N'Visual_Seg_Spain_Exclude_Scope_Broking_Platform', N'Excluding Visual Seg Policies out of scope', 1, 1, 1, 1, 24, 50044, N'SBP.ScopeBrokingPlatform = 0', N'INNER JOIN
            (
                SELECT
			    POL.PolicyId,
                ScopeBrokingPlatform = ISNULL(paspa.EleviaScopeBrokingPlatform,0)
                FROM dbo.[Policy] pol
                INNER JOIN PAS.Policy pasp ON pasp.PASPolicyId = pol.PACTPolicyId
                INNER JOIN PAS.PolicyAttribute paspa ON paspa.PolicyKey = pasp.PolicyKey
                    AND paspa.DataSourceInstanceId = pasp.DataSourceInstanceId
                    AND paspa.IsDeleted = 0
            ) SBP
                ON SBP.PolicyId = pol.PolicyId', N'Min(CONVERT(NVARCHAR(50), ISNULL(DSI.DataSourceInstanceName,''None'')) + N''/''+ CONVERT(NVARCHAR(50), pol.[PolicyId]) + N''/''+ CONVERT(NVARCHAR(8), pol.[InceptionDate], 112))', NULL, 50366, NULL, NULL);

INSERT @ExclusionRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledQA, enabledDev, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID)
VALUES
    (-59, 3, N'eGlobal_Australia_Sydney_Aviation', 1, N'eGlobal_Australia_Sydney_Aviation Policies', N'eGlobal_Australia Excluding Sydney Aviation Policies', 0, 0, 0, 0, 101, 50004, N'P.Department in (''SAR'')', N'INNER JOIN dbo.[Policy] P WITH (NOLOCK) ON P.PolicyID = pol.PolicyID', N'Min(CONVERT(NVARCHAR(50), ISNULL(DSI.DataSourceInstanceName,''None'')) + N''/''+ CONVERT(NVARCHAR(50), pol.[PolicyID]) + N''/''+ CONVERT(NVARCHAR(8), pol.[InceptionDate], 112))', NULL, 50366, NULL, NULL);

/*End of Exclusion Rules*/

-- By default set everything to disabled
UPDATE @ExclusionRules
SET enabled = 0;

/* LOCAL/DEV/UNIT TEST ENVIRONMENTS ONLY  */
IF devops.IsLocalEnv() = 1
   OR devops.IsDevEnv() = 1
   OR devops.IsUnitTestEnv() = 1
BEGIN
    UPDATE @ExclusionRules
    SET enabled = enabledDev;
END;

/* QA ENVIRONMENTS ONLY  */
IF
    (SELECT devops.IsQaEnv()) = 1
BEGIN
    UPDATE @ExclusionRules
    SET enabled = enabledQA;
END;

/* UAT/IAT ENVIRONMENTS ONLY  */
IF
    (SELECT devops.IsUatOrIatEnv()) = 1
BEGIN
    UPDATE @ExclusionRules
    SET enabled = enabledIAT;
END;

/* PRODUCTION ENVIRONMENTS ONLY  */
IF
    (SELECT devops.IsProdEnv()) = 1
BEGIN
    UPDATE @ExclusionRules
    SET enabled = enabledProd;
END;

MERGE PactConfig.[rule] T
USING
    (SELECT ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabled, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID, UsePlacementValueYr2, IsDeleted = 0
     FROM
         @ExclusionRules) S
ON S.ruleid = T.ruleid
--New Rules
WHEN NOT MATCHED BY TARGET
    THEN INSERT
             (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabled, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID, UsePlacementValueYr2)
         VALUES
             (S.ruleid, S.ruletypeid, S.rulekey, S.ruleversion, S.rulename, S.ruledescription, S.enabled, S.[order], S.datasourceinstanceid, S.wheresql, S.joinsql, S.idsql, S.datesql, S.placementdatasourceinstanceid, S.parentruleid, S.TeamID, S.ResultID, S.UsePlacementValueYr2)
WHEN MATCHED AND NOT EXISTS
                         (SELECT S.ruletypeid, S.rulekey, S.ruleversion, S.rulename, S.ruledescription, S.enabled, S.[order], S.datasourceinstanceid, S.wheresql, S.joinsql, S.idsql, S.datesql, S.placementdatasourceinstanceid, S.parentruleid, S.TeamID, S.ResultID, S.UsePlacementValueYr2, S.IsDeleted
                          INTERSECT
                          SELECT T.ruletypeid, T.rulekey, T.ruleversion, T.rulename, T.ruledescription, T.enabled, T.[order], T.datasourceinstanceid, T.wheresql, T.joinsql, T.idsql, T.datesql, T.placementdatasourceinstanceid, T.parentruleid, T.TeamID, T.ResultID, T.UsePlacementValueYr2, T.IsDeleted)
    THEN UPDATE SET T.ruletypeid = S.ruletypeid, T.rulekey = S.rulekey, T.ruleversion = S.ruleversion, T.rulename = S.rulename, T.ruledescription = S.ruledescription, T.enabled = S.enabled, T.[order] = S.[order], T.datasourceinstanceid = S.datasourceinstanceid, T.wheresql = S.wheresql, T.joinsql = S.joinsql, T.idsql = S.idsql, T.datesql = S.datesql, T.placementdatasourceinstanceid = S.placementdatasourceinstanceid, T.parentruleid = S.parentruleid, T.TeamID = S.TeamID, T.ResultID = S.ResultID, T.UsePlacementValueYr2 = S.UsePlacementValueYr2, T.ETLUpdatedDate = GETUTCDATE(), T.IsDeleted = S.IsDeleted
WHEN NOT MATCHED BY SOURCE AND T.ruletypeid = 3
                               AND T.IsDeleted = 0
    THEN UPDATE SET T.IsDeleted = 1, T.ETLUpdatedDate = GETUTCDATE();
GO