/*
--------------------------------------------------------------------------------------
    Sub-Segment rules
--------------------------------------------------------------------------------------
*/
DECLARE @SubSegmentRules TABLE (
    ruleid                        INT            NOT NULL
  , ruletypeid                    INT            NOT NULL
  , rulekey                       NVARCHAR(50)   NOT NULL
  , ruleversion                   INT            NOT NULL
  , rulename                      NVARCHAR(255)  NOT NULL
  , ruledescription               NVARCHAR(4000) NULL
  , enabled                       BIT            NULL
  , [order]                       INT            NULL
  , datasourceinstanceid          INT            NULL
  , wheresql                      NVARCHAR(MAX)  NULL
  , joinsql                       NVARCHAR(MAX)  NULL
  , idsql                         NVARCHAR(MAX)  NULL
  , datesql                       NVARCHAR(MAX)  NULL
  , placementdatasourceinstanceid INT            NULL
  , parentruleid                  INT            NULL
  , enabledDev                    BIT            NULL
  , enabledQA                     BIT            NULL
  , enabledIAT                    BIT            NULL
  , enabledProd                   BIT            NULL
  , TeamID                        INT            NULL
  , ResultID                      INT            NULL
  , UsePlacementValueYr2          BIT
        DEFAULT 0
);

/* SubSegment */

-- 50001 North America
INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300001, 6, N'BrokingSubSegment', 1, N'BrokingSubSegment - Affinity', N'BrokingSubSegment - Affinity', 1, 1, 1, 1, 5, 50001, N'BSS.BrokingSubSegment = ''Affinity''', N' INNER JOIN APIv1.PolicyProductDetails PBSM WITH (nolock) ON PBSM.PolicyID = Pol.PolicyID
		   INNER JOIN ref.BrokingSubSegment BSS WITH (nolock) ON BSS.BrokingSubSegmentId = PBSM.ReferenceProductClassID', NULL, NULL, 50366, NULL, NULL, 2);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300002, 6, N'BrokingSubSegment', 1, N'BrokingSubSegment - Aerospace', N'BrokingSubSegment - Aerospace', 1, 1, 1, 1, 4, 50001, N'BSS.BrokingSubSegment = ''Aerospace''', N' INNER JOIN APIv1.PolicyProductDetails PBSM WITH (nolock) ON PBSM.PolicyID = Pol.PolicyID
		   INNER JOIN ref.BrokingSubSegment BSS WITH (nolock) ON BSS.BrokingSubSegmentId = PBSM.ReferenceProductClassID', NULL, NULL, 50366, NULL, NULL, 3);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300003, 6, N'BrokingSubSegment', 1, N'BrokingSubSegment - Benefits', N'BrokingSubSegment - Benefits', 1, 1, 1, 1, 6, 50001, N'BSS.BrokingSubSegment = ''Benefits''', N' INNER JOIN APIv1.PolicyProductDetails PBSM WITH (nolock) ON PBSM.PolicyID = Pol.PolicyID
		   INNER JOIN ref.BrokingSubSegment BSS WITH (nolock) ON BSS.BrokingSubSegmentId = PBSM.ReferenceProductClassID', NULL, NULL, 50366, NULL, NULL, 4);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300004, 6, N'BrokingSubSegment', 1, N'BrokingSubSegment - Bonds', N'BrokingSubSegment - Bonds', 1, 1, 1, 1, 7, 50001, N'BSS.BrokingSubSegment = ''Bonds''', N' INNER JOIN APIv1.PolicyProductDetails PBSM WITH (nolock) ON PBSM.PolicyID = Pol.PolicyID
		   INNER JOIN ref.BrokingSubSegment BSS WITH (nolock) ON BSS.BrokingSubSegmentId = PBSM.ReferenceProductClassID', NULL, NULL, 50366, NULL, NULL, 5);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300005, 6, N'BrokingSubSegment', 1, N'BrokingSubSegment - Casualty', N'BrokingSubSegment - Casualty', 1, 1, 1, 1, 1, 50001, N'BSS.BrokingSubSegment = ''Casualty''', N' INNER JOIN APIv1.PolicyProductDetails PBSM WITH (nolock) ON PBSM.PolicyID = Pol.PolicyID
		   INNER JOIN ref.BrokingSubSegment BSS WITH (nolock) ON BSS.BrokingSubSegmentId = PBSM.ReferenceProductClassID', NULL, NULL, 50366, NULL, NULL, 6);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300006, 6, N'BrokingSubSegment', 1, N'BrokingSubSegment - Construction', N'BrokingSubSegment - Construction', 1, 1, 1, 1, 8, 50001, N'BSS.BrokingSubSegment = ''Construction''', N' INNER JOIN APIv1.PolicyProductDetails PBSM WITH (nolock) ON PBSM.PolicyID = Pol.PolicyID
		   INNER JOIN ref.BrokingSubSegment BSS WITH (nolock) ON BSS.BrokingSubSegmentId = PBSM.ReferenceProductClassID', NULL, NULL, 50366, NULL, NULL, 7);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300007, 6, N'BrokingSubSegment', 1, N'BrokingSubSegment - Natural Resources', N'BrokingSubSegment - Natural Resources', 1, 1, 1, 1, 12, 50001, N'BSS.BrokingSubSegment = ''Natural Resources''', N' INNER JOIN APIv1.PolicyProductDetails PBSM WITH (nolock) ON PBSM.PolicyID = Pol.PolicyID
		   INNER JOIN ref.BrokingSubSegment BSS WITH (nolock) ON BSS.BrokingSubSegmentId = PBSM.ReferenceProductClassID', NULL, NULL, 50366, NULL, NULL, 9);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300008, 6, N'BrokingSubSegment', 1, N'BrokingSubSegment - Environmental', N'BrokingSubSegment - Environmental', 1, 1, 1, 1, 9, 50001, N'BSS.BrokingSubSegment = ''Environmental''', N' INNER JOIN APIv1.PolicyProductDetails PBSM WITH (nolock) ON PBSM.PolicyID = Pol.PolicyID
		   INNER JOIN ref.BrokingSubSegment BSS WITH (nolock) ON BSS.BrokingSubSegmentId = PBSM.ReferenceProductClassID', NULL, NULL, 50366, NULL, NULL, 11);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300009, 6, N'BrokingSubSegment', 1, N'BrokingSubSegment - Financial & Executive Risks', N'BrokingSubSegment - Financial & Executive Risks', 1, 1, 1, 1, 3, 50001, N'BSS.BrokingSubSegment = ''Financial & Executive Risks''', N' INNER JOIN APIv1.PolicyProductDetails PBSM WITH (nolock) ON PBSM.PolicyID = Pol.PolicyID
		   INNER JOIN ref.BrokingSubSegment BSS WITH (nolock) ON BSS.BrokingSubSegmentId = PBSM.ReferenceProductClassID', NULL, NULL, 50366, NULL, NULL, 12);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300010, 6, N'BrokingSubSegment', 1, N'BrokingSubSegment - Marine', N'BrokingSubSegment - Marine', 1, 1, 1, 1, 11, 50001, N'BSS.BrokingSubSegment = ''Marine''', N' INNER JOIN APIv1.PolicyProductDetails PBSM WITH (nolock) ON PBSM.PolicyID = Pol.PolicyID
		   INNER JOIN ref.BrokingSubSegment BSS WITH (nolock) ON BSS.BrokingSubSegmentId = PBSM.ReferenceProductClassID', NULL, NULL, 50366, NULL, NULL, 14);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300011, 6, N'BrokingSubSegment', 1, N'BrokingSubSegment - Personal', N'BrokingSubSegment - Personal', 1, 1, 1, 1, 13, 50001, N'BSS.BrokingSubSegment = ''Personal''', N' INNER JOIN APIv1.PolicyProductDetails PBSM WITH (nolock) ON PBSM.PolicyID = Pol.PolicyID
		   INNER JOIN ref.BrokingSubSegment BSS WITH (nolock) ON BSS.BrokingSubSegmentId = PBSM.ReferenceProductClassID', NULL, NULL, 50366, NULL, NULL, 16);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300012, 6, N'BrokingSubSegment', 1, N'BrokingSubSegment - Property', N'BrokingSubSegment - Property', 1, 1, 1, 1, 2, 50001, N'BSS.BrokingSubSegment = ''Property''', N' INNER JOIN APIv1.PolicyProductDetails PBSM WITH (nolock) ON PBSM.PolicyID = Pol.PolicyID
		   INNER JOIN ref.BrokingSubSegment BSS WITH (nolock) ON BSS.BrokingSubSegmentId = PBSM.ReferenceProductClassID', NULL, NULL, 50366, NULL, NULL, 17);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300013, 6, N'BrokingSubSegment', 1, N'BrokingSubSegment - Financial Solutions', N'BrokingSubSegment - Financial Solutions', 1, 1, 1, 1, 10, 50001, N'BSS.BrokingSubSegment = ''Financial Solutions''', N' INNER JOIN APIv1.PolicyProductDetails PBSM WITH (nolock) ON PBSM.PolicyID = Pol.PolicyID
		   INNER JOIN ref.BrokingSubSegment BSS WITH (nolock) ON BSS.BrokingSubSegmentId = PBSM.ReferenceProductClassID', NULL, NULL, 50366, NULL, NULL, 19);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300014, 6, N'BrokingSubSegment', 1, N'BrokingSubSegment - Special Contingency', N'BrokingSubSegment - Special Contingency', 1, 1, 1, 1, 14, 50001, N'BSS.BrokingSubSegment = ''Special Contingency''', N' INNER JOIN APIv1.PolicyProductDetails PBSM WITH (nolock) ON PBSM.PolicyID = Pol.PolicyID
		   INNER JOIN ref.BrokingSubSegment BSS WITH (nolock) ON BSS.BrokingSubSegmentId = PBSM.ReferenceProductClassID', NULL, NULL, 50366, NULL, NULL, 21);

-- 50015 South Africa
INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300015, 6, N'BrokingSubSegment', 1, N'BrokingSubSegment - Aerospace', N'BrokingSubSegment - Aerospace', 1, 1, 1, 1, NULL, 50015, N'BSS.BrokingSubSegment = ''Aerospace''', N' INNER JOIN APIv1.PolicyProductDetails PBSM WITH (nolock) ON PBSM.PolicyID = Pol.PolicyID
		   INNER JOIN ref.BrokingSubSegment BSS WITH (nolock) ON BSS.BrokingSubSegmentId = PBSM.ReferenceProductClassID', NULL, NULL, 50366, NULL, NULL, 3);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300016, 6, N'BrokingSubSegment', 1, N'BrokingSubSegment - Casualty', N'BrokingSubSegment - Casualty', 1, 1, 1, 1, NULL, 50015, N'BSS.BrokingSubSegment = ''Casualty''', N' INNER JOIN APIv1.PolicyProductDetails PBSM WITH (nolock) ON PBSM.PolicyID = Pol.PolicyID
		   INNER JOIN ref.BrokingSubSegment BSS WITH (nolock) ON BSS.BrokingSubSegmentId = PBSM.ReferenceProductClassID', NULL, NULL, 50366, NULL, NULL, 6);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300017, 6, N'BrokingSubSegment', 1, N'BrokingSubSegment - Construction', N'BrokingSubSegment - Construction', 1, 1, 1, 1, NULL, 50015, N'BSS.BrokingSubSegment = ''Construction''', N' INNER JOIN APIv1.PolicyProductDetails PBSM WITH (nolock) ON PBSM.PolicyID = Pol.PolicyID
		   INNER JOIN ref.BrokingSubSegment BSS WITH (nolock) ON BSS.BrokingSubSegmentId = PBSM.ReferenceProductClassID', NULL, NULL, 50366, NULL, NULL, 7);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300018, 6, N'BrokingSubSegment', 1, N'BrokingSubSegment - Financial & Executive Risks', N'BrokingSubSegment - Financial & Executive Risks', 1, 1, 1, 1, NULL, 50015, N'BSS.BrokingSubSegment = ''Financial & Executive Risks''', N' INNER JOIN APIv1.PolicyProductDetails PBSM WITH (nolock) ON PBSM.PolicyID = Pol.PolicyID
		   INNER JOIN ref.BrokingSubSegment BSS WITH (nolock) ON BSS.BrokingSubSegmentId = PBSM.ReferenceProductClassID', NULL, NULL, 50366, NULL, NULL, 12);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300019, 6, N'BrokingSubSegment', 1, N'BrokingSubSegment - Marine', N'BrokingSubSegment - Marine', 1, 1, 1, 1, NULL, 50015, N'BSS.BrokingSubSegment = ''Marine''', N' INNER JOIN APIv1.PolicyProductDetails PBSM WITH (nolock) ON PBSM.PolicyID = Pol.PolicyID
		   INNER JOIN ref.BrokingSubSegment BSS WITH (nolock) ON BSS.BrokingSubSegmentId = PBSM.ReferenceProductClassID', NULL, NULL, 50366, NULL, NULL, 14);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300020, 6, N'BrokingSubSegment', 1, N'BrokingSubSegment - Property', N'BrokingSubSegment - Property', 1, 1, 1, 1, NULL, 50015, N'BSS.BrokingSubSegment = ''Property''', N' INNER JOIN APIv1.PolicyProductDetails PBSM WITH (nolock) ON PBSM.PolicyID = Pol.PolicyID
		   INNER JOIN ref.BrokingSubSegment BSS WITH (nolock) ON BSS.BrokingSubSegmentId = PBSM.ReferenceProductClassID', NULL, NULL, 50366, NULL, NULL, 17);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300021, 6, N'BrokingSubSegment', 1, N'BrokingSubSegment - Financial Solutions', N'BrokingSubSegment - Financial Solutions', 1, 1, 1, 1, NULL, 50015, N'BSS.BrokingSubSegment = ''Financial Solutions''', N' INNER JOIN APIv1.PolicyProductDetails PBSM WITH (nolock) ON PBSM.PolicyID = Pol.PolicyID
		   INNER JOIN ref.BrokingSubSegment BSS WITH (nolock) ON BSS.BrokingSubSegmentId = PBSM.ReferenceProductClassID', NULL, NULL, 50366, NULL, NULL, 19);

-- 50000 Global FAC NA Property
INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300022, 6, N'BrokingSubSegment - GlobalFAC NA Property', 1, N'BrokingSubSegment - Facultative', N'BrokingSubSegment - Facultative', 1, 1, 1, 1, NULL, 50000, N'OrgLevel1 = ''Willis Re Inc'' AND OrgLevel2 = ''Willis Facultative'' AND OrgLevel3 = ''Ownership Willis Fac'' AND OrgLevel4 = ''Willis Fac Client Ownership'' AND OrgLevel5  IN (''Atlanta'',''Chicago'',''New York'',''NA FP Atlanta'')', N'INNER JOIN [Rules].[policiesandclients] pol1 WITH (nolock) ON pol.policyid = pol1.policyid', NULL, NULL, 50366, NULL, NULL, 24);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300023, 6, N'BrokingSubSegment - GlobalFAC NA Property', 1, N'BrokingSubSegment - Casualty', N'BrokingSubSegment - Casualty', 0, 0, 0, 0, NULL, 50000, N'BSS.BrokingSubSegment = ''Casualty''
		AND OrgLevel1 = ''Willis Re Inc'' AND OrgLevel2 = ''Willis Facultative'' AND OrgLevel3 = ''Ownership Willis Fac'' AND OrgLevel4 = ''Willis Fac Client Ownership'' AND OrgLevel5  IN (''Atlanta'',''Chicago'',''New York'',''NA FP Atlanta'')', N' INNER JOIN APIv1.PolicyProductDetails PBSM WITH (nolock) ON PBSM.PolicyID = Pol.PolicyID
		   INNER JOIN ref.BrokingSubSegment BSS WITH (nolock) ON BSS.BrokingSubSegmentId = PBSM.ReferenceProductClassID
           INNER JOIN [Rules].[policiesandclients] pol1 WITH (nolock) ON pol.policyid = pol1.policyid', NULL, NULL, 50366, NULL, NULL, 6);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300024, 6, N'BrokingSubSegment - GlobalFAC NA Property', 1, N'BrokingSubSegment - Financial & Executive Risks', N'BrokingSubSegment - Financial & Executive Risks', 0, 0, 0, 0, 0, 50000, N'BSS.BrokingSubSegment = ''Financial & Executive Risks''
		AND OrgLevel1 = ''Willis Re Inc'' AND OrgLevel2 = ''Willis Facultative'' AND OrgLevel3 = ''Ownership Willis Fac'' AND OrgLevel4 = ''Willis Fac Client Ownership'' AND OrgLevel5  IN (''Atlanta'',''Chicago'',''New York'',''NA FP Atlanta'')', N' INNER JOIN APIv1.PolicyProductDetails PBSM WITH (nolock) ON PBSM.PolicyID = Pol.PolicyID
		   INNER JOIN ref.BrokingSubSegment BSS WITH (nolock) ON BSS.BrokingSubSegmentId = PBSM.ReferenceProductClassID
           INNER JOIN [Rules].[policiesandclients] pol1 WITH (nolock) ON pol.policyid = pol1.policyid', NULL, NULL, 50366, NULL, NULL, 12);

-- 50010 Global NL Rules
-- NL Property
INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300025, 6, N'BrokingSubSegment - eGlobal NL Property', 1, N'BrokingSubSegment - eGlobal NL Property', N'BrokingSubSegment - eGlobal NL Property', 1, 1, 1, 0, NULL, 50010, N'PROD.ProductClass = ''Property'' AND pol.Department NOT IN (''P51'',''P52'')  AND SUBSTRING(PROD.ProductKey,CHARINDEX(''|'',PROD.ProductKey,1)+1,(CHARINDEX(''|'',PROD.ProductKey,(CHARINDEX(''|'',PROD.ProductKey)+1))-(CHARINDEX(''|'',PROD.ProductKey,1)+1))) NOT IN (''1328'',''S1090'',''S1156'',''S1157'')', N'LEFT JOIN dbo.policysection AS POLS WITH (nolock) ON POLS.policyid = pol.policyid AND POLS.isdeleted = 0 LEFT JOIN dbo.policysectionproduct AS PSP WITH (nolock) ON PSP.policysectionid = POLS.policysectionid AND PSP.isdeleted = 0 LEFT JOIN dbo.product AS PROD WITH (nolock) ON PROD.productid = PSP.productid AND PROD.DataSourceInstanceID = pol.DataSourceInstanceID AND PROD.isdeleted = 0', NULL, NULL, 50366, NULL, NULL, 17);

-- NL Liability
INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300026, 6, N'BrokingSubSegment - eGlobal NL Liability', 1, N'BrokingSubSegment - eGlobal NL Liability', N'BrokingSubSegment - eGlobal NL Liability', 1, 1, 1, 0, NULL, 50010, N'PROD.ProductClass = ''Liability'' AND pol.Department NOT IN (''P51'',''P52'') AND SUBSTRING(PROD.ProductKey,CHARINDEX(''|'',PROD.ProductKey,1)+1,(CHARINDEX(''|'',PROD.ProductKey,(CHARINDEX(''|'',PROD.ProductKey)+1))-(CHARINDEX(''|'',PROD.ProductKey,1)+1))) NOT IN (''1328'',''S1090'',''S1156'',''S1157'')', N'LEFT JOIN dbo.policysection AS POLS WITH (nolock) ON POLS.policyid = pol.policyid AND POLS.isdeleted = 0 LEFT JOIN dbo.policysectionproduct AS PSP WITH (nolock) ON PSP.policysectionid = POLS.policysectionid AND PSP.isdeleted = 0 LEFT JOIN dbo.product AS PROD WITH (nolock) ON PROD.productid = PSP.productid AND PROD.DataSourceInstanceID = pol.DataSourceInstanceID AND PROD.isdeleted = 0', NULL, NULL, 50366, NULL, NULL, 22);

-- NL SME
INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300027, 6, N'BrokingSubSegment - eGlobal NL SME', 1, N'BrokingSubSegment - eGlobal NL SME', N'BrokingSubSegment - eGlobal NL SME', 1, 1, 1, 0, NULL, 50010, N'PROD.ProductClass = ''SME'' AND pol.Department NOT IN (''P51'',''P52'') AND SUBSTRING(PROD.ProductKey,CHARINDEX(''|'',PROD.ProductKey,1)+1,(CHARINDEX(''|'',PROD.ProductKey,(CHARINDEX(''|'',PROD.ProductKey)+1))-(CHARINDEX(''|'',PROD.ProductKey,1)+1))) NOT IN (''1328'',''S1090'',''S1156'',''S1157'')', N'LEFT JOIN dbo.policysection AS POLS WITH (nolock) ON POLS.policyid = pol.policyid AND POLS.isdeleted = 0 LEFT JOIN dbo.policysectionproduct AS PSP WITH (nolock) ON PSP.policysectionid = POLS.policysectionid AND PSP.isdeleted = 0 LEFT JOIN dbo.product AS PROD WITH (nolock) ON PROD.productid = PSP.productid AND PROD.DataSourceInstanceID = pol.DataSourceInstanceID AND PROD.isdeleted = 0', NULL, NULL, 50366, NULL, NULL, 23);

-- NL Finex
INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300028, 6, N'BrokingSubSegment - eGlobal NL Finex', 1, N'BrokingSubSegment - eGlobal NL Finex', N'BrokingSubSegment - eGlobal NL Finex', 1, 1, 1, 0, NULL, 50010, N'PROD.ProductClass = ''Finex'' AND pol.Department NOT IN (''P51'',''P52'') AND SUBSTRING(PROD.ProductKey,CHARINDEX(''|'',PROD.ProductKey,1)+1,(CHARINDEX(''|'',PROD.ProductKey,(CHARINDEX(''|'',PROD.ProductKey)+1))-(CHARINDEX(''|'',PROD.ProductKey,1)+1))) NOT IN (''1328'',''S1090'',''S1156'',''S1157'')', N'LEFT JOIN dbo.policysection AS POLS WITH (nolock) ON POLS.policyid = pol.policyid AND POLS.isdeleted = 0 LEFT JOIN dbo.policysectionproduct AS PSP WITH (nolock) ON PSP.policysectionid = POLS.policysectionid AND PSP.isdeleted = 0 LEFT JOIN dbo.product AS PROD WITH (nolock) ON PROD.productid = PSP.productid AND PROD.DataSourceInstanceID = pol.DataSourceInstanceID AND PROD.isdeleted = 0', NULL, NULL, 50366, NULL, NULL, 12);

-- NL Construction
INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300029, 6, N'BrokingSubSegment - eGlobal NL Construction', 1, N'BrokingSubSegment - eGlobal NL Construction', N'BrokingSubSegment - eGlobal NL Construction', 1, 1, 1, 0, NULL, 50010, N'PROD.ProductClass = ''Construction'' AND pol.Department NOT IN (''P51'',''P52'') AND SUBSTRING(PROD.ProductKey,CHARINDEX(''|'',PROD.ProductKey,1)+1,(CHARINDEX(''|'',PROD.ProductKey,(CHARINDEX(''|'',PROD.ProductKey)+1))-(CHARINDEX(''|'',PROD.ProductKey,1)+1))) NOT IN (''1328'',''S1090'',''S1156'',''S1157'')', N'LEFT JOIN dbo.policysection AS POLS WITH (nolock) ON POLS.policyid = pol.policyid AND POLS.isdeleted = 0 LEFT JOIN dbo.policysectionproduct AS PSP WITH (nolock) ON PSP.policysectionid = POLS.policysectionid AND PSP.isdeleted = 0 LEFT JOIN dbo.product AS PROD WITH (nolock) ON PROD.productid = PSP.productid AND PROD.DataSourceInstanceID = pol.DataSourceInstanceID AND PROD.isdeleted = 0', NULL, NULL, 50366, NULL, NULL, 7);

-- NL Marine
INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300030, 6, N'BrokingSubSegment - eGlobal NL Marine', 1, N'BrokingSubSegment - eGlobal NL Marine', N'BrokingSubSegment - eGlobal NL Marine', 1, 1, 1, 0, NULL, 50010, N'PROD.ProductClass = ''Marine'' AND pol.Department NOT IN (''P51'',''P52'') AND SUBSTRING(PROD.ProductKey,CHARINDEX(''|'',PROD.ProductKey,1)+1,(CHARINDEX(''|'',PROD.ProductKey,(CHARINDEX(''|'',PROD.ProductKey)+1))-(CHARINDEX(''|'',PROD.ProductKey,1)+1))) NOT IN (''1328'',''S1090'',''S1156'',''S1157'')', N'LEFT JOIN dbo.policysection AS POLS WITH (nolock) ON POLS.policyid = pol.policyid AND POLS.isdeleted = 0 LEFT JOIN dbo.policysectionproduct AS PSP WITH (nolock) ON PSP.policysectionid = POLS.policysectionid AND PSP.isdeleted = 0 LEFT JOIN dbo.product AS PROD WITH (nolock) ON PROD.productid = PSP.productid AND PROD.DataSourceInstanceID = pol.DataSourceInstanceID AND PROD.isdeleted = 0', NULL, NULL, 50366, NULL, NULL, 14);

-- Hong Kong Subsegment
INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300031, 6, N'BrokingSubSegment - eGlobal HK CAS', 1, N'BrokingSubSegment - eGlobal HK CAS', N'BrokingSubSegment - eGlobal HK Casualty', 1, 1, 1, 1, NULL, 50007, N'pol.Department = ''RCA''', NULL, NULL, NULL, 50366, NULL, NULL, 6);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300032, 6, N'BrokingSubSegment - eGlobal HK CON', 1, N'BrokingSubSegment - eGlobal HK CON', N'BrokingSubSegment - eGlobal HK Construction', 1, 1, 1, 1, NULL, 50007, N'pol.Department = ''CON''', NULL, NULL, NULL, 50366, NULL, NULL, 7);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300033, 6, N'BrokingSubSegment - eGlobal HK P&C', 1, N'BrokingSubSegment - eGlobal HK P&C', N'BrokingSubSegment - eGlobal HK Property & Casualty', 1, 1, 1, 1, NULL, 50007, N'pol.Department in (''COR'',''GRM'',''MAC'',''NBD'',''NWK'')', NULL, NULL, NULL, 50366, NULL, NULL, 25);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300034, 6, N'BrokingSubSegment - Eclipse HK Marine', 1, N'BrokingSubSegment - Eclipse HK Marine', N'BrokingSubSegment - Eclipse HK Marine', 1, 1, 1, 1, NULL, 50000, N'PROD.ProductClass like ''%Marine%'' AND OrgLevel1 = ''Willis Hong Kong Limited'' AND OrgLevel2 = ''Marine'' AND OrgLevel3 = ''Ownership Marine'' AND OrgLevel4 = ''Marine Ownership''', N'INNER JOIN [Rules].[policiesandclients] pol1 WITH (nolock) ON pol.policyid = pol1.policyid
          LEFT JOIN dbo.policysection AS POLS WITH (nolock) ON POLS.policyid = pol.policyid AND POLS.isdeleted = 0
          LEFT JOIN dbo.policysectionproduct AS PSP WITH (nolock) ON PSP.policysectionid = POLS.policysectionid AND PSP.isdeleted = 0
          LEFT JOIN dbo.product AS PROD WITH (nolock) ON PROD.productid = PSP.productid AND PROD.isdeleted = 0', NULL, NULL, 50366, NULL, NULL, 14);

--Germany Marine--
INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300035, 6, N'BrokingSubSegment - ASYS Germany Marine', 1, N'BrokingSubSegment - ASYS Germany Marine', N'BrokingSubSegment - ASYS Germany Marine', 1, 1, 1, 1, NULL, 50029, N'ORG.OrganisationKey in (''10309501'',''10409501'',''10509501'',''10609501'',''10709501'')', N'LEFT JOIN dbo.PolicyOrganisation AS PORG ON PORG.PolicyID = POL.PolicyID AND PORG.IsDeleted = 0
          LEFT JOIN PS.Organisation O ON O.OrganisationSK = PORG.OrganisationID LEFT JOIN PAS.Organisation AS ORG ON ORG.OrganisationKey = O.OrganisationKey AND ORG.DataSourceInstanceId = O.DataSourceInstanceId AND ORG.IsDeleted = 0', NULL, NULL, 50366, NULL, NULL, 14);

/* Eclipse GB - 50000 */
-- Eclipse GB - Natural Resources --
INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300036, 6, N'BrokingSubSegment - Eclipse GB Natural Resources', 1, N'BrokingSubSegment - Eclipse GB Natural Resources', N'BrokingSubSegment - Eclipse GB Natural Resources', 1, 1, 1, 1, NULL, 50000, N'(
            p.orglevel1 IN (''Willis Limited'',''Willis Towers Watson SA NV'')
            AND (
                    (
                    p.OrgLevel4 = ''Ownership Energy'' AND p.OrgLevel5 IN (''Upstream - CEMEA Russia'',''Upstream - Asia'',''Upstream - North America'',''Upstream - Western Europe UK'',''Upstream - Western Europe Europe'',''Upstream - LATAM'',''Upstream - CEMEA Middle East'',''Upstream - CEMEA Africa'',''Upstream - CEEMEA'',''Energy Upstream'')
                    )
                OR (
                    p.OrgLevel4 = ''Ownership GMI'' AND p.OrgLevel5 = ''Downstream - Renewables (NR)''
                    )
                OR (
                    p.OrgLevel4 IN (''Ownership GMI'',''Ownership Energy'',''Ownership WNA London'') AND p.OrgLevel5 IN (''Downstream - CIS (NR)'',''Downstream - Utilities (NR)'',''Downstream - Europe (ex. GB) (NR)'',''Downstream - LATAM (NR)'',''Downstream - INEOS (NR)'',''Downstream - UK (NR)'',''Downstream - North America (NR)'',''Downstream - BHP (NR)'',''Downstream - Pacific (NR)'',''Downstream - Asia (NR)'',''Downstream - MEA (NR)'',''Downstream - Engineers'',''Downstream North America'',''Downstream - INEOS'',''Downstream - North America'',''Downstream - Europe (ex. GB)'',''Downstream - CIS'',''Downstream - MEA'',''Downstream - UK'',''Downstream - LATAM'',''Downstream - Utilities'',''Downstream - BHP'',''Downstream - Asia'',''Downstream - Pacific'',''Downstream - DIFC'')
                    )
                OR (
                    p.OrgLevel4 IN (''Ownership Energy'',''Ownership GMI'') AND p.OrgLevel5 IN (''Downstream - Casualty (NR)'',''Liability Energy'',''Downstream - Casualty'')
                    )
                )
            )', N'INNER JOIN [rules].[policiesandclients] p WITH (nolock) ON P.PolicyId = pol.PolicyId LEFT JOIN reference.datasourceinstance dsi WITH (nolock) ON dsi.datasourceinstanceid = pol.datasourceinstanceid LEFT JOIN dbo.party pty WITH (nolock) ON p.partyid = pty.partyid LEFT JOIN PS.Organisation O ON O.OrganisationSK = p.OrganisationId LEFT JOIN PS.Organisation org WITH (nolock) ON org.organisationKey = O.OrganisationKey AND org.DataSourceInstanceId = O.DataSourceInstanceId', NULL, NULL, 50366, NULL, NULL, 9);

--Spain Subsegment Rules--
INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300037, 6, N'BrokingSubSegment - VisualSeg Spain Aerospace', 1, N'BrokingSubSegment - VisualSeg Spain Aerospace', N'BrokingSubSegment - VisualSeg Spain Aerospace', 1, 1, 1, 1, NULL, 50044, N'BU.BusinessUnit LIKE ''CRB - AEROSPACE%''', N'INNER JOIN (
				SELECT
				LTRIM(RTRIM(REVERSE(PARSENAME(REPLACE(REVERSE(REPLACE(org.Organisation,''.'','''')), ''|'', ''.''), 2)))) AS BusinessUnit,
				POL.PolicyID,
				OrganisationId = O.OrganisationSK
				FROM dbo.[Policy] POL
					LEFT JOIN dbo.PolicyOrganisation PORG
						ON POL.PolicyID = PORG.PolicyId
						AND PORG.OrganisationRoleID < 100000
						AND PORG.DataSourceInstanceID = 50044
						AND PORG.IsDeleted = 0
					LEFT JOIN PS.Organisation O
						ON O.OrganisationSK = PORG.OrganisationId
					LEFT JOIN PAS.Organisation AS ORG
						ON ORG.OrganisationKey = O.OrganisationKey
						AND ORG.DataSourceInstanceId = O.DataSourceInstanceId

						AND ORG.IsDeleted = 0
						AND ORG.DataSourceInstanceID = 50044
					 ) BU
						ON BU.PolicyID = pol.PolicyID', NULL, NULL, 50366, NULL, NULL, 3);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300038, 6, N'BrokingSubSegment - VisualSeg Spain Construction', 1, N'BrokingSubSegment - VisualSeg Spain Construction', N'BrokingSubSegment - VisualSeg Spain Construction', 1, 1, 1, 1, NULL, 50044, N'BU.BusinessUnit LIKE ''CRB - CONSTRUCTION%''', N'INNER JOIN (
				SELECT
				LTRIM(RTRIM(REVERSE(PARSENAME(REPLACE(REVERSE(REPLACE(org.Organisation,''.'','''')), ''|'', ''.''), 2)))) AS BusinessUnit,
				POL.PolicyID,
				OrganisationId = O.OrganisationSK
				FROM dbo.[Policy] POL
					LEFT JOIN dbo.PolicyOrganisation PORG
						ON POL.PolicyID = PORG.PolicyId
						AND PORG.OrganisationRoleID < 100000
						AND PORG.DataSourceInstanceID = 50044
						AND PORG.IsDeleted = 0
					LEFT JOIN PS.Organisation O
						ON O.OrganisationSK = PORG.OrganisationId
					LEFT JOIN PAS.Organisation AS ORG
						ON ORG.OrganisationKey = O.OrganisationKey
						AND ORG.DataSourceInstanceId = O.DataSourceInstanceId

						AND ORG.IsDeleted = 0
						AND ORG.DataSourceInstanceID = 50044
					 ) BU
						ON BU.PolicyID = pol.PolicyID', NULL, NULL, 50366, NULL, NULL, 7);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300039, 6, N'BrokingSubSegment - VisualSeg Spain Credit', 1, N'BrokingSubSegment - VisualSeg Spain Credit', N'BrokingSubSegment - VisualSeg Spain Credit', 1, 1, 1, 1, NULL, 50044, N'BU.BusinessUnit LIKE ''CRB - CREDIT%''', N'INNER JOIN (
				SELECT
				LTRIM(RTRIM(REVERSE(PARSENAME(REPLACE(REVERSE(REPLACE(org.Organisation,''.'','''')), ''|'', ''.''), 2)))) AS BusinessUnit,
				POL.PolicyID,
				OrganisationId = O.OrganisationSK
				FROM dbo.[Policy] POL
					LEFT JOIN dbo.PolicyOrganisation PORG
						ON POL.PolicyID = PORG.PolicyId
						AND PORG.OrganisationRoleID < 100000
						AND PORG.DataSourceInstanceID = 50044
						AND PORG.IsDeleted = 0
					LEFT JOIN PS.Organisation O
						ON O.OrganisationSK = PORG.OrganisationId
					LEFT JOIN PAS.Organisation AS ORG
						ON ORG.OrganisationKey = O.OrganisationKey
						AND ORG.DataSourceInstanceId = O.DataSourceInstanceId
						AND ORG.IsDeleted = 0
						AND ORG.DataSourceInstanceID = 50044
					 ) BU
						ON BU.PolicyID = pol.PolicyID', NULL, NULL, 50366, NULL, NULL, 19);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300040, 6, N'BrokingSubSegment - VisualSeg Spain Energy', 1, N'BrokingSubSegment - VisualSeg Spain Energy', N'BrokingSubSegment - VisualSeg Spain Energy', 1, 1, 1, 1, NULL, 50044, N'BU.BusinessUnit LIKE ''CRB - ENERGY%''', N'INNER JOIN (
				SELECT
				LTRIM(RTRIM(REVERSE(PARSENAME(REPLACE(REVERSE(REPLACE(org.Organisation,''.'','''')), ''|'', ''.''), 2)))) AS BusinessUnit,
				POL.PolicyID,
				OrganisationId = O.OrganisationSK
				FROM dbo.[Policy] POL
					LEFT JOIN dbo.PolicyOrganisation PORG
						ON POL.PolicyID = PORG.PolicyId
						AND PORG.OrganisationRoleID < 100000
						AND PORG.DataSourceInstanceID = 50044
						AND PORG.IsDeleted = 0
					LEFT JOIN PS.Organisation O
						ON O.OrganisationSK = PORG.OrganisationId
					LEFT JOIN PAS.Organisation AS ORG
						ON ORG.OrganisationKey = O.OrganisationKey
						AND ORG.DataSourceInstanceId = O.DataSourceInstanceId
						AND ORG.IsDeleted = 0
						AND ORG.DataSourceInstanceID = 50044
					 ) BU
						ON BU.PolicyID = pol.PolicyID', NULL, NULL, 50366, NULL, NULL, 9);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300041, 6, N'BrokingSubSegment - VisualSeg Spain FAC', 1, N'BrokingSubSegment - VisualSeg Spain FAC', N'BrokingSubSegment - VisualSeg Spain FAC', 1, 1, 1, 1, NULL, 50044, N'BU.BusinessUnit LIKE ''CRB - FAC%''', N'INNER JOIN (
				SELECT
				LTRIM(RTRIM(REVERSE(PARSENAME(REPLACE(REVERSE(REPLACE(org.Organisation,''.'','''')), ''|'', ''.''), 2)))) AS BusinessUnit,
				POL.PolicyID,
				OrganisationId = O.OrganisationSK
				FROM dbo.[Policy] POL
					LEFT JOIN dbo.PolicyOrganisation PORG
						ON POL.PolicyID = PORG.PolicyId
						AND PORG.OrganisationRoleID < 100000
						AND PORG.DataSourceInstanceID = 50044
						AND PORG.IsDeleted = 0
					LEFT JOIN PS.Organisation O
						ON O.OrganisationSK = PORG.OrganisationId
					LEFT JOIN PAS.Organisation AS ORG
						ON ORG.OrganisationKey = O.OrganisationKey
						AND ORG.DataSourceInstanceId = O.DataSourceInstanceId
						AND ORG.IsDeleted = 0
						AND ORG.DataSourceInstanceID = 50044
					 ) BU
						ON BU.PolicyID = pol.PolicyID', NULL, NULL, 50366, NULL, NULL, 24);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300042, 6, N'BrokingSubSegment - VisualSeg Spain Finex', 1, N'BrokingSubSegment - VisualSeg Spain Finex', N'BrokingSubSegment - VisualSeg Spain Finex', 1, 1, 1, 1, NULL, 50044, N'BU.BusinessUnit LIKE ''CRB - FINEX%''', N'INNER JOIN (
				SELECT
				LTRIM(RTRIM(REVERSE(PARSENAME(REPLACE(REVERSE(REPLACE(org.Organisation,''.'','''')), ''|'', ''.''), 2)))) AS BusinessUnit,
				POL.PolicyID,
				OrganisationId = O.OrganisationSK
				FROM dbo.[Policy] POL
					LEFT JOIN dbo.PolicyOrganisation PORG
						ON POL.PolicyID = PORG.PolicyId
						AND PORG.OrganisationRoleID < 100000
						AND PORG.DataSourceInstanceID = 50044
						AND PORG.IsDeleted = 0
					LEFT JOIN PS.Organisation O
						ON O.OrganisationSK = PORG.OrganisationId
					LEFT JOIN PAS.Organisation AS ORG
						ON ORG.OrganisationKey = O.OrganisationKey
						AND ORG.DataSourceInstanceId = O.DataSourceInstanceId
						AND ORG.IsDeleted = 0
						AND ORG.DataSourceInstanceID = 50044
					 ) BU
						ON BU.PolicyID = pol.PolicyID', NULL, NULL, 50366, NULL, NULL, 12);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300043, 6, N'BrokingSubSegment - VisualSeg Spain Marine', 1, N'BrokingSubSegment - VisualSeg Spain Marine', N'BrokingSubSegment - VisualSeg Spain Marine', 1, 1, 1, 1, NULL, 50044, N'BU.BusinessUnit LIKE ''CRB - MARINE%''', N'INNER JOIN (
				SELECT
				LTRIM(RTRIM(REVERSE(PARSENAME(REPLACE(REVERSE(REPLACE(org.Organisation,''.'','''')), ''|'', ''.''), 2)))) AS BusinessUnit,
				POL.PolicyID,
				OrganisationId = O.OrganisationSK
				FROM dbo.[Policy] POL
					LEFT JOIN dbo.PolicyOrganisation PORG
						ON POL.PolicyID = PORG.PolicyId
						AND PORG.OrganisationRoleID < 100000
						AND PORG.DataSourceInstanceID = 50044
						AND PORG.IsDeleted = 0
					LEFT JOIN PS.Organisation O
						ON O.OrganisationSK = PORG.OrganisationId
					LEFT JOIN PAS.Organisation AS ORG
						ON ORG.OrganisationKey = O.OrganisationKey
						AND ORG.DataSourceInstanceId = O.DataSourceInstanceId
						AND ORG.IsDeleted = 0
						AND ORG.DataSourceInstanceID = 50044
					 ) BU
						ON BU.PolicyID = pol.PolicyID', NULL, NULL, 50366, NULL, NULL, 14);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300044, 6, N'BrokingSubSegment - VisualSeg Spain P&C', 1, N'BrokingSubSegment - VisualSeg Spain P&C', N'BrokingSubSegment - VisualSeg Spain P&C - CRB OTHERS', 1, 1, 1, 1, NULL, 50044, N'BU.BusinessUnit LIKE ''CRB - OTHERS%'' AND LTRIM(substring(prd.SourceProductName, CHARINDEX(''|'', prd.SourceProductName) + 1, LEN(PRD.SourceProductName))) in (''RESPONSABILIDAD CIVIL'',''DAÑOS'')', N'INNER JOIN (
				SELECT
				LTRIM(RTRIM(REVERSE(PARSENAME(REPLACE(REVERSE(REPLACE(org.Organisation,''.'','''')), ''|'', ''.''), 2)))) AS BusinessUnit,
				POL.PolicyID,
				OrganisationId = O.OrganisationSK
				FROM dbo.[Policy] POL
					LEFT JOIN dbo.PolicyOrganisation PORG
						ON POL.PolicyID = PORG.PolicyId
						AND PORG.OrganisationRoleID < 100000
						AND PORG.DataSourceInstanceID = 50044
						AND PORG.IsDeleted = 0
					LEFT JOIN PS.Organisation O
						ON O.OrganisationSK = PORG.OrganisationId
					LEFT JOIN PAS.Organisation AS ORG
						ON ORG.OrganisationKey = O.OrganisationKey
						AND ORG.DataSourceInstanceId = O.DataSourceInstanceId
						AND ORG.IsDeleted = 0
						AND ORG.DataSourceInstanceID = 50044
					 ) BU
						ON BU.PolicyID = pol.PolicyID
		LEFT JOIN dbo.PolicySection PS
			ON PS.PolicyID = Pol.PolicyID
			AND PS.IsDeleted = 0
		LEFT JOIN dbo.PolicySectionProduct PSP
			ON PSP.PolicySectionID = PS.PolicySectionID
			AND PSP.IsDeleted = 0
		LEFT JOIN dbo.Product PRD
			ON PRD.ProductID = PSP.ProductID
			AND PRD.IsDeleted = 0', NULL, NULL, 50366, NULL, NULL, 25);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300045, 6, N'BrokingSubSegment - VisualSeg Spain FS', 1, N'BrokingSubSegment - VisualSeg Spain FS', N'BrokingSubSegment - VisualSeg Spain FS - CRB OTHERS', 1, 1, 1, 1, NULL, 50044, N'BU.BusinessUnit LIKE ''CRB - OTHERS%'' AND LTRIM(substring(prd.SourceProductName, CHARINDEX(''|'', prd.SourceProductName) + 1, LEN(PRD.SourceProductName))) in (''CREDITO Y CAUCION'',''RIESGO POLÍTICO'')', N'INNER JOIN (
				SELECT
				LTRIM(RTRIM(REVERSE(PARSENAME(REPLACE(REVERSE(REPLACE(org.Organisation,''.'','''')), ''|'', ''.''), 2)))) AS BusinessUnit,
				POL.PolicyID,
				OrganisationId = O.OrganisationSK
				FROM dbo.[Policy] POL
					LEFT JOIN dbo.PolicyOrganisation PORG
						ON POL.PolicyID = PORG.PolicyId
						AND PORG.OrganisationRoleID < 100000
						AND PORG.DataSourceInstanceID = 50044
						AND PORG.IsDeleted = 0
					LEFT JOIN PS.Organisation O
						ON O.OrganisationSK = PORG.OrganisationId
					LEFT JOIN PAS.Organisation AS ORG
						ON ORG.OrganisationKey = O.OrganisationKey
						AND ORG.DataSourceInstanceId = O.DataSourceInstanceId
						AND ORG.IsDeleted = 0
						AND ORG.DataSourceInstanceID = 50044
					 ) BU
						ON BU.PolicyID = pol.PolicyID
		LEFT JOIN dbo.PolicySection PS
			ON PS.PolicyID = Pol.PolicyID
			AND PS.IsDeleted = 0
		LEFT JOIN dbo.PolicySectionProduct PSP
			ON PSP.PolicySectionID = PS.PolicySectionID
			AND PSP.IsDeleted = 0
		LEFT JOIN dbo.Product PRD
			ON PRD.ProductID = PSP.ProductID
			AND PRD.IsDeleted = 0', NULL, NULL, 50366, NULL, NULL, 19);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300046, 6, N'BrokingSubSegment - VisualSeg Spain Marine', 1, N'BrokingSubSegment - VisualSeg Spain Marine', N'BrokingSubSegment - VisualSeg Spain Marine - CRB OTHERS', 1, 1, 1, 1, NULL, 50044, N'BU.BusinessUnit LIKE ''CRB - OTHERS%'' AND LTRIM(substring(prd.SourceProductName, CHARINDEX(''|'', prd.SourceProductName) + 1, LEN(PRD.SourceProductName))) in (''TRANSPORTES'',''CASCOS'',''ARTE'')', N'INNER JOIN (
				SELECT
				LTRIM(RTRIM(REVERSE(PARSENAME(REPLACE(REVERSE(REPLACE(org.Organisation,''.'','''')), ''|'', ''.''), 2)))) AS BusinessUnit,
				POL.PolicyID,
				OrganisationId = O.OrganisationSK
				FROM dbo.[Policy] POL
					LEFT JOIN dbo.PolicyOrganisation PORG
						ON POL.PolicyID = PORG.PolicyId
						AND PORG.OrganisationRoleID < 100000
						AND PORG.DataSourceInstanceID = 50044
						AND PORG.IsDeleted = 0
					LEFT JOIN PS.Organisation O
						ON O.OrganisationSK = PORG.OrganisationId
					LEFT JOIN PAS.Organisation AS ORG
						ON ORG.OrganisationKey = O.OrganisationKey
						AND ORG.DataSourceInstanceId = O.DataSourceInstanceId
						AND ORG.IsDeleted = 0
						AND ORG.DataSourceInstanceID = 50044
					 ) BU
						ON BU.PolicyID = pol.PolicyID
		LEFT JOIN dbo.PolicySection PS
			ON PS.PolicyID = Pol.PolicyID
			AND PS.IsDeleted = 0
		LEFT JOIN dbo.PolicySectionProduct PSP
			ON PSP.PolicySectionID = PS.PolicySectionID
			AND PSP.IsDeleted = 0
		LEFT JOIN dbo.Product PRD
			ON PRD.ProductID = PSP.ProductID
			AND PRD.IsDeleted = 0', NULL, NULL, 50366, NULL, NULL, 14);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300047, 6, N'BrokingSubSegment - VisualSeg Spain Aerospace', 1, N'BrokingSubSegment - VisualSeg Spain Aerospace', N'BrokingSubSegment - VisualSeg Spain Aerospace - CRB OTHERS', 1, 1, 1, 1, NULL, 50044, N'BU.BusinessUnit LIKE ''CRB - OTHERS%'' AND LTRIM(substring(prd.SourceProductName, CHARINDEX(''|'', prd.SourceProductName) + 1, LEN(PRD.SourceProductName))) in (''AEREO'')', N'INNER JOIN (
				SELECT
				LTRIM(RTRIM(REVERSE(PARSENAME(REPLACE(REVERSE(REPLACE(org.Organisation,''.'','''')), ''|'', ''.''), 2)))) AS BusinessUnit,
				POL.PolicyID,
				OrganisationId = O.OrganisationSK
				FROM dbo.[Policy] POL
					LEFT JOIN dbo.PolicyOrganisation PORG
						ON POL.PolicyID = PORG.PolicyId
						AND PORG.OrganisationRoleID < 100000
						AND PORG.DataSourceInstanceID = 50044
						AND PORG.IsDeleted = 0
					LEFT JOIN PS.Organisation O
						ON O.OrganisationSK = PORG.OrganisationId
					LEFT JOIN PAS.Organisation AS ORG
						ON ORG.OrganisationKey = O.OrganisationKey
						AND ORG.DataSourceInstanceId = O.DataSourceInstanceId
						AND ORG.IsDeleted = 0
						AND ORG.DataSourceInstanceID = 50044
					 ) BU
						ON BU.PolicyID = pol.PolicyID
		LEFT JOIN dbo.PolicySection PS
			ON PS.PolicyID = Pol.PolicyID
			AND PS.IsDeleted = 0
		LEFT JOIN dbo.PolicySectionProduct PSP
			ON PSP.PolicySectionID = PS.PolicySectionID
			AND PSP.IsDeleted = 0
		LEFT JOIN dbo.Product PRD
			ON PRD.ProductID = PSP.ProductID
			AND PRD.IsDeleted = 0', NULL, NULL, 50366, NULL, NULL, 3);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300048, 6, N'BrokingSubSegment - VisualSeg Spain Finex', 1, N'BrokingSubSegment - VisualSeg Spain Finex', N'BrokingSubSegment - VisualSeg Spain Finex - CRB OTHERS', 1, 1, 1, 1, NULL, 50044, N'BU.BusinessUnit LIKE ''CRB - OTHERS%'' AND LTRIM(substring(prd.SourceProductName, CHARINDEX(''|'', prd.SourceProductName) + 1, LEN(PRD.SourceProductName))) in (''INFIDELIDAD'')', N'INNER JOIN (
				SELECT
				LTRIM(RTRIM(REVERSE(PARSENAME(REPLACE(REVERSE(REPLACE(org.Organisation,''.'','''')), ''|'', ''.''), 2)))) AS BusinessUnit,
				POL.PolicyID,
				OrganisationId = O.OrganisationSK
				FROM dbo.[Policy] POL
					LEFT JOIN dbo.PolicyOrganisation PORG
						ON POL.PolicyID = PORG.PolicyId
						AND PORG.OrganisationRoleID < 100000
						AND PORG.DataSourceInstanceID = 50044
						AND PORG.IsDeleted = 0
					LEFT JOIN PS.Organisation O
						ON O.OrganisationSK = PORG.OrganisationId
					LEFT JOIN PAS.Organisation AS ORG
						ON ORG.OrganisationKey = O.OrganisationKey
						AND ORG.DataSourceInstanceId = O.DataSourceInstanceId
						AND ORG.IsDeleted = 0
						AND ORG.DataSourceInstanceID = 50044
					 ) BU
						ON BU.PolicyID = pol.PolicyID
		LEFT JOIN dbo.PolicySection PS
			ON PS.PolicyID = Pol.PolicyID
			AND PS.IsDeleted = 0
		LEFT JOIN dbo.PolicySectionProduct PSP
			ON PSP.PolicySectionID = PS.PolicySectionID
			AND PSP.IsDeleted = 0
		LEFT JOIN dbo.Product PRD
			ON PRD.ProductID = PSP.ProductID
			AND PRD.IsDeleted = 0', NULL, NULL, 50366, NULL, NULL, 12);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300049, 6, N'BrokingSubSegment - VisualSeg Spain Casualty', 1, N'BrokingSubSegment - VisualSeg Spain Casualty', N'BrokingSubSegment - VisualSeg Spain Casualty - CRB OTHERS', 1, 1, 1, 1, NULL, 50044, N'BU.BusinessUnit LIKE ''CRB - OTHERS%'' AND LTRIM(substring(prd.SourceProductName, CHARINDEX(''|'', prd.SourceProductName) + 1, LEN(PRD.SourceProductName))) in (''AUTOS'')', N'INNER JOIN (
				SELECT
				LTRIM(RTRIM(REVERSE(PARSENAME(REPLACE(REVERSE(REPLACE(org.Organisation,''.'','''')), ''|'', ''.''), 2)))) AS BusinessUnit,
				POL.PolicyID,
				OrganisationId = O.OrganisationSK
				FROM dbo.[Policy] POL
					LEFT JOIN dbo.PolicyOrganisation PORG
						ON POL.PolicyID = PORG.PolicyId
						AND PORG.OrganisationRoleID < 100000
						AND PORG.DataSourceInstanceID = 50044
						AND PORG.IsDeleted = 0
					LEFT JOIN PS.Organisation O
						ON O.OrganisationSK = PORG.OrganisationId
					LEFT JOIN PAS.Organisation AS ORG
						ON ORG.OrganisationKey = O.OrganisationKey
						AND ORG.DataSourceInstanceId = O.DataSourceInstanceId
						AND ORG.IsDeleted = 0
						AND ORG.DataSourceInstanceID = 50044
					 ) BU
						ON BU.PolicyID = pol.PolicyID
		LEFT JOIN dbo.PolicySection PS
			ON PS.PolicyID = Pol.PolicyID
			AND PS.IsDeleted = 0
		LEFT JOIN dbo.PolicySectionProduct PSP
			ON PSP.PolicySectionID = PS.PolicySectionID
			AND PSP.IsDeleted = 0
		LEFT JOIN dbo.Product PRD
			ON PRD.ProductID = PSP.ProductID
			AND PRD.IsDeleted = 0', NULL, NULL, 50366, NULL, NULL, 6);

--China Subsegment Rules--
INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300050, 6, N'BrokingSubSegment - eGlobal China Aerospace', 1, N'BrokingSubSegment - eGlobal China Aerospace', N'BrokingSubSegment - eGlobal China Aerospace', 1, 1, 1, 1, NULL, 50006, N'pol.department = ''AER''', NULL, NULL, NULL, 50366, NULL, NULL, 3);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300051, 6, N'BrokingSubSegment - eGlobal China Affinity', 1, N'BrokingSubSegment - eGlobal China Affinity', N'BrokingSubSegment - eGlobal China Affinity', 1, 1, 1, 1, NULL, 50006, N'pol.department in (''AFF'',''EC'')', NULL, NULL, NULL, 50366, NULL, NULL, 2);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300052, 6, N'BrokingSubSegment - eGlobal China Construction', 1, N'BrokingSubSegment - eGlobal China Construction', N'BrokingSubSegment - eGlobal China Construction', 1, 1, 1, 1, NULL, 50006, N'pol.department in (''CON'',''CPV'')', NULL, NULL, NULL, 50366, NULL, NULL, 7);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300053, 6, N'BrokingSubSegment - eGlobal China Facultative', 1, N'BrokingSubSegment - eGlobal China Facultative', N'BrokingSubSegment - eGlobal China Facultative', 1, 1, 1, 1, NULL, 50006, N'pol.department in (''REI'',''RE2'')', NULL, NULL, NULL, 50366, NULL, NULL, 24);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300054, 6, N'BrokingSubSegment - eGlobal China F&E Risks', 1, N'BrokingSubSegment - eGlobal China F&E Risks', N'BrokingSubSegment - eGlobal China F&E Risks', 1, 1, 1, 1, NULL, 50006, N'pol.department = ''FIN''', NULL, NULL, NULL, 50366, NULL, NULL, 12);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300055, 6, N'BrokingSubSegment - eGlobal China FS', 1, N'BrokingSubSegment - eGlobal China FS', N'BrokingSubSegment - eGlobal China FS', 1, 1, 1, 1, NULL, 50006, N'pol.department in (''SUR'',''TCR'')', NULL, NULL, NULL, 50366, NULL, NULL, 19);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300056, 6, N'BrokingSubSegment - eGlobal China Marine', 1, N'BrokingSubSegment - eGlobal China Marine', N'BrokingSubSegment - eGlobal China Marine', 1, 1, 1, 1, NULL, 50006, N'pol.department = ''SPO''', NULL, NULL, NULL, 50366, NULL, NULL, 14);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300057, 6, N'BrokingSubSegment - eGlobal China NR', 1, N'BrokingSubSegment - eGlobal China NR', N'BrokingSubSegment - eGlobal China NR', 1, 1, 1, 1, NULL, 50006, N'pol.department in (''OID'',''MIN'',''OIU'',''TIJ'',''ENS'',''POW'',''NJ2'',''NAN'')', NULL, NULL, NULL, 50366, NULL, NULL, 9);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300058, 6, N'BrokingSubSegment - eGlobal China P&C', 1, N'BrokingSubSegment - eGlobal China P&C', N'BrokingSubSegment - eGlobal China P&C', 1, 1, 1, 1, NULL, 50006, N'pol.department in (''AGE'',''CMM'',''FB'',''GTS'',''TJ2'',''KUM'',''CIB'',''GUZ'',''NWK'',''LIA'',''HLR'',''CR2'',''SM2'',''SM1'',''NAJ'',''CIJ'',''PLM'')', NULL, NULL, NULL, 50366, NULL, NULL, 25);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300059, 6, N'BrokingSubSegment - eGlobal China TMT', 1, N'BrokingSubSegment - eGlobal China TMT', N'BrokingSubSegment - eGlobal China TMT', 1, 1, 1, 1, NULL, 50006, N'pol.department = ''TMT''', NULL, NULL, NULL, 50366, NULL, NULL, 27);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300060, 6, N'BrokingSubSegment - GB Aerospace', 1, N'BrokingSubSegment - GB Aerospace', N'BrokingSubSegment - GB Aerospace', 1, 1, 1, 1, NULL, 50000, N'(p.OrgLevel1 IN (''Willis Limited'',''Willis Towers Watson SA NV'') AND p.OrgLevel2 IN (''Aerospace''))', N'INNER JOIN [rules].[policiesandclients] p WITH (nolock) ON P.PolicyId = pol.PolicyId LEFT JOIN reference.datasourceinstance dsi WITH (nolock) ON dsi.datasourceinstanceid = pol.datasourceinstanceid LEFT JOIN dbo.party pty WITH (nolock) ON p.partyid = pty.partyid LEFT JOIN PS.Organisation O ON O.OrganisationSK = p.OrganisationId LEFT JOIN PAS.Organisation org WITH (nolock) ON org.organisationKey = O.OrganisationKey AND org.DataSourceInstanceId = O.DataSourceInstanceId', NULL, NULL, 50366, NULL, NULL, 3);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300061, 6, N'BrokingSubSegment - GB Financial Solutions', 1, N'BrokingSubSegment - GB Financial Solutions', N'BrokingSubSegment - GB Financial Solutions', 1, 1, 1, 1, NULL, 50000, N'(p.OrgLevel1 IN (''Willis Limited'',''Willis Towers Watson SA NV'') AND p.OrgLevel2 = ''Financial Solutions'')', N'INNER JOIN [rules].[policiesandclients] p WITH (nolock) ON P.PolicyId = pol.PolicyId LEFT JOIN reference.datasourceinstance dsi WITH (nolock) ON dsi.datasourceinstanceid = pol.datasourceinstanceid LEFT JOIN dbo.party pty WITH (nolock) ON p.partyid = pty.partyid LEFT JOIN PS.Organisation O ON O.OrganisationSK = p.OrganisationId LEFT JOIN PAS.Organisation org WITH (nolock) ON org.organisationKey = O.OrganisationKey AND org.DataSourceInstanceId = O.DataSourceInstanceId', NULL, NULL, 50366, NULL, NULL, 19);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300062, 6, N'BrokingSubSegment - GB FINEX', 1, N'BrokingSubSegment - GB FINEX', N'BrokingSubSegment - GB FINEX', 1, 1, 1, 1, NULL, 50000, N'(p.OrgLevel1 IN (''Willis Limited'',''Willis Towers Watson SA NV'') AND (p.OrgLevel2 LIKE ''finex%'' OR p.OrgLevel2 LIKE ''%Prime%''))', N'INNER JOIN [rules].[policiesandclients] p WITH (nolock) ON P.PolicyId = pol.PolicyId LEFT JOIN reference.datasourceinstance dsi WITH (nolock) ON dsi.datasourceinstanceid = pol.datasourceinstanceid LEFT JOIN dbo.party pty WITH (nolock) ON p.partyid = pty.partyid LEFT JOIN PS.Organisation O ON O.OrganisationSK = p.OrganisationId LEFT JOIN PAS.Organisation org WITH (nolock) ON org.organisationKey = O.OrganisationKey AND org.DataSourceInstanceId = O.DataSourceInstanceId', NULL, NULL, 50366, NULL, NULL, 12);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300063, 6, N'BrokingSubSegment - GB Marine', 1, N'BrokingSubSegment - GB Marine', N'BrokingSubSegment - GB Marine', 1, 1, 1, 1, NULL, 50000, N'(p.OrgLevel1 IN (''Willis Limited'',''Willis Towers Watson SA NV'') AND p.OrgLevel2 LIKE ''%marine%'')
		OR (p.orglevel1 IN (''Willis Limited'',''Willis Towers Watson SA NV'') AND p.OrgLevel2 in (''FAJS'',''Hughes-Gibb''))', N'INNER JOIN [rules].[policiesandclients] p WITH (nolock) ON P.PolicyId = pol.PolicyId LEFT JOIN reference.datasourceinstance dsi WITH (nolock) ON dsi.datasourceinstanceid = pol.datasourceinstanceid LEFT JOIN dbo.party pty WITH (nolock) ON p.partyid = pty.partyid LEFT JOIN PS.Organisation O ON O.OrganisationSK = p.OrganisationId LEFT JOIN PAS.Organisation org WITH (nolock) ON org.organisationKey = O.OrganisationKey AND org.DataSourceInstanceId = O.DataSourceInstanceId', NULL, NULL, 50366, NULL, NULL, 14);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300064, 6, N'BrokingSubSegment - GB Property', 1, N'BrokingSubSegment - GB Property', N'BrokingSubSegment - GB Property', 1, 1, 1, 1, NULL, 50000, N'(p.OrgLevel1 IN (''Willis Limited'',''Willis Towers Watson SA NV'') AND p.OrgLevel5 IN (''D P - Europe'',''D P - Asia Pac'',''D P - Australasia'',''D P - MEA'',''D P - Latam/Carribean'',''Risk Solutions'',''D P - North America'',''D P - Facilities''))', N'INNER JOIN [rules].[policiesandclients] p WITH (nolock) ON P.PolicyId = pol.PolicyId LEFT JOIN reference.datasourceinstance dsi WITH (nolock) ON dsi.datasourceinstanceid = pol.datasourceinstanceid LEFT JOIN dbo.party pty WITH (nolock) ON p.partyid = pty.partyid LEFT JOIN PS.Organisation O ON O.OrganisationSK = p.OrganisationId LEFT JOIN PAS.Organisation org WITH (nolock) ON org.organisationKey = O.OrganisationKey AND org.DataSourceInstanceId = O.DataSourceInstanceId', NULL, NULL, 50366, NULL, NULL, 25);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300065, 6, N'BrokingSubSegment - GB Casualty', 1, N'BrokingSubSegment - GB Casualty', N'BrokingSubSegment - GB Casualty', 1, 1, 1, 1, NULL, 50000, N'(p.OrgLevel1 IN (''Willis Limited'',''Willis Towers Watson SA NV'') AND p.OrgLevel5 IN (''D C - Asia Pac'',''D C - Australasia'',''D C - Europe'',''D C - Latam/Carribean'',''D C - MEA'',''D C - North America'',''WNA Casualty''))', N'INNER JOIN [rules].[policiesandclients] p WITH (nolock) ON P.PolicyId = pol.PolicyId LEFT JOIN reference.datasourceinstance dsi WITH (nolock) ON dsi.datasourceinstanceid = pol.datasourceinstanceid LEFT JOIN dbo.party pty WITH (nolock) ON p.partyid = pty.partyid LEFT JOIN PS.Organisation O ON O.OrganisationSK = p.OrganisationId LEFT JOIN PAS.Organisation org WITH (nolock) ON org.organisationKey = O.OrganisationKey AND org.DataSourceInstanceId = O.DataSourceInstanceId', NULL, NULL, 50366, NULL, NULL, 25);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300066, 6, N'BrokingSubSegment - GB Construction', 1, N'BrokingSubSegment - GB Construction', N'BrokingSubSegment - GB Construction', 1, 1, 1, 1, NULL, 50000, N'(p.OrgLevel1 IN (''Willis Limited'',''Willis Towers Watson SA NV'') AND (p.OrgLevel2 = ''GSP'' OR p.OrgLevel2 = ''ISP'' OR p.OrgLevel3 IN (''Ownership Construction'')))', N'INNER JOIN [rules].[policiesandclients] p WITH (nolock) ON P.PolicyId = pol.PolicyId LEFT JOIN reference.datasourceinstance dsi WITH (nolock) ON dsi.datasourceinstanceid = pol.datasourceinstanceid LEFT JOIN dbo.party pty WITH (nolock) ON p.partyid = pty.partyid LEFT JOIN PS.Organisation O ON O.OrganisationSK = p.OrganisationId LEFT JOIN PAS.Organisation org WITH (nolock) ON org.organisationKey = O.OrganisationKey AND org.DataSourceInstanceId = O.DataSourceInstanceId', NULL, NULL, 50366, NULL, NULL, 7);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300067, 6, N'BrokingSubSegment - GB FAC', 1, N'BrokingSubSegment - GB FAC', N'BrokingSubSegment - GB FAC', 1, 1, 1, 1, NULL, 50000, N'(p.OrgLevel1 IN (''Facultative'',''Willis Towers Watson SA NV'') AND p.OrgLevel2 IN (''Faber WL''))', N'INNER JOIN [rules].[policiesandclients] p WITH (nolock) ON P.PolicyId = pol.PolicyId LEFT JOIN reference.datasourceinstance dsi WITH (nolock) ON dsi.datasourceinstanceid = pol.datasourceinstanceid LEFT JOIN dbo.party pty WITH (nolock) ON p.partyid = pty.partyid LEFT JOIN PS.Organisation O ON O.OrganisationSK = p.OrganisationId LEFT JOIN PAS.Organisation org WITH (nolock) ON org.organisationKey = O.OrganisationKey AND org.DataSourceInstanceId = O.DataSourceInstanceId', NULL, NULL, 50366, NULL, NULL, 24);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300068, 6, N'BrokingSubSegment - GB Environmental', 1, N'BrokingSubSegment - GB Environmental', N'BrokingSubSegment - GB Environmental', 1, 1, 1, 1, NULL, 50000, N'(p.OrgLevel1 IN (''Willis Limited'',''Willis Towers Watson SA NV'') AND p.OrgLevel5 IN (''D C - Environmental''))', N'INNER JOIN [rules].[policiesandclients] p WITH (nolock) ON P.PolicyId = pol.PolicyId LEFT JOIN reference.datasourceinstance dsi WITH (nolock) ON dsi.datasourceinstanceid = pol.datasourceinstanceid LEFT JOIN dbo.party pty WITH (nolock) ON p.partyid = pty.partyid LEFT JOIN PS.Organisation O ON O.OrganisationSK = p.OrganisationId LEFT JOIN PAS.Organisation org WITH (nolock) ON org.organisationKey = O.OrganisationKey AND org.DataSourceInstanceId = O.DataSourceInstanceId', NULL, NULL, 50366, NULL, NULL, 25);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300069, 6, N'BrokingSubSegment - GB Product Recall', 1, N'BrokingSubSegment - GB Product Recall', N'BrokingSubSegment - GB Product Recall', 1, 1, 1, 1, NULL, 50000, N'((p.OrgLevel1 IN (''Willis Limited'',''Willis Towers Watson SA NV'') AND p.OrgLevel5 IN (''D C - Product Recall'')) OR (p.orglevel1 IN (''Facultative'') AND p.orglevel2 IN (''Faber WL'') AND p.orglevel5 IN (''Faber Recall'')))', N'INNER JOIN [rules].[policiesandclients] p WITH (nolock) ON P.PolicyId = pol.PolicyId LEFT JOIN reference.datasourceinstance dsi WITH (nolock) ON dsi.datasourceinstanceid = pol.datasourceinstanceid LEFT JOIN dbo.party pty WITH (nolock) ON p.partyid = pty.partyid LEFT JOIN PS.Organisation O ON O.OrganisationSK = p.OrganisationId LEFT JOIN PAS.Organisation org WITH (nolock) ON org.organisationKey = O.OrganisationKey AND org.DataSourceInstanceId = O.DataSourceInstanceId', NULL, NULL, 50366, NULL, NULL, 25);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300070, 6, N'BrokingSubSegment - GB Terrorism', 1, N'BrokingSubSegment - GB Terrorism', N'BrokingSubSegment - GB Terrorism', 1, 1, 1, 1, NULL, 50000, N'(p.OrgLevel1 IN (''Willis Limited'',''Willis Towers Watson SA NV'') AND p.Orglevel5 in (''Terrorism'', ''Ownership Terrorism (Singapore)'', ''WNA-L Terrorism''))', N'INNER JOIN [rules].[policiesandclients] p WITH (nolock) ON P.PolicyId = pol.PolicyId LEFT JOIN reference.datasourceinstance dsi WITH (nolock) ON dsi.datasourceinstanceid = pol.datasourceinstanceid LEFT JOIN dbo.party pty WITH (nolock) ON p.partyid = pty.partyid LEFT JOIN PS.Organisation O ON O.OrganisationSK = p.OrganisationId LEFT JOIN PAS.Organisation org WITH (nolock) ON org.organisationKey = O.OrganisationKey AND org.DataSourceInstanceId = O.DataSourceInstanceId', NULL, NULL, 50366, NULL, NULL, 19);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300071, 6, N'BrokingSubSegment - GB SCR', 1, N'BrokingSubSegment - GB SCR', N'BrokingSubSegment - GB SCR -Financial Solutions', 1, 1, 1, 1, NULL, 50000, N'(p.OrgLevel1 IN (''Willis Limited'',''Willis Towers Watson SA NV'') AND p.OrgLevel2 IN (''Special Contingency Risks'') AND p.OrgLevel3 IN (''Ownership SCR'') AND p.OrgLevel5 IN (''Accident and Health'',''Corporate Clients''))', N'INNER JOIN [rules].[policiesandclients] p WITH (nolock) ON P.PolicyId = pol.PolicyId', NULL, NULL, 50366, NULL, NULL, 19);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300072, 6, N'BrokingSubSegment - NA SCR', 1, N'BrokingSubSegment - NA SCR', N'BrokingSubSegment - GB SCR -Financial Solutions', 1, 1, 1, 1, NULL, 50000, N'(p.OrgLevel1 IN (''Special Contingency Risks Inc.'') AND p.OrgLevel3 IN (''Ownership SCR'') AND p.OrgLevel5 IN (''SCR Inc.''))', N'INNER JOIN [rules].[policiesandclients] p WITH (nolock) ON P.PolicyId = pol.PolicyId', NULL, NULL, 50366, NULL, NULL, 19);

--Canada 50354
INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300073, 6, N'BrokingSubSegment - Canada', 1, N'BrokingSubSegment - Canada', N'BrokingSubSegment - Canada - Aerospace', 1, 1, 1, 1, NULL, 50354, N'ORD.RelatedOrganisationCode = ''AER''', N'LEFT JOIN (
						Select P.PolicyId, ORG.OrganisationId OrganisationId, ORG.OrganisationId porg, ORG.OrganisationId plorg
						From dbo.Policy P
						LEFT JOIN dbo.PolicyOrganisation ORG
							ON P.PolicyID = ORG.PolicyId
							AND ORG.IsDeleted = 0


					) PORG
						ON PORG.PolicyID = POL.PolicyID
		LEFT JOIN PS.Organisation O
			ON O.OrganisationSK = PORG.OrganisationId
		LEFT JOIN PS.OrganisationRelationship AS ORD
			ON ORD.OrganisationKey = O.OrganisationKey
			AND ORD.DataSourceInstanceId = O.DataSourceInstanceId
			AND ORD.IsDeleted = 0
			AND ORD.OrganisationRelationshipType = ''Department''', NULL, NULL, 50366, NULL, NULL, 3);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300074, 6, N'BrokingSubSegment - Canada', 1, N'BrokingSubSegment - Canada', N'BrokingSubSegment - Canada - Bonds', 1, 1, 1, 1, NULL, 50354, N'ORD.RelatedOrganisationCode in (''COS'',''SUR'')', N'LEFT JOIN (
						Select P.PolicyId, ORG.OrganisationId OrganisationId, ORG.OrganisationId porg, ORG.OrganisationId plorg
						From dbo.Policy P
						LEFT JOIN dbo.PolicyOrganisation ORG
							ON P.PolicyID = ORG.PolicyId
							AND ORG.IsDeleted = 0
					) PORG
						ON PORG.PolicyID = POL.PolicyID
		LEFT JOIN PS.Organisation O
			ON O.OrganisationSK = PORG.OrganisationId
		LEFT JOIN PS.OrganisationRelationship AS ORD
			ON ORD.OrganisationKey = O.OrganisationKey
			AND ORD.DataSourceInstanceId = O.DataSourceInstanceId
			AND ORD.IsDeleted = 0
			AND ORD.OrganisationRelationshipType = ''Department''', NULL, NULL, 50366, NULL, NULL, 5);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300075, 6, N'BrokingSubSegment - Canada', 1, N'BrokingSubSegment - Canada', N'BrokingSubSegment - Canada - Cyber', 1, 1, 1, 1, NULL, 50354, N'ORD.RelatedOrganisationCode in (''FCY'',''FCR'')', N'LEFT JOIN (
						Select P.PolicyId, ORG.OrganisationId OrganisationId, ORG.OrganisationId porg, ORG.OrganisationId plorg
						From dbo.Policy P
						LEFT JOIN dbo.PolicyOrganisation ORG
							ON P.PolicyID = ORG.PolicyId
							AND ORG.IsDeleted = 0
					) PORG
						ON PORG.PolicyID = POL.PolicyID
		LEFT JOIN PS.Organisation O
			ON O.OrganisationSK = PORG.OrganisationId
		LEFT JOIN PS.OrganisationRelationship AS ORD
			ON ORD.OrganisationKey = O.OrganisationKey
			AND ORD.DataSourceInstanceId = O.DataSourceInstanceId
			AND ORD.IsDeleted = 0
			AND ORD.OrganisationRelationshipType = ''Department''', NULL, NULL, 50366, NULL, NULL, 8);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300076, 6, N'BrokingSubSegment - Canada', 1, N'BrokingSubSegment - Canada', N'BrokingSubSegment - Canada - Financial & Executive Risks', 1, 1, 1, 1, NULL, 50354, N'ORD.RelatedOrganisationCode in (''FIN'',''FWF'',''FWI'')', N'LEFT JOIN (
						Select P.PolicyId, ORG.OrganisationId OrganisationId, ORG.OrganisationId porg, ORG.OrganisationId plorg
						From dbo.Policy P
						LEFT JOIN dbo.PolicyOrganisation ORG
							ON P.PolicyID = ORG.PolicyId
							AND ORG.IsDeleted = 0
					) PORG
						ON PORG.PolicyID = POL.PolicyID
		LEFT JOIN PS.Organisation O
			ON O.OrganisationSK = PORG.OrganisationId
		LEFT JOIN PS.OrganisationRelationship AS ORD
			ON ORD.OrganisationKey = O.OrganisationKey
			AND ORD.IsDeleted = 0
			AND ORD.OrganisationRelationshipType = ''Department''', NULL, NULL, 50366, NULL, NULL, 12);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300077, 6, N'BrokingSubSegment - Canada', 1, N'BrokingSubSegment - Canada', N'BrokingSubSegment - Canada - Financial Solutions', 1, 1, 1, 1, NULL, 50354, N'ORD.RelatedOrganisationCode in (''FSL'',''FFI'')', N'LEFT JOIN (
						Select P.PolicyId, ORG.OrganisationId OrganisationId, ORG.OrganisationId porg, ORG.OrganisationId plorg
						From dbo.Policy P
						LEFT JOIN dbo.PolicyOrganisation ORG
							ON P.PolicyID = ORG.PolicyId
							AND ORG.IsDeleted = 0
					) PORG
						ON PORG.PolicyID = POL.PolicyID
		LEFT JOIN PS.Organisation O
			ON O.OrganisationSK = PORG.OrganisationId
		LEFT JOIN PS.OrganisationRelationship AS ORD
			ON ORD.OrganisationKey = O.OrganisationKey
			AND ORD.IsDeleted = 0
			AND ORD.OrganisationRelationshipType = ''Department''', NULL, NULL, 50366, NULL, NULL, 19);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300078, 6, N'BrokingSubSegment - Canada', 1, N'BrokingSubSegment - Canada', N'BrokingSubSegment - Canada - Marine', 1, 1, 1, 1, NULL, 50354, N'ORD.RelatedOrganisationCode in (''MCA'',''MFJ'',''MFA'',''MSO'',''MSY'')', N'LEFT JOIN (
						Select P.PolicyId, ORG.OrganisationId OrganisationId, ORG.OrganisationId porg, ORG.OrganisationId plorg
						From dbo.Policy P
						LEFT JOIN dbo.PolicyOrganisation ORG
							ON P.PolicyID = ORG.PolicyId
							AND ORG.IsDeleted = 0
					) PORG
						ON PORG.PolicyID = POL.PolicyID
		LEFT JOIN PS.Organisation O
			ON O.OrganisationSK = PORG.OrganisationId
		LEFT JOIN PS.OrganisationRelationship AS ORD
			ON ORD.OrganisationKey = O.OrganisationKey
			AND ORD.IsDeleted = 0
			AND ORD.OrganisationRelationshipType = ''Department''', NULL, NULL, 50366, NULL, NULL, 14);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300079, 6, N'BrokingSubSegment - Canada', 1, N'BrokingSubSegment - Canada', N'BrokingSubSegment - Canada - Natural Resources', 1, 1, 1, 1, NULL, 50354, N'ORD.RelatedOrganisationCode = ''NAR''', N'LEFT JOIN (
						Select P.PolicyId, ORG.OrganisationId OrganisationId, ORG.OrganisationId porg, ORG.OrganisationId plorg
						From dbo.Policy P
						LEFT JOIN dbo.PolicyOrganisation ORG
							ON P.PolicyID = ORG.PolicyId
							AND ORG.IsDeleted = 0
					) PORG
						ON PORG.PolicyID = POL.PolicyID
		LEFT JOIN PS.Organisation O
			ON O.OrganisationSK = PORG.OrganisationId
		LEFT JOIN PS.OrganisationRelationship AS ORD
			ON ORD.OrganisationKey = O.OrganisationKey
			AND ORD.IsDeleted = 0
			AND ORD.OrganisationRelationshipType = ''Department''', NULL, NULL, 50366, NULL, NULL, 9);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300080, 6, N'BrokingSubSegment - Canada', 1, N'BrokingSubSegment - Canada', N'BrokingSubSegment - Canada - Property & Casualty', 1, 1, 1, 1, NULL, 50354, N'ORD.RelatedOrganisationCode in (''CON'',''P&C'')', N'LEFT JOIN (
						Select P.PolicyId, ORG.OrganisationId OrganisationId, ORG.OrganisationId porg, ORG.OrganisationId plorg
						From dbo.Policy P
						LEFT JOIN dbo.PolicyOrganisation ORG
							ON P.PolicyID = ORG.PolicyId
							AND ORG.IsDeleted = 0
					) PORG
						ON PORG.PolicyID = POL.PolicyID
		LEFT JOIN PS.Organisation O
			ON O.OrganisationSK = PORG.OrganisationId
		LEFT JOIN PS.OrganisationRelationship AS ORD
			ON ORD.OrganisationKey = O.OrganisationKey
			AND ORD.IsDeleted = 0
			AND ORD.OrganisationRelationshipType = ''Department''', NULL, NULL, 50366, NULL, NULL, 25);

--Germany Property--
INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300081, 6, N'BrokingSubSegment - ASYS Germany Property', 1, N'BrokingSubSegment - ASYS Germany Property', N'BrokingSubSegment - ASYS Germany Property', 1, 1, 1, 0, NULL, 50029, N'ORG.OrganisationKey in (''10052120'',''10601020'')', N'LEFT JOIN dbo.PolicyOrganisation AS PORG ON PORG.PolicyID = POL.PolicyID AND PORG.IsDeleted = 0
          LEFT JOIN PS.Organisation O ON O.OrganisationSK = PORG.OrganisationID LEFT JOIN PAS.Organisation AS ORG ON ORG.OrganisationKey = O.OrganisationKey AND ORG.DataSourceInstanceId = O.DataSourceInstanceId AND ORG.IsDeleted = 0', NULL, NULL, 50366, NULL, NULL, 17);

--Germany Liability--
INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300082, 6, N'BrokingSubSegment - ASYS Germany Liability', 1, N'BrokingSubSegment - ASYS Germany Liability', N'BrokingSubSegment - ASYS Germany Liability', 1, 1, 1, 0, NULL, 50029, N'ORG.OrganisationKey in (''10052130'',''10601030'')', N'LEFT JOIN dbo.PolicyOrganisation AS PORG ON PORG.PolicyID = POL.PolicyID AND PORG.IsDeleted = 0
          LEFT JOIN PS.Organisation O ON O.OrganisationSK = PORG.OrganisationID LEFT JOIN PAS.Organisation AS ORG ON ORG.OrganisationKey = O.OrganisationKey AND ORG.DataSourceInstanceId = O.DataSourceInstanceId AND ORG.IsDeleted = 0', NULL, NULL, 50366, NULL, NULL, 22);

--Germany Construction--
INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300083, 6, N'BrokingSubSegment - ASYS Germany Construction', 1, N'BrokingSubSegment - ASYS Germany Construction', N'BrokingSubSegment - ASYS Germany Construction', 1, 1, 1, 0, NULL, 50029, N'ORG.OrganisationKey =''10052321''', N'LEFT JOIN dbo.PolicyOrganisation AS PORG ON PORG.PolicyID = POL.PolicyID AND PORG.IsDeleted = 0
          LEFT JOIN PS.Organisation O ON O.OrganisationSK = PORG.OrganisationID LEFT JOIN PAS.Organisation AS ORG ON ORG.OrganisationKey = O.OrganisationKey AND ORG.DataSourceInstanceId = O.DataSourceInstanceId AND ORG.IsDeleted = 0', NULL, NULL, 50366, NULL, NULL, 7);

--Germany P&C--
INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300084, 6, N'BrokingSubSegment - ASYS Germany P&C', 1, N'BrokingSubSegment - ASYS Germany P&C', N'BrokingSubSegment - ASYS Germany P&C', 1, 1, 1, 0, NULL, 50029, N'ORG.OrganisationKey =''10052322''', N'LEFT JOIN dbo.PolicyOrganisation AS PORG ON PORG.PolicyID = POL.PolicyID AND PORG.IsDeleted = 0
          LEFT JOIN PS.Organisation O ON O.OrganisationSK = PORG.OrganisationID LEFT JOIN PAS.Organisation AS ORG ON ORG.OrganisationKey = O.OrganisationKey AND ORG.DataSourceInstanceId = O.DataSourceInstanceId AND ORG.IsDeleted = 0', NULL, NULL, 50366, NULL, NULL, 25);

--Germany Financial Solutions--
INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300085, 6, N'BrokingSubSegment - ASYS Germany F/S', 1, N'BrokingSubSegment - ASYS Germany F/S', N'BrokingSubSegment - ASYS Germany Financial Solutions', 1, 1, 1, 0, NULL, 50029, N'ORG.OrganisationKey =''10052323''', N'LEFT JOIN dbo.PolicyOrganisation AS PORG ON PORG.PolicyID = POL.PolicyID AND PORG.IsDeleted = 0
          LEFT JOIN PS.Organisation O ON O.OrganisationSK = PORG.OrganisationID LEFT JOIN PAS.Organisation AS ORG ON ORG.OrganisationKey = O.OrganisationKey AND ORG.DataSourceInstanceId = O.DataSourceInstanceId AND ORG.IsDeleted = 0', NULL, NULL, 50366, NULL, NULL, 19);

--Singapore--
INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300086, 6, N'BrokingSubSegment - Singapore Facultative', 1, N'BrokingSubSegment - Singapore Facultative', N'BrokingSubSegment - Singapore Facultative', 1, 1, 1, 1, NULL, 50000, N'[OrgLevel1] in (''Willis (Singapore) Pte Ltd (Reinsurance)'') AND [OrgLevel2] = ''Retail (Singapore)'' AND [OrgLevel5] = ''Facultative RI (Singapore)''', N'INNER JOIN [rules].[policiesandclients] p WITH (nolock) ON P.PolicyId = pol.PolicyId', NULL, NULL, 50366, NULL, NULL, 24);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300087, 6, N'BrokingSubSegment - Singapore Construction', 1, N'BrokingSubSegment - Singapore Construction', N'BrokingSubSegment - Singapore Construction', 1, 1, 1, 1, NULL, 50000, N'[OrgLevel1] in (''Willis (Singapore) Pte Ltd (Direct)'',''Willis (Singapore) Pte Ltd (Reinsurance)'') AND [OrgLevel2] = ''Retail (Singapore)'' AND [OrgLevel5] = ''Construction (Singapore)''', N'INNER JOIN [rules].[policiesandclients] p WITH (nolock) ON P.PolicyId = pol.PolicyId', NULL, NULL, 50366, NULL, NULL, 7);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300088, 6, N'BrokingSubSegment - Singapore Marine', 1, N'BrokingSubSegment - Singapore Marine', N'BrokingSubSegment - Singapore Marine', 1, 1, 1, 1, NULL, 50000, N'[OrgLevel1] in (''Willis (Singapore) Pte Ltd (Direct)'',''Willis (Singapore) Pte Ltd (Reinsurance)'') AND [OrgLevel2] = ''Marine (Singapore)''', N'INNER JOIN [rules].[policiesandclients] p WITH (nolock) ON P.PolicyId = pol.PolicyId', NULL, NULL, 50366, NULL, NULL, 14);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300089, 6, N'BrokingSubSegment - Singapore N/R', 1, N'BrokingSubSegment - Singapore N/R', N'BrokingSubSegment - Singapore Natural Resources', 1, 1, 1, 1, NULL, 50000, N'[OrgLevel1] in (''Willis (Singapore) Pte Ltd (Direct)'',''Willis (Singapore) Pte Ltd (Reinsurance)'') AND [OrgLevel2] = ''Energy (Singapore)''', N'INNER JOIN [rules].[policiesandclients] p WITH (nolock) ON P.PolicyId = pol.PolicyId', NULL, NULL, 50366, NULL, NULL, 9);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300090, 6, N'BrokingSubSegment - Singapore P&C', 1, N'BrokingSubSegment - Singapore P&C', N'BrokingSubSegment - Singapore Property & Casualty', 1, 1, 1, 1, NULL, 50000, N'[OrgLevel1] in (''Willis (Singapore) Pte Ltd (Direct)'',''Willis (Singapore) Pte Ltd (Reinsurance)'') AND [OrgLevel2] = ''GMI (Singapore)'' AND [OrgLevel5] in (''Asia Pacific (Singapore)'',''Liability International (Singapore)'')', N'INNER JOIN [rules].[policiesandclients] p WITH (nolock) ON P.PolicyId = pol.PolicyId', NULL, NULL, 50366, NULL, NULL, 25);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300091, 6, N'BrokingSubSegment - Singapore Terrorism', 1, N'BrokingSubSegment - Singapore Terrorism', N'BrokingSubSegment - Singapore Terrorism', 1, 1, 1, 1, NULL, 50000, N'[OrgLevel1] in (''Willis (Singapore) Pte Ltd (Direct)'',''Willis (Singapore) Pte Ltd (Reinsurance)'') AND [OrgLevel2] = ''Terrorism (Singapore)''', N'INNER JOIN [rules].[policiesandclients] p WITH (nolock) ON P.PolicyId = pol.PolicyId', NULL, NULL, 50366, NULL, NULL, 19);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300092, 6, N'BrokingSubSegment', 1, N'BrokingSubSegment - Australia Construction', N'BrokingSubSegment - Australia Construction', 1, 1, 1, 1, NULL, 50004, N'pa.eGlobalRI_CODE in
		(''AUCR'',''AUFR'',''AUGL'',''AUXL1'',''AUXL2'',''CGL'',''CGLI'',''CGX'',''CONLI'',''CWA'',''CWL'',''CWLIA'',''CWLIP'',''CWMDA'',''CWMDP'',''CWP'',''CWPM'',''DCP'',''FEP'',''GLW'',''ISR'',''CWPEL'',
		''ISRA'',''ISRIF'',''ISRIS'',''ISRV'',''LIACO'',''PBL'',''PBX'',''PPL'',''PPX'',''PRO'',''PROPE'',''UMB'',''XSPL1'',''XSPL2'',''XSPL3'',''XSPL4'')
        AND PROD.ProductClass = ''Construction''', N'LEFT JOIN dbo.policysection AS POLS WITH (nolock) ON POLS.policyid = Pol.policyid AND POLS.isdeleted = 0
        LEFT JOIN dbo.policysectionproduct AS PSP WITH (nolock) ON PSP.policysectionid = POLS.policysectionid AND PSP.isdeleted = 0
        LEFT JOIN dbo.product AS PROD WITH (nolock) ON PROD.productid = PSP.productid and prod.DataSourceInstanceID = 50004 AND PROD.isdeleted = 0
		LEFT JOIN PAS.Product pasp ON pasp.PASProductId = PROD.SourceProductId AND pasp.DataSourceInstanceId = PROD.DataSourceInstanceId
		LEFT JOIN PAS.ProductAttribute pa ON pa.ProductKey = pasp.ProductKey AND pa.DataSourceInstanceId = pasp.DataSourceInstanceId AND pa.IsDeleted = 0', NULL, NULL, 50366, NULL, NULL, 7);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300093, 6, N'BrokingSubSegment', 1, N'BrokingSubSegment - Australia P&C', N'BrokingSubSegment - Australia P&C', 1, 1, 1, 1, NULL, 50004, N'pa.eGlobalRI_CODE in
		(''AUCR'',''AUFR'',''AUGL'',''AUXL1'',''AUXL2'',''CGL'',''CGLI'',''CGX'',''CONLI'',''CWA'',''CWL'',''CWLIA'',''CWLIP'',''CWMDA'',''CWMDP'',''CWP'',''CWPM'',''DCP'',''FEP'',''GLW'',''ISR'',''CWPEL'',
		''ISRA'',''ISRIF'',''ISRIS'',''ISRV'',''LIACO'',''PBL'',''PBX'',''PPL'',''PPX'',''PRO'',''PROPE'',''UMB'',''XSPL1'',''XSPL2'',''XSPL3'',''XSPL4'')
        AND PROD.ProductClass in (''Commercial Property'',''Commercial Liability'')', N'LEFT JOIN dbo.policysection AS POLS WITH (nolock) ON POLS.policyid = Pol.policyid AND POLS.isdeleted = 0
        LEFT JOIN dbo.policysectionproduct AS PSP WITH (nolock) ON PSP.policysectionid = POLS.policysectionid AND PSP.isdeleted = 0
        LEFT JOIN dbo.product AS PROD WITH (nolock) ON PROD.productid = PSP.productid and prod.DataSourceInstanceID = 50004 AND PROD.isdeleted = 0
		LEFT JOIN PAS.Product pasp ON pasp.PASProductId = PROD.SourceProductId AND pasp.DataSourceInstanceId = PROD.DataSourceInstanceId
		LEFT JOIN PAS.ProductAttribute pa ON pa.ProductKey = pasp.ProductKey AND pa.DataSourceInstanceId = pasp.DataSourceInstanceId AND pa.IsDeleted = 0', NULL, NULL, 50366, NULL, NULL, 25);

/* Italy Subsegment */
INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300094, 6, N'BrokingSubSegment', 1, N'BrokingSubSegment - Aerospace', N'BrokingSubSegment - Italy Aerospace', 1, 1, 1, 0, NULL, 50045, N'PRD.ProductClass in (''Aerospace'')', N' LEFT JOIN dbo.PolicySection PS WITH (nolock) on PS.PolicyID = pol.PolicyID AND PS.IsDeleted = 0
	    LEFT JOIN [dbo].[PolicySectionProduct] PSP WITH (nolock) on PSP.PolicySectionID = PS.PolicySectionID AND PSP.IsDeleted = 0
	    LEFT JOIN [dbo].Product PRD WITH (nolock) on PRD.ProductID = PSP.ProductID AND PRD.IsDeleted = 0', NULL, NULL, 50366, NULL, NULL, 3);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300095, 6, N'BrokingSubSegment', 1, N'BrokingSubSegment - Bonds', N'BrokingSubSegment - Italy Bonds', 1, 1, 1, 0, NULL, 50045, N'PRD.ProductClass in (''Bonds'')', N' LEFT JOIN dbo.PolicySection PS WITH (nolock) on PS.PolicyID = pol.PolicyID AND PS.IsDeleted = 0
	    LEFT JOIN [dbo].[PolicySectionProduct] PSP WITH (nolock) on PSP.PolicySectionID = PS.PolicySectionID AND PSP.IsDeleted = 0
	    LEFT JOIN [dbo].Product PRD WITH (nolock) on PRD.ProductID = PSP.ProductID AND PRD.IsDeleted = 0', NULL, NULL, 50366, NULL, NULL, 5);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300096, 6, N'BrokingSubSegment', 1, N'BrokingSubSegment - Construction', N'BrokingSubSegment - Italy Construction', 1, 1, 1, 0, NULL, 50045, N'PRD.ProductClass in (''Construction'')', N' LEFT JOIN dbo.PolicySection PS WITH (nolock) on PS.PolicyID = pol.PolicyID AND PS.IsDeleted = 0
	    LEFT JOIN [dbo].[PolicySectionProduct] PSP WITH (nolock) on PSP.PolicySectionID = PS.PolicySectionID AND PSP.IsDeleted = 0
	    LEFT JOIN [dbo].Product PRD WITH (nolock) on PRD.ProductID = PSP.ProductID AND PRD.IsDeleted = 0', NULL, NULL, 50366, NULL, NULL, 7);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300097, 6, N'BrokingSubSegment', 1, N'BrokingSubSegment - Financial Solutions', N'BrokingSubSegment - Italy Financial Solutions', 1, 1, 1, 0, NULL, 50045, N'PRD.ProductClass in (''Credit'')', N' LEFT JOIN dbo.PolicySection PS WITH (nolock) on PS.PolicyID = pol.PolicyID AND PS.IsDeleted = 0
	    LEFT JOIN [dbo].[PolicySectionProduct] PSP WITH (nolock) on PSP.PolicySectionID = PS.PolicySectionID AND PSP.IsDeleted = 0
	    LEFT JOIN [dbo].Product PRD WITH (nolock) on PRD.ProductID = PSP.ProductID AND PRD.IsDeleted = 0', NULL, NULL, 50366, NULL, NULL, 19);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300098, 6, N'BrokingSubSegment', 1, N'BrokingSubSegment - Facultative', N'BrokingSubSegment - Italy Facultative', 1, 1, 1, 0, NULL, 50045, N'PRD.ProductClass in (''Facultative'')', N' LEFT JOIN dbo.PolicySection PS WITH (nolock) on PS.PolicyID = pol.PolicyID AND PS.IsDeleted = 0
	    LEFT JOIN [dbo].[PolicySectionProduct] PSP WITH (nolock) on PSP.PolicySectionID = PS.PolicySectionID AND PSP.IsDeleted = 0
	    LEFT JOIN [dbo].Product PRD WITH (nolock) on PRD.ProductID = PSP.ProductID AND PRD.IsDeleted = 0', NULL, NULL, 50366, NULL, NULL, 24);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300099, 6, N'BrokingSubSegment', 1, N'BrokingSubSegment - Finex', N'BrokingSubSegment - Italy Finex', 1, 1, 1, 0, NULL, 50045, N'PRD.ProductClass in (''Financial Lines'')', N' LEFT JOIN dbo.PolicySection PS WITH (nolock) on PS.PolicyID = pol.PolicyID AND PS.IsDeleted = 0
	    LEFT JOIN [dbo].[PolicySectionProduct] PSP WITH (nolock) on PSP.PolicySectionID = PS.PolicySectionID AND PSP.IsDeleted = 0
	    LEFT JOIN [dbo].Product PRD WITH (nolock) on PRD.ProductID = PSP.ProductID AND PRD.IsDeleted = 0', NULL, NULL, 50366, NULL, NULL, 12);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300100, 6, N'BrokingSubSegment', 1, N'BrokingSubSegment - Liability', N'BrokingSubSegment - Italy Liability', 1, 1, 1, 0, NULL, 50045, N'PRD.ProductClass in (''Liability'')', N' LEFT JOIN dbo.PolicySection PS WITH (nolock) on PS.PolicyID = pol.PolicyID AND PS.IsDeleted = 0
	    LEFT JOIN [dbo].[PolicySectionProduct] PSP WITH (nolock) on PSP.PolicySectionID = PS.PolicySectionID AND PSP.IsDeleted = 0
	    LEFT JOIN [dbo].Product PRD WITH (nolock) on PRD.ProductID = PSP.ProductID AND PRD.IsDeleted = 0', NULL, NULL, 50366, NULL, NULL, 22);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300101, 6, N'BrokingSubSegment', 1, N'BrokingSubSegment - Marine', N'BrokingSubSegment - Italy Marine', 1, 1, 1, 0, NULL, 50045, N'PRD.ProductClass in (''Marine'')', N' LEFT JOIN dbo.PolicySection PS WITH (nolock) on PS.PolicyID = pol.PolicyID AND PS.IsDeleted = 0
	    LEFT JOIN [dbo].[PolicySectionProduct] PSP WITH (nolock) on PSP.PolicySectionID = PS.PolicySectionID AND PSP.IsDeleted = 0
	    LEFT JOIN [dbo].Product PRD WITH (nolock) on PRD.ProductID = PSP.ProductID AND PRD.IsDeleted = 0', NULL, NULL, 50366, NULL, NULL, 14);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300102, 6, N'BrokingSubSegment', 1, N'BrokingSubSegment - Property & Casualty', N'BrokingSubSegment - Italy Property & Casualty', 1, 1, 1, 0, NULL, 50045, N'PRD.ProductClass in (''Multirisks'')', N' LEFT JOIN dbo.PolicySection PS WITH (nolock) on PS.PolicyID = pol.PolicyID AND PS.IsDeleted = 0
	    LEFT JOIN [dbo].[PolicySectionProduct] PSP WITH (nolock) on PSP.PolicySectionID = PS.PolicySectionID AND PSP.IsDeleted = 0
	    LEFT JOIN [dbo].Product PRD WITH (nolock) on PRD.ProductID = PSP.ProductID AND PRD.IsDeleted = 0', NULL, NULL, 50366, NULL, NULL, 25);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300103, 6, N'BrokingSubSegment', 1, N'BrokingSubSegment - Special Contingency Risks', N'BrokingSubSegment - Italy Special Contingency Risks', 1, 1, 1, 0, NULL, 50045, N'PRD.ProductClass in (''Pecuniary Loss'')', N' LEFT JOIN dbo.PolicySection PS WITH (nolock) on PS.PolicyID = pol.PolicyID AND PS.IsDeleted = 0
	    LEFT JOIN [dbo].[PolicySectionProduct] PSP WITH (nolock) on PSP.PolicySectionID = PS.PolicySectionID AND PSP.IsDeleted = 0
	    LEFT JOIN [dbo].Product PRD WITH (nolock) on PRD.ProductID = PSP.ProductID AND PRD.IsDeleted = 0', NULL, NULL, 50366, NULL, NULL, 21);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300104, 6, N'BrokingSubSegment', 1, N'BrokingSubSegment - Property', N'BrokingSubSegment - Italy Property', 1, 1, 1, 0, NULL, 50045, N'PRD.ProductClass in (''Property'')', N' LEFT JOIN dbo.PolicySection PS WITH (nolock) on PS.PolicyID = pol.PolicyID AND PS.IsDeleted = 0
	    LEFT JOIN [dbo].[PolicySectionProduct] PSP WITH (nolock) on PSP.PolicySectionID = PS.PolicySectionID AND PSP.IsDeleted = 0
	    LEFT JOIN [dbo].Product PRD WITH (nolock) on PRD.ProductID = PSP.ProductID AND PRD.IsDeleted = 0', NULL, NULL, 50366, NULL, NULL, 17);

/*Portugal Subsegment Rules*/
INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300105, 6, N'BrokingSubSegment - Portugal', 1, N'BrokingSubSegment - Accident & Health', N'BrokingSubSegment - Portugal - Accident & Health', 1, 1, 1, 1, NULL, 50041, N'PRD.SourceProductDescription LIKE (''%ACIDENTES PESSOAIS INDIVIDUAL | ACIDENTES PESSOAIS%'')', N' LEFT JOIN dbo.PolicySection PS WITH (nolock) on PS.PolicyID = pol.PolicyID AND PS.IsDeleted = 0
	    LEFT JOIN [dbo].[PolicySectionProduct] PSP WITH (nolock) on PSP.PolicySectionID = PS.PolicySectionID AND PSP.IsDeleted = 0
	    LEFT JOIN [dbo].Product PRD WITH (nolock) on PRD.ProductID = PSP.ProductID AND PRD.IsDeleted = 0', NULL, NULL, 50366, NULL, NULL, 1);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300106, 6, N'BrokingSubSegment - Portugal', 1, N'BrokingSubSegment - Aerospace', N'BrokingSubSegment - Portugal - Aerospace', 1, 1, 1, 1, NULL, 50041, N'PRD.SourceProductDescription LIKE (''%AVIAÇÃO GERAL | AVIAÇÃO%'')', N' LEFT JOIN dbo.PolicySection PS WITH (nolock) on PS.PolicyID = pol.PolicyID AND PS.IsDeleted = 0
	    LEFT JOIN [dbo].[PolicySectionProduct] PSP WITH (nolock) on PSP.PolicySectionID = PS.PolicySectionID AND PSP.IsDeleted = 0
	    LEFT JOIN [dbo].Product PRD WITH (nolock) on PRD.ProductID = PSP.ProductID AND PRD.IsDeleted = 0', NULL, NULL, 50366, NULL, NULL, 3);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300107, 6, N'BrokingSubSegment - Portugal', 1, N'BrokingSubSegment - Bonds', N'BrokingSubSegment - Portugal - Bonds', 1, 1, 1, 1, NULL, 50041, N'PRD.SourceProductDescription LIKE (''%CAUÇAO GERAL | CAUÇAO%'')', N'LEFT JOIN dbo.PolicySection PS WITH (nolock) on PS.PolicyID = pol.PolicyID AND PS.IsDeleted = 0
	    LEFT JOIN [dbo].[PolicySectionProduct] PSP WITH (nolock) on PSP.PolicySectionID = PS.PolicySectionID AND PSP.IsDeleted = 0
	    LEFT JOIN [dbo].Product PRD WITH (nolock) on PRD.ProductID = PSP.ProductID AND PRD.IsDeleted = 0', NULL, NULL, 50366, NULL, NULL, 5);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300108, 6, N'BrokingSubSegment - Portugal', 1, N'BrokingSubSegment - Casualty', N'BrokingSubSegment - Portugal - Casualty', 1, 1, 1, 1, NULL, 50041, N'(PRD.SourceProductDescription LIKE (''%AUTO INDIVIDUAL | AUTO%'') OR PRD.SourceProductDescription LIKE (''%AUTO FROTA | AUTO%''))', N'LEFT JOIN dbo.PolicySection PS WITH (nolock) on PS.PolicyID = pol.PolicyID AND PS.IsDeleted = 0
	    LEFT JOIN [dbo].[PolicySectionProduct] PSP WITH (nolock) on PSP.PolicySectionID = PS.PolicySectionID AND PSP.IsDeleted = 0
	    LEFT JOIN [dbo].Product PRD WITH (nolock) on PRD.ProductID = PSP.ProductID AND PRD.IsDeleted = 0', NULL, NULL, 50366, NULL, NULL, 6);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300109, 6, N'BrokingSubSegment - Portugal', 1, N'BrokingSubSegment - Construction', N'BrokingSubSegment - Portugal - Construction', 1, 1, 1, 1, NULL, 50041, N'(PRD.SourceProductDescription LIKE (''%TODO RISCO CONSTRUÇAO GERAL | TODO RISCO CONSTRUÇAO%'') OR PRD.SourceProductDescription LIKE (''%TODO RISCO MONTAGEM GERAL | TODO RISCO MONTAGEM%''))', N'LEFT JOIN dbo.PolicySection PS WITH (nolock) on PS.PolicyID = pol.PolicyID AND PS.IsDeleted = 0
	    LEFT JOIN [dbo].[PolicySectionProduct] PSP WITH (nolock) on PSP.PolicySectionID = PS.PolicySectionID AND PSP.IsDeleted = 0
	    LEFT JOIN [dbo].Product PRD WITH (nolock) on PRD.ProductID = PSP.ProductID AND PRD.IsDeleted = 0', NULL, NULL, 50366, NULL, NULL, 7);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300110, 6, N'BrokingSubSegment - Portugal', 1, N'BrokingSubSegment - Cyber', N'BrokingSubSegment - Portugal - Cyber', 1, 1, 1, 1, NULL, 50041, N'PRD.SourceProductDescription LIKE (''%CYBER GERAL | CYBER%'')', N'LEFT JOIN dbo.PolicySection PS WITH (nolock) on PS.PolicyID = pol.PolicyID AND PS.IsDeleted = 0
	    LEFT JOIN [dbo].[PolicySectionProduct] PSP WITH (nolock) on PSP.PolicySectionID = PS.PolicySectionID AND PSP.IsDeleted = 0
	    LEFT JOIN [dbo].Product PRD WITH (nolock) on PRD.ProductID = PSP.ProductID AND PRD.IsDeleted = 0', NULL, NULL, 50366, NULL, NULL, 8);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300111, 6, N'BrokingSubSegment - Portugal', 1, N'BrokingSubSegment - Environmental', N'BrokingSubSegment - Portugal - Environmental', 1, 1, 1, 1, NULL, 50041, N'PRD.SourceProductDescription LIKE (''%RESPONSAB. CIVIL AMBIENTAL | RESPONSABILIDADE CIVIL%'')', N'LEFT JOIN dbo.PolicySection PS WITH (nolock) on PS.PolicyID = pol.PolicyID AND PS.IsDeleted = 0
	    LEFT JOIN [dbo].[PolicySectionProduct] PSP WITH (nolock) on PSP.PolicySectionID = PS.PolicySectionID AND PSP.IsDeleted = 0
	    LEFT JOIN [dbo].Product PRD WITH (nolock) on PRD.ProductID = PSP.ProductID AND PRD.IsDeleted = 0', NULL, NULL, 50366, NULL, NULL, 11);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300112, 6, N'BrokingSubSegment - Portugal', 1, N'BrokingSubSegment - Financial & Executive Risks', N'BrokingSubSegment - Portugal - Financial & Executive Risks', 1, 1, 1, 1, NULL, 50041, N'(PRD.SourceProductDescription LIKE (''%RESPONS. CIVIL ADM DIR D/O | RESPONSABILIDADE CIVIL%'') OR PRD.SourceProductDescription LIKE (''%RESPONS. CIVIL PROFISSIONAL | RESPONSABILIDADE CIVIL%'') OR PRD.SourceProductDescription LIKE (''%INFIDELIDADE | INFIDELIDADE%''))', N'LEFT JOIN dbo.PolicySection PS WITH (nolock) on PS.PolicyID = pol.PolicyID AND PS.IsDeleted = 0
	    LEFT JOIN [dbo].[PolicySectionProduct] PSP WITH (nolock) on PSP.PolicySectionID = PS.PolicySectionID AND PSP.IsDeleted = 0
	    LEFT JOIN [dbo].Product PRD WITH (nolock) on PRD.ProductID = PSP.ProductID AND PRD.IsDeleted = 0', NULL, NULL, 50366, NULL, NULL, 12);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300113, 6, N'BrokingSubSegment - Portugal', 1, N'BrokingSubSegment - Marine', N'BrokingSubSegment - Portugal - Marine', 1, 1, 1, 1, NULL, 50041, N'(PRD.SourceProductDescription LIKE (''%TRANSPORTES ALL RISKS | TRANSPORTES%'') OR PRD.SourceProductDescription LIKE (''%TRANSPORTES CLAUSULA C | TRANSPORTES%'') OR PRD.SourceProductDescription LIKE (''%CASCOS MARITIMOS GERAL | CASCOS MARITIMOS%''))', N'LEFT JOIN dbo.PolicySection PS WITH (nolock) on PS.PolicyID = pol.PolicyID AND PS.IsDeleted = 0
	    LEFT JOIN [dbo].[PolicySectionProduct] PSP WITH (nolock) on PSP.PolicySectionID = PS.PolicySectionID AND PSP.IsDeleted = 0
	    LEFT JOIN [dbo].Product PRD WITH (nolock) on PRD.ProductID = PSP.ProductID AND PRD.IsDeleted = 0', NULL, NULL, 50366, NULL, NULL, 14);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300114, 6, N'BrokingSubSegment - Portugal', 1, N'BrokingSubSegment - Property', N'BrokingSubSegment - Portugal - Property', 1, 1, 1, 1, NULL, 50041, N'(PRD.SourceProductDescription LIKE (''%MULTIRISCOS COMERCIAL | MULTIRISCOS%'') OR PRD.SourceProductDescription LIKE (''%EQUIPAMENTO ELECTRONICO GERAL | EQUIPAMENTO ELECTRONICO%'') OR PRD.SourceProductDescription LIKE (''%MULTIRISCO INDUSTRIAL | MULTIRISCOS%'')
		OR PRD.SourceProductDescription LIKE (''%BENS EM LEASING | BENS EM LEASING%'') OR PRD.SourceProductDescription LIKE (''%MULTIRISCO HABITAÇAO | MULTIRISCOS%'') OR PRD.SourceProductDescription LIKE (''%AVARIAS MAQUINARIA GERAL | AVARIAS MAQUINARIA%'')
		OR PRD.SourceProductDescription LIKE (''%PERDAS EXPLORAÇAO INCENDIO | PERDAS EXPLORAÇAO%'') OR PRD.SourceProductDescription LIKE (''%ALL RISKS GERAL | ALL RISKS%'') OR PRD.SourceProductDescription LIKE (''%MAQUINAS CASCOS GERAL | MAQUINAS CASCOS%'')
		OR PRD.SourceProductDescription LIKE (''%DANOS PATRIMONIAIS GERAL | DANOS PATRIMONIAIS%'') OR PRD.SourceProductDescription LIKE (''%INCENDIO GERAL | INCENDIO%'') OR PRD.SourceProductDescription LIKE (''%PERDAS EXPLORAÇAO MULTIRISCOS | PERDAS EXPLORAÇAO%'')
		OR PRD.SourceProductDescription LIKE (''%PERDAS EXPLORAÇAO GERAL | PERDAS EXPLORAÇAO%''))', N'LEFT JOIN dbo.PolicySection PS WITH (nolock) on PS.PolicyID = pol.PolicyID AND PS.IsDeleted = 0
	    LEFT JOIN [dbo].[PolicySectionProduct] PSP WITH (nolock) on PSP.PolicySectionID = PS.PolicySectionID AND PSP.IsDeleted = 0
	    LEFT JOIN [dbo].Product PRD WITH (nolock) on PRD.ProductID = PSP.ProductID AND PRD.IsDeleted = 0', NULL, NULL, 50366, NULL, NULL, 17);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300115, 6, N'BrokingSubSegment - Portugal', 1, N'BrokingSubSegment - Workers Compensation', N'BrokingSubSegment - Portugal - Workers Compensation', 1, 1, 1, 1, NULL, 50041, N'(PRD.SourceProductDescription LIKE (''%ACIDENTES DE TRABALHO GERAL | ACIDENTES DE TRABALHO%'') OR PRD.SourceProductDescription LIKE (''%ACIDENTES TRABALHO CONTA PROPR | ACIDENTES DE TRABALHO%'') OR PRD.SourceProductDescription LIKE (''%ACIDENTES TRABALHO EMPR. DOMES | ACIDENTES DE TRABALHO%''))', N'LEFT JOIN dbo.PolicySection PS WITH (nolock) on PS.PolicyID = pol.PolicyID AND PS.IsDeleted = 0
	    LEFT JOIN [dbo].[PolicySectionProduct] PSP WITH (nolock) on PSP.PolicySectionID = PS.PolicySectionID AND PSP.IsDeleted = 0
	    LEFT JOIN [dbo].Product PRD WITH (nolock) on PRD.ProductID = PSP.ProductID AND PRD.IsDeleted = 0', NULL, NULL, 50366, NULL, NULL, 18);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300116, 6, N'BrokingSubSegment - Portugal', 1, N'BrokingSubSegment - Special Contingency Risk', N'BrokingSubSegment - Portugal - Special Contingency Risk', 1, 1, 1, 1, NULL, 50041, N'(PRD.SourceProductDescription LIKE (''%ROUBO GERAL | ROUBO%'') OR PRD.SourceProductDescription LIKE (''%VALORES EM TRANSITO | VALORES EM TRANSITO%''))', N'LEFT JOIN dbo.PolicySection PS WITH (nolock) on PS.PolicyID = pol.PolicyID AND PS.IsDeleted = 0
	    LEFT JOIN [dbo].[PolicySectionProduct] PSP WITH (nolock) on PSP.PolicySectionID = PS.PolicySectionID AND PSP.IsDeleted = 0
	    LEFT JOIN [dbo].Product PRD WITH (nolock) on PRD.ProductID = PSP.ProductID AND PRD.IsDeleted = 0', NULL, NULL, 50366, NULL, NULL, 21);

INSERT @SubSegmentRules
    (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabledDev, enabledQA, enabledIAT, enabledProd, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID)
VALUES
    (300117, 6, N'BrokingSubSegment - Portugal', 1, N'BrokingSubSegment - Liability', N'BrokingSubSegment - Portugal - Liability', 1, 1, 1, 1, NULL, 50041, N'(PRD.SourceProductDescription LIKE (''%RESPONS. CIVIL GERAL | RESPONSABILIDADE CIVIL%'') OR PRD.SourceProductDescription LIKE (''%RESPONS. CIVIL PRODUTOS | RESPONSABILIDADE CIVIL%'') OR PRD.SourceProductDescription LIKE (''%ARAG PROTEÇAO JURIDICA | PROTEÇAO JURIDICA%'') OR PRD.SourceProductDescription LIKE (''%RESPONS. CIVIL CAÇADORES | RESPONSABILIDADE CIVIL%''))', N'LEFT JOIN dbo.PolicySection PS WITH (nolock) on PS.PolicyID = pol.PolicyID AND PS.IsDeleted = 0
	    LEFT JOIN [dbo].[PolicySectionProduct] PSP WITH (nolock) on PSP.PolicySectionID = PS.PolicySectionID AND PSP.IsDeleted = 0
	    LEFT JOIN [dbo].Product PRD WITH (nolock) on PRD.ProductID = PSP.ProductID AND PRD.IsDeleted = 0', NULL, NULL, 50366, NULL, NULL, 22);

/* End of Sub Segment */

-- By default set everything to disabled
UPDATE @SubSegmentRules
SET enabled = 0;

/* LOCAL/DEV/UNIT TEST ENVIRONMENTS ONLY  */
IF devops.IsLocalEnv() = 1
   OR devops.IsDevEnv() = 1
   OR devops.IsUnitTestEnv() = 1
BEGIN
    UPDATE @SubSegmentRules
    SET enabled = enabledDev;
END;

/* QA ENVIRONMENTS ONLY  */
IF
    (SELECT devops.IsQaEnv()) = 1
BEGIN
    UPDATE @SubSegmentRules
    SET enabled = enabledQA;
END;

/* UAT/IAT ENVIRONMENTS ONLY  */
IF
    (SELECT devops.IsUatOrIatEnv()) = 1
BEGIN
    UPDATE @SubSegmentRules
    SET enabled = enabledIAT;
END;

/* PRODUCTION ENVIRONMENTS ONLY  */
IF
    (SELECT devops.IsProdEnv()) = 1
BEGIN
    UPDATE @SubSegmentRules
    SET enabled = enabledProd;
END;

MERGE PactConfig.[rule] T
USING
    (SELECT ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabled, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID, UsePlacementValueYr2, IsDeleted = 0
     FROM
         @SubSegmentRules) S
ON S.ruleid = T.ruleid
--New Rules
WHEN NOT MATCHED BY TARGET
    THEN INSERT
             (ruleid, ruletypeid, rulekey, ruleversion, rulename, ruledescription, enabled, [order], datasourceinstanceid, wheresql, joinsql, idsql, datesql, placementdatasourceinstanceid, parentruleid, TeamID, ResultID, UsePlacementValueYr2)
         VALUES
             (S.ruleid, S.ruletypeid, S.rulekey, S.ruleversion, S.rulename, S.ruledescription, S.enabled, S.[order], S.datasourceinstanceid, S.wheresql, S.joinsql, S.idsql, S.datesql, S.placementdatasourceinstanceid, S.parentruleid, S.TeamID, S.ResultID, S.UsePlacementValueYr2)
WHEN MATCHED AND NOT EXISTS
                         (SELECT T.ruletypeid, T.rulekey, T.ruleversion, T.rulename, T.ruledescription, T.enabled, T.[order], T.datasourceinstanceid, T.wheresql, T.joinsql, T.idsql, T.datesql, T.placementdatasourceinstanceid, T.parentruleid, T.TeamID, T.ResultID, T.UsePlacementValueYr2, T.IsDeleted
                          INTERSECT
                          SELECT S.ruletypeid, S.rulekey, S.ruleversion, S.rulename, S.ruledescription, S.enabled, S.[order], S.datasourceinstanceid, S.wheresql, S.joinsql, S.idsql, S.datesql, S.placementdatasourceinstanceid, S.parentruleid, S.TeamID, S.ResultID, S.UsePlacementValueYr2, S.IsDeleted)
    THEN UPDATE SET T.ruletypeid = S.ruletypeid, T.rulekey = S.rulekey, T.ruleversion = S.ruleversion, T.rulename = S.rulename, T.ruledescription = S.ruledescription, T.enabled = S.enabled, T.[order] = S.[order], T.datasourceinstanceid = S.datasourceinstanceid, T.wheresql = S.wheresql, T.joinsql = S.joinsql, T.idsql = S.idsql, T.datesql = S.datesql, T.placementdatasourceinstanceid = S.placementdatasourceinstanceid, T.parentruleid = S.parentruleid, T.TeamID = S.TeamID, T.ResultID = S.ResultID, T.UsePlacementValueYr2 = S.UsePlacementValueYr2, T.ETLUpdatedDate = GETUTCDATE(), T.IsDeleted = S.IsDeleted
WHEN NOT MATCHED BY SOURCE AND T.IsDeleted = 0
                               AND T.ruletypeid = 6
    THEN UPDATE SET T.IsDeleted = 1, T.ETLUpdatedDate = GETUTCDATE();
GO