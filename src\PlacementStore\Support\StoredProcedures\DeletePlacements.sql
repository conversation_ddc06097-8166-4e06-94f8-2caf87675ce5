/*   
        This SP executions delete placements from PS 
*/

CREATE PROCEDURE Support.DeletePlacements (
    @PlacementList NVARCHAR(MAX)
  , @Reason        VARCHAR(MAX)
)
AS
-- Create a temporary table containing the PlacementIDs to be deleted,
DECLARE @Placements TABLE (
    PlacementId INT NOT NULL PRIMARY KEY
);

DECLARE @msg NVARCHAR(1000);

--Insert into @Placements
DECLARE @SQL NVARCHAR(MAX) = 'Select ' + REPLACE(@PlacementList, ',', ' UNION Select ');

--
-- Need to populate the PlacmentIDs into the @Placements table.
--
INSERT INTO
    @Placements
EXEC sp_executesql @SQL;

BEGIN TRY
    BEGIN TRANSACTION;

    -- Rollback if we get an error 

    -- Logging to help watch the progress.
    SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' *** Starting ***';

    RAISERROR(@msg, 0, 1) WITH NOWAIT;

    SET @msg =
        CONVERT(NVARCHAR(23), GETDATE(), 121) + ' ' + CAST((
                                                          SELECT COUNT(*) FROM @Placements
                                                      ) AS NVARCHAR(10)) + ' Placements found to be deleted.';

    RAISERROR(@msg, 0, 1) WITH NOWAIT;

    -- If none are found don't bother
    IF (
        SELECT COUNT(*) FROM @Placements
    ) > 0
    BEGIN
        -- dbo.Layer                         
        SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' DELETE dbo.Layer';

        RAISERROR(@msg, 0, 1) WITH NOWAIT;

        DELETE FROM
               dbo.Layer
        WHERE
            PlacementId IN (
                SELECT PlacementId FROM @Placements
            );

        -------------------------
        -- Market Selection Tree
        -------------------------
        -- dbo.MarketInteractionFacilities
        SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' DELETE dbo.MarketInteractionFacilities';

        RAISERROR(@msg, 0, 1) WITH NOWAIT;

        DELETE
        mif
        FROM
            dbo.MarketInteractionFacilities mif
            INNER JOIN dbo.MarketInteraction mi
                ON mi.CarrierResponseId = mif.CarrierResponseId

            INNER JOIN dbo.MarketSelection ms
                ON ms.MarketSelectionId = mi.MarketSelectionId
        WHERE
            ms.PlacementId IN (
                SELECT PlacementId FROM @Placements
            );

        -- dbo.MarketInteraction
        SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' DELETE dbo.MarketInteraction';

        RAISERROR(@msg, 0, 1) WITH NOWAIT;

        DELETE
        mi
        FROM
            dbo.MarketInteraction mi
            INNER JOIN dbo.MarketSelection ms
                ON ms.MarketSelectionId = mi.MarketSelectionId
        WHERE
            ms.PlacementId IN (
                SELECT PlacementId FROM @Placements
            );

        -- dbo.MarketSelection
        SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' DELETE dbo.MarketSelection';

        RAISERROR(@msg, 0, 1) WITH NOWAIT;

        DELETE FROM
               dbo.MarketSelection
        WHERE
            PlacementId IN (
                SELECT PlacementId FROM @Placements
            );

        -------------------------
        -- End Market Selection Tree
        -------------------------

        -- dbo.MergedPlacements
        SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' DELETE dbo.MergedPlacements (1)';

        RAISERROR(@msg, 0, 1) WITH NOWAIT;

        DELETE FROM
               dbo.MergedPlacements
        WHERE
            MergedPlacementId IN (
                SELECT PlacementId FROM @Placements
            );

        SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' DELETE dbo.MergedPlacements (2)';

        RAISERROR(@msg, 0, 1) WITH NOWAIT;

        DELETE FROM
               dbo.MergedPlacements
        WHERE
            OriginalPlacementId IN (
                SELECT PlacementId FROM @Placements
            );

        -- dbo.PlacementExtension
        SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' DELETE dbo.PlacementExtension';

        RAISERROR(@msg, 0, 1) WITH NOWAIT;

        DELETE FROM
               dbo.PlacementExtension
        WHERE
            PlacementId IN (
                SELECT PlacementId FROM @Placements
            );

        -- dbo.PlacementOrganisation
        SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' DELETE dbo.PlacementOrganisation';

        RAISERROR(@msg, 0, 1) WITH NOWAIT;

        DELETE FROM
               dbo.PlacementOrganisation
        WHERE
            PlacementId IN (
                SELECT PlacementId FROM @Placements
            );

        -- dbo.PlacementPartyRole
        SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' DELETE dbo.PlacementPartyRole';

        RAISERROR(@msg, 0, 1) WITH NOWAIT;

        DELETE FROM
               dbo.PlacementPartyRole
        WHERE
            PlacementId IN (
                SELECT PlacementId FROM @Placements
            );

        -- dbo.PlacementPremium
        SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' DELETE dbo.PlacementPremium';

        RAISERROR(@msg, 0, 1) WITH NOWAIT;

        DELETE FROM
               dbo.PlacementPremium
        WHERE
            PlacementId IN (
                SELECT PlacementId FROM @Placements
            );

        -----------------------------------------------------
        -- ContractDocumentElement
        -----------------------------------------------------
        SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' DELETE dbo.ContractDocumentElement';

        RAISERROR(@msg, 0, 1) WITH NOWAIT;

        DELETE
        cde
        FROM
            dbo.ContractDocumentElement cde
            INNER JOIN PS.Contract c
                ON c.ContractId = cde.ContractId
        WHERE
            c.PlacementId IN (
                SELECT PlacementId FROM @Placements
            );

        -------------------------------------------
        -- Placement Structure tree
        -------------------------------------------
        -- dbo.PlacementProduct
        SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' DELETE dbo.PlacementProduct';

        RAISERROR(@msg, 0, 1) WITH NOWAIT;

        DELETE
        pp
        FROM
            dbo.PlacementProduct pp
            INNER JOIN dbo.PlacementStructure plcst
                ON plcst.PlacementStructureId = pp.PlacementStructureId
        WHERE
            plcst.PlacementId IN (
                SELECT PlacementId FROM @Placements
            );

        -- dbo.Layer                         
        SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' DELETE dbo.Layer (2)';

        RAISERROR(@msg, 0, 1) WITH NOWAIT;

        DELETE
        l
        FROM
            dbo.Layer l
            INNER JOIN dbo.PlacementStructure plcst
                ON plcst.PlacementStructureId = l.PlacementStructureId
        WHERE
            plcst.PlacementId IN (
                SELECT PlacementId FROM @Placements
            );

        -- dbo.PlacementStructure
        SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' DELETE dbo.PlacementStructure';

        RAISERROR(@msg, 0, 1) WITH NOWAIT;

        DELETE FROM
               dbo.PlacementStructure
        WHERE
            PlacementId IN (
                SELECT PlacementId FROM @Placements
            );

        -------------------------------------------
        -- End Placement Structure tree
        -------------------------------------------

        -- dbo.PlacementSystemAudit
        SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' DELETE dbo.PlacementSystemAudit';

        RAISERROR(@msg, 0, 1) WITH NOWAIT;

        DELETE FROM
               dbo.PlacementSystemAudit
        WHERE
            PlacementId IN (
                SELECT PlacementId FROM @Placements
            );

        -- dbo.PlacementTeamMember
        SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' DELETE dbo.PlacementTeamMember';

        RAISERROR(@msg, 0, 1) WITH NOWAIT;

        DELETE FROM
               dbo.PlacementTeamMember
        WHERE
            PlacementId IN (
                SELECT PlacementId FROM @Placements
            );

        -- dbo.PlacementTeams
        SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' DELETE dbo.PlacementTeams';

        RAISERROR(@msg, 0, 1) WITH NOWAIT;

        DELETE FROM
               dbo.PlacementTeams
        WHERE
            PlacementId IN (
                SELECT PlacementId FROM @Placements
            );

        -- dbo.PlacementWorker
        SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' DELETE dbo.PlacementWorker';

        RAISERROR(@msg, 0, 1) WITH NOWAIT;

        DELETE FROM
               dbo.PlacementWorker
        WHERE
            PlacementId IN (
                SELECT PlacementId FROM @Placements
            );

        ---------------------
        -- Specification Tree
        ---------------------
        -- dbo.CoverageGroupSection
        SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' DELETE dbo.CoverageGroupSection';

        RAISERROR(@msg, 0, 1) WITH NOWAIT;

        DELETE
        cgs
        FROM
            dbo.CoverageGroupSection cgs
            INNER JOIN dbo.CoverageGroup cg
                ON cg.CoverageGroupId = cgs.CoverageGroupId

            INNER JOIN dbo.Specification s
                ON s.SpecificationId = cg.SpecificationId
        WHERE
            s.PlacementId IN (
                SELECT PlacementId FROM @Placements
            );

        -- dbo.SubmissionCoverageGroup
        SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' DELETE dbo.SubmissionCoverageGroup';

        RAISERROR(@msg, 0, 1) WITH NOWAIT;

        DELETE
        scg
        FROM
            dbo.SubmissionCoverageGroup scg
            INNER JOIN dbo.CoverageGroup cg
                ON cg.CoverageGroupId = scg.CoverageGroupId

            INNER JOIN dbo.Specification s
                ON s.SpecificationId = cg.SpecificationId
        WHERE
            s.PlacementId IN (
                SELECT PlacementId FROM @Placements
            );

        -- dbo.CoverageGroup
        SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' DELETE dbo.CoverageGroup';

        RAISERROR(@msg, 0, 1) WITH NOWAIT;

        DELETE
        cg
        FROM
            dbo.CoverageGroup cg
            INNER JOIN dbo.Specification s
                ON s.SpecificationId = cg.SpecificationId
        WHERE
            s.PlacementId IN (
                SELECT PlacementId FROM @Placements
            );

        -- dbo.Specification
        SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' DELETE dbo.Specification';

        RAISERROR(@msg, 0, 1) WITH NOWAIT;

        DELETE FROM
               dbo.Specification
        WHERE
            PlacementId IN (
                SELECT PlacementId FROM @Placements
            );

        ---------------------
        -- End Specification Tree
        ---------------------

        ----------------------------
        -- Submission Container Tree
        ----------------------------
        -- PS.MarketResponseBasis
        SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' DELETE PS.MarketResponseBasis';

        RAISERROR(@msg, 0, 1) WITH NOWAIT;

        DELETE
        MRB
        FROM
            PS.MarketResponseBasis MRB
            INNER JOIN PS.MarketResponse MR
                ON MR.MarketResponseId = MRB.MarketResponseId

            INNER JOIN PS.NegotiationMarket NM
                ON NM.NegotiationMarketId = MR.NegotiationMarketId

            INNER JOIN PS.Negotiation N
                ON N.NegotiationId = NM.NegotiationId
        WHERE
            N.PlacementId IN (
                SELECT PlacementId FROM @Placements
            );

        -- PS.MarketResponse
        SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' DELETE PS.MarketResponse';

        RAISERROR(@msg, 0, 1) WITH NOWAIT;

        DELETE
        MR
        FROM
            PS.MarketResponse MR
            INNER JOIN PS.NegotiationMarket NM
                ON NM.NegotiationMarketId = MR.NegotiationMarketId

            INNER JOIN PS.Negotiation N
                ON N.NegotiationId = NM.NegotiationId
        WHERE
            N.PlacementId IN (
                SELECT PlacementId FROM @Placements
            );

        -- PS.Negotiation
        SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' DELETE PS.Negotiation';

        RAISERROR(@msg, 0, 1) WITH NOWAIT;

        DELETE
        N
        FROM
            PS.Negotiation N
        WHERE
            N.PlacementId IN (
                SELECT PlacementId FROM @Placements
            );

        -- dbo.MarketResponseSecurity
        SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' DELETE dbo.MarketResponseSecurity';

        RAISERROR(@msg, 0, 1) WITH NOWAIT;

        DELETE
        MRS
        FROM
            dbo.MarketResponseSecurity MRS
            INNER JOIN dbo.vwMarketResponse MR
                ON MR.Id = MRS.MarketResponseId

            INNER JOIN dbo.vwSubmissionMarket SCM
                ON SCM.SubmissionContainerMarketId = MR.SubmissionContainerMarketId

            INNER JOIN dbo.vwSubmissionContainer SC
                ON SC.SubmissionContainerId = SCM.SubmissionContainerId
        WHERE
            SC.PlacementId IN (
                SELECT PlacementId FROM @Placements
            );

        -- PS.NegotiationMarket
        SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' DELETE PS.NegotiationMarket';

        RAISERROR(@msg, 0, 1) WITH NOWAIT;

        DELETE
        NM
        FROM
            PS.NegotiationMarket NM
            INNER JOIN PS.Negotiation N
                ON N.NegotiationId = NM.NegotiationId
                   AND N.IsDeleted = 0
                   AND N.NegotiationKey LIKE 'SUBC|%'
                   AND N.DataSourceInstanceId = 50366

            INNER JOIN dbo.vwSubmissionContainer SC
                ON SC.SubmissionContainerId = TRY_CAST(SUBSTRING(N.NegotiationKey, 6, 11) AS INT)
        WHERE
            SC.PlacementId IN (
                SELECT PlacementId FROM @Placements
            );

        -------------------------
        -- Market Selection Tree
        -------------------------
        -- dbo.MarketInteractionFacilities
        SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' DELETE dbo.MarketInteractionFacilities';

        RAISERROR(@msg, 0, 1) WITH NOWAIT;

        DELETE
        mif
        FROM
            dbo.MarketInteractionFacilities mif
            INNER JOIN dbo.MarketInteraction mi
                ON mi.CarrierResponseId = mif.CarrierResponseId

            INNER JOIN dbo.MarketSelection ms
                ON ms.MarketSelectionId = mi.MarketSelectionId
        WHERE
            ms.PlacementId IN (
                SELECT PlacementId FROM @Placements
            );

        -- dbo.MarketInteraction
        SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' DELETE dbo.MarketInteraction';

        RAISERROR(@msg, 0, 1) WITH NOWAIT;

        DELETE
        mi
        FROM
            dbo.MarketInteraction mi
            INNER JOIN dbo.MarketSelection ms
                ON ms.MarketSelectionId = mi.MarketSelectionId
        WHERE
            ms.PlacementId IN (
                SELECT PlacementId FROM @Placements
            );

        -- dbo.MarketSelection
        SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' DELETE dbo.MarketSelection';

        RAISERROR(@msg, 0, 1) WITH NOWAIT;

        DELETE FROM
               dbo.MarketSelection
        WHERE
            PlacementId IN (
                SELECT PlacementId FROM @Placements
            );

        -- dbo.PlacementMarket
        SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' DELETE dbo.PlacementMarket';

        RAISERROR(@msg, 0, 1) WITH NOWAIT;

        DELETE FROM
               dbo.PlacementMarket
        WHERE
            PlacementId IN (
                SELECT PlacementId FROM @Placements
            );

        -- dbo.PlacementMarketValidation
        SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' DELETE dbo.PlacementMarketValidation';

        RAISERROR(@msg, 0, 1) WITH NOWAIT;

        DELETE FROM
               dbo.PlacementMarketValidation
        WHERE
            PlacementId IN (
                SELECT PlacementId FROM @Placements
            );

        -------------------------
        -- End Market Selection Tree
        -------------------------

        -- dbo.SubmissionCoverageGroup
        SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' DELETE dbo.SubmissionCoverageGroup';

        RAISERROR(@msg, 0, 1) WITH NOWAIT;

        DELETE
        scg
        FROM
            dbo.SubmissionCoverageGroup scg
            INNER JOIN dbo.vwSubmissionContainer s
                ON s.SubmissionContainerId = scg.SubmissionContainerId
        WHERE
            s.PlacementId IN (
                SELECT PlacementId FROM @Placements
            );

        -- PS.NegotiationContract
        SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' DELETE PS.NegotiationContract';

        RAISERROR(@msg, 0, 1) WITH NOWAIT;

        DELETE
        NC
        FROM
            PS.NegotiationContract NC
            INNER JOIN PS.Negotiation N
                ON NC.NegotiationId = N.NegotiationId

            INNER JOIN dbo.vwSubmissionContainer s
                ON s.SubmissionContainerId = TRY_CAST(SUBSTRING(N.NegotiationKey, 6, 11) AS INT)
        WHERE
            s.PlacementId IN (
                SELECT PlacementId FROM @Placements
            );

        -- PS.Negotiation
        SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' DELETE PS.Negotiation';

        RAISERROR(@msg, 0, 1) WITH NOWAIT;

        DELETE FROM
               PS.Negotiation
        WHERE
            PlacementId IN (
                SELECT PlacementId FROM @Placements
            );

        SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' DELETE PS.Negotiation';

        RAISERROR(@msg, 0, 1) WITH NOWAIT;

        DELETE FROM
               PS.Negotiation
        WHERE
            PlacementId IN (
                SELECT PlacementId FROM @Placements
            );

        ----------------------------
        -- End Submission Container Tree
        ----------------------------
        -----------------------------------------------------
        -- ContractDocumentElement
        -----------------------------------------------------
        SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' DELETE dbo.ContractDocumentElement';

        RAISERROR(@msg, 0, 1) WITH NOWAIT;

        DELETE
        cde
        FROM
            dbo.ContractDocumentElement cde
            INNER JOIN PS.Contract c
                ON c.ContractId = cde.ContractId
        WHERE
            c.PlacementId IN (
                SELECT PlacementId FROM @Placements
            );

        ---------------------
        -- Specification Tree
        ---------------------
        -- dbo.CoverageGroupSection
        SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' DELETE dbo.CoverageGroupSection';

        RAISERROR(@msg, 0, 1) WITH NOWAIT;

        DELETE
        cgs
        FROM
            dbo.CoverageGroupSection cgs
            INNER JOIN dbo.CoverageGroup cg
                ON cg.CoverageGroupId = cgs.CoverageGroupId

            INNER JOIN dbo.Specification s
                ON s.SpecificationId = cg.SpecificationId
        WHERE
            s.PlacementId IN (
                SELECT PlacementId FROM @Placements
            );

        -- dbo.CoverageGroup
        SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' DELETE dbo.CoverageGroup';

        RAISERROR(@msg, 0, 1) WITH NOWAIT;

        DELETE
        cg
        FROM
            dbo.CoverageGroup cg
            INNER JOIN dbo.Specification s
                ON s.SpecificationId = cg.SpecificationId
        WHERE
            s.PlacementId IN (
                SELECT PlacementId FROM @Placements
            );

        -- dbo.Specification
        SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' DELETE dbo.Specification';

        RAISERROR(@msg, 0, 1) WITH NOWAIT;

        DELETE FROM
               dbo.Specification
        WHERE
            PlacementId IN (
                SELECT PlacementId FROM @Placements
            );

        ---------------------
        -- End Specification Tree
        ---------------------

        -------------------------------------------
        -- Placement Structure tree
        -------------------------------------------
        -- dbo.PlacementProduct
        SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' DELETE dbo.PlacementProduct';

        RAISERROR(@msg, 0, 1) WITH NOWAIT;

        DELETE
        pp
        FROM
            dbo.PlacementProduct pp
            INNER JOIN dbo.PlacementStructure plcst
                ON plcst.PlacementStructureId = pp.PlacementStructureId
        WHERE
            plcst.PlacementId IN (
                SELECT PlacementId FROM @Placements
            );

        -- dbo.Layer                         
        SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' DELETE dbo.Layer (2)';

        RAISERROR(@msg, 0, 1) WITH NOWAIT;

        DELETE
        l
        FROM
            dbo.Layer l
            INNER JOIN dbo.PlacementStructure plcst
                ON plcst.PlacementStructureId = l.PlacementStructureId
        WHERE
            plcst.PlacementId IN (
                SELECT PlacementId FROM @Placements
            );

        -- dbo.Layer                         
        SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' DELETE dbo.Layer';

        RAISERROR(@msg, 0, 1) WITH NOWAIT;

        DELETE FROM
               dbo.Layer
        WHERE
            PlacementId IN (
                SELECT PlacementId FROM @Placements
            );

        -- dbo.PlacementStructure
        SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' DELETE dbo.PlacementStructure';

        RAISERROR(@msg, 0, 1) WITH NOWAIT;

        DELETE FROM
               dbo.PlacementStructure
        WHERE
            PlacementId IN (
                SELECT PlacementId FROM @Placements
            );

        -------------------------------------------
        -- End Placement Structure tree
        -------------------------------------------

        -----------------------------------------------------
        -- Placement Risk Definition Element tree
        -- Changed from previous version
        -----------------------------------------------------

        -- dbo.PlacementRiskDefinitionElement as can't guarantee the delete order will obey.
        SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' DELETE dbo.PlacementRiskDefinitionElement';

        RAISERROR(@msg, 0, 1) WITH NOWAIT;

        -- Then do the delete.
        DELETE FROM
               dbo.PlacementRiskDefinitionElement
        WHERE
            PlacementId IN (
                SELECT PlacementId FROM @Placements
            );

        -------------------------------------------
        -- End Placement Risk Definition Element tree
        -------------------------------------------
        -->RiskStructureContractLink
        SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' DELETE PS.RiskStructureContractLink';

        RAISERROR(@msg, 0, 1) WITH NOWAIT;

        DELETE
        rsc
        FROM
            PS.RiskStructureContract rsc
            INNER JOIN PS.RiskStructure rs
                ON rsc.RiskStructureId = rs.RiskStructureId
        WHERE
            rs.PlacementId IN (
                SELECT PlacementId FROM @Placements
            );

        -->RiskStructurePolicyLink
        SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' DELETE PS.RiskStructurePolicyLink';

        RAISERROR(@msg, 0, 1) WITH NOWAIT;

        DELETE
        rsp
        FROM
            PS.RiskStructurePolicy rsp
            INNER JOIN PS.RiskStructure rs
                ON rsp.RiskStructureId = rs.RiskStructureId
        WHERE
            rs.PlacementId IN (
                SELECT PlacementId FROM @Placements
            );

        -->RiskStructure
        SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' DELETE PS.RiskStructure';

        RAISERROR(@msg, 0, 1) WITH NOWAIT;

        DELETE FROM
               PS.RiskStructure
        WHERE
            PlacementId IN (
                SELECT PlacementId FROM @Placements
            );

        -- PS.RiskProfile
        SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' DELETE PS.RiskProfile';

        RAISERROR(@msg, 0, 1) WITH NOWAIT;

        DELETE FROM
               PS.RiskProfile
        WHERE
            PlacementId IN (
                SELECT PlacementId FROM @Placements
            );

        -->QuoteComparison
        SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' DELETE PS.QuoteComparison';

        RAISERROR(@msg, 0, 1) WITH NOWAIT;

        DELETE FROM
               PS.QuoteComparison
        WHERE
            PlacementId IN (
                SELECT PlacementId FROM @Placements
            );

        -->ClientProposalPlacement
        SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' DELETE PS.ClientProposalPlacement';

        RAISERROR(@msg, 0, 1) WITH NOWAIT;

        DELETE FROM
               PS.ClientProposalPlacement
        WHERE
            PlacementId IN (
                SELECT PlacementId FROM @Placements
            );

        -->rpt.ContractDocument
        SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' rpt.ContractDocument';

        RAISERROR(@msg, 0, 1) WITH NOWAIT;

        DELETE
        cd
        FROM
            rpt.ContractDocument cd
            INNER JOIN rpt.Contract c
                ON cd.ContractId = c.ContractId
        WHERE
            c.PlacementId IN (
                SELECT PlacementId FROM @Placements
            );

        -->rpt.Contract
        SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' DELETE rpt.Contract';

        RAISERROR(@msg, 0, 1) WITH NOWAIT;

        DELETE FROM
               rpt.Contract
        WHERE
            PlacementId IN (
                SELECT PlacementId FROM @Placements
            );

        -->rpt.SubmissionMarketSelection
        SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' DELETE rpt.SubmissionMarketSelection';

        RAISERROR(@msg, 0, 1) WITH NOWAIT;

        DELETE FROM
               rpt.SubmissionMarketSelection
        WHERE
            PlacementId IN (
                SELECT PlacementId FROM @Placements
            );

        -->rpt.AuditUser
        SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' DELETE rpt.AuditUser';

        RAISERROR(@msg, 0, 1) WITH NOWAIT;

        DELETE FROM
               rpt.AuditUser
        WHERE
            PlacementId IN (
                SELECT PlacementId FROM @Placements
            );

        -->rpt.MarketActivity
        SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' DELETE rpt.MarketActivity';

        RAISERROR(@msg, 0, 1) WITH NOWAIT;

        DELETE FROM
               rpt.MarketActivity
        WHERE
            PlacementId IN (
                SELECT PlacementId FROM @Placements
            );

        -->rpt.PlacementPrimaryRole
        SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' DELETE rpt.PlacementPrimaryRole';

        RAISERROR(@msg, 0, 1) WITH NOWAIT;

        DELETE FROM
               rpt.PlacementPrimaryRole
        WHERE
            PlacementId IN (
                SELECT PlacementId FROM @Placements
            );

        -->rpt.PlacementSecurity
        SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' DELETE rpt.PlacementSecurity';

        RAISERROR(@msg, 0, 1) WITH NOWAIT;

        DELETE FROM
               rpt.PlacementSecurity
        WHERE
            PlacementId IN (
                SELECT PlacementId FROM @Placements
            );

        -- dbo.MergedPlacements
        SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' DELETE dbo.MergedPlacements (1)';

        RAISERROR(@msg, 0, 1) WITH NOWAIT;

        DELETE FROM
               dbo.MergedPlacements
        WHERE
            MergedPlacementId IN (
                SELECT PlacementId FROM @Placements
            );

        SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' DELETE dbo.MergedPlacements (2)';

        RAISERROR(@msg, 0, 1) WITH NOWAIT;

        DELETE FROM
               dbo.MergedPlacements
        WHERE
            OriginalPlacementId IN (
                SELECT PlacementId FROM @Placements
            );

        -- dbo.PlacementExtension
        SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' DELETE dbo.PlacementExtension';

        RAISERROR(@msg, 0, 1) WITH NOWAIT;

        DELETE FROM
               dbo.PlacementExtension
        WHERE
            PlacementId IN (
                SELECT PlacementId FROM @Placements
            );

        -- dbo.PlacementOrganisation
        SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' DELETE dbo.PlacementOrganisation';

        RAISERROR(@msg, 0, 1) WITH NOWAIT;

        DELETE FROM
               dbo.PlacementOrganisation
        WHERE
            PlacementId IN (
                SELECT PlacementId FROM @Placements
            );

        -- dbo.PlacementPartyRole
        SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' DELETE dbo.PlacementPartyRole';

        RAISERROR(@msg, 0, 1) WITH NOWAIT;

        DELETE FROM
               dbo.PlacementPartyRole
        WHERE
            PlacementId IN (
                SELECT PlacementId FROM @Placements
            );

        -- dbo.PlacementPremium
        SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' DELETE dbo.PlacementPremium';

        RAISERROR(@msg, 0, 1) WITH NOWAIT;

        DELETE FROM
               dbo.PlacementPremium
        WHERE
            PlacementId IN (
                SELECT PlacementId FROM @Placements
            );

        -- dbo.PlacementPolicy
        SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' DELETE dbo.PlacementPolicy';

        RAISERROR(@msg, 0, 1) WITH NOWAIT;

        DELETE FROM
               dbo.PlacementPolicy
        WHERE
            PlacementId IN (
                SELECT PlacementId FROM @Placements
            );

        -- dbo.PlacementSystemAudit
        SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' DELETE dbo.PlacementSystemAudit';

        RAISERROR(@msg, 0, 1) WITH NOWAIT;

        DELETE FROM
               dbo.PlacementSystemAudit
        WHERE
            PlacementId IN (
                SELECT PlacementId FROM @Placements
            );

        -- dbo.PlacementTeamMember
        SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' DELETE dbo.PlacementTeamMember';

        RAISERROR(@msg, 0, 1) WITH NOWAIT;

        DELETE FROM
               dbo.PlacementTeamMember
        WHERE
            PlacementId IN (
                SELECT PlacementId FROM @Placements
            );

        -- dbo.PlacementTeams
        SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' DELETE dbo.PlacementTeams';

        RAISERROR(@msg, 0, 1) WITH NOWAIT;

        DELETE FROM
               dbo.PlacementTeams
        WHERE
            PlacementID IN (
                SELECT PlacementId FROM @Placements
            );

        -- dbo.PlacementWorker
        SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' DELETE dbo.PlacementWorker';

        RAISERROR(@msg, 0, 1) WITH NOWAIT;

        DELETE FROM
               dbo.PlacementWorker
        WHERE
            PlacementId IN (
                SELECT PlacementId FROM @Placements
            );

        -- Rules.PlacementPolicies - PlacementID is not the same as elsewhere so use DataSourceInstanceID
        SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' DELETE Rules.PlacementPolicies';

        RAISERROR(@msg, 0, 1) WITH NOWAIT;

        -- This is different as we can't just delete all.
        DELETE
        PP
        FROM
            Rules.PlacementPolicies PP
            JOIN dbo.Placement P
                ON P.PlacementReference = PP.PlacementId
                   AND P.GroupingRunId = PP.RunId
                   AND P.GroupingRuleId = PP.RuleId
        WHERE
            P.PlacementId IN (
                SELECT PlacementId FROM @Placements
            );

        -- dbo.PlacementListener
        SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' DELETE dbo.PlacementListener';

        RAISERROR(@msg, 0, 1) WITH NOWAIT;

        DELETE FROM
               dbo.PlacementListener
        WHERE
            PlacementId IN (
                SELECT PlacementId FROM @Placements
            );

        -- rpt.ContractTimeline
        SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' DELETE rpt.ContractTimeline';

        RAISERROR(@msg, 0, 1) WITH NOWAIT;

        DELETE FROM
               rpt.ContractTimeline
        WHERE
            PlacementId IN (
                SELECT PlacementId FROM @Placements
            );

        -- Contract
        SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' DELETE dbo.Contract';

        RAISERROR(@msg, 0, 1) WITH NOWAIT;

        DELETE FROM
               PS.Contract
        WHERE
            PlacementId IN (
                SELECT PlacementId FROM @Placements
            );

        -- PlacementPolicy
        SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' DELETE dbo.PlacementPolicy';

        RAISERROR(@msg, 0, 1) WITH NOWAIT;

        DELETE FROM
               dbo.PlacementPolicy
        WHERE
            PlacementID IN (
                SELECT PlacementId FROM @Placements
            );

        -- dbo.Placement
        SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' DELETE dbo.Placement';

        RAISERROR(@msg, 0, 1) WITH NOWAIT;

        DELETE FROM
               dbo.Placement
        WHERE
            PlacementId IN (
                SELECT PlacementId FROM @Placements
            );

        /*Log the placement delete details*/
        INSERT INTO
            Support.DeletePlacementLog
            (
                PlacementId
              , DeletedUTCDate
              , ReasonforDelete
            )
        SELECT
            PlacementId
          , GETUTCDATE()
          , @Reason
        FROM
            @Placements;
    END;

    -- Placements found to delete

    -- ROLLBACK TRANSACTION;
    SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' *** All Done ***';

    RAISERROR(@msg, 0, 1) WITH NOWAIT;

    COMMIT;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();
    SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' *** ERROR:' + @ErrorMessage + ' ***';

    RAISERROR(@msg, 0, 1) WITH NOWAIT;

    SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' *** Rolling Back ***';

    RAISERROR(@msg, 0, 1) WITH NOWAIT;

    ROLLBACK TRANSACTION;

    SET @msg = CONVERT(NVARCHAR(23), GETDATE(), 121) + ' *** Roll Back Complete ***';

    RAISERROR(@msg, 0, 1) WITH NOWAIT;
END CATCH;
