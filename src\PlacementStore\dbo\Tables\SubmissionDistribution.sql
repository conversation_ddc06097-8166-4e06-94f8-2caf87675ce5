CREATE TABLE dbo.SubmissionDistribution (
    SubmissionMarketId          INT NOT NULL
  , SubmissionContainerMarketId INT NOT NULL
  , SubmissionId                INT NULL
  , CONSTRAINT PK_dbo_SubmissionDistribution
        PRIMARY KEY
        (
            SubmissionMarketId
        )
  , CONSTRAINT FK_dbo_SubmissionDistribution_BP_Submission
        FOREIGN KEY
        (
            SubmissionId
        )
        REFERENCES BP.Submission
        (
            Id
        ) ON DELETE CASCADE
);
GO

CREATE INDEX IX_dbo_SubmissionDistribution_SubmissionContainerMarketId
ON dbo.SubmissionDistribution
(
    SubmissionContainerMarketId
);
GO

CREATE INDEX IX_dbo_SubmissionDistribution_SubmissionId
ON dbo.SubmissionDistribution
(
    SubmissionId
);
