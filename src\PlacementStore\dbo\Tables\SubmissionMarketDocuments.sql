CREATE TABLE SubmissionMarketDocuments (
    SubmissionId                INT NOT NULL
  , SubmissionContainerMarketId INT NOT NULL
  , NumberOfDocuments           INT
  , LastDocumentUpdate          DATETIMEOFFSET(7)
  , FirstDocumentUpdate         DATETIMEOFFSET(7)
  , NumberOfPortalUsers         INT
  , FirstDocumentDownload       DATETIME2(7)
  , LastDocumentDownload        DATETIME2(7)
  , DocumentNotDownloaded       INT
  , DocumentDownloaded          INT
  , CONSTRAINT PK_dbo_SubmissionMarketDocuments
        PRIMARY KEY
        (
            SubmissionId
          , SubmissionContainerMarketId
        )
  , CONSTRAINT FK_dbo_SubmissionMarketDocuments_BP_Submission
        FOREIGN KEY
        (
            SubmissionId
        )
        REFERENCES BP.Submission
        (
            Id
        ) ON DELETE CASCADE
);
GO

CREATE INDEX IX_dbo_SubmissionMarketDocuments_SubmissionContainerMarketId
ON dbo.SubmissionMarketDocuments
(
    SubmissionContainerMarketId
);
GO