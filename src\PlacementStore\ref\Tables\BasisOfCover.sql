CREATE TABLE ref.BasisOfCover (
    BasisOfCoverId       INT           NOT NULL IDENTITY(1, 1)
  , DataSourceInstanceId INT           NOT NULL
  , BasisOfCoverKey      NVARCHAR(100) NOT NULL
  , TranslationKey       NVARCHAR(100) NOT NULL
  , BasisOfCover         NVARCHAR(200) NULL
  , ScopeId              INT           NULL
  , ETLCreatedDate       DATETIME2(7)  NOT NULL
  , ETLUpdatedDate       DATETIME2(7)  NOT NULL
  , SourceUpdatedDate    DATETIME2(7)  NOT NULL
  , IsDeprecated         BIT           NOT NULL
  , CONSTRAINT PK_ref_BasisOfCover
        PRIMARY KEY
        (
            BasisOfCoverId
        )
);
