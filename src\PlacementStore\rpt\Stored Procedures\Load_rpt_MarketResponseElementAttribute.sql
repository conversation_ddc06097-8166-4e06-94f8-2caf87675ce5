/*
Lineage
rpt.MarketResponseElementAttribute.CarrierResponseId=PS.MarketResponse.MarketResponseKey
rpt.MarketResponseElementAttribute.MarketResponseId=PS.MarketResponse.MarketResponseId
rpt.MarketResponseElementAttribute.AggregateLimitAmount=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.AggregateLimitAmount=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.AggregateLimitAmount=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.AggregateLimitAmount=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.AggregateLimitAmount=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.AggregateLimitAmount=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.AggregateLimitAmount=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.AggregateLimitAmount=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.AggregateLimitAmountUSD=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.AggregateLimitAmountUSD=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.AggregateLimitAmountUSD=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.AggregateLimitAmountUSD=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.AggregateLimitAmountUSD=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.AggregateLimitAmountUSD=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.AggregateLimitAmountUSD=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.AggregateLimitAmountUSD=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.AggregateLimitBasis=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.AggregateLimitBasis=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.AggregateLimitBasis=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.AggregateLimitBasis=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.AggregateLimitBasis=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.AggregateLimitBasis=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.AggregateLimitBasis=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.AggregateLimitBasis=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.AggregateLimitCurrency=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.AggregateLimitCurrency=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.AggregateLimitCurrency=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.AggregateLimitCurrency=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.AggregateLimitCurrency=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.AggregateLimitCurrency=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.AggregateLimitCurrency=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.AggregateLimitCurrency=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.AggregateLimitInclusionType=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.AggregateLimitInclusionType=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.AggregateLimitInclusionType=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.AggregateLimitInclusionType=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.AggregateLimitInclusionType=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.AggregateLimitInclusionType=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.AggregateLimitInclusionType=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.AggregateLimitInclusionType=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.AggregateLimitNotApplicable=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.AggregateLimitNotApplicable=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.AggregateLimitNotApplicable=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.AggregateLimitNotApplicable=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.AggregateLimitNotApplicable=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.AggregateLimitNotApplicable=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.AggregateLimitNotApplicable=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.AggregateLimitNotApplicable=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.AggregateLimitPercentage=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.AggregateLimitPercentage=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.AggregateLimitPercentage=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.AggregateLimitPercentage=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.AggregateLimitPercentage=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.AggregateLimitPercentage=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.AggregateLimitPercentage=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.AggregateLimitPercentage=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.AggregateLimitText=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.AggregateLimitText=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.AggregateLimitText=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.AggregateLimitText=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.AggregateLimitText=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.AggregateLimitText=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.AggregateLimitText=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.AggregateLimitText=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.AutoLiabilityEffectiveDate=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.AutoLiabilityEffectiveDate=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.AutoLiabilityEffectiveDate=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.AutoLiabilityEffectiveDate=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.AutoLiabilityEffectiveDate=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.AutoLiabilityEffectiveDate=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.AutoLiabilityEffectiveDate=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.AutoLiabilityEffectiveDate=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.AutoLiabilityExpirationDate=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.AutoLiabilityExpirationDate=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.AutoLiabilityExpirationDate=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.AutoLiabilityExpirationDate=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.AutoLiabilityExpirationDate=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.AutoLiabilityExpirationDate=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.AutoLiabilityExpirationDate=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.AutoLiabilityExpirationDate=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.AutoPhysicalDamageCollisionAmount=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.AutoPhysicalDamageCollisionAmount=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.AutoPhysicalDamageCollisionAmount=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.AutoPhysicalDamageCollisionAmount=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.AutoPhysicalDamageCollisionAmount=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.AutoPhysicalDamageCollisionAmount=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.AutoPhysicalDamageCollisionAmount=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.AutoPhysicalDamageCollisionAmount=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.AutoPhysicalDamageCollisionAmountUSD=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.AutoPhysicalDamageCollisionAmountUSD=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.AutoPhysicalDamageCollisionAmountUSD=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.AutoPhysicalDamageCollisionAmountUSD=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.AutoPhysicalDamageCollisionAmountUSD=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.AutoPhysicalDamageCollisionAmountUSD=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.AutoPhysicalDamageCollisionAmountUSD=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.AutoPhysicalDamageCollisionAmountUSD=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.AutoPhysicalDamageCollisionBasis=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.AutoPhysicalDamageCollisionBasis=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.AutoPhysicalDamageCollisionBasis=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.AutoPhysicalDamageCollisionBasis=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.AutoPhysicalDamageCollisionBasis=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.AutoPhysicalDamageCollisionBasis=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.AutoPhysicalDamageCollisionBasis=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.AutoPhysicalDamageCollisionBasis=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.AutoPhysicalDamageCollisionCurrency=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.AutoPhysicalDamageCollisionCurrency=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.AutoPhysicalDamageCollisionCurrency=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.AutoPhysicalDamageCollisionCurrency=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.AutoPhysicalDamageCollisionCurrency=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.AutoPhysicalDamageCollisionCurrency=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.AutoPhysicalDamageCollisionCurrency=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.AutoPhysicalDamageCollisionCurrency=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.AutoPhysicalDamageCollisionInclusionType=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.AutoPhysicalDamageCollisionInclusionType=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.AutoPhysicalDamageCollisionInclusionType=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.AutoPhysicalDamageCollisionInclusionType=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.AutoPhysicalDamageCollisionInclusionType=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.AutoPhysicalDamageCollisionInclusionType=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.AutoPhysicalDamageCollisionInclusionType=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.AutoPhysicalDamageCollisionInclusionType=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.AutoPhysicalDamageCollisionText=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.AutoPhysicalDamageCollisionText=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.AutoPhysicalDamageCollisionText=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.AutoPhysicalDamageCollisionText=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.AutoPhysicalDamageCollisionText=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.AutoPhysicalDamageCollisionText=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.AutoPhysicalDamageCollisionText=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.AutoPhysicalDamageCollisionText=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.AutoPhysicalDamageCollisionValuation=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.AutoPhysicalDamageCollisionValuation=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.AutoPhysicalDamageCollisionValuation=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.AutoPhysicalDamageCollisionValuation=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.AutoPhysicalDamageCollisionValuation=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.AutoPhysicalDamageCollisionValuation=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.AutoPhysicalDamageCollisionValuation=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.AutoPhysicalDamageCollisionValuation=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.AutoPhysicalDamageComprehensiveAmount=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.AutoPhysicalDamageComprehensiveAmount=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.AutoPhysicalDamageComprehensiveAmount=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.AutoPhysicalDamageComprehensiveAmount=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.AutoPhysicalDamageComprehensiveAmount=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.AutoPhysicalDamageComprehensiveAmount=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.AutoPhysicalDamageComprehensiveAmount=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.AutoPhysicalDamageComprehensiveAmount=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.AutoPhysicalDamageComprehensiveAmountUSD=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.AutoPhysicalDamageComprehensiveAmountUSD=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.AutoPhysicalDamageComprehensiveAmountUSD=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.AutoPhysicalDamageComprehensiveAmountUSD=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.AutoPhysicalDamageComprehensiveAmountUSD=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.AutoPhysicalDamageComprehensiveAmountUSD=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.AutoPhysicalDamageComprehensiveAmountUSD=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.AutoPhysicalDamageComprehensiveAmountUSD=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.AutoPhysicalDamageComprehensiveBasis=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.AutoPhysicalDamageComprehensiveBasis=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.AutoPhysicalDamageComprehensiveBasis=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.AutoPhysicalDamageComprehensiveBasis=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.AutoPhysicalDamageComprehensiveBasis=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.AutoPhysicalDamageComprehensiveBasis=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.AutoPhysicalDamageComprehensiveBasis=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.AutoPhysicalDamageComprehensiveBasis=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.AutoPhysicalDamageComprehensiveCurrency=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.AutoPhysicalDamageComprehensiveCurrency=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.AutoPhysicalDamageComprehensiveCurrency=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.AutoPhysicalDamageComprehensiveCurrency=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.AutoPhysicalDamageComprehensiveCurrency=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.AutoPhysicalDamageComprehensiveCurrency=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.AutoPhysicalDamageComprehensiveCurrency=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.AutoPhysicalDamageComprehensiveCurrency=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.AutoPhysicalDamageComprehensiveInclusionType=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.AutoPhysicalDamageComprehensiveInclusionType=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.AutoPhysicalDamageComprehensiveInclusionType=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.AutoPhysicalDamageComprehensiveInclusionType=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.AutoPhysicalDamageComprehensiveInclusionType=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.AutoPhysicalDamageComprehensiveInclusionType=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.AutoPhysicalDamageComprehensiveInclusionType=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.AutoPhysicalDamageComprehensiveInclusionType=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.AutoPhysicalDamageComprehensivePercentage=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.AutoPhysicalDamageComprehensivePercentage=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.AutoPhysicalDamageComprehensivePercentage=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.AutoPhysicalDamageComprehensivePercentage=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.AutoPhysicalDamageComprehensivePercentage=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.AutoPhysicalDamageComprehensivePercentage=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.AutoPhysicalDamageComprehensivePercentage=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.AutoPhysicalDamageComprehensivePercentage=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.AutoPhysicalDamageComprehensiveText=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.AutoPhysicalDamageComprehensiveText=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.AutoPhysicalDamageComprehensiveText=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.AutoPhysicalDamageComprehensiveText=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.AutoPhysicalDamageComprehensiveText=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.AutoPhysicalDamageComprehensiveText=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.AutoPhysicalDamageComprehensiveText=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.AutoPhysicalDamageComprehensiveText=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.AutoPhysicalDamageComprehensiveValuation=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.AutoPhysicalDamageComprehensiveValuation=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.AutoPhysicalDamageComprehensiveValuation=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.AutoPhysicalDamageComprehensiveValuation=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.AutoPhysicalDamageComprehensiveValuation=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.AutoPhysicalDamageComprehensiveValuation=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.AutoPhysicalDamageComprehensiveValuation=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.AutoPhysicalDamageComprehensiveValuation=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.BasisofCoverBasis=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.BasisofCoverBasis=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.BasisofCoverBasis=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.BasisofCoverBasis=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.BasisofCoverBasis=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.BasisofCoverBasis=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.BasisofCoverBasis=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.BasisofCoverBasis=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.BodilyInjuryByAccidentEachAccidentAmount=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.BodilyInjuryByAccidentEachAccidentAmount=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.BodilyInjuryByAccidentEachAccidentAmount=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.BodilyInjuryByAccidentEachAccidentAmount=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.BodilyInjuryByAccidentEachAccidentAmount=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.BodilyInjuryByAccidentEachAccidentAmount=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.BodilyInjuryByAccidentEachAccidentAmount=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.BodilyInjuryByAccidentEachAccidentAmount=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.BodilyInjuryByAccidentEachAccidentAmountUSD=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.BodilyInjuryByAccidentEachAccidentAmountUSD=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.BodilyInjuryByAccidentEachAccidentAmountUSD=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.BodilyInjuryByAccidentEachAccidentAmountUSD=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.BodilyInjuryByAccidentEachAccidentAmountUSD=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.BodilyInjuryByAccidentEachAccidentAmountUSD=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.BodilyInjuryByAccidentEachAccidentAmountUSD=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.BodilyInjuryByAccidentEachAccidentAmountUSD=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.BodilyInjuryByAccidentEachAccidentBasis=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.BodilyInjuryByAccidentEachAccidentBasis=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.BodilyInjuryByAccidentEachAccidentBasis=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.BodilyInjuryByAccidentEachAccidentBasis=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.BodilyInjuryByAccidentEachAccidentBasis=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.BodilyInjuryByAccidentEachAccidentBasis=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.BodilyInjuryByAccidentEachAccidentBasis=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.BodilyInjuryByAccidentEachAccidentBasis=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.BodilyInjuryByAccidentEachAccidentCurrency=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.BodilyInjuryByAccidentEachAccidentCurrency=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.BodilyInjuryByAccidentEachAccidentCurrency=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.BodilyInjuryByAccidentEachAccidentCurrency=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.BodilyInjuryByAccidentEachAccidentCurrency=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.BodilyInjuryByAccidentEachAccidentCurrency=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.BodilyInjuryByAccidentEachAccidentCurrency=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.BodilyInjuryByAccidentEachAccidentCurrency=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.BodilyInjuryByAccidentEachAccidentInclusionType=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.BodilyInjuryByAccidentEachAccidentInclusionType=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.BodilyInjuryByAccidentEachAccidentInclusionType=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.BodilyInjuryByAccidentEachAccidentInclusionType=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.BodilyInjuryByAccidentEachAccidentInclusionType=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.BodilyInjuryByAccidentEachAccidentInclusionType=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.BodilyInjuryByAccidentEachAccidentInclusionType=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.BodilyInjuryByAccidentEachAccidentInclusionType=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.BodilyInjuryByAccidentEachAccidentPercentage=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.BodilyInjuryByAccidentEachAccidentPercentage=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.BodilyInjuryByAccidentEachAccidentPercentage=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.BodilyInjuryByAccidentEachAccidentPercentage=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.BodilyInjuryByAccidentEachAccidentPercentage=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.BodilyInjuryByAccidentEachAccidentPercentage=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.BodilyInjuryByAccidentEachAccidentPercentage=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.BodilyInjuryByAccidentEachAccidentPercentage=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.BodilyInjuryByAccidentEachAccidentText=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.BodilyInjuryByAccidentEachAccidentText=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.BodilyInjuryByAccidentEachAccidentText=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.BodilyInjuryByAccidentEachAccidentText=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.BodilyInjuryByAccidentEachAccidentText=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.BodilyInjuryByAccidentEachAccidentText=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.BodilyInjuryByAccidentEachAccidentText=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.BodilyInjuryByAccidentEachAccidentText=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseaseEachEmployeeAmount=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseaseEachEmployeeAmount=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseaseEachEmployeeAmount=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseaseEachEmployeeAmount=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseaseEachEmployeeAmount=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseaseEachEmployeeAmount=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseaseEachEmployeeAmount=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseaseEachEmployeeAmount=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseaseEachEmployeeAmountUSD=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseaseEachEmployeeAmountUSD=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseaseEachEmployeeAmountUSD=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseaseEachEmployeeAmountUSD=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseaseEachEmployeeAmountUSD=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseaseEachEmployeeAmountUSD=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseaseEachEmployeeAmountUSD=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseaseEachEmployeeAmountUSD=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseaseEachEmployeeBasis=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseaseEachEmployeeBasis=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseaseEachEmployeeBasis=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseaseEachEmployeeBasis=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseaseEachEmployeeBasis=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseaseEachEmployeeBasis=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseaseEachEmployeeBasis=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseaseEachEmployeeBasis=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseaseEachEmployeeCurrency=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseaseEachEmployeeCurrency=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseaseEachEmployeeCurrency=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseaseEachEmployeeCurrency=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseaseEachEmployeeCurrency=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseaseEachEmployeeCurrency=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseaseEachEmployeeCurrency=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseaseEachEmployeeCurrency=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseaseEachEmployeeInclusionType=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseaseEachEmployeeInclusionType=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseaseEachEmployeeInclusionType=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseaseEachEmployeeInclusionType=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseaseEachEmployeeInclusionType=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseaseEachEmployeeInclusionType=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseaseEachEmployeeInclusionType=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseaseEachEmployeeInclusionType=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseaseEachEmployeeNumberOfUnits=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseaseEachEmployeeNumberOfUnits=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseaseEachEmployeeNumberOfUnits=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseaseEachEmployeeNumberOfUnits=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseaseEachEmployeeNumberOfUnits=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseaseEachEmployeeNumberOfUnits=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseaseEachEmployeeNumberOfUnits=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseaseEachEmployeeNumberOfUnits=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseaseEachEmployeePercentage=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseaseEachEmployeePercentage=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseaseEachEmployeePercentage=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseaseEachEmployeePercentage=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseaseEachEmployeePercentage=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseaseEachEmployeePercentage=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseaseEachEmployeePercentage=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseaseEachEmployeePercentage=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseaseEachEmployeeText=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseaseEachEmployeeText=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseaseEachEmployeeText=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseaseEachEmployeeText=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseaseEachEmployeeText=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseaseEachEmployeeText=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseaseEachEmployeeText=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseaseEachEmployeeText=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseasePolicyLimitAmount=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseasePolicyLimitAmount=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseasePolicyLimitAmount=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseasePolicyLimitAmount=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseasePolicyLimitAmount=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseasePolicyLimitAmount=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseasePolicyLimitAmount=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseasePolicyLimitAmount=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseasePolicyLimitAmountUSD=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseasePolicyLimitAmountUSD=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseasePolicyLimitAmountUSD=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseasePolicyLimitAmountUSD=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseasePolicyLimitAmountUSD=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseasePolicyLimitAmountUSD=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseasePolicyLimitAmountUSD=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseasePolicyLimitAmountUSD=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseasePolicyLimitBasis=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseasePolicyLimitBasis=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseasePolicyLimitBasis=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseasePolicyLimitBasis=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseasePolicyLimitBasis=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseasePolicyLimitBasis=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseasePolicyLimitBasis=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseasePolicyLimitBasis=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseasePolicyLimitCurrency=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseasePolicyLimitCurrency=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseasePolicyLimitCurrency=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseasePolicyLimitCurrency=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseasePolicyLimitCurrency=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseasePolicyLimitCurrency=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseasePolicyLimitCurrency=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseasePolicyLimitCurrency=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseasePolicyLimitInclusionType=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseasePolicyLimitInclusionType=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseasePolicyLimitInclusionType=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseasePolicyLimitInclusionType=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseasePolicyLimitInclusionType=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseasePolicyLimitInclusionType=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseasePolicyLimitInclusionType=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseasePolicyLimitInclusionType=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseasePolicyLimitPercentage=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseasePolicyLimitPercentage=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseasePolicyLimitPercentage=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseasePolicyLimitPercentage=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseasePolicyLimitPercentage=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseasePolicyLimitPercentage=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseasePolicyLimitPercentage=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseasePolicyLimitPercentage=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseasePolicyLimitText=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseasePolicyLimitText=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseasePolicyLimitText=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseasePolicyLimitText=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseasePolicyLimitText=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseasePolicyLimitText=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseasePolicyLimitText=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.BodilyInjuryByDiseasePolicyLimitText=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.ClaimsHandlingStructure=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.ClaimsHandlingStructure=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.ClaimsHandlingStructure=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.ClaimsHandlingStructure=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.ClaimsHandlingStructure=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.ClaimsHandlingStructure=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.ClaimsHandlingStructure=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.ClaimsHandlingStructure=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.DamageToPremisesRentedToYouAmount=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.DamageToPremisesRentedToYouAmount=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.DamageToPremisesRentedToYouAmount=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.DamageToPremisesRentedToYouAmount=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.DamageToPremisesRentedToYouAmount=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.DamageToPremisesRentedToYouAmount=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.DamageToPremisesRentedToYouAmount=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.DamageToPremisesRentedToYouAmount=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.DamageToPremisesRentedToYouAmountUSD=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.DamageToPremisesRentedToYouAmountUSD=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.DamageToPremisesRentedToYouAmountUSD=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.DamageToPremisesRentedToYouAmountUSD=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.DamageToPremisesRentedToYouAmountUSD=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.DamageToPremisesRentedToYouAmountUSD=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.DamageToPremisesRentedToYouAmountUSD=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.DamageToPremisesRentedToYouAmountUSD=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.DamageToPremisesRentedToYouBasis=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.DamageToPremisesRentedToYouBasis=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.DamageToPremisesRentedToYouBasis=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.DamageToPremisesRentedToYouBasis=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.DamageToPremisesRentedToYouBasis=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.DamageToPremisesRentedToYouBasis=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.DamageToPremisesRentedToYouBasis=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.DamageToPremisesRentedToYouBasis=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.DamageToPremisesRentedToYouCurrency=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.DamageToPremisesRentedToYouCurrency=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.DamageToPremisesRentedToYouCurrency=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.DamageToPremisesRentedToYouCurrency=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.DamageToPremisesRentedToYouCurrency=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.DamageToPremisesRentedToYouCurrency=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.DamageToPremisesRentedToYouCurrency=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.DamageToPremisesRentedToYouCurrency=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.DamageToPremisesRentedToYouInclusionType=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.DamageToPremisesRentedToYouInclusionType=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.DamageToPremisesRentedToYouInclusionType=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.DamageToPremisesRentedToYouInclusionType=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.DamageToPremisesRentedToYouInclusionType=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.DamageToPremisesRentedToYouInclusionType=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.DamageToPremisesRentedToYouInclusionType=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.DamageToPremisesRentedToYouInclusionType=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.DamageToPremisesRentedToYouText=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.DamageToPremisesRentedToYouText=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.DamageToPremisesRentedToYouText=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.DamageToPremisesRentedToYouText=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.DamageToPremisesRentedToYouText=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.DamageToPremisesRentedToYouText=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.DamageToPremisesRentedToYouText=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.DamageToPremisesRentedToYouText=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.DeductibleAmount=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.DeductibleAmount=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.DeductibleAmount=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.DeductibleAmount=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.DeductibleAmount=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.DeductibleAmount=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.DeductibleAmount=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.DeductibleAmount=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.DeductibleAmountUSD=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.DeductibleAmountUSD=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.DeductibleAmountUSD=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.DeductibleAmountUSD=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.DeductibleAmountUSD=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.DeductibleAmountUSD=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.DeductibleAmountUSD=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.DeductibleAmountUSD=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.DeductibleBasis=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.DeductibleBasis=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.DeductibleBasis=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.DeductibleBasis=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.DeductibleBasis=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.DeductibleBasis=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.DeductibleBasis=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.DeductibleBasis=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.DeductibleCurrency=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.DeductibleCurrency=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.DeductibleCurrency=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.DeductibleCurrency=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.DeductibleCurrency=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.DeductibleCurrency=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.DeductibleCurrency=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.DeductibleCurrency=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.DeductibleNumberOfUnits=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.DeductibleNumberOfUnits=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.DeductibleNumberOfUnits=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.DeductibleNumberOfUnits=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.DeductibleNumberOfUnits=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.DeductibleNumberOfUnits=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.DeductibleNumberOfUnits=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.DeductibleNumberOfUnits=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.DeductiblePercentage=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.DeductiblePercentage=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.DeductiblePercentage=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.DeductiblePercentage=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.DeductiblePercentage=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.DeductiblePercentage=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.DeductiblePercentage=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.DeductiblePercentage=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.DeductibleText=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.DeductibleText=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.DeductibleText=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.DeductibleText=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.DeductibleText=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.DeductibleText=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.DeductibleText=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.DeductibleText=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.EachOccurrenceLimitAmount=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.EachOccurrenceLimitAmount=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.EachOccurrenceLimitAmount=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.EachOccurrenceLimitAmount=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.EachOccurrenceLimitAmount=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.EachOccurrenceLimitAmount=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.EachOccurrenceLimitAmount=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.EachOccurrenceLimitAmount=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.EachOccurrenceLimitAmountUSD=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.EachOccurrenceLimitAmountUSD=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.EachOccurrenceLimitAmountUSD=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.EachOccurrenceLimitAmountUSD=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.EachOccurrenceLimitAmountUSD=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.EachOccurrenceLimitAmountUSD=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.EachOccurrenceLimitAmountUSD=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.EachOccurrenceLimitAmountUSD=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.EachOccurrenceLimitBasis=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.EachOccurrenceLimitBasis=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.EachOccurrenceLimitBasis=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.EachOccurrenceLimitBasis=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.EachOccurrenceLimitBasis=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.EachOccurrenceLimitBasis=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.EachOccurrenceLimitBasis=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.EachOccurrenceLimitBasis=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.EachOccurrenceLimitCurrency=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.EachOccurrenceLimitCurrency=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.EachOccurrenceLimitCurrency=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.EachOccurrenceLimitCurrency=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.EachOccurrenceLimitCurrency=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.EachOccurrenceLimitCurrency=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.EachOccurrenceLimitCurrency=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.EachOccurrenceLimitCurrency=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.EachOccurrenceLimitInclusionType=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.EachOccurrenceLimitInclusionType=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.EachOccurrenceLimitInclusionType=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.EachOccurrenceLimitInclusionType=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.EachOccurrenceLimitInclusionType=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.EachOccurrenceLimitInclusionType=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.EachOccurrenceLimitInclusionType=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.EachOccurrenceLimitInclusionType=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.EachOccurrenceLimitNumberOfUnits=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.EachOccurrenceLimitNumberOfUnits=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.EachOccurrenceLimitNumberOfUnits=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.EachOccurrenceLimitNumberOfUnits=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.EachOccurrenceLimitNumberOfUnits=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.EachOccurrenceLimitNumberOfUnits=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.EachOccurrenceLimitNumberOfUnits=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.EachOccurrenceLimitNumberOfUnits=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.EachOccurrenceLimitPercentage=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.EachOccurrenceLimitPercentage=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.EachOccurrenceLimitPercentage=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.EachOccurrenceLimitPercentage=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.EachOccurrenceLimitPercentage=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.EachOccurrenceLimitPercentage=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.EachOccurrenceLimitPercentage=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.EachOccurrenceLimitPercentage=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.EachOccurrenceLimitText=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.EachOccurrenceLimitText=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.EachOccurrenceLimitText=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.EachOccurrenceLimitText=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.EachOccurrenceLimitText=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.EachOccurrenceLimitText=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.EachOccurrenceLimitText=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.EachOccurrenceLimitText=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.EmployeeBenefitsLiabilityDeductibleRetentionAmountAmount=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.EmployeeBenefitsLiabilityDeductibleRetentionAmountAmount=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.EmployeeBenefitsLiabilityDeductibleRetentionAmountAmount=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.EmployeeBenefitsLiabilityDeductibleRetentionAmountAmount=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.EmployeeBenefitsLiabilityDeductibleRetentionAmountAmount=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.EmployeeBenefitsLiabilityDeductibleRetentionAmountAmount=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.EmployeeBenefitsLiabilityDeductibleRetentionAmountAmount=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.EmployeeBenefitsLiabilityDeductibleRetentionAmountAmount=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.EmployeeBenefitsLiabilityDeductibleRetentionAmountAmountUSD=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.EmployeeBenefitsLiabilityDeductibleRetentionAmountAmountUSD=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.EmployeeBenefitsLiabilityDeductibleRetentionAmountAmountUSD=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.EmployeeBenefitsLiabilityDeductibleRetentionAmountAmountUSD=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.EmployeeBenefitsLiabilityDeductibleRetentionAmountAmountUSD=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.EmployeeBenefitsLiabilityDeductibleRetentionAmountAmountUSD=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.EmployeeBenefitsLiabilityDeductibleRetentionAmountAmountUSD=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.EmployeeBenefitsLiabilityDeductibleRetentionAmountAmountUSD=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.EmployeeBenefitsLiabilityDeductibleRetentionAmountBasis=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.EmployeeBenefitsLiabilityDeductibleRetentionAmountBasis=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.EmployeeBenefitsLiabilityDeductibleRetentionAmountBasis=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.EmployeeBenefitsLiabilityDeductibleRetentionAmountBasis=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.EmployeeBenefitsLiabilityDeductibleRetentionAmountBasis=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.EmployeeBenefitsLiabilityDeductibleRetentionAmountBasis=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.EmployeeBenefitsLiabilityDeductibleRetentionAmountBasis=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.EmployeeBenefitsLiabilityDeductibleRetentionAmountBasis=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.EmployeeBenefitsLiabilityDeductibleRetentionAmountCurrency=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.EmployeeBenefitsLiabilityDeductibleRetentionAmountCurrency=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.EmployeeBenefitsLiabilityDeductibleRetentionAmountCurrency=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.EmployeeBenefitsLiabilityDeductibleRetentionAmountCurrency=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.EmployeeBenefitsLiabilityDeductibleRetentionAmountCurrency=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.EmployeeBenefitsLiabilityDeductibleRetentionAmountCurrency=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.EmployeeBenefitsLiabilityDeductibleRetentionAmountCurrency=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.EmployeeBenefitsLiabilityDeductibleRetentionAmountCurrency=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.EmployeeBenefitsLiabilityDeductibleRetentionAmountText=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.EmployeeBenefitsLiabilityDeductibleRetentionAmountText=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.EmployeeBenefitsLiabilityDeductibleRetentionAmountText=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.EmployeeBenefitsLiabilityDeductibleRetentionAmountText=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.EmployeeBenefitsLiabilityDeductibleRetentionAmountText=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.EmployeeBenefitsLiabilityDeductibleRetentionAmountText=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.EmployeeBenefitsLiabilityDeductibleRetentionAmountText=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.EmployeeBenefitsLiabilityDeductibleRetentionAmountText=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.EmployersLiabilityEffectiveDate=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.EmployersLiabilityEffectiveDate=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.EmployersLiabilityEffectiveDate=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.EmployersLiabilityEffectiveDate=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.EmployersLiabilityEffectiveDate=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.EmployersLiabilityEffectiveDate=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.EmployersLiabilityEffectiveDate=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.EmployersLiabilityEffectiveDate=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.EmployersLiabilityExpirationDate=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.EmployersLiabilityExpirationDate=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.EmployersLiabilityExpirationDate=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.EmployersLiabilityExpirationDate=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.EmployersLiabilityExpirationDate=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.EmployersLiabilityExpirationDate=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.EmployersLiabilityExpirationDate=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.EmployersLiabilityExpirationDate=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.EstimatedClaimsHandlingCostAmount=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.EstimatedClaimsHandlingCostAmount=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.EstimatedClaimsHandlingCostAmount=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.EstimatedClaimsHandlingCostAmount=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.EstimatedClaimsHandlingCostAmount=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.EstimatedClaimsHandlingCostAmount=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.EstimatedClaimsHandlingCostAmount=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.EstimatedClaimsHandlingCostAmount=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.EstimatedClaimsHandlingCostAmountUSD=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.EstimatedClaimsHandlingCostAmountUSD=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.EstimatedClaimsHandlingCostAmountUSD=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.EstimatedClaimsHandlingCostAmountUSD=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.EstimatedClaimsHandlingCostAmountUSD=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.EstimatedClaimsHandlingCostAmountUSD=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.EstimatedClaimsHandlingCostAmountUSD=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.EstimatedClaimsHandlingCostAmountUSD=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.EstimatedClaimsHandlingCostCurrency=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.EstimatedClaimsHandlingCostCurrency=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.EstimatedClaimsHandlingCostCurrency=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.EstimatedClaimsHandlingCostCurrency=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.EstimatedClaimsHandlingCostCurrency=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.EstimatedClaimsHandlingCostCurrency=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.EstimatedClaimsHandlingCostCurrency=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.EstimatedClaimsHandlingCostCurrency=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.EstimatedClaimsHandlingCostText=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.EstimatedClaimsHandlingCostText=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.EstimatedClaimsHandlingCostText=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.EstimatedClaimsHandlingCostText=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.EstimatedClaimsHandlingCostText=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.EstimatedClaimsHandlingCostText=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.EstimatedClaimsHandlingCostText=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.EstimatedClaimsHandlingCostText=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.FormOfCollateral=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.FormOfCollateral=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.FormOfCollateral=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.FormOfCollateral=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.FormOfCollateral=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.FormOfCollateral=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.FormOfCollateral=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.FormOfCollateral=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.GeneralAggregateAmount=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.GeneralAggregateAmount=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.GeneralAggregateAmount=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.GeneralAggregateAmount=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.GeneralAggregateAmount=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.GeneralAggregateAmount=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.GeneralAggregateAmount=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.GeneralAggregateAmount=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.GeneralAggregateAmountUSD=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.GeneralAggregateAmountUSD=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.GeneralAggregateAmountUSD=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.GeneralAggregateAmountUSD=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.GeneralAggregateAmountUSD=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.GeneralAggregateAmountUSD=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.GeneralAggregateAmountUSD=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.GeneralAggregateAmountUSD=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.GeneralAggregateBasis=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.GeneralAggregateBasis=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.GeneralAggregateBasis=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.GeneralAggregateBasis=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.GeneralAggregateBasis=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.GeneralAggregateBasis=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.GeneralAggregateBasis=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.GeneralAggregateBasis=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.GeneralAggregateCurrency=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.GeneralAggregateCurrency=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.GeneralAggregateCurrency=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.GeneralAggregateCurrency=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.GeneralAggregateCurrency=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.GeneralAggregateCurrency=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.GeneralAggregateCurrency=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.GeneralAggregateCurrency=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.GeneralAggregateInclusionType=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.GeneralAggregateInclusionType=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.GeneralAggregateInclusionType=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.GeneralAggregateInclusionType=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.GeneralAggregateInclusionType=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.GeneralAggregateInclusionType=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.GeneralAggregateInclusionType=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.GeneralAggregateInclusionType=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.GeneralAggregateText=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.GeneralAggregateText=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.GeneralAggregateText=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.GeneralAggregateText=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.GeneralAggregateText=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.GeneralAggregateText=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.GeneralAggregateText=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.GeneralAggregateText=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.GeneralLiabilityEffectiveDate=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.GeneralLiabilityEffectiveDate=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.GeneralLiabilityEffectiveDate=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.GeneralLiabilityEffectiveDate=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.GeneralLiabilityEffectiveDate=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.GeneralLiabilityEffectiveDate=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.GeneralLiabilityEffectiveDate=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.GeneralLiabilityEffectiveDate=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.GeneralLiabilityExpirationDate=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.GeneralLiabilityExpirationDate=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.GeneralLiabilityExpirationDate=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.GeneralLiabilityExpirationDate=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.GeneralLiabilityExpirationDate=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.GeneralLiabilityExpirationDate=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.GeneralLiabilityExpirationDate=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.GeneralLiabilityExpirationDate=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.LiabilityAmount=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.LiabilityAmount=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.LiabilityAmount=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.LiabilityAmount=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.LiabilityAmount=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.LiabilityAmount=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.LiabilityAmount=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.LiabilityAmount=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.LiabilityAmountUSD=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.LiabilityAmountUSD=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.LiabilityAmountUSD=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.LiabilityAmountUSD=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.LiabilityAmountUSD=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.LiabilityAmountUSD=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.LiabilityAmountUSD=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.LiabilityAmountUSD=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.LiabilityBasis=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.LiabilityBasis=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.LiabilityBasis=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.LiabilityBasis=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.LiabilityBasis=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.LiabilityBasis=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.LiabilityBasis=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.LiabilityBasis=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.LiabilityCurrency=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.LiabilityCurrency=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.LiabilityCurrency=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.LiabilityCurrency=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.LiabilityCurrency=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.LiabilityCurrency=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.LiabilityCurrency=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.LiabilityCurrency=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.LiabilityNumberOfUnits=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.LiabilityNumberOfUnits=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.LiabilityNumberOfUnits=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.LiabilityNumberOfUnits=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.LiabilityNumberOfUnits=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.LiabilityNumberOfUnits=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.LiabilityNumberOfUnits=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.LiabilityNumberOfUnits=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.LiabilityPercentage=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.LiabilityPercentage=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.LiabilityPercentage=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.LiabilityPercentage=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.LiabilityPercentage=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.LiabilityPercentage=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.LiabilityPercentage=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.LiabilityPercentage=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.LiabilityText=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.LiabilityText=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.LiabilityText=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.LiabilityText=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.LiabilityText=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.LiabilityText=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.LiabilityText=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.LiabilityText=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.MedPayAmount=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.MedPayAmount=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.MedPayAmount=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.MedPayAmount=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.MedPayAmount=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.MedPayAmount=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.MedPayAmount=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.MedPayAmount=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.MedPayAmountUSD=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.MedPayAmountUSD=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.MedPayAmountUSD=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.MedPayAmountUSD=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.MedPayAmountUSD=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.MedPayAmountUSD=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.MedPayAmountUSD=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.MedPayAmountUSD=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.MedPayBasis=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.MedPayBasis=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.MedPayBasis=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.MedPayBasis=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.MedPayBasis=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.MedPayBasis=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.MedPayBasis=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.MedPayBasis=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.MedPayCurrency=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.MedPayCurrency=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.MedPayCurrency=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.MedPayCurrency=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.MedPayCurrency=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.MedPayCurrency=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.MedPayCurrency=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.MedPayCurrency=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.MedPayInclusionType=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.MedPayInclusionType=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.MedPayInclusionType=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.MedPayInclusionType=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.MedPayInclusionType=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.MedPayInclusionType=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.MedPayInclusionType=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.MedPayInclusionType=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.MedPayText=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.MedPayText=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.MedPayText=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.MedPayText=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.MedPayText=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.MedPayText=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.MedPayText=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.MedPayText=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.NumberOfExtraHeavyTrucks=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.NumberOfExtraHeavyTrucks=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.NumberOfExtraHeavyTrucks=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.NumberOfExtraHeavyTrucks=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.NumberOfExtraHeavyTrucks=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.NumberOfExtraHeavyTrucks=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.NumberOfExtraHeavyTrucks=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.NumberOfExtraHeavyTrucks=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.NumberOfHeavyTrucks=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.NumberOfHeavyTrucks=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.NumberOfHeavyTrucks=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.NumberOfHeavyTrucks=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.NumberOfHeavyTrucks=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.NumberOfHeavyTrucks=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.NumberOfHeavyTrucks=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.NumberOfHeavyTrucks=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.NumberOfLightTrucks=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.NumberOfLightTrucks=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.NumberOfLightTrucks=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.NumberOfLightTrucks=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.NumberOfLightTrucks=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.NumberOfLightTrucks=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.NumberOfLightTrucks=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.NumberOfLightTrucks=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.NumberOfPowerUnits=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.NumberOfPowerUnits=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.NumberOfPowerUnits=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.NumberOfPowerUnits=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.NumberOfPowerUnits=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.NumberOfPowerUnits=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.NumberOfPowerUnits=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.NumberOfPowerUnits=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.NumberOfPrivatePersonalTransportVehicles=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.NumberOfPrivatePersonalTransportVehicles=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.NumberOfPrivatePersonalTransportVehicles=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.NumberOfPrivatePersonalTransportVehicles=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.NumberOfPrivatePersonalTransportVehicles=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.NumberOfPrivatePersonalTransportVehicles=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.NumberOfPrivatePersonalTransportVehicles=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.NumberOfPrivatePersonalTransportVehicles=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.NumberOfTrailers=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.NumberOfTrailers=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.NumberOfTrailers=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.NumberOfTrailers=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.NumberOfTrailers=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.NumberOfTrailers=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.NumberOfTrailers=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.NumberOfTrailers=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.OtherStatesCoverage=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.OtherStatesCoverage=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.OtherStatesCoverage=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.OtherStatesCoverage=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.OtherStatesCoverage=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.OtherStatesCoverage=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.OtherStatesCoverage=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.OtherStatesCoverage=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.PersonalAdvertisingInjuryPerPersonOrOrganizationAmount=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.PersonalAdvertisingInjuryPerPersonOrOrganizationAmount=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.PersonalAdvertisingInjuryPerPersonOrOrganizationAmount=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.PersonalAdvertisingInjuryPerPersonOrOrganizationAmount=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.PersonalAdvertisingInjuryPerPersonOrOrganizationAmount=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.PersonalAdvertisingInjuryPerPersonOrOrganizationAmount=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.PersonalAdvertisingInjuryPerPersonOrOrganizationAmount=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.PersonalAdvertisingInjuryPerPersonOrOrganizationAmount=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.PersonalAdvertisingInjuryPerPersonOrOrganizationAmountUSD=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.PersonalAdvertisingInjuryPerPersonOrOrganizationAmountUSD=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.PersonalAdvertisingInjuryPerPersonOrOrganizationAmountUSD=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.PersonalAdvertisingInjuryPerPersonOrOrganizationAmountUSD=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.PersonalAdvertisingInjuryPerPersonOrOrganizationAmountUSD=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.PersonalAdvertisingInjuryPerPersonOrOrganizationAmountUSD=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.PersonalAdvertisingInjuryPerPersonOrOrganizationAmountUSD=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.PersonalAdvertisingInjuryPerPersonOrOrganizationAmountUSD=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.PersonalAdvertisingInjuryPerPersonOrOrganizationBasis=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.PersonalAdvertisingInjuryPerPersonOrOrganizationBasis=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.PersonalAdvertisingInjuryPerPersonOrOrganizationBasis=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.PersonalAdvertisingInjuryPerPersonOrOrganizationBasis=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.PersonalAdvertisingInjuryPerPersonOrOrganizationBasis=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.PersonalAdvertisingInjuryPerPersonOrOrganizationBasis=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.PersonalAdvertisingInjuryPerPersonOrOrganizationBasis=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.PersonalAdvertisingInjuryPerPersonOrOrganizationBasis=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.PersonalAdvertisingInjuryPerPersonOrOrganizationCurrency=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.PersonalAdvertisingInjuryPerPersonOrOrganizationCurrency=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.PersonalAdvertisingInjuryPerPersonOrOrganizationCurrency=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.PersonalAdvertisingInjuryPerPersonOrOrganizationCurrency=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.PersonalAdvertisingInjuryPerPersonOrOrganizationCurrency=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.PersonalAdvertisingInjuryPerPersonOrOrganizationCurrency=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.PersonalAdvertisingInjuryPerPersonOrOrganizationCurrency=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.PersonalAdvertisingInjuryPerPersonOrOrganizationCurrency=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.PersonalAdvertisingInjuryPerPersonOrOrganizationInclusionType=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.PersonalAdvertisingInjuryPerPersonOrOrganizationInclusionType=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.PersonalAdvertisingInjuryPerPersonOrOrganizationInclusionType=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.PersonalAdvertisingInjuryPerPersonOrOrganizationInclusionType=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.PersonalAdvertisingInjuryPerPersonOrOrganizationInclusionType=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.PersonalAdvertisingInjuryPerPersonOrOrganizationInclusionType=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.PersonalAdvertisingInjuryPerPersonOrOrganizationInclusionType=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.PersonalAdvertisingInjuryPerPersonOrOrganizationInclusionType=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.PersonalAdvertisingInjuryPerPersonOrOrganizationText=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.PersonalAdvertisingInjuryPerPersonOrOrganizationText=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.PersonalAdvertisingInjuryPerPersonOrOrganizationText=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.PersonalAdvertisingInjuryPerPersonOrOrganizationText=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.PersonalAdvertisingInjuryPerPersonOrOrganizationText=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.PersonalAdvertisingInjuryPerPersonOrOrganizationText=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.PersonalAdvertisingInjuryPerPersonOrOrganizationText=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.PersonalAdvertisingInjuryPerPersonOrOrganizationText=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.PolicyEffectiveDate=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.PolicyEffectiveDate=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.PolicyEffectiveDate=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.PolicyEffectiveDate=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.PolicyEffectiveDate=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.PolicyEffectiveDate=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.PolicyEffectiveDate=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.PolicyEffectiveDate=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.PolicyEffectiveDateCurrency=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.PolicyEffectiveDateCurrency=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.PolicyEffectiveDateCurrency=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.PolicyEffectiveDateCurrency=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.PolicyEffectiveDateCurrency=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.PolicyEffectiveDateCurrency=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.PolicyEffectiveDateCurrency=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.PolicyEffectiveDateCurrency=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.PolicyEffectiveDateText=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.PolicyEffectiveDateText=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.PolicyEffectiveDateText=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.PolicyEffectiveDateText=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.PolicyEffectiveDateText=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.PolicyEffectiveDateText=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.PolicyEffectiveDateText=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.PolicyEffectiveDateText=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.PolicyExpirationDate=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.PolicyExpirationDate=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.PolicyExpirationDate=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.PolicyExpirationDate=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.PolicyExpirationDate=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.PolicyExpirationDate=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.PolicyExpirationDate=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.PolicyExpirationDate=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.PolicyExpirationDateCurrency=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.PolicyExpirationDateCurrency=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.PolicyExpirationDateCurrency=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.PolicyExpirationDateCurrency=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.PolicyExpirationDateCurrency=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.PolicyExpirationDateCurrency=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.PolicyExpirationDateCurrency=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.PolicyExpirationDateCurrency=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.PolicyExpirationDateText=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.PolicyExpirationDateText=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.PolicyExpirationDateText=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.PolicyExpirationDateText=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.PolicyExpirationDateText=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.PolicyExpirationDateText=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.PolicyExpirationDateText=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.PolicyExpirationDateText=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.PolicyFormReportingForm=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.PolicyFormReportingForm=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.PolicyFormReportingForm=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.PolicyFormReportingForm=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.PolicyFormReportingForm=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.PolicyFormReportingForm=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.PolicyFormReportingForm=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.PolicyFormReportingForm=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.PolicyFormText=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.PolicyFormText=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.PolicyFormText=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.PolicyFormText=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.PolicyFormText=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.PolicyFormText=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.PolicyFormText=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.PolicyFormText=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.PolicyLimitAmount=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.PolicyLimitAmount=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.PolicyLimitAmount=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.PolicyLimitAmount=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.PolicyLimitAmount=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.PolicyLimitAmount=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.PolicyLimitAmount=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.PolicyLimitAmount=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.PolicyLimitAmountUSD=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.PolicyLimitAmountUSD=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.PolicyLimitAmountUSD=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.PolicyLimitAmountUSD=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.PolicyLimitAmountUSD=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.PolicyLimitAmountUSD=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.PolicyLimitAmountUSD=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.PolicyLimitAmountUSD=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.PolicyLimitBasis=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.PolicyLimitBasis=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.PolicyLimitBasis=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.PolicyLimitBasis=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.PolicyLimitBasis=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.PolicyLimitBasis=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.PolicyLimitBasis=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.PolicyLimitBasis=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.PolicyLimitCurrency=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.PolicyLimitCurrency=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.PolicyLimitCurrency=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.PolicyLimitCurrency=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.PolicyLimitCurrency=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.PolicyLimitCurrency=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.PolicyLimitCurrency=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.PolicyLimitCurrency=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.PolicyLimitInclusionType=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.PolicyLimitInclusionType=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.PolicyLimitInclusionType=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.PolicyLimitInclusionType=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.PolicyLimitInclusionType=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.PolicyLimitInclusionType=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.PolicyLimitInclusionType=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.PolicyLimitInclusionType=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.PolicyLimitNumberOfUnits=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.PolicyLimitNumberOfUnits=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.PolicyLimitNumberOfUnits=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.PolicyLimitNumberOfUnits=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.PolicyLimitNumberOfUnits=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.PolicyLimitNumberOfUnits=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.PolicyLimitNumberOfUnits=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.PolicyLimitNumberOfUnits=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.PolicyLimitPercentage=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.PolicyLimitPercentage=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.PolicyLimitPercentage=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.PolicyLimitPercentage=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.PolicyLimitPercentage=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.PolicyLimitPercentage=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.PolicyLimitPercentage=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.PolicyLimitPercentage=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.PolicyLimitText=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.PolicyLimitText=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.PolicyLimitText=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.PolicyLimitText=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.PolicyLimitText=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.PolicyLimitText=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.PolicyLimitText=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.PolicyLimitText=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.PolicyTrigger=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.PolicyTrigger=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.PolicyTrigger=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.PolicyTrigger=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.PolicyTrigger=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.PolicyTrigger=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.PolicyTrigger=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.PolicyTrigger=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.PolicyTriggerText=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.PolicyTriggerText=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.PolicyTriggerText=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.PolicyTriggerText=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.PolicyTriggerText=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.PolicyTriggerText=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.PolicyTriggerText=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.PolicyTriggerText=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.ProductsCompletedOpsAggregateLimitAmount=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.ProductsCompletedOpsAggregateLimitAmount=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.ProductsCompletedOpsAggregateLimitAmount=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.ProductsCompletedOpsAggregateLimitAmount=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.ProductsCompletedOpsAggregateLimitAmount=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.ProductsCompletedOpsAggregateLimitAmount=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.ProductsCompletedOpsAggregateLimitAmount=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.ProductsCompletedOpsAggregateLimitAmount=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.ProductsCompletedOpsAggregateLimitAmountUSD=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.ProductsCompletedOpsAggregateLimitAmountUSD=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.ProductsCompletedOpsAggregateLimitAmountUSD=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.ProductsCompletedOpsAggregateLimitAmountUSD=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.ProductsCompletedOpsAggregateLimitAmountUSD=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.ProductsCompletedOpsAggregateLimitAmountUSD=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.ProductsCompletedOpsAggregateLimitAmountUSD=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.ProductsCompletedOpsAggregateLimitAmountUSD=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.ProductsCompletedOpsAggregateLimitBasis=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.ProductsCompletedOpsAggregateLimitBasis=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.ProductsCompletedOpsAggregateLimitBasis=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.ProductsCompletedOpsAggregateLimitBasis=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.ProductsCompletedOpsAggregateLimitBasis=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.ProductsCompletedOpsAggregateLimitBasis=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.ProductsCompletedOpsAggregateLimitBasis=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.ProductsCompletedOpsAggregateLimitBasis=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.ProductsCompletedOpsAggregateLimitCurrency=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.ProductsCompletedOpsAggregateLimitCurrency=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.ProductsCompletedOpsAggregateLimitCurrency=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.ProductsCompletedOpsAggregateLimitCurrency=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.ProductsCompletedOpsAggregateLimitCurrency=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.ProductsCompletedOpsAggregateLimitCurrency=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.ProductsCompletedOpsAggregateLimitCurrency=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.ProductsCompletedOpsAggregateLimitCurrency=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.ProductsCompletedOpsAggregateLimitInclusionType=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.ProductsCompletedOpsAggregateLimitInclusionType=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.ProductsCompletedOpsAggregateLimitInclusionType=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.ProductsCompletedOpsAggregateLimitInclusionType=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.ProductsCompletedOpsAggregateLimitInclusionType=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.ProductsCompletedOpsAggregateLimitInclusionType=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.ProductsCompletedOpsAggregateLimitInclusionType=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.ProductsCompletedOpsAggregateLimitInclusionType=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.ProductsCompletedOpsAggregateLimitPercentage=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.ProductsCompletedOpsAggregateLimitPercentage=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.ProductsCompletedOpsAggregateLimitPercentage=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.ProductsCompletedOpsAggregateLimitPercentage=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.ProductsCompletedOpsAggregateLimitPercentage=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.ProductsCompletedOpsAggregateLimitPercentage=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.ProductsCompletedOpsAggregateLimitPercentage=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.ProductsCompletedOpsAggregateLimitPercentage=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.ProductsCompletedOpsAggregateLimitText=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.ProductsCompletedOpsAggregateLimitText=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.ProductsCompletedOpsAggregateLimitText=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.ProductsCompletedOpsAggregateLimitText=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.ProductsCompletedOpsAggregateLimitText=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.ProductsCompletedOpsAggregateLimitText=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.ProductsCompletedOpsAggregateLimitText=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.ProductsCompletedOpsAggregateLimitText=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.ProfessionalLiabilityEffectiveDate=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.ProfessionalLiabilityEffectiveDate=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.ProfessionalLiabilityEffectiveDate=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.ProfessionalLiabilityEffectiveDate=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.ProfessionalLiabilityEffectiveDate=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.ProfessionalLiabilityEffectiveDate=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.ProfessionalLiabilityEffectiveDate=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.ProfessionalLiabilityEffectiveDate=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.ProfessionalLiabilityExpirationDate=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.ProfessionalLiabilityExpirationDate=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.ProfessionalLiabilityExpirationDate=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.ProfessionalLiabilityExpirationDate=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.ProfessionalLiabilityExpirationDate=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.ProfessionalLiabilityExpirationDate=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.ProfessionalLiabilityExpirationDate=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.ProfessionalLiabilityExpirationDate=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.QuoteExpiryDate=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.QuoteExpiryDate=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.QuoteExpiryDate=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.QuoteExpiryDate=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.QuoteExpiryDate=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.QuoteExpiryDate=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.QuoteExpiryDate=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.QuoteExpiryDate=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.QuotedExpirationDate=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.QuotedExpirationDate=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.QuotedExpirationDate=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.QuotedExpirationDate=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.QuotedExpirationDate=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.QuotedExpirationDate=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.QuotedExpirationDate=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.QuotedExpirationDate=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.RevenueAmount=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.RevenueAmount=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.RevenueAmount=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.RevenueAmount=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.RevenueAmount=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.RevenueAmount=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.RevenueAmount=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.RevenueAmount=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.RevenueAmountUSD=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.RevenueAmountUSD=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.RevenueAmountUSD=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.RevenueAmountUSD=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.RevenueAmountUSD=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.RevenueAmountUSD=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.RevenueAmountUSD=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.RevenueAmountUSD=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.RevenueCurrency=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.RevenueCurrency=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.RevenueCurrency=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.RevenueCurrency=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.RevenueCurrency=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.RevenueCurrency=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.RevenueCurrency=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.RevenueCurrency=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.StructureProgramStructure=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.StructureProgramStructure=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.StructureProgramStructure=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.StructureProgramStructure=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.StructureProgramStructure=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.StructureProgramStructure=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.StructureProgramStructure=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.StructureProgramStructure=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.SurplusLinesTax=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.SurplusLinesTax=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.SurplusLinesTax=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.SurplusLinesTax=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.SurplusLinesTax=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.SurplusLinesTax=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.SurplusLinesTax=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.SurplusLinesTax=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.SurplusLinesTaxAmount=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.SurplusLinesTaxAmount=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.SurplusLinesTaxAmount=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.SurplusLinesTaxAmount=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.SurplusLinesTaxAmount=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.SurplusLinesTaxAmount=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.SurplusLinesTaxAmount=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.SurplusLinesTaxAmount=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.SurplusLinesTaxCurrency=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.SurplusLinesTaxCurrency=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.SurplusLinesTaxCurrency=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.SurplusLinesTaxCurrency=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.SurplusLinesTaxCurrency=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.SurplusLinesTaxCurrency=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.SurplusLinesTaxCurrency=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.SurplusLinesTaxCurrency=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.ThirdPartyAdministrator=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.ThirdPartyAdministrator=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.ThirdPartyAdministrator=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.ThirdPartyAdministrator=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.ThirdPartyAdministrator=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.ThirdPartyAdministrator=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.ThirdPartyAdministrator=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.ThirdPartyAdministrator=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.TotalCollateralRequirementAmount=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.TotalCollateralRequirementAmount=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.TotalCollateralRequirementAmount=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.TotalCollateralRequirementAmount=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.TotalCollateralRequirementAmount=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.TotalCollateralRequirementAmount=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.TotalCollateralRequirementAmount=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.TotalCollateralRequirementAmount=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.TotalCollateralRequirementAmountUSD=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.TotalCollateralRequirementAmountUSD=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.TotalCollateralRequirementAmountUSD=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.TotalCollateralRequirementAmountUSD=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.TotalCollateralRequirementAmountUSD=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.TotalCollateralRequirementAmountUSD=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.TotalCollateralRequirementAmountUSD=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.TotalCollateralRequirementAmountUSD=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.TotalCollateralRequirementCurrency=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.TotalCollateralRequirementCurrency=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.TotalCollateralRequirementCurrency=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.TotalCollateralRequirementCurrency=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.TotalCollateralRequirementCurrency=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.TotalCollateralRequirementCurrency=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.TotalCollateralRequirementCurrency=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.TotalCollateralRequirementCurrency=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.TotalCollateralRequirementPerStateRequirements=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.TotalCollateralRequirementPerStateRequirements=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.TotalCollateralRequirementPerStateRequirements=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.TotalCollateralRequirementPerStateRequirements=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.TotalCollateralRequirementPerStateRequirements=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.TotalCollateralRequirementPerStateRequirements=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.TotalCollateralRequirementPerStateRequirements=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.TotalCollateralRequirementPerStateRequirements=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.TotalCollateralRequirementText=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.TotalCollateralRequirementText=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.TotalCollateralRequirementText=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.TotalCollateralRequirementText=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.TotalCollateralRequirementText=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.TotalCollateralRequirementText=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.TotalCollateralRequirementText=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.TotalCollateralRequirementText=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.TotalEstimatedPayrollDate=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.TotalEstimatedPayrollDate=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.TotalEstimatedPayrollDate=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.TotalEstimatedPayrollDate=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.TotalEstimatedPayrollDate=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.TotalEstimatedPayrollDate=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.TotalEstimatedPayrollDate=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.TotalEstimatedPayrollDate=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.TotalEstimatedPayrollAmount=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.TotalEstimatedPayrollAmount=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.TotalEstimatedPayrollAmount=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.TotalEstimatedPayrollAmount=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.TotalEstimatedPayrollAmount=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.TotalEstimatedPayrollAmount=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.TotalEstimatedPayrollAmount=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.TotalEstimatedPayrollAmount=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.TotalEstimatedPayrollAmountUSD=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.TotalEstimatedPayrollAmountUSD=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.TotalEstimatedPayrollAmountUSD=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.TotalEstimatedPayrollAmountUSD=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.TotalEstimatedPayrollAmountUSD=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.TotalEstimatedPayrollAmountUSD=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.TotalEstimatedPayrollAmountUSD=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.TotalEstimatedPayrollAmountUSD=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.TotalEstimatedPayrollCurrency=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.TotalEstimatedPayrollCurrency=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.TotalEstimatedPayrollCurrency=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.TotalEstimatedPayrollCurrency=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.TotalEstimatedPayrollCurrency=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.TotalEstimatedPayrollCurrency=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.TotalEstimatedPayrollCurrency=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.TotalEstimatedPayrollCurrency=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.TotalExcessLimitsAmount=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.TotalExcessLimitsAmount=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.TotalExcessLimitsAmount=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.TotalExcessLimitsAmount=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.TotalExcessLimitsAmount=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.TotalExcessLimitsAmount=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.TotalExcessLimitsAmount=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.TotalExcessLimitsAmount=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.TotalExcessLimitsCurrency=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.TotalExcessLimitsCurrency=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.TotalExcessLimitsCurrency=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.TotalExcessLimitsCurrency=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.TotalExcessLimitsCurrency=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.TotalExcessLimitsCurrency=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.TotalExcessLimitsCurrency=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.TotalExcessLimitsCurrency=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.TotalExcessLimitsPercentage=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.TotalExcessLimitsPercentage=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.TotalExcessLimitsPercentage=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.TotalExcessLimitsPercentage=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.TotalExcessLimitsPercentage=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.TotalExcessLimitsPercentage=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.TotalExcessLimitsPercentage=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.TotalExcessLimitsPercentage=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.TotalExcessLimitsText=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.TotalExcessLimitsText=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.TotalExcessLimitsText=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.TotalExcessLimitsText=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.TotalExcessLimitsText=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.TotalExcessLimitsText=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.TotalExcessLimitsText=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.TotalExcessLimitsText=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.TotalOutstandingActuarialLiabilityCarrierAmount=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.TotalOutstandingActuarialLiabilityCarrierAmount=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.TotalOutstandingActuarialLiabilityCarrierAmount=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.TotalOutstandingActuarialLiabilityCarrierAmount=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.TotalOutstandingActuarialLiabilityCarrierAmount=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.TotalOutstandingActuarialLiabilityCarrierAmount=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.TotalOutstandingActuarialLiabilityCarrierAmount=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.TotalOutstandingActuarialLiabilityCarrierAmount=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.TotalOutstandingActuarialLiabilityCarrierAmountUSD=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.TotalOutstandingActuarialLiabilityCarrierAmountUSD=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.TotalOutstandingActuarialLiabilityCarrierAmountUSD=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.TotalOutstandingActuarialLiabilityCarrierAmountUSD=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.TotalOutstandingActuarialLiabilityCarrierAmountUSD=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.TotalOutstandingActuarialLiabilityCarrierAmountUSD=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.TotalOutstandingActuarialLiabilityCarrierAmountUSD=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.TotalOutstandingActuarialLiabilityCarrierAmountUSD=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.TotalOutstandingActuarialLiabilityCarrierCurrency=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.TotalOutstandingActuarialLiabilityCarrierCurrency=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.TotalOutstandingActuarialLiabilityCarrierCurrency=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.TotalOutstandingActuarialLiabilityCarrierCurrency=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.TotalOutstandingActuarialLiabilityCarrierCurrency=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.TotalOutstandingActuarialLiabilityCarrierCurrency=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.TotalOutstandingActuarialLiabilityCarrierCurrency=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.TotalOutstandingActuarialLiabilityCarrierCurrency=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.TotalOutstandingActuarialLiabilityCarrierText=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.TotalOutstandingActuarialLiabilityCarrierText=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.TotalOutstandingActuarialLiabilityCarrierText=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.TotalOutstandingActuarialLiabilityCarrierText=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.TotalOutstandingActuarialLiabilityCarrierText=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.TotalOutstandingActuarialLiabilityCarrierText=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.TotalOutstandingActuarialLiabilityCarrierText=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.TotalOutstandingActuarialLiabilityCarrierText=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.TotalValueAmount=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.TotalValueAmount=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.TotalValueAmount=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.TotalValueAmount=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.TotalValueAmount=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.TotalValueAmount=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.TotalValueAmount=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.TotalValueAmount=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.TotalValueAmountUSD=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.TotalValueAmountUSD=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.TotalValueAmountUSD=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.TotalValueAmountUSD=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.TotalValueAmountUSD=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.TotalValueAmountUSD=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.TotalValueAmountUSD=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.TotalValueAmountUSD=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.TotalValueCurrency=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.TotalValueCurrency=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.TotalValueCurrency=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.TotalValueCurrency=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.TotalValueCurrency=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.TotalValueCurrency=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.TotalValueCurrency=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.TotalValueCurrency=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.TreatmentOfAllocatedLossAdjustmentExpenses=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.TreatmentOfAllocatedLossAdjustmentExpenses=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.TreatmentOfAllocatedLossAdjustmentExpenses=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.TreatmentOfAllocatedLossAdjustmentExpenses=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.TreatmentOfAllocatedLossAdjustmentExpenses=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.TreatmentOfAllocatedLossAdjustmentExpenses=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.TreatmentOfAllocatedLossAdjustmentExpenses=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.TreatmentOfAllocatedLossAdjustmentExpenses=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.TerrorismRiskInsuranceActPremiumAmount=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.TerrorismRiskInsuranceActPremiumAmount=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.TerrorismRiskInsuranceActPremiumAmount=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.TerrorismRiskInsuranceActPremiumAmount=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.TerrorismRiskInsuranceActPremiumAmount=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.TerrorismRiskInsuranceActPremiumAmount=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.TerrorismRiskInsuranceActPremiumAmount=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.TerrorismRiskInsuranceActPremiumAmount=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.TerrorismRiskInsuranceActPremiumAmountUSD=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.TerrorismRiskInsuranceActPremiumAmountUSD=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.TerrorismRiskInsuranceActPremiumAmountUSD=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.TerrorismRiskInsuranceActPremiumAmountUSD=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.TerrorismRiskInsuranceActPremiumAmountUSD=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.TerrorismRiskInsuranceActPremiumAmountUSD=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.TerrorismRiskInsuranceActPremiumAmountUSD=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.TerrorismRiskInsuranceActPremiumAmountUSD=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.TerrorismRiskInsuranceActPremiumCurrency=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.TerrorismRiskInsuranceActPremiumCurrency=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.TerrorismRiskInsuranceActPremiumCurrency=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.TerrorismRiskInsuranceActPremiumCurrency=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.TerrorismRiskInsuranceActPremiumCurrency=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.TerrorismRiskInsuranceActPremiumCurrency=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.TerrorismRiskInsuranceActPremiumCurrency=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.TerrorismRiskInsuranceActPremiumCurrency=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.TerrorismRiskInsuranceActPremiumIncludedExcluded=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.TerrorismRiskInsuranceActPremiumIncludedExcluded=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.TerrorismRiskInsuranceActPremiumIncludedExcluded=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.TerrorismRiskInsuranceActPremiumIncludedExcluded=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.TerrorismRiskInsuranceActPremiumIncludedExcluded=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.TerrorismRiskInsuranceActPremiumIncludedExcluded=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.TerrorismRiskInsuranceActPremiumIncludedExcluded=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.TerrorismRiskInsuranceActPremiumIncludedExcluded=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.WorkersCompensationAmount=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.WorkersCompensationAmount=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.WorkersCompensationAmount=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.WorkersCompensationAmount=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.WorkersCompensationAmount=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.WorkersCompensationAmount=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.WorkersCompensationAmount=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.WorkersCompensationAmount=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.WorkersCompensationAmountUSD=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.WorkersCompensationAmountUSD=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.WorkersCompensationAmountUSD=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.WorkersCompensationAmountUSD=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.WorkersCompensationAmountUSD=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.WorkersCompensationAmountUSD=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.WorkersCompensationAmountUSD=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.WorkersCompensationAmountUSD=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.WorkersCompensationBasis=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.WorkersCompensationBasis=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.WorkersCompensationBasis=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.WorkersCompensationBasis=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.WorkersCompensationBasis=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.WorkersCompensationBasis=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.WorkersCompensationBasis=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.WorkersCompensationBasis=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.WorkersCompensationCurrency=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.WorkersCompensationCurrency=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.WorkersCompensationCurrency=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.WorkersCompensationCurrency=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.WorkersCompensationCurrency=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.WorkersCompensationCurrency=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.WorkersCompensationCurrency=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.WorkersCompensationCurrency=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.WorkersCompensationNumberOfUnits=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.WorkersCompensationNumberOfUnits=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.WorkersCompensationNumberOfUnits=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.WorkersCompensationNumberOfUnits=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.WorkersCompensationNumberOfUnits=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.WorkersCompensationNumberOfUnits=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.WorkersCompensationNumberOfUnits=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.WorkersCompensationNumberOfUnits=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.WorkersCompensationStatutoryRejected=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.WorkersCompensationStatutoryRejected=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.WorkersCompensationStatutoryRejected=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.WorkersCompensationStatutoryRejected=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.WorkersCompensationStatutoryRejected=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.WorkersCompensationStatutoryRejected=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.WorkersCompensationStatutoryRejected=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.WorkersCompensationStatutoryRejected=PS.vwToAverageExchangeRate.ExchangeRate
rpt.MarketResponseElementAttribute.WorkersCompensationText=ref.BasisOfCover.BasisOfCover
rpt.MarketResponseElementAttribute.WorkersCompensationText=ref.CoverageBasis.CoverageBasis
rpt.MarketResponseElementAttribute.WorkersCompensationText=ref.ProgramStructureType.ProgramStructureType
rpt.MarketResponseElementAttribute.WorkersCompensationText=Reference.Currency.CurrencyAlphaCode
rpt.MarketResponseElementAttribute.WorkersCompensationText=PS.MarketResponseElementAttribute.Value
rpt.MarketResponseElementAttribute.WorkersCompensationText=PS.MarketResponseElementAttribute.DisplayValue
rpt.MarketResponseElementAttribute.WorkersCompensationText=ref.ElementAttributeReferenceOption.ElementAttributeReference
rpt.MarketResponseElementAttribute.WorkersCompensationText=PS.vwToAverageExchangeRate.ExchangeRate
*/

CREATE PROCEDURE rpt.Load_rpt_MarketResponseElementAttribute
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'rpt.MarketResponseElementAttribute';
DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET NOCOUNT ON;

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    DROP TABLE IF EXISTS #ExchangeRate;

    -- INSERT currency data into temp table
    SELECT
        FromCurrencyId
      , FromCurrencyAlphaCode
      , EffectiveFromDate
      , EffectiveToDate
      , ExchangeRate
    INTO #ExchangeRate
    FROM
        PS.vwToAverageExchangeRate
    WHERE
        ToCurrencyAlphaCode = 'USD';

    -- Create temp tables for look up values
    DROP TABLE IF EXISTS #ElementAttributeLookUp;
    DROP TABLE IF EXISTS #CoverageBasisLookUp;
    DROP TABLE IF EXISTS #ProgramStructureTypeLookUp;
    DROP TABLE IF EXISTS #BasisOfCover;

    -- Add ElementAttributeReferenceOption data
    SELECT
        ElementAttributeReferenceOptionKey
      , ElementAttributeReference
    INTO #ElementAttributeLookUp
    FROM
        ref.ElementAttributeReferenceOption;

    -- Add CoverageBasis data
    SELECT
        CoverageBasisId
      , CoverageBasis
    INTO #CoverageBasisLookUp
    FROM
        ref.CoverageBasis;

    -- Add CoverageBasis data
    SELECT
        ProgramStructureTypeKey
      , ProgramStructureType
    INTO #ProgramStructureTypeLookUp
    FROM
        ref.ProgramStructureType;

    -- Add BasisOfCover data
    SELECT
        BasisOfCoverId
      , BasisOfCover
    INTO #BasisOfCover
    FROM
        ref.BasisOfCover;

    -- Create source data for the final pivot
    DROP TABLE IF EXISTS #MarketResponseElementAttribute;

    CREATE TABLE #MarketResponseElementAttribute (
        CarrierResponseId    INT            NOT NULL
      , MarketResponseId     INT            NOT NULL
      , ResponseDate         DATETIME2      NULL
      , ElementId            INT            NOT NULL
      , ElementType          NVARCHAR(255)  NOT NULL
      , ElementAttributeType NVARCHAR(255)  NOT NULL
      , LookUpValue          NVARCHAR(2000) NULL
    );

    -- Get Market Response Element data
    INSERT INTO
        #MarketResponseElementAttribute
        (
            CarrierResponseId
          , MarketResponseId
          , ResponseDate
          , ElementId
          , ElementType
          , ElementAttributeType
          , LookUpValue
        )
    SELECT
        CarrierResponseId = TRY_CAST(SUBSTRING(mr.MarketResponseKey, 8, 11) AS INT)
      , mr.MarketResponseId
      , mr.ResponseDate
      , mre.ElementId
      , ElementType = mre.ElementType
                      + CASE WHEN et.ElementType = 'Total Estimated Payroll '
                                  AND mrea.ElementAttributeType = 'Date'
                                 THEN ' ' + mrea.ElementAttributeType
                             WHEN mrea.ElementAttributeType IN (
                                 'Policy Trigger', 'Date', 'Other States Coverage', 'ALAE Option'
                               , 'Claims Handling Structure', 'Form of Collateral', 'Number of Items'
                               , 'Third Party Administrator', 'Surplus Lines Tax'
                             )
                                 THEN ''
                             ELSE ' ' + mrea.ElementAttributeType END
      , mrea.ElementAttributeType
      , LookupValue = CASE WHEN et.ElementType = 'Basis of Cover' -- New Element type and look up from BP data
                               THEN (
                               SELECT BasisOfCover FROM #BasisOfCover WHERE BasisOfCoverId = mrea.Value
                           )
                           WHEN mrea.ElementAttributeType = 'Basis'
                               THEN (
                               SELECT CoverageBasis FROM #CoverageBasisLookUp WHERE CoverageBasisId = mrea.Value
                           )
                           WHEN mrea.ElementAttributeType = 'Program Structure'
                               THEN (
                               SELECT ProgramStructureType FROM #ProgramStructureTypeLookUp WHERE ProgramStructureTypeKey = mrea.Value
                           )
                           WHEN mrea.ElementAttributeType = 'Currency'
                               THEN (
                               SELECT CurrencyAlphaCode FROM Reference.Currency WHERE CAST(CurrencyId AS NVARCHAR(255)) = mrea.Value
                           )
                           WHEN mrea.ElementAttributeType = 'Date'
                               THEN LEFT(mrea.Value, 10)
                           WHEN mrea.ElementAttributeType = 'Amount'
                               THEN mrea.Value
                           WHEN mrea.ElementAttributeType = 'Text'
                               THEN SUBSTRING(mrea.Value, 0, 2000)
                           WHEN mrea.ElementAttributeType = 'Percentage'
                               THEN mrea.Value
                           WHEN mrea.ElementAttributeType = 'Number of Units'
                               THEN mrea.Value
                           WHEN mrea.ElementAttributeType = 'Number of Items'
                               THEN mrea.Value
                           WHEN mrea.ElementAttributeType = 'Policy Trigger'
                               THEN mrea.DisplayValue
                           WHEN mrea.ElementAttributeType = 'Third Party Administrator'
                               THEN mrea.DisplayValue
                           ELSE
                      (
                          SELECT ElementAttributeReference FROM #ElementAttributeLookUp WHERE ElementAttributeReferenceOptionKey = mrea.Value
                      ) END
    FROM
        PS.MarketResponseElement mre
        INNER JOIN ref.ElementType et
            ON et.ElementTypeId = mre.ElementTypeId
               AND et.IsDeleted = 0

        INNER JOIN PS.MarketResponseElementAttribute mrea
            ON mrea.MarketResponseElementKey = mre.MarketResponseElementKey
               AND mrea.IsDeleted = 0

        INNER JOIN PS.MarketResponse mr
            ON mr.MarketResponseId = mre.MarketResponseId
               AND mr.IsDeleted = 0
    WHERE
        mr.MarketResponseKey LIKE 'MKTRES%'
        AND mre.IsDeleted = 0
        AND mrea.Value IS NOT NULL
        AND NOT (
                mre.ElementType = 'Other States Coverage'
                AND mrea.ElementAttributeType <> 'Other States Coverage'
            )
        AND et.ElementTypeKey IN (
                'responseCapture_metric_quoteExpiryDate', 'responseCapture_metric_premium_tria'
              , 'responseCapture_metric_limit_policyEffectiveDate'
              , 'responseCapture_metric_autoLiability_effectiveDate'
              , 'responseCapture_metric_employersLiability_effectiveDate'
              , 'responseCapture_metric_generalLiability_effectiveDate'
              , 'responseCapture_metric_professionalLiability_effectiveDate'
              , 'responseCapture_metric_limit_policyExpiryDate', 'responseCapture_metric_autoLiability_expirationDate'
              , 'responseCapture_metric_employersLiability_expirationDate'
              , 'responseCapture_metric_generalLiability_expirationDate'
              , 'responseCapture_metric_professionalLiability_expirationDate'
              , 'responseCapture_metric_quotedExpiration', 'responseCapture_metric_policyTrigger'
              , 'responseCapture_metric_programStructure', 'responseCapture_metric_treatmentAlae '
              , 'responseCapture_metric_limit_generalLiability_aggregate'
              , 'responseCapture_metric_limit_pncOpsAggregate'
              , 'responseCapture_metric_limit_generalLiability_personalAndAdvertising'
              , 'responseCapture_metric_limit_perOcurrence'
              , 'responseCapture_metric_limit_generalLiability_damageToPremises', 'responseCapture_metric_limit_medPay'
              , 'responseCapture_metric_limit_workersCompensation'
              , 'responseCapture_metric_limit_employersLiability_bodilyInjuryAccident'
              , 'responseCapture_metric_limit_employersLiability_bodilyInjuryDiseaseEmployee'
              , 'responseCapture_metric_limit_employersLiability_bodilyInjuryDiseasePolicyLimit'
              , 'responseCapture_metric_limit_otherStatesCoverage', 'responseCapture_metric_limit_generalAggregate'
              , 'responseCapture_metric_limit_policyLimit', 'responseCapture_metric_deductible'
              , 'responseCapture_metric_deductible_liability', 'responseCapture_metric_deductible_employeeBenefitsLiab'
              , 'responseCapture_metric_limit_automotive_physicalDamageCollision'
              , 'responseCapture_metric_limit_automotive_physicalDamageComprehensive'
              , 'responseCapture_metric_deductible_CollateralClaimsHandling_TotalOSAcutariaLiab_Carrier'
              , 'responseCapture_metric_deductible_CollateralClaimsHandling_TotalCollateralReq'
              , 'responseCapture_metric_deductible_CollateralClaimsHandling_FormOfCollateral'
              , 'responseCapture_metric_deductible_CollateralClaimsHandling_ClaimsHandllingStructure'
              , 'responseCapture_metric_deductible_CollateralClaimsHandling_TPAdmin'
              , 'responseCapture_metric_deductible_CollateralClaimsHandling_EstClaimsHandleCost'
              , 'responseCapture_metric_policyForm', 'exposure_exposuremetric_TotalValue'
              , 'exposure_exposuremetric_Revenue', 'exposure_exposuremetric_NumberOfExtraHeavyTrucks'
              , 'exposure_exposuremetric_NumberOfHeavyTrucks', 'exposure_exposuremetric_NumberOfLightTrucks'
              , 'exposure_exposuremetric_NumberOfPrivatePersonalTransportVehicles'
              , 'exposure_exposuremetric_PowerUnitCount', 'exposure_exposuremetric_TrailerCount'
              , 'exposure_exposuremetric_TotalEstimatedPayroll', 'responseCapture_metric_premium_suplusLinesTax'
              , 'responseCapture_metric_basisOfCover', 'responseCapture_metric_totalExcessLimits'
            );

    --Create additional records for the calculated USD values
    INSERT INTO
        #MarketResponseElementAttribute
        (
            CarrierResponseId
          , MarketResponseId
          , ResponseDate
          , ElementId
          , ElementType
          , ElementAttributeType
          , LookUpValue
        )
    SELECT
        mreaa.CarrierResponseId
      , mreaa.MarketResponseId
      , mreaa.ResponseDate
      , mreaa.ElementId
      , ElementType = mreaa.ElementType + ' USD'
      , ElementAttributeType = 'Amount USD'
      , LookUpValue = CAST(TRY_CAST(NULLIF(mreaa.LookUpValue, '') AS NUMERIC(38, 2)) * ER.ExchangeRate AS NVARCHAR(100))
    FROM
        #MarketResponseElementAttribute mreaa
        INNER JOIN #MarketResponseElementAttribute mreac
            ON mreac.CarrierResponseId = mreaa.CarrierResponseId
               AND mreac.ElementId = mreaa.ElementId
               AND mreac.ElementAttributeType = 'Currency'

        INNER JOIN #ExchangeRate ER
            ON ER.FromCurrencyAlphaCode = mreac.LookUpValue
               AND ER.EffectiveFromDate <= ISNULL(mreaa.ResponseDate, GETUTCDATE())
               AND ER.EffectiveToDate > ISNULL(mreaa.ResponseDate, GETUTCDATE())
    WHERE
        mreaa.ElementAttributeType = 'Amount';

    TRUNCATE TABLE rpt.MarketResponseElementAttribute;

    INSERT INTO
        rpt.MarketResponseElementAttribute
        (
            CarrierResponseId
          , MarketResponseId
          , AggregateLimitAmount
          , AggregateLimitAmountUSD
          , AggregateLimitBasis
          , AggregateLimitCurrency
          , AggregateLimitInclusionType
          , AggregateLimitNotApplicable
          , AggregateLimitPercentage
          , AggregateLimitText
          , AutoLiabilityEffectiveDate
          , AutoLiabilityExpirationDate
          , AutoPhysicalDamageCollisionAmount
          , AutoPhysicalDamageCollisionAmountUSD
          , AutoPhysicalDamageCollisionBasis
          , AutoPhysicalDamageCollisionCurrency
          , AutoPhysicalDamageCollisionInclusionType
          , AutoPhysicalDamageCollisionText
          , AutoPhysicalDamageCollisionValuation
          , AutoPhysicalDamageComprehensiveAmount
          , AutoPhysicalDamageComprehensiveAmountUSD
          , AutoPhysicalDamageComprehensiveBasis
          , AutoPhysicalDamageComprehensiveCurrency
          , AutoPhysicalDamageComprehensiveInclusionType
          , AutoPhysicalDamageComprehensivePercentage
          , AutoPhysicalDamageComprehensiveText
          , AutoPhysicalDamageComprehensiveValuation
          , BasisofCoverBasis
          , BodilyInjuryByAccidentEachAccidentAmount
          , BodilyInjuryByAccidentEachAccidentAmountUSD
          , BodilyInjuryByAccidentEachAccidentBasis
          , BodilyInjuryByAccidentEachAccidentCurrency
          , BodilyInjuryByAccidentEachAccidentInclusionType
          , BodilyInjuryByAccidentEachAccidentPercentage
          , BodilyInjuryByAccidentEachAccidentText
          , BodilyInjuryByDiseaseEachEmployeeAmount
          , BodilyInjuryByDiseaseEachEmployeeAmountUSD
          , BodilyInjuryByDiseaseEachEmployeeBasis
          , BodilyInjuryByDiseaseEachEmployeeCurrency
          , BodilyInjuryByDiseaseEachEmployeeInclusionType
          , BodilyInjuryByDiseaseEachEmployeeNumberOfUnits
          , BodilyInjuryByDiseaseEachEmployeePercentage
          , BodilyInjuryByDiseaseEachEmployeeText
          , BodilyInjuryByDiseasePolicyLimitAmount
          , BodilyInjuryByDiseasePolicyLimitAmountUSD
          , BodilyInjuryByDiseasePolicyLimitBasis
          , BodilyInjuryByDiseasePolicyLimitCurrency
          , BodilyInjuryByDiseasePolicyLimitInclusionType
          , BodilyInjuryByDiseasePolicyLimitPercentage
          , BodilyInjuryByDiseasePolicyLimitText
          , ClaimsHandlingStructure
          , DamageToPremisesRentedToYouAmount
          , DamageToPremisesRentedToYouAmountUSD
          , DamageToPremisesRentedToYouBasis
          , DamageToPremisesRentedToYouCurrency
          , DamageToPremisesRentedToYouInclusionType
          , DamageToPremisesRentedToYouText
          , DeductibleAmount
          , DeductibleAmountUSD
          , DeductibleBasis
          , DeductibleCurrency
          , DeductibleNumberOfUnits
          , DeductiblePercentage
          , DeductibleText
          , EachOccurrenceLimitAmount
          , EachOccurrenceLimitAmountUSD
          , EachOccurrenceLimitBasis
          , EachOccurrenceLimitCurrency
          , EachOccurrenceLimitInclusionType
          , EachOccurrenceLimitNumberOfUnits
          , EachOccurrenceLimitPercentage
          , EachOccurrenceLimitText
          , EmployeeBenefitsLiabilityDeductibleRetentionAmountAmount
          , EmployeeBenefitsLiabilityDeductibleRetentionAmountAmountUSD
          , EmployeeBenefitsLiabilityDeductibleRetentionAmountBasis
          , EmployeeBenefitsLiabilityDeductibleRetentionAmountCurrency
          , EmployeeBenefitsLiabilityDeductibleRetentionAmountText
          , EmployersLiabilityEffectiveDate
          , EmployersLiabilityExpirationDate
          , EstimatedClaimsHandlingCostAmount
          , EstimatedClaimsHandlingCostAmountUSD
          , EstimatedClaimsHandlingCostCurrency
          , EstimatedClaimsHandlingCostText
          , FormOfCollateral
          , GeneralAggregateAmount
          , GeneralAggregateAmountUSD
          , GeneralAggregateBasis
          , GeneralAggregateCurrency
          , GeneralAggregateInclusionType
          , GeneralAggregateText
          , GeneralLiabilityEffectiveDate
          , GeneralLiabilityExpirationDate
          , LiabilityAmount
          , LiabilityAmountUSD
          , LiabilityBasis
          , LiabilityCurrency
          , LiabilityNumberOfUnits
          , LiabilityPercentage
          , LiabilityText
          , MedPayAmount
          , MedPayAmountUSD
          , MedPayBasis
          , MedPayCurrency
          , MedPayInclusionType
          , MedPayText
          , NumberOfExtraHeavyTrucks
          , NumberOfHeavyTrucks
          , NumberOfLightTrucks
          , NumberOfPowerUnits
          , NumberOfPrivatePersonalTransportVehicles
          , NumberOfTrailers
          , OtherStatesCoverage
          , PersonalAdvertisingInjuryPerPersonOrOrganizationAmount
          , PersonalAdvertisingInjuryPerPersonOrOrganizationAmountUSD
          , PersonalAdvertisingInjuryPerPersonOrOrganizationBasis
          , PersonalAdvertisingInjuryPerPersonOrOrganizationCurrency
          , PersonalAdvertisingInjuryPerPersonOrOrganizationInclusionType
          , PersonalAdvertisingInjuryPerPersonOrOrganizationText
          , PolicyEffectiveDate
          , PolicyEffectiveDateCurrency
          , PolicyEffectiveDateText
          , PolicyExpirationDate
          , PolicyExpirationDateCurrency
          , PolicyExpirationDateText
          , PolicyFormReportingForm
          , PolicyFormText
          , PolicyLimitAmount
          , PolicyLimitAmountUSD
          , PolicyLimitBasis
          , PolicyLimitCurrency
          , PolicyLimitInclusionType
          , PolicyLimitNumberOfUnits
          , PolicyLimitPercentage
          , PolicyLimitText
          , PolicyTrigger
          , PolicyTriggerText
          , ProductsCompletedOpsAggregateLimitAmount
          , ProductsCompletedOpsAggregateLimitAmountUSD
          , ProductsCompletedOpsAggregateLimitBasis
          , ProductsCompletedOpsAggregateLimitCurrency
          , ProductsCompletedOpsAggregateLimitInclusionType
          , ProductsCompletedOpsAggregateLimitPercentage
          , ProductsCompletedOpsAggregateLimitText
          , ProfessionalLiabilityEffectiveDate
          , ProfessionalLiabilityExpirationDate
          , QuoteExpiryDate
          , QuotedExpirationDate
          , RevenueAmount
          , RevenueAmountUSD
          , RevenueCurrency
          , StructureProgramStructure
          , SurplusLinesTax
          , SurplusLinesTaxAmount
          , SurplusLinesTaxCurrency
          , ThirdPartyAdministrator
          , TotalCollateralRequirementAmount
          , TotalCollateralRequirementAmountUSD
          , TotalCollateralRequirementCurrency
          , TotalCollateralRequirementPerStateRequirements
          , TotalCollateralRequirementText
          , TotalEstimatedPayrollDate
          , TotalEstimatedPayrollAmount
          , TotalEstimatedPayrollAmountUSD
          , TotalEstimatedPayrollCurrency
          , TotalExcessLimitsAmount
          , TotalExcessLimitsCurrency
          , TotalExcessLimitsPercentage
          , TotalExcessLimitsText
          , TotalOutstandingActuarialLiabilityCarrierAmount
          , TotalOutstandingActuarialLiabilityCarrierAmountUSD
          , TotalOutstandingActuarialLiabilityCarrierCurrency
          , TotalOutstandingActuarialLiabilityCarrierText
          , TotalValueAmount
          , TotalValueAmountUSD
          , TotalValueCurrency
          , TreatmentOfAllocatedLossAdjustmentExpenses
          , TerrorismRiskInsuranceActPremiumAmount
          , TerrorismRiskInsuranceActPremiumAmountUSD
          , TerrorismRiskInsuranceActPremiumCurrency
          , TerrorismRiskInsuranceActPremiumIncludedExcluded
          , WorkersCompensationAmount
          , WorkersCompensationAmountUSD
          , WorkersCompensationBasis
          , WorkersCompensationCurrency
          , WorkersCompensationNumberOfUnits
          , WorkersCompensationStatutoryRejected
          , WorkersCompensationText
        )
    SELECT
        p.CarrierResponseId
      , p.MarketResponseId
      , AggregateLimitAmount = TRY_CAST(p.[Aggregate Limit Amount] AS NUMERIC(38, 2))
      , AggregateLimitAmountUSD = TRY_CAST(p.[Aggregate Limit Amount USD] AS NUMERIC(38, 2))
      , AggregateLimitBasis = p.[Aggregate Limit Basis]
      , AggregateLimitCurrency = p.[Aggregate Limit Currency]
      , AggregateLimitInclusionType = p.[Aggregate Limit Inclusion Type]
      , AggregateLimitNotApplicable = p.[Aggregate Limit Not Applicable]
      , AggregateLimitPercentage = CAST(NULLIF(p.[Aggregate Limit Percentage], '') AS NUMERIC(18, 2)) / 100
      , AggregateLimitText = p.[Aggregate Limit Text]
      , AutoLiabilityEffectiveDate = TRY_CAST(p.[Auto Liability Effective Date] AS DATE)
      , AutoLiabilityExpirationDate = TRY_CAST(p.[Auto Liability Expiration Date] AS DATE)
      , AutoPhysicalDamageCollisionAmount = TRY_CAST(p.[Auto Physical Damage - Collision Amount] AS NUMERIC(38, 2))
      , AutoPhysicalDamageCollisionAmountUSD = TRY_CAST(p.[Auto Physical Damage - Collision Amount USD] AS NUMERIC(38, 2))
      , AutoPhysicalDamageCollisionBasis = p.[Auto Physical Damage - Collision Basis]
      , AutoPhysicalDamageCollisionCurrency = p.[Auto Physical Damage - Collision Currency]
      , AutoPhysicalDamageCollisionInclusionType = p.[Auto Physical Damage - Collision Inclusion Type]
      , AutoPhysicalDamageCollisionText = p.[Auto Physical Damage - Collision Text]
      , AutoPhysicalDamageCollisionValuation = p.[Auto Physical Damage - Collision Valuation]
      , AutoPhysicalDamageComprehensiveAmount = TRY_CAST(p.[Auto Physical Damage - Comprehensive Amount] AS NUMERIC(38, 2))
      , AutoPhysicalDamageComprehensiveAmountUSD = TRY_CAST(p.[Auto Physical Damage - Comprehensive Amount USD] AS NUMERIC(38, 2))
      , AutoPhysicalDamageComprehensiveBasis = p.[Auto Physical Damage - Comprehensive Basis]
      , AutoPhysicalDamageComprehensiveCurrency = p.[Auto Physical Damage - Comprehensive Currency]
      , AutoPhysicalDamageComprehensiveInclusionType = p.[Auto Physical Damage - Comprehensive Inclusion Type]
      , AutoPhysicalDamageComprehensivePercentage = CAST(NULLIF(p.[Auto Physical Damage - Comprehensive Percentage], '') AS NUMERIC(18, 2))
                                                    / 100
      , AutoPhysicalDamageComprehensiveText = p.[Auto Physical Damage - Comprehensive Text]
      , AutoPhysicalDamageComprehensiveValuation = p.[Auto Physical Damage - Comprehensive Valuation]
      , BasisofCoverBasis = p.[Basis of Cover Basis]
      , BodilyInjuryByAccidentEachAccidentAmount = TRY_CAST(p.[Bodily Injury by Accident - Each Accident Amount] AS NUMERIC(38, 2))
      , BodilyInjuryByAccidentEachAccidentAmountUSD = TRY_CAST(p.[Bodily Injury by Accident - Each Accident Amount USD] AS NUMERIC(38, 2))
      , BodilyInjuryByAccidentEachAccidentBasis = p.[Bodily Injury by Accident - Each Accident Basis]
      , BodilyInjuryByAccidentEachAccidentCurrency = p.[Bodily Injury by Accident - Each Accident Currency]
      , BodilyInjuryByAccidentEachAccidentInclusionType = p.[Bodily Injury by Accident - Each Accident Inclusion Type]
      , BodilyInjuryByAccidentEachAccidentPercentage = CAST(NULLIF(
                                                                p.[Bodily Injury by Accident - Each Accident Percentage]
                                                              , ''
                                                            ) AS NUMERIC(18, 2)) / 100
      , BodilyInjuryByAccidentEachAccidentText = p.[Bodily Injury by Accident - Each Accident Text]
      , BodilyInjuryByDiseaseEachEmployeeAmount = TRY_CAST(p.[Bodily Injury by Disease - Each Employee Amount] AS NUMERIC(38, 2))
      , BodilyInjuryByDiseaseEachEmployeeAmountUSD = TRY_CAST(p.[Bodily Injury by Disease - Each Employee Amount USD] AS NUMERIC(38, 2))
      , BodilyInjuryByDiseaseEachEmployeeBasis = p.[Bodily Injury by Disease - Each Employee Basis]
      , BodilyInjuryByDiseaseEachEmployeeCurrency = p.[Bodily Injury by Disease - Each Employee Currency]
      , BodilyInjuryByDiseaseEachEmployeeInclusionType = p.[Bodily Injury by Disease - Each Employee Inclusion Type]
      , BodilyInjuryByDiseaseEachEmployeeNumberOfUnits = TRY_CAST(p.[Bodily Injury by Disease - Each Employee Number of Units] AS INT)
      , BodilyInjuryByDiseaseEachEmployeePercentage = CAST(NULLIF(
                                                               p.[Bodily Injury by Disease - Each Employee Percentage]
                                                             , ''
                                                           ) AS NUMERIC(18, 2)) / 100
      , BodilyInjuryByDiseaseEachEmployeeText = p.[Bodily Injury by Disease - Each Employee Text]
      , BodilyInjuryByDiseasePolicyLimitAmount = TRY_CAST(p.[Bodily Injury by Disease - Policy Limit Amount] AS NUMERIC(38, 2))
      , BodilyInjuryByDiseasePolicyLimitAmountUSD = TRY_CAST(p.[Bodily Injury by Disease - Policy Limit Amount USD] AS NUMERIC(38, 2))
      , BodilyInjuryByDiseasePolicyLimitBasis = p.[Bodily Injury by Disease - Policy Limit Basis]
      , BodilyInjuryByDiseasePolicyLimitCurrency = p.[Bodily Injury by Disease - Policy Limit Currency]
      , BodilyInjuryByDiseasePolicyLimitInclusionType = p.[Bodily Injury by Disease - Policy Limit Inclusion Type]
      , BodilyInjuryByDiseasePolicyLimitPercentage = CAST(NULLIF(
                                                              p.[Bodily Injury by Disease - Policy Limit Percentage]
                                                            , ''
                                                          ) AS NUMERIC(18, 2)) / 100
      , BodilyInjuryByDiseasePolicyLimitText = p.[Bodily Injury by Disease - Policy Limit Text]
      , ClaimsHandlingStructure = p.[Claims Handling Structure]
      , DamageToPremisesRentedToYouAmount = TRY_CAST(p.[Damage to Premises Rented to You Amount] AS NUMERIC(38, 2))
      , DamageToPremisesRentedToYouAmountUSD = TRY_CAST(p.[Damage to Premises Rented to You Amount USD] AS NUMERIC(38, 2))
      , DamageToPremisesRentedToYouBasis = p.[Damage to Premises Rented to You Basis]
      , DamageToPremisesRentedToYouCurrency = p.[Damage to Premises Rented to You Currency]
      , DamageToPremisesRentedToYouInclusionType = p.[Damage to Premises Rented to You Inclusion Type]
      , DamageToPremisesRentedToYouText = p.[Damage to Premises Rented to You Text]
      , DeductibleAmount = TRY_CAST(p.[Deductible Amount] AS NUMERIC(38, 2))
      , DeductibleAmountUSD = TRY_CAST(p.[Deductible Amount USD] AS NUMERIC(38, 2))
      , DeductibleBasis = p.[Deductible Basis]
      , DeductibleCurrency = p.[Deductible Currency]
      , DeductibleNumberOfUnits = p.[Deductible Number of Units]
      , DeductiblePercentage = CAST(NULLIF(p.[Deductible Percentage], '') AS NUMERIC(18, 2)) / 100
      , DeductibleText = p.[Deductible Text]
      , EachOccurrenceLimitAmount = TRY_CAST(p.[Each Occurrence Limit Amount] AS NUMERIC(38, 2))
      , EachOccurrenceLimitAmountUSD = TRY_CAST(p.[Each Occurrence Limit Amount USD] AS NUMERIC(38, 2))
      , EachOccurrenceLimitBasis = p.[Each Occurrence Limit Basis]
      , EachOccurrenceLimitCurrency = p.[Each Occurrence Limit Currency]
      , EachOccurrenceLimitInclusionType = p.[Each Occurrence Limit Inclusion Type]
      , EachOccurrenceLimitNumberOfUnits = TRY_CAST(p.[Each Occurrence Limit Number of Units] AS INT)
      , EachOccurrenceLimitPercentage = CAST(NULLIF(p.[Each Occurrence Limit Percentage], '') AS NUMERIC(18, 2)) / 100
      , EachOccurrenceLimitText = p.[Each Occurrence Limit Text]
      , EmployeeBenefitsLiabilityDeductibleRetentionAmountAmount = TRY_CAST(p.[Employee Benefits Liability Deductible / Retention Amount Amount] AS NUMERIC(38, 2))
      , EmployeeBenefitsLiabilityDeductibleRetentionAmountAmountUSD = TRY_CAST(p.[Employee Benefits Liability Deductible / Retention Amount Amount USD] AS NUMERIC(38, 2))
      , EmployeeBenefitsLiabilityDeductibleRetentionAmountBasis = p.[Employee Benefits Liability Deductible / Retention Amount Basis]
      , EmployeeBenefitsLiabilityDeductibleRetentionAmountCurrency = p.[Employee Benefits Liability Deductible / Retention Amount Currency]
      , EmployeeBenefitsLiabilityDeductibleRetentionAmountText = p.[Employee Benefits Liability Deductible / Retention Amount Text]
      , EmployersLiabilityEffectiveDate = TRY_CAST(p.[Employers Liability Effective Date] AS DATE)
      , EmployersLiabilityExpirationDate = TRY_CAST(p.[Employers Liability Expiration Date] AS DATE)
      , EstimatedClaimsHandlingCostAmount = TRY_CAST(p.[Estimated Claims Handling Cost Amount] AS NUMERIC(38, 2))
      , EstimatedClaimsHandlingCostAmountUSD = TRY_CAST(p.[Estimated Claims Handling Cost Amount USD] AS NUMERIC(38, 2))
      , EstimatedClaimsHandlingCostCurrency = p.[Estimated Claims Handling Cost Currency]
      , EstimatedClaimsHandlingCostText = p.[Estimated Claims Handling Cost Text]
      , FormOfCollateral = p.[Form of Collateral]
      , GeneralAggregateAmount = TRY_CAST(p.[General Aggregate Amount] AS NUMERIC(38, 2))
      , GeneralAggregateAmountUSD = TRY_CAST(p.[General Aggregate Amount USD] AS NUMERIC(38, 2))
      , GeneralAggregateBasis = p.[General Aggregate Basis]
      , GeneralAggregateCurrency = p.[General Aggregate Currency]
      , GeneralAggregateInclusionType = p.[General Aggregate Inclusion Type]
      , GeneralAggregateText = p.[General Aggregate Text]
      , GeneralLiabilityEffectiveDate = TRY_CAST(p.[General Liability Effective Date] AS DATE)
      , GeneralLiabilityExpirationDate = TRY_CAST(p.[General Liability Expiration Date] AS DATE)
      , LiabilityAmount = TRY_CAST(p.[Liability Amount] AS NUMERIC(38, 2))
      , LiabilityAmountUSD = TRY_CAST(p.[Liability Amount USD] AS NUMERIC(38, 2))
      , LiabilityBasis = p.[Liability Basis]
      , LiabilityCurrency = p.[Liability Currency]
      , LiabilityNumberOfUnits = p.[Liability Number of Units]
      , LiabilityPercentage = CAST(NULLIF(p.[Liability Percentage], '') AS NUMERIC(18, 2)) / 100
      , LiabilityText = p.[Liability Text]
      , MedPayAmount = TRY_CAST(p.[Med Pay Amount] AS NUMERIC(38, 2))
      , MedPayAmountUSD = TRY_CAST(p.[Med Pay Amount USD] AS NUMERIC(38, 2))
      , MedPayBasis = p.[Med Pay Basis]
      , MedPayCurrency = p.[Med Pay Currency]
      , MedPayInclusionType = p.[Med Pay Inclusion Type]
      , MedPayText = p.[Med Pay Text]
      , NumberOfExtraHeavyTrucks = p.[Number of Extra Heavy Trucks]
      , NumberOfHeavyTrucks = p.[Number of Heavy Trucks]
      , NumberOfLightTrucks = p.[Number of Light Trucks]
      , NumberOfPowerUnits = p.[Number of Power Units]
      , NumberOfPrivatePersonalTransportVehicles = p.[Number of Private Personal Transport Vehicles]
      , NumberOfTrailers = p.[Number of Trailers]
      , OtherStatesCoverage = p.[Other States Coverage]
      , PersonalAdvertisingInjuryPerPersonOrOrganizationAmount = TRY_CAST(p.[Personal & Advertising Injury - Per Person or Organization Amount] AS NUMERIC(38, 2))
      , PersonalAdvertisingInjuryPerPersonOrOrganizationAmountUSD = TRY_CAST(p.[Personal & Advertising Injury - Per Person or Organization Amount USD] AS NUMERIC(38, 2))
      , PersonalAdvertisingInjuryPerPersonOrOrganizationBasis = p.[Personal & Advertising Injury - Per Person or Organization Basis]
      , PersonalAdvertisingInjuryPerPersonOrOrganizationCurrency = p.[Personal & Advertising Injury - Per Person or Organization Currency]
      , PersonalAdvertisingInjuryPerPersonOrOrganizationInclusionType = p.[Personal & Advertising Injury - Per Person or Organization Inclusion Type]
      , PersonalAdvertisingInjuryPerPersonOrOrganizationText = p.[Personal & Advertising Injury - Per Person or Organization Text]
      , PolicyEffectiveDate = TRY_CAST(p.[Policy Effective Date] AS DATE)
      , PolicyEffectiveDateCurrency = p.[Policy Effective Date Currency]
      , PolicyEffectiveDateText = p.[Policy Effective Date Text]
      , PolicyExpirationDate = TRY_CAST(p.[Policy Expiration Date] AS DATE)
      , PolicyExpirationDateCurrency = p.[Policy Expiration Date Currency]
      , PolicyExpirationDateText = p.[Policy Expiration Date Text]
      , PolicyFormReportingForm = p.[Policy Form Reporting Form]
      , PolicyFormText = p.[Policy Form Text]
      , PolicyLimitAmount = TRY_CAST(p.[Policy Limit Amount] AS NUMERIC(38, 2))
      , PolicyLimitAmountUSD = TRY_CAST(p.[Policy Limit Amount USD] AS NUMERIC(38, 2))
      , PolicyLimitBasis = p.[Policy Limit Basis]
      , PolicyLimitCurrency = p.[Policy Limit Currency]
      , PolicyLimitInclusionType = p.[Policy Limit Inclusion Type]
      , PolicyLimitNumberOfUnits = TRY_CAST(p.[Policy Limit Number of Units] AS INT)
      , PolicyLimitPercentage = CAST(NULLIF(p.[Policy Limit Percentage], '') AS NUMERIC(18, 2)) / 100
      , PolicyLimitText = p.[Policy Limit Text]
      , PolicyTrigger = p.[Policy Trigger]
      , PolicyTriggerText = p.[Policy Trigger Text]
      , ProductsCompletedOpsAggregateLimitAmount = TRY_CAST(p.[Products & Completed Ops Aggregate Limit Amount] AS NUMERIC(38, 2))
      , ProductsCompletedOpsAggregateLimitAmountUSD = TRY_CAST(p.[Products & Completed Ops Aggregate Limit Amount USD] AS NUMERIC(38, 2))
      , ProductsCompletedOpsAggregateLimitBasis = p.[Products & Completed Ops Aggregate Limit Basis]
      , ProductsCompletedOpsAggregateLimitCurrency = p.[Products & Completed Ops Aggregate Limit Currency]
      , ProductsCompletedOpsAggregateLimitInclusionType = p.[Products & Completed Ops Aggregate Limit Inclusion Type]
      , ProductsCompletedOpsAggregateLimitPercentage = CAST(NULLIF(
                                                                p.[Products & Completed Ops Aggregate Limit Percentage]
                                                              , ''
                                                            ) AS NUMERIC(18, 2)) / 100
      , ProductsCompletedOpsAggregateLimitText = p.[Products & Completed Ops Aggregate Limit Text]
      , ProfessionalLiabilityEffectiveDate = TRY_CAST(p.[Professional Liability Effective Date] AS DATE)
      , ProfessionalLiabilityExpirationDate = TRY_CAST(p.[Professional Liability Expiration Date] AS DATE)
      , QuoteExpiryDate = TRY_CAST(p.[Quote Expiry Date] AS DATE)
      , QuotedExpirationDate = TRY_CAST(p.[Quoted Expiration Date] AS DATE)
      , RevenueAmount = TRY_CAST(p.[Revenue Amount] AS NUMERIC(38, 2))
      , RevenueAmountUSD = TRY_CAST(p.[Revenue Amount USD] AS NUMERIC(38, 2))
      , RevenueCurrency = p.[Revenue Currency]
      , StructureProgramStructure = p.[Structure Program Structure]
      , SurplusLinesTax = p.[Surplus Lines Tax]
      , SurplusLinesTaxAmount = p.[Surplus Lines Tax Amount]
      , SurplusLinesTaxCurrency = p.[Surplus Lines Tax Currency]
      , ThirdPartyAdministrator = p.[Third Party Administrator]
      , TotalCollateralRequirementAmount = TRY_CAST(p.[Total Collateral Requirement Amount] AS NUMERIC(38, 2))
      , TotalCollateralRequirementAmountUSD = TRY_CAST(p.[Total Collateral Requirement Amount USD] AS NUMERIC(38, 2))
      , TotalCollateralRequirementCurrency = p.[Total Collateral Requirement Currency]
      , TotalCollateralRequirementPerStateRequirements = p.[Total Collateral Requirement Per State Requirements]
      , TotalCollateralRequirementText = p.[Total Collateral Requirement Text]
      , TotalEstimatedPayroll = p.[Total Estimated Payroll  Date]
      , TotalEstimatedPayrollAmount = TRY_CAST(p.[Total Estimated Payroll  Amount] AS NUMERIC(38, 2))
      , TotalEstimatedPayrollAmountUSD = TRY_CAST(p.[Total Estimated Payroll  Amount USD] AS NUMERIC(38, 2))
      , TotalEstimatedPayrollCurrency = p.[Total Estimated Payroll  Currency]
      , TotalExcessLimitsAmount = p.[Total Excess Limits Amount]
      , TotalExcessLimitsCurrency = p.[Total Excess Limits Currency]
      , TotalExcessLimitsPercentage = CAST(NULLIF(p.[Total Excess Limits Percentage], '') AS NUMERIC(18, 2)) / 100
      , TotalExcessLimitsText = p.[Total Excess Limits Text]
      , TotalOutstandingActuarialLiabilityCarrierAmount = TRY_CAST(p.[Total Outstanding Actuarial Liability - Carrier Amount] AS NUMERIC(38, 2))
      , TotalOutstandingActuarialLiabilityCarrierAmountUSD = TRY_CAST(p.[Total Outstanding Actuarial Liability - Carrier Amount USD] AS NUMERIC(38, 2))
      , TotalOutstandingActuarialLiabilityCarrierCurrency = p.[Total Outstanding Actuarial Liability - Carrier Currency]
      , TotalOutstandingActuarialLiabilityCarrierText = p.[Total Outstanding Actuarial Liability - Carrier Text]
      , TotalValueAmount = TRY_CAST(p.[Total Value Amount] AS NUMERIC(38, 2))
      , TotalValueAmountUSD = TRY_CAST(p.[Total Value Amount USD] AS NUMERIC(38, 2))
      , TotalValueCurrency = p.[Total Value Currency]
      , TreatmentOfAllocatedLossAdjustmentExpenses = p.[Treatment of ALAE ]
      , TerrorismRiskInsuranceActPremiumAmount = TRY_CAST(p.[TRIA Premium Amount] AS NUMERIC(38, 2))
      , TerrorismRiskInsuranceActPremiumAmountUSD = TRY_CAST(p.[TRIA Premium Amount USD] AS NUMERIC(38, 2))
      , TerrorismRiskInsuranceActPremiumCurrency = p.[TRIA Premium Currency]
      , TerrorismRiskInsuranceActPremiumIncludedExcluded = p.[TRIA Premium Included / Excluded]
      , WorkersCompensationAmount = TRY_CAST(p.[Workers Compensation Amount] AS NUMERIC(38, 2))
      , WorkersCompensationAmountUSD = TRY_CAST(p.[Workers Compensation Amount USD] AS NUMERIC(38, 2))
      , WorkersCompensationBasis = p.[Workers Compensation Basis]
      , WorkersCompensationCurrency = p.[Workers Compensation Currency]
      , WorkersCompensationNumberOfUnits = TRY_CAST(p.[Workers Compensation Number of Units] AS INT)
      , WorkersCompensationStatutoryRejected = p.[Workers Compensation Statutory / Rejected]
      , WorkersCompensationText = p.[Workers Compensation Text]
    FROM (
        SELECT
            mrea.CarrierResponseId
          , mrea.MarketResponseId
          , mrea.ElementType
          , mrea.LookUpValue
        FROM
            #MarketResponseElementAttribute mrea
    ) pvt
    PIVOT (
        MAX(LookUpValue)
        FOR ElementType IN (
            [Aggregate Limit Amount], [Aggregate Limit Amount USD], [Aggregate Limit Basis], [Aggregate Limit Currency]
          , [Aggregate Limit Inclusion Type], [Aggregate Limit Not Applicable], [Aggregate Limit Percentage]
          , [Aggregate Limit Text], [Auto Liability Effective Date], [Auto Liability Expiration Date]
          , [Auto Physical Damage - Collision Amount], [Auto Physical Damage - Collision Amount USD]
          , [Auto Physical Damage - Collision Basis], [Auto Physical Damage - Collision Currency]
          , [Auto Physical Damage - Collision Inclusion Type], [Auto Physical Damage - Collision Text]
          , [Auto Physical Damage - Collision Valuation], [Auto Physical Damage - Comprehensive Amount]
          , [Auto Physical Damage - Comprehensive Amount USD], [Auto Physical Damage - Comprehensive Basis]
          , [Auto Physical Damage - Comprehensive Currency], [Auto Physical Damage - Comprehensive Inclusion Type]
          , [Auto Physical Damage - Comprehensive Percentage], [Auto Physical Damage - Comprehensive Text]
          , [Auto Physical Damage - Comprehensive Valuation], [Bodily Injury by Accident - Each Accident Amount]
          , [Basis of Cover Basis], [Bodily Injury by Accident - Each Accident Amount USD]
          , [Bodily Injury by Accident - Each Accident Basis], [Bodily Injury by Accident - Each Accident Currency]
          , [Bodily Injury by Accident - Each Accident Inclusion Type]
          , [Bodily Injury by Accident - Each Accident Percentage], [Bodily Injury by Accident - Each Accident Text]
          , [Bodily Injury by Disease - Each Employee Amount], [Bodily Injury by Disease - Each Employee Amount USD]
          , [Bodily Injury by Disease - Each Employee Basis], [Bodily Injury by Disease - Each Employee Currency]
          , [Bodily Injury by Disease - Each Employee Inclusion Type]
          , [Bodily Injury by Disease - Each Employee Number of Units]
          , [Bodily Injury by Disease - Each Employee Percentage], [Bodily Injury by Disease - Each Employee Text]
          , [Bodily Injury by Disease - Policy Limit Amount], [Bodily Injury by Disease - Policy Limit Amount USD]
          , [Bodily Injury by Disease - Policy Limit Basis], [Bodily Injury by Disease - Policy Limit Currency]
          , [Bodily Injury by Disease - Policy Limit Inclusion Type]
          , [Bodily Injury by Disease - Policy Limit Percentage], [Bodily Injury by Disease - Policy Limit Text]
          , [Claims Handling Structure], [Damage to Premises Rented to You Amount]
          , [Damage to Premises Rented to You Amount USD], [Damage to Premises Rented to You Basis]
          , [Damage to Premises Rented to You Currency], [Damage to Premises Rented to You Inclusion Type]
          , [Damage to Premises Rented to You Text], [Deductible Amount], [Deductible Amount USD], [Deductible Basis]
          , [Deductible Currency], [Deductible Number of Units], [Deductible Percentage], [Deductible Text]
          , [Each Occurrence Limit Amount], [Each Occurrence Limit Amount USD], [Each Occurrence Limit Basis]
          , [Each Occurrence Limit Currency], [Each Occurrence Limit Inclusion Type]
          , [Each Occurrence Limit Number of Units], [Each Occurrence Limit Percentage], [Each Occurrence Limit Text]
          , [Employee Benefits Liability Deductible / Retention Amount Amount]
          , [Employee Benefits Liability Deductible / Retention Amount Amount USD]
          , [Employee Benefits Liability Deductible / Retention Amount Basis]
          , [Employee Benefits Liability Deductible / Retention Amount Currency]
          , [Employee Benefits Liability Deductible / Retention Amount Text], [Employers Liability Effective Date]
          , [Employers Liability Expiration Date], [Estimated Claims Handling Cost Amount]
          , [Estimated Claims Handling Cost Amount USD], [Estimated Claims Handling Cost Currency]
          , [Estimated Claims Handling Cost Text], [Form of Collateral], [General Aggregate Amount]
          , [General Aggregate Amount USD], [General Aggregate Basis], [General Aggregate Currency]
          , [General Aggregate Inclusion Type], [General Aggregate Text], [General Liability Effective Date]
          , [General Liability Expiration Date], [Liability Amount], [Liability Amount USD], [Liability Basis]
          , [Liability Currency], [Liability Number of Units], [Liability Percentage], [Liability Text]
          , [Med Pay Amount], [Med Pay Amount USD], [Med Pay Basis], [Med Pay Currency], [Med Pay Inclusion Type]
          , [Med Pay Text], [Number of Extra Heavy Trucks], [Number of Heavy Trucks], [Number of Light Trucks]
          , [Number of Power Units], [Number of Private Personal Transport Vehicles], [Number of Trailers]
          , [Other States Coverage], [Personal & Advertising Injury - Per Person or Organization Amount]
          , [Personal & Advertising Injury - Per Person or Organization Amount USD]
          , [Personal & Advertising Injury - Per Person or Organization Basis]
          , [Personal & Advertising Injury - Per Person or Organization Currency]
          , [Personal & Advertising Injury - Per Person or Organization Inclusion Type]
          , [Personal & Advertising Injury - Per Person or Organization Text], [Policy Effective Date]
          , [Policy Effective Date Currency], [Policy Effective Date Text], [Policy Expiration Date]
          , [Policy Expiration Date Currency], [Policy Expiration Date Text], [Policy Form Reporting Form]
          , [Policy Form Text], [Policy Limit Amount], [Policy Limit Amount USD], [Policy Limit Basis]
          , [Policy Limit Currency], [Policy Limit Inclusion Type], [Policy Limit Number of Units]
          , [Policy Limit Percentage], [Policy Limit Text], [Policy Trigger], [Policy Trigger Text]
          , [Products & Completed Ops Aggregate Limit Amount], [Products & Completed Ops Aggregate Limit Amount USD]
          , [Products & Completed Ops Aggregate Limit Basis], [Products & Completed Ops Aggregate Limit Currency]
          , [Products & Completed Ops Aggregate Limit Inclusion Type]
          , [Products & Completed Ops Aggregate Limit Percentage], [Products & Completed Ops Aggregate Limit Text]
          , [Professional Liability Effective Date], [Professional Liability Expiration Date], [Quote Expiry Date]
          , [Quoted Expiration Date], [Revenue Amount], [Revenue Amount USD], [Revenue Currency]
          , [Structure Program Structure], [Surplus Lines Tax], [Surplus Lines Tax Amount]
          , [Surplus Lines Tax Currency], [Third Party Administrator], [Total Collateral Requirement Amount]
          , [Total Collateral Requirement Amount USD], [Total Collateral Requirement Currency]
          , [Total Collateral Requirement Per State Requirements], [Total Collateral Requirement Text]
          , [Total Estimated Payroll  Date], [Total Estimated Payroll  Amount], [Total Estimated Payroll  Amount USD]
          , [Total Estimated Payroll  Currency], [Total Excess Limits Amount], [Total Excess Limits Currency]
          , [Total Excess Limits Percentage], [Total Excess Limits Text]
          , [Total Outstanding Actuarial Liability - Carrier Amount]
          , [Total Outstanding Actuarial Liability - Carrier Amount USD]
          , [Total Outstanding Actuarial Liability - Carrier Currency]
          , [Total Outstanding Actuarial Liability - Carrier Text], [Total Value Amount], [Total Value Amount USD]
          , [Total Value Currency], [Treatment of ALAE ], [TRIA Premium Amount], [TRIA Premium Amount USD]
          , [TRIA Premium Currency], [TRIA Premium Included / Excluded], [Workers Compensation Amount]
          , [Workers Compensation Amount USD], [Workers Compensation Basis], [Workers Compensation Currency]
          , [Workers Compensation Number of Units], [Workers Compensation Statutory / Rejected]
          , [Workers Compensation Text]
        )
    ) p;

    SELECT @InsertedCount = @@ROWCOUNT;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Insert ' + @TargetTable;

DROP TABLE IF EXISTS #MarketResponseElementAttribute;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);