/*
Lineage
rpt.PlacementPrimaryRole.PlacementId=dbo.Placement.PlacementId
rpt.PlacementPrimaryRole.Client=dbo.Party.PartyName
rpt.PlacementPrimaryRole.Client=dbo.PlacementPartyRole.PartyName
rpt.PlacementPrimaryRole.Broker=dbo.PlacementSystemUser.GivenName
rpt.PlacementPrimaryRole.Broker=dbo.PlacementSystemUser.Surname
rpt.PlacementPrimaryRole.AccountExecutive=dbo.PlacementSystemUser.GivenName
rpt.PlacementPrimaryRole.AccountExecutive=dbo.PlacementSystemUser.Surname
rpt.PlacementPrimaryRole.Insured=dbo.Party.PartyName
rpt.PlacementPrimaryRole.Insured=dbo.PlacementPartyRole.PartyName
rpt.PlacementPrimaryRole.AccountManager=dbo.PlacementSystemUser.GivenName
rpt.PlacementPrimaryRole.AccountManager=dbo.PlacementSystemUser.Surname
rpt.PlacementPrimaryRole.AccountTechnician=dbo.PlacementSystemUser.GivenName
rpt.PlacementPrimaryRole.AccountTechnician=dbo.PlacementSystemUser.Surname
rpt.PlacementPrimaryRole.Analyst=dbo.PlacementSystemUser.GivenName
rpt.PlacementPrimaryRole.Analyst=dbo.PlacementSystemUser.Surname
rpt.PlacementPrimaryRole.AssistantClientServiceSpecialist=dbo.PlacementSystemUser.GivenName
rpt.PlacementPrimaryRole.AssistantClientServiceSpecialist=dbo.PlacementSystemUser.Surname
rpt.PlacementPrimaryRole.ClientAdvocate=dbo.PlacementSystemUser.GivenName
rpt.PlacementPrimaryRole.ClientAdvocate=dbo.PlacementSystemUser.Surname
rpt.PlacementPrimaryRole.ClientManager=dbo.PlacementSystemUser.GivenName
rpt.PlacementPrimaryRole.ClientManager=dbo.PlacementSystemUser.Surname
rpt.PlacementPrimaryRole.Compliance=dbo.PlacementSystemUser.GivenName
rpt.PlacementPrimaryRole.Compliance=dbo.PlacementSystemUser.Surname
rpt.PlacementPrimaryRole.GlobalClientAdvocate=dbo.PlacementSystemUser.GivenName
rpt.PlacementPrimaryRole.GlobalClientAdvocate=dbo.PlacementSystemUser.Surname
rpt.PlacementPrimaryRole.PlacementCreator=dbo.PlacementSystemUser.GivenName
rpt.PlacementPrimaryRole.PlacementCreator=dbo.PlacementSystemUser.Surname
rpt.PlacementPrimaryRole.Producer=dbo.PlacementSystemUser.GivenName
rpt.PlacementPrimaryRole.Producer=dbo.PlacementSystemUser.Surname
rpt.PlacementPrimaryRole.ProductGroupLeader=dbo.PlacementSystemUser.GivenName
rpt.PlacementPrimaryRole.ProductGroupLeader=dbo.PlacementSystemUser.Surname
rpt.PlacementPrimaryRole.SeniorClientManager=dbo.PlacementSystemUser.GivenName
rpt.PlacementPrimaryRole.SeniorClientManager=dbo.PlacementSystemUser.Surname
rpt.PlacementPrimaryRole.StrategicBrokingAdvisor=dbo.PlacementSystemUser.GivenName
rpt.PlacementPrimaryRole.StrategicBrokingAdvisor=dbo.PlacementSystemUser.Surname
rpt.PlacementPrimaryRole.SupportBroker=dbo.PlacementSystemUser.GivenName
rpt.PlacementPrimaryRole.SupportBroker=dbo.PlacementSystemUser.Surname
rpt.PlacementPrimaryRole.UmbrellaAndExcessBroker=dbo.PlacementSystemUser.GivenName
rpt.PlacementPrimaryRole.UmbrellaAndExcessBroker=dbo.PlacementSystemUser.Surname
rpt.PlacementPrimaryRole.BLU=dbo.PlacementSystemUser.GivenName
rpt.PlacementPrimaryRole.BLU=dbo.PlacementSystemUser.Surname
*/
CREATE PROCEDURE rpt.Load_rpt_PlacementPrimaryRole
AS
BEGIN
    DECLARE @InsertedCount INT = 0;
    DECLARE @UpdatedCount INT = 0;
    DECLARE @DeletedCount INT = 0;
    DECLARE @RejectedCount INT = 0;
    DECLARE @TargetTable VARCHAR(50) = 'rpt.PlacementPrimaryRole';
    DECLARE @SprocName VARCHAR(255);
    DECLARE @Action NVARCHAR(255);

    SET NOCOUNT ON;

    SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

    EXEC ADF.StoredProcStartLog @SprocName;

    BEGIN TRY
        DROP TABLE IF EXISTS #TempTable;
        DROP TABLE IF EXISTS #ClientInsuredFromParty;
        DROP TABLE IF EXISTS #TeamMember;
        DROP TABLE IF EXISTS #TeamMemberPV;

        CREATE TABLE #TempTable (
            PlacementId          INT           NOT NULL
          , PartyName            NVARCHAR(500) NULL
          , PartyRoleId          INT           NOT NULL
          , PartyRoleDescription NVARCHAR(250) NOT NULL
          , RowNum               INT           NOT NULL
        );

        INSERT INTO
            #TempTable
            (
                PlacementId
              , PartyName
              , PartyRoleDescription
              , RowNum
            )
        SELECT DISTINCT
               PlacementId = PPR.PlacementId
             , PartyName = ISNULL(P.PartyName, PPR.PartyName)
             , GPR.PartyRoleDescription
             , RowNum = ROW_NUMBER() OVER (PARTITION BY PPR.PlacementId, GPR.PartyRoleDescription ORDER BY PPR.IsPrimaryParty DESC, PPR.PartyId ASC)
        FROM
            dbo.PlacementPartyRole PPR
            LEFT JOIN dbo.Party P
                ON PPR.PartyId = P.PartyId

            INNER JOIN ref.PartyRole PR
                ON PPR.PartyRoleId = PR.PartyRoleId

            INNER JOIN Reference.PartyRole GPR
                ON GPR.PartyRoleId = PR.GlobalPartyRoleId
                   AND GPR.PartyRoleDescription IN (
                           N'Client', N'Insured'
                       )
        WHERE
            PPR.IsDeleted = 0
            AND PPR.OnPlacement = 1; --> Placement on Party at BP

        CREATE TABLE #ClientInsuredFromParty (
            PlacementId          INT           NOT NULL
          , PartyName            NVARCHAR(500) NULL
          , PartyRoleDescription NVARCHAR(250) NOT NULL
          , RowNum               INT           NOT NULL
        );

        INSERT INTO
            #ClientInsuredFromParty
            (
                PlacementId
              , PartyName
              , PartyRoleDescription
              , RowNum
            )
        SELECT
            PlacementId
          , PartyName
          , PartyRoleDescription
          , RowNum
        FROM
            #TempTable
        UNION
        SELECT
            t.PlacementId
          , t.PartyName
          , PartyRoleDescription = pseudoinsured.BPPartyRoleDescription
          , t.RowNum
        FROM
            #TempTable t
            INNER JOIN (
                SELECT
                    PR.PartyRoleId
                  , BPPartyRoleDescription = N'Insured'
                FROM
                    ref.PartyRole PR
                    JOIN Reference.PartyRole GPR
                        ON GPR.PartyRoleId = PR.GlobalPartyRoleId
                           AND GPR.PartyRoleDescription = N'Client'
            ) pseudoinsured
                ON pseudoinsured.PartyRoleId = t.PartyRoleId
        WHERE
            NOT EXISTS (
            SELECT * FROM #TempTable x WHERE x.PlacementId = t.PlacementId AND x.PartyRoleDescription = 'Insured'
        ); /* Only if it doesn't already have insureds. */

        CREATE TABLE #TeamMember (
            PlacementId   INT           NOT NULL
          , UserName      NVARCHAR(250) NOT NULL
          , ServicingRole NVARCHAR(100) NOT NULL
          , RowNum        INT           NOT NULL
        );

        INSERT INTO
            #TeamMember
            (
                PlacementId
              , UserName
              , ServicingRole
              , RowNum
            )
        SELECT
            PTM.PlacementId
          , UserName = PSU.GivenName + ' ' + PSU.Surname
          , PSR.ServicingRole
          , RowNum = ROW_NUMBER() OVER (PARTITION BY PTM.PlacementId, PSR.ServicingRole ORDER BY PTM.ETLUpdatedDate)
        FROM
            dbo.PlacementTeamMember PTM
            INNER JOIN ref.ServicingRole PSR
                ON PTM.RoleId = PSR.ServicingRoleId
                   AND PTM.DataSourceInstanceId = PSR.DataSourceInstanceId

            INNER JOIN dbo.PlacementSystemUser PSU
                ON PTM.UserId = PSU.UserId
                   AND PSU.DataSourceInstanceId = PTM.DataSourceInstanceId
        WHERE
            PTM.IsDeleted = 0;

        CREATE TABLE #TeamMemberPV (
            PlacementId                           INT           NOT NULL
          , [Account Executive]                   NVARCHAR(255) NULL
          , [Account Manager]                     NVARCHAR(255) NULL
          , [Account Technician]                  NVARCHAR(255) NULL
          , Analyst                               NVARCHAR(255) NULL
          , [Assistant Client Service Specialist] NVARCHAR(255) NULL
          , Broker                                NVARCHAR(255) NULL
          , [Client Advocate]                     NVARCHAR(255) NULL
          , [Client Executive]                    NVARCHAR(255) NULL
          , [Client Manager]                      NVARCHAR(255) NULL
          , Compliance                            NVARCHAR(255) NULL
          , [Global Client Advocate]              NVARCHAR(255) NULL
          , [Placement Creator]                   NVARCHAR(255) NULL
          , Producer                              NVARCHAR(255) NULL
          , [Product Group Leader]                NVARCHAR(255) NULL
          , [Senior Client Manager]               NVARCHAR(255) NULL
          , [Strategic Broking Advisor]           NVARCHAR(255) NULL
          , [Support Broker]                      NVARCHAR(255) NULL
          , [Umbrella And Excess Broker]          NVARCHAR(255) NULL
          , BLU                                   NVARCHAR(255) NULL
        );

        INSERT INTO
            #TeamMemberPV
            (
                PlacementId
              , [Account Executive]
              , [Account Manager]
              , [Account Technician]
              , Analyst
              , [Assistant Client Service Specialist]
              , Broker
              , [Client Advocate]
              , [Client Executive]
              , [Client Manager]
              , Compliance
              , [Global Client Advocate]
              , [Placement Creator]
              , Producer
              , [Product Group Leader]
              , [Senior Client Manager]
              , [Strategic Broking Advisor]
              , [Support Broker]
              , [Umbrella And Excess Broker]
              , BLU
            )
        SELECT
            PV.PlacementId
          , PV.[Account Executive]
          , PV.[Account Manager]
          , PV.[Account Technician]
          , PV.Analyst
          , PV.[Assistant Client Service Specialist]
          , PV.Broker
          , PV.[Client Advocate]
          , PV.[Client Executive]
          , PV.[Client Manager]
          , PV.Compliance
          , PV.[Global Client Advocate]
          , PV.[Placement Creator]
          , PV.Producer
          , PV.[Product Group Leader]
          , PV.[Senior Client Manager]
          , PV.[Strategic Broking Advisor]
          , PV.[Support Broker]
          , PV.[Umbrella & Excess Broker]
          , PV.BLU
        FROM (SELECT PlacementId, ServicingRole, UserName FROM #TeamMember WHERE RowNum = 1) S
        PIVOT (
            MAX(UserName)
            FOR ServicingRole IN (
                [Account Executive], [Account Manager], [Account Technician], Analyst
              , [Assistant Client Service Specialist], Broker, [Client Advocate], [Client Executive], [Client Manager]
              , Compliance, [Global Client Advocate], [Placement Creator], Producer, [Product Group Leader]
              , [Senior Client Manager], [Strategic Broking Advisor], [Support Broker], [Umbrella & Excess Broker], BLU
            )
        ) PV;

        TRUNCATE TABLE rpt.PlacementPrimaryRole;

        INSERT INTO
            rpt.PlacementPrimaryRole
            (
                PlacementId
              , Client
              , Broker
              , AccountExecutive
              , Insured
              , AccountManager
              , AccountTechnician
              , Analyst
              , AssistantClientServiceSpecialist
              , ClientAdvocate
              , ClientManager
              , Compliance
              , GlobalClientAdvocate
              , PlacementCreator
              , Producer
              , ProductGroupLeader
              , SeniorClientManager
              , StrategicBrokingAdvisor
              , SupportBroker
              , UmbrellaAndExcessBroker
              , BLU
            )
        SELECT
            pl.PlacementId
          , Client = cip.PartyName
          , TM.Broker
          , [Account Executive] = ISNULL(TM.[Client Executive], TM.[Account Executive])
          , Insured = insured.PartyName
          , TM.[Account Manager]
          , TM.[Account Technician]
          , TM.Analyst
          , TM.[Assistant Client Service Specialist]
          , TM.[Client Advocate]
          , TM.[Client Manager]
          , TM.Compliance
          , TM.[Global Client Advocate]
          , TM.[Placement Creator]
          , TM.Producer
          , TM.[Product Group Leader]
          , TM.[Senior Client Manager]
          , TM.[Strategic Broking Advisor]
          , TM.[Support Broker]
          , TM.[Umbrella And Excess Broker]
          , TM.BLU
        FROM
            dbo.Placement pl
            LEFT JOIN #ClientInsuredFromParty cip
                ON pl.PlacementId = cip.PlacementId
                   AND cip.RowNum = 1
                   AND cip.PartyRoleDescription = 'Client'

            LEFT JOIN #ClientInsuredFromParty insured
                ON pl.PlacementId = insured.PlacementId
                   AND insured.RowNum = 1
                   AND insured.PartyRoleDescription = 'Insured'

            LEFT JOIN #TeamMemberPV TM
                ON pl.PlacementId = TM.PlacementId
        WHERE
            pl.PlacementSystemId IS NOT NULL
            AND pl.DataSourceInstanceId = 50366
            AND pl.IsDeleted = 0;

        SELECT @InsertedCount = @@ROWCOUNT;

        DROP TABLE #ClientInsuredFromParty;
        DROP TABLE #TeamMember;

        SET @Action = N'Insert into ' + @TargetTable;

        EXEC ADF.StoredProcSetSqlLog
            @SprocName
          , @InsertedCount
          , @UpdatedCount
          , @DeletedCount
          , @RejectedCount
          , @Action
          , NULL;
    END TRY
    BEGIN CATCH
        DECLARE @ErrorMessage NVARCHAR(MAX);

        SET @ErrorMessage = ERROR_MESSAGE();

        EXEC ADF.StoredProcErrorLog
            @SprocName
          , @ErrorMessage;

        SET @RejectedCount = 1;
    END CATCH;

    EXEC ADF.StoredProcEndLog @SprocName;

    SELECT
        InsertedCount = ISNULL(@InsertedCount, 0)
      , UpdatedCount = ISNULL(@UpdatedCount, 0)
      , DeletedCount = ISNULL(@DeletedCount, 0)
      , RejectedCount = ISNULL(@RejectedCount, 0);

    RETURN 0;
END;