/*
Lineage
rpt.PlacementSecurity.DataSourceInstanceId=dbo.Placement.DataSourceInstanceId
rpt.PlacementSecurity.DataSourceInstanceId=dbo.Placement.ServicingPlatformId
rpt.PlacementSecurity.PlacementId=dbo.Placement.PlacementId
rpt.PlacementSecurity.TeamId=dbo.Placement.PlacementId
rpt.PlacementSecurity.TeamId=dbo.Scope.ScopeId
rpt.PlacementSecurity.TeamId=dbo.Scope.ScopeItemId
rpt.PlacementSecurity.TeamId=dbo.Scope.TeamId
*/
CREATE PROCEDURE rpt.Load_rpt_PlacementSecurity
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'rpt.PlacementSecurity';
DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET NOCOUNT ON;

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    -------------------------------------------------------------------------------------------------------------
    DROP TABLE IF EXISTS #SourceforMerge;

    CREATE TABLE #SourceforMerge (
        PlacementId          BIGINT       NOT NULL
      , DataSourceInstanceId INT          NOT NULL
      , TeamId               NVARCHAR(30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL
      , TeamType             NVARCHAR(50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL
    );

    -------------------------------------------------------------------------------------------------------------
    INSERT INTO
        #SourceforMerge
        (
            PlacementId
          , DataSourceInstanceId
          , TeamId
          , TeamType
        )
    --Placement Links
    SELECT DISTINCT
           pl.PlacementId
         , DataSourceInstanceId = pl.DataSourceInstanceId
         , TeamId = CAST(pl.PlacementId AS NVARCHAR(20))
         , TeamType = N'Placement'
    FROM
        dbo.Placement pl
    WHERE
        pl.PlacementSystemId IS NOT NULL
        AND pl.DataSourceInstanceId = 50366
        AND EXISTS (
        SELECT 1 FROM dbo.PlacementTeamMember ptm WHERE ptm.PlacementId = pl.PlacementId
    );

    -------------------------------------------------------------------------------------------------------------

    -- Extract the team hierarchy so we don't rely on the view for just two columns!
    DROP TABLE IF EXISTS #th;

    SELECT
        TeamID = TeamId
      , OrgNode
    INTO #th
    FROM
        ref.Team;

    -- Get all the relationships between teams and placements.
    DROP TABLE IF EXISTS #related;

    SELECT
        TopTeamId = tt.TeamID
      , pt.PlacementId
    INTO #related
    FROM
        #th tt
        INNER JOIN #th dt
            ON dt.OrgNode.IsDescendantOf(tt.OrgNode) = 1

        INNER JOIN dbo.PlacementTeams pt
            ON pt.TeamId = dt.TeamID
               AND pt.IsDeleted = 0;

    CREATE UNIQUE INDEX IXU_#related
    ON #related
    (
        PlacementId
      , TopTeamId
    );

    -- Get the placement information and link it to the team.
    DROP TABLE IF EXISTS #pl;

    SELECT
        pl.PlacementId
      , rel.TopTeamId
      , pl.ServicingPlatformId
      , BrokingSegmentId = ISNULL(pl.BrokingSegmentId, 0)
      , BrokingRegionId = ISNULL(pl.BrokingRegionId, 0)
      , BrokingSubSegmentId = ISNULL(pl.BrokingSubSegmentId, 0)
    INTO #pl
    FROM
        dbo.Placement pl
        INNER JOIN #related rel
            ON rel.PlacementId = pl.PlacementId
    WHERE
        pl.IsDeleted = 0
        AND pl.PlacementSystemId IS NOT NULL
        AND pl.DataSourceInstanceId = 50366;

    CREATE UNIQUE INDEX IXU_#pl
    ON #pl
    (
        TopTeamId
      , PlacementId
    )
    INCLUDE
    (
        ServicingPlatformId
      , BrokingSegmentId
      , BrokingRegionId
      , BrokingSubSegmentId
    );

    INSERT INTO
        #SourceforMerge
        (
            PlacementId
          , DataSourceInstanceId
          , TeamId
          , TeamType
        )
    --Scoped Placements 
    SELECT DISTINCT
           PlacementId = pl.PlacementId
         , DataSourceInstanceId = pl.ServicingPlatformId
         , TeamId = CAST(CONCAT(sc.ScopeId, N'|', sc.ScopeItemId, '|', sc.TeamId) AS NVARCHAR(30))
         , TeamType = N'Scope'
    FROM
        dbo.Scope sc
        INNER JOIN #pl pl
            ON sc.TeamId = pl.TopTeamId
               AND ISNULL(sc.BrokingSegmentId, pl.BrokingSegmentId) = pl.BrokingSegmentId
               AND ISNULL(sc.BrokingRegionId, pl.BrokingRegionId) = pl.BrokingRegionId
               AND ISNULL(sc.BrokingSubSegmentId, pl.BrokingSubSegmentId) = pl.BrokingSubSegmentId
    WHERE
        sc.IsDeleted = 0;

    DROP TABLE #pl;
    DROP TABLE #related;
    DROP TABLE #th;

    TRUNCATE TABLE rpt.PlacementSecurity;

    INSERT INTO
        rpt.PlacementSecurity
        (
            TeamType
          , DataSourceInstanceId
          , PlacementId
          , TeamId
        )
    SELECT
        S.TeamType
      , S.DataSourceInstanceId
      , S.PlacementId
      , S.TeamId
    FROM
        #SourceforMerge S;

    SELECT @InsertedCount = @@ROWCOUNT;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

DROP TABLE IF EXISTS #SourceforMerge;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);

RETURN 0;
GO