/*
Lineage
rpt.UserSecurity.TeamId=dbo.Placement.PlacementId
rpt.UserSecurity.TeamId=dbo.Scope.ScopeId
rpt.UserSecurity.TeamId=dbo.Scope.ScopeItemId
rpt.UserSecurity.TeamId=ref.Team.TeamId
rpt.UserSecurity.UserId=dbo.PlacementTeamMember.UserId
rpt.UserSecurity.UserId=dbo.UserScope.UserId
rpt.UserSecurity.RoleId=dbo.PlacementTeamMember.RoleId
rpt.UserSecurity.RoleId=dbo.UserScope.RoleId
*/
CREATE PROCEDURE rpt.Load_rpt_UserSecurity
AS
DECLARE @InsertedCount INT = 0;
DECLARE @UpdatedCount INT = 0;
DECLARE @DeletedCount INT = 0;
DECLARE @RejectedCount INT = 0;
DECLARE @TargetTable VARCHAR(50) = 'rpt.UserSecurity';

DECLARE @Actions TABLE (
    Change VARCHAR(20)
);

DECLARE @SprocName VARCHAR(255);
DECLARE @Action NVARCHAR(255);

SET NOCOUNT ON;

SET @SprocName = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID);

EXEC ADF.StoredProcStartLog @SprocName;

BEGIN TRY
    MERGE rpt.UserSecurity T
    USING (
        --Placement Teams
        SELECT
            TeamType = 'Placement'
          , TeamId = CAST(pl.PlacementId AS NVARCHAR(20))
          , UserId = ptm.UserId
          , RoleId = ptm.RoleId
        FROM
            dbo.PlacementTeamMember ptm
            INNER JOIN dbo.Placement pl
                ON ptm.PlacementId = pl.PlacementId
        WHERE
            ptm.DataSourceInstanceId = 50366
            AND pl.PlacementSystemId IS NOT NULL
            AND pl.DataSourceInstanceId = 50366
            AND pl.IsDeleted = 0
        UNION ALL

        --Scoped Teams
        SELECT
            TeamType = 'Scope'
          , TeamId = CONCAT(sc.ScopeId, '|', sc.ScopeItemId, '|', subTeam.TeamId)
          , UserId = usc.UserId
          , RoleId = usc.RoleId
        FROM
            dbo.UserScope usc
            INNER JOIN dbo.Scope sc
                ON usc.ScopeId = sc.ScopeId
                   AND sc.IsDeleted = 0

            INNER JOIN ref.Team t
                ON t.TeamId = sc.TeamId

            INNER JOIN ref.Team subTeam
                ON subTeam.OrgNode.IsDescendantOf(t.OrgNode) = 1
        WHERE
            usc.DataSourceInstanceId = 50366
            AND usc.IsDeleted = 0
    ) S
    ON T.TeamType = S.TeamType
       AND T.TeamId = S.TeamId
       AND T.UserId = S.UserId
       AND T.RoleId = S.RoleId
    WHEN NOT MATCHED
        THEN INSERT (
                 TeamType
               , TeamId
               , UserId
               , RoleId
             )
             VALUES
                 (
                     S.TeamType
                   , S.TeamId
                   , S.UserId
                   , S.RoleId
                 )
    WHEN NOT MATCHED BY SOURCE
        THEN DELETE
    OUTPUT $ACTION
    INTO @Actions;

    SELECT
        @InsertedCount = SUM(CASE WHEN Change = 'INSERT'
                                      THEN 1
                                  ELSE 0 END
                         )
      , @UpdatedCount = SUM(CASE WHEN Change = 'UPDATE'
                                     THEN 1
                                 ELSE 0 END
                        )
      , @DeletedCount = SUM(CASE WHEN Change = 'DELETE'
                                     THEN 1
                                 ELSE 0 END
                        )
    FROM
        @Actions;
END TRY
BEGIN CATCH
    DECLARE @ErrorMessage NVARCHAR(MAX);

    SET @ErrorMessage = ERROR_MESSAGE();

    EXEC ADF.StoredProcErrorLog
        @SprocName
      , @ErrorMessage;

    SET @RejectedCount = 1;
END CATCH;

SET @Action = N'Merge ' + @TargetTable;

EXEC ADF.StoredProcSetSqlLog
    @SprocName
  , @InsertedCount
  , @UpdatedCount
  , @DeletedCount
  , @RejectedCount
  , @Action
  , NULL;

EXEC ADF.StoredProcEndLog @SprocName;

SELECT
    InsertedCount = ISNULL(@InsertedCount, 0)
  , UpdatedCount = ISNULL(@UpdatedCount, 0)
  , DeletedCount = ISNULL(@DeletedCount, 0)
  , RejectedCount = ISNULL(@RejectedCount, 0);

RETURN 0;