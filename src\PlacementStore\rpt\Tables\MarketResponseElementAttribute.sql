CREATE TABLE rpt.MarketResponseElementAttribute (
    CarrierResponseId                                             INT            NOT NULL
  , MarketResponseId                                              INT            NOT NULL
  , AggregateLimitAmount                                          NUMERIC(38, 2) NULL
  , AggregateLimitAmountUSD                                       NUMERIC(38, 2) NULL
  , AggregateLimitBasis                                           NVARCHAR(255)  NULL
  , AggregateLimitCurrency                                        NCHAR(3)       NULL
  , AggregateLimitInclusionType                                   NVARCHAR(255)  NULL
  , AggregateLimitNotApplicable                                   NVARCHAR(255)  NULL
  , AggregateLimitPercentage                                      NUMERIC(18, 2) NULL
  , AggregateLimitText                                            NVARCHAR(2000) NULL
  , AutoLiabilityEffectiveDate                                    DATE           NULL
  , AutoLiabilityExpirationDate                                   DATE           NULL
  , AutoPhysicalDamageCollisionAmount                             NUMERIC(38, 2) NULL
  , AutoPhysicalDamageCollisionAmountUSD                          NUMERIC(38, 2) NULL
  , AutoPhysicalDamageCollisionBasis                              NVARCHAR(255)  NULL
  , AutoPhysicalDamageCollisionCurrency                           NCHAR(3)       NULL
  , AutoPhysicalDamageCollisionInclusionType                      NVARCHAR(255)  NULL
  , AutoPhysicalDamageCollisionText                               NVARCHAR(2000) NULL
  , AutoPhysicalDamageCollisionValuation                          NVARCHAR(255)  NULL
  , AutoPhysicalDamageComprehensiveAmount                         NUMERIC(38, 2) NULL
  , AutoPhysicalDamageComprehensiveAmountUSD                      NUMERIC(38, 2) NULL
  , AutoPhysicalDamageComprehensiveBasis                          NVARCHAR(255)  NULL
  , AutoPhysicalDamageComprehensiveCurrency                       NCHAR(3)       NULL
  , AutoPhysicalDamageComprehensiveInclusionType                  NVARCHAR(255)  NULL
  , AutoPhysicalDamageComprehensivePercentage                     NUMERIC(18, 2) NULL
  , AutoPhysicalDamageComprehensiveText                           NVARCHAR(2000) NULL
  , AutoPhysicalDamageComprehensiveValuation                      NVARCHAR(255)  NULL
  , BasisofCoverBasis                                             NVARCHAR(255)  NULL
  , BodilyInjuryByAccidentEachAccidentAmount                      NUMERIC(38, 2) NULL
  , BodilyInjuryByAccidentEachAccidentAmountUSD                   NUMERIC(38, 2) NULL
  , BodilyInjuryByAccidentEachAccidentBasis                       NVARCHAR(255)  NULL
  , BodilyInjuryByAccidentEachAccidentCurrency                    NCHAR(3)       NULL
  , BodilyInjuryByAccidentEachAccidentInclusionType               NVARCHAR(255)  NULL
  , BodilyInjuryByAccidentEachAccidentPercentage                  NUMERIC(18, 2) NULL
  , BodilyInjuryByAccidentEachAccidentText                        NVARCHAR(2000) NULL
  , BodilyInjuryByDiseaseEachEmployeeAmount                       NUMERIC(38, 2) NULL
  , BodilyInjuryByDiseaseEachEmployeeAmountUSD                    NUMERIC(38, 2) NULL
  , BodilyInjuryByDiseaseEachEmployeeBasis                        NVARCHAR(255)  NULL
  , BodilyInjuryByDiseaseEachEmployeeCurrency                     NCHAR(3)       NULL
  , BodilyInjuryByDiseaseEachEmployeeInclusionType                NVARCHAR(255)  NULL
  , BodilyInjuryByDiseaseEachEmployeeNumberOfUnits                INT            NULL
  , BodilyInjuryByDiseaseEachEmployeePercentage                   NUMERIC(18, 2) NULL
  , BodilyInjuryByDiseaseEachEmployeeText                         NVARCHAR(2000) NULL
  , BodilyInjuryByDiseasePolicyLimitAmount                        NUMERIC(38, 2) NULL
  , BodilyInjuryByDiseasePolicyLimitAmountUSD                     NUMERIC(38, 2) NULL
  , BodilyInjuryByDiseasePolicyLimitBasis                         NVARCHAR(255)  NULL
  , BodilyInjuryByDiseasePolicyLimitCurrency                      NCHAR(3)       NULL
  , BodilyInjuryByDiseasePolicyLimitInclusionType                 NVARCHAR(255)  NULL
  , BodilyInjuryByDiseasePolicyLimitPercentage                    NUMERIC(18, 2) NULL
  , BodilyInjuryByDiseasePolicyLimitText                          NVARCHAR(2000) NULL
  , ClaimsHandlingStructure                                       NVARCHAR(255)  NULL
  , DamageToPremisesRentedToYouAmount                             NUMERIC(38, 2) NULL
  , DamageToPremisesRentedToYouAmountUSD                          NUMERIC(38, 2) NULL
  , DamageToPremisesRentedToYouBasis                              NVARCHAR(255)  NULL
  , DamageToPremisesRentedToYouCurrency                           NCHAR(3)       NULL
  , DamageToPremisesRentedToYouInclusionType                      NVARCHAR(255)  NULL
  , DamageToPremisesRentedToYouText                               NVARCHAR(2000) NULL
  , DeductibleAmount                                              NUMERIC(38, 2) NULL
  , DeductibleAmountUSD                                           NUMERIC(38, 2) NULL
  , DeductibleBasis                                               NVARCHAR(255)  NULL
  , DeductibleCurrency                                            NCHAR(3)       NULL
  , DeductibleNumberOfUnits                                       INT            NULL
  , DeductiblePercentage                                          NUMERIC(18, 2) NULL
  , DeductibleText                                                NVARCHAR(2000) NULL
  , EachOccurrenceLimitAmount                                     NUMERIC(38, 2) NULL
  , EachOccurrenceLimitAmountUSD                                  NUMERIC(38, 2) NULL
  , EachOccurrenceLimitBasis                                      NVARCHAR(255)  NULL
  , EachOccurrenceLimitCurrency                                   NCHAR(3)       NULL
  , EachOccurrenceLimitInclusionType                              NVARCHAR(255)  NULL
  , EachOccurrenceLimitNumberOfUnits                              INT            NULL
  , EachOccurrenceLimitPercentage                                 NUMERIC(18, 2) NULL
  , EachOccurrenceLimitText                                       NVARCHAR(2000) NULL
  , EmployeeBenefitsLiabilityDeductibleRetentionAmountAmount      NUMERIC(38, 2) NULL
  , EmployeeBenefitsLiabilityDeductibleRetentionAmountAmountUSD   NUMERIC(38, 2) NULL
  , EmployeeBenefitsLiabilityDeductibleRetentionAmountBasis       NVARCHAR(255)  NULL
  , EmployeeBenefitsLiabilityDeductibleRetentionAmountCurrency    NCHAR(3)       NULL
  , EmployeeBenefitsLiabilityDeductibleRetentionAmountText        NVARCHAR(2000) NULL
  , EmployersLiabilityEffectiveDate                               DATE           NULL
  , EmployersLiabilityExpirationDate                              DATE           NULL
  , EstimatedClaimsHandlingCostAmount                             NUMERIC(38, 2) NULL
  , EstimatedClaimsHandlingCostAmountUSD                          NUMERIC(38, 2) NULL
  , EstimatedClaimsHandlingCostCurrency                           NCHAR(3)       NULL
  , EstimatedClaimsHandlingCostText                               NVARCHAR(2000) NULL
  , FormOfCollateral                                              NVARCHAR(255)  NULL
  , GeneralAggregateAmount                                        NUMERIC(38, 2) NULL
  , GeneralAggregateAmountUSD                                     NUMERIC(38, 2) NULL
  , GeneralAggregateBasis                                         NVARCHAR(255)  NULL
  , GeneralAggregateCurrency                                      NCHAR(3)       NULL
  , GeneralAggregateInclusionType                                 NVARCHAR(255)  NULL
  , GeneralAggregateText                                          NVARCHAR(2000) NULL
  , GeneralLiabilityEffectiveDate                                 DATE           NULL
  , GeneralLiabilityExpirationDate                                DATE           NULL
  , LiabilityAmount                                               NUMERIC(38, 2) NULL
  , LiabilityAmountUSD                                            NUMERIC(38, 2) NULL
  , LiabilityBasis                                                NVARCHAR(255)  NULL
  , LiabilityCurrency                                             NCHAR(3)       NULL
  , LiabilityNumberOfUnits                                        INT            NULL
  , LiabilityPercentage                                           NUMERIC(18, 2) NULL
  , LiabilityText                                                 NVARCHAR(2000) NULL
  , MedPayAmount                                                  NUMERIC(38, 2) NULL
  , MedPayAmountUSD                                               NUMERIC(38, 2) NULL
  , MedPayBasis                                                   NVARCHAR(255)  NULL
  , MedPayCurrency                                                NCHAR(3)       NULL
  , MedPayInclusionType                                           NVARCHAR(255)  NULL
  , MedPayText                                                    NVARCHAR(2000) NULL
  , NumberOfExtraHeavyTrucks                                      BIGINT         NULL
  , NumberOfHeavyTrucks                                           BIGINT         NULL
  , NumberOfLightTrucks                                           BIGINT         NULL
  , NumberOfPowerUnits                                            BIGINT         NULL
  , NumberOfPrivatePersonalTransportVehicles                      BIGINT         NULL
  , NumberOfTrailers                                              BIGINT         NULL
  , OtherStatesCoverage                                           NVARCHAR(255)  NULL
  , PersonalAdvertisingInjuryPerPersonOrOrganizationAmount        NUMERIC(38, 2) NULL
  , PersonalAdvertisingInjuryPerPersonOrOrganizationAmountUSD     NUMERIC(38, 2) NULL
  , PersonalAdvertisingInjuryPerPersonOrOrganizationBasis         NVARCHAR(255)  NULL
  , PersonalAdvertisingInjuryPerPersonOrOrganizationCurrency      NCHAR(3)       NULL
  , PersonalAdvertisingInjuryPerPersonOrOrganizationInclusionType NVARCHAR(255)  NULL
  , PersonalAdvertisingInjuryPerPersonOrOrganizationText          NVARCHAR(2000) NULL
  , PolicyEffectiveDate                                           DATE           NULL
  , PolicyEffectiveDateCurrency                                   NCHAR(3)       NULL
  , PolicyEffectiveDateText                                       NVARCHAR(2000) NULL
  , PolicyExpirationDate                                          DATE           NULL
  , PolicyExpirationDateCurrency                                  NCHAR(3)       NULL
  , PolicyExpirationDateText                                      NVARCHAR(2000) NULL
  , PolicyFormReportingForm                                       NVARCHAR(255)  NULL
  , PolicyFormText                                                NVARCHAR(2000) NULL
  , PolicyLimitAmount                                             NUMERIC(38, 2) NULL
  , PolicyLimitAmountUSD                                          NUMERIC(38, 2) NULL
  , PolicyLimitBasis                                              NVARCHAR(255)  NULL
  , PolicyLimitCurrency                                           NCHAR(3)       NULL
  , PolicyLimitInclusionType                                      NVARCHAR(255)  NULL
  , PolicyLimitNumberOfUnits                                      INT            NULL
  , PolicyLimitPercentage                                         NUMERIC(18, 2) NULL
  , PolicyLimitText                                               NVARCHAR(2000) NULL
  , PolicyTrigger                                                 NVARCHAR(255)  NULL
  , PolicyTriggerText                                             NVARCHAR(2000) NULL
  , ProductsCompletedOpsAggregateLimitAmount                      NUMERIC(38, 2) NULL
  , ProductsCompletedOpsAggregateLimitAmountUSD                   NUMERIC(38, 2) NULL
  , ProductsCompletedOpsAggregateLimitBasis                       NVARCHAR(255)  NULL
  , ProductsCompletedOpsAggregateLimitCurrency                    NCHAR(3)       NULL
  , ProductsCompletedOpsAggregateLimitInclusionType               NVARCHAR(255)  NULL
  , ProductsCompletedOpsAggregateLimitPercentage                  NUMERIC(18, 2) NULL
  , ProductsCompletedOpsAggregateLimitText                        NVARCHAR(2000) NULL
  , ProfessionalLiabilityEffectiveDate                            DATE           NULL
  , ProfessionalLiabilityExpirationDate                           DATE           NULL
  , QuoteExpiryDate                                               DATE           NULL
  , QuotedExpirationDate                                          DATE           NULL
  , RevenueAmount                                                 NUMERIC(38, 2) NULL
  , RevenueAmountUSD                                              NUMERIC(38, 2) NULL
  , RevenueCurrency                                               NCHAR(3)       NULL
  , StructureProgramStructure                                     NVARCHAR(255)  NULL
  , SurplusLinesTax                                               NVARCHAR(255)  NULL
  , SurplusLinesTaxAmount                                         NUMERIC(38, 2) NULL
  , SurplusLinesTaxCurrency                                       NCHAR(3)       NULL
  , ThirdPartyAdministrator                                       NVARCHAR(255)  NULL
  , TotalCollateralRequirementAmount                              NUMERIC(38, 2) NULL
  , TotalCollateralRequirementAmountUSD                           NUMERIC(38, 2) NULL
  , TotalCollateralRequirementCurrency                            NCHAR(3)       NULL
  , TotalCollateralRequirementPerStateRequirements                NVARCHAR(255)  NULL
  , TotalCollateralRequirementText                                NVARCHAR(2000) NULL
  , TotalEstimatedPayrollDate                                     DATE           NULL
  , TotalEstimatedPayrollAmount                                   NUMERIC(38, 2) NULL
  , TotalEstimatedPayrollAmountUSD                                NUMERIC(38, 2) NULL
  , TotalEstimatedPayrollCurrency                                 NCHAR(3)       NULL
  , TotalExcessLimitsAmount                                       NUMERIC(38, 2) NULL
  , TotalExcessLimitsCurrency                                     NCHAR(3)       NULL
  , TotalExcessLimitsPercentage                                   NUMERIC(18, 2) NULL
  , TotalExcessLimitsText                                         NVARCHAR(2000) NULL
  , TotalOutstandingActuarialLiabilityCarrierAmount               NUMERIC(38, 2) NULL
  , TotalOutstandingActuarialLiabilityCarrierAmountUSD            NUMERIC(38, 2) NULL
  , TotalOutstandingActuarialLiabilityCarrierCurrency             NCHAR(3)       NULL
  , TotalOutstandingActuarialLiabilityCarrierText                 NVARCHAR(2000) NULL
  , TotalValueAmount                                              NUMERIC(38, 2) NULL
  , TotalValueAmountUSD                                           NUMERIC(38, 2) NULL
  , TotalValueCurrency                                            NCHAR(3)       NULL
  , TreatmentOfAllocatedLossAdjustmentExpenses                    NVARCHAR(255)  NULL
  , TerrorismRiskInsuranceActPremiumAmount                        NUMERIC(38, 2) NULL
  , TerrorismRiskInsuranceActPremiumAmountUSD                     NUMERIC(38, 2) NULL
  , TerrorismRiskInsuranceActPremiumCurrency                      NCHAR(3)       NULL
  , TerrorismRiskInsuranceActPremiumIncludedExcluded              NVARCHAR(255)  NULL
  , WorkersCompensationAmount                                     NUMERIC(38, 2) NULL
  , WorkersCompensationAmountUSD                                  NUMERIC(38, 2) NULL
  , WorkersCompensationBasis                                      NVARCHAR(255)  NULL
  , WorkersCompensationCurrency                                   NCHAR(3)       NULL
  , WorkersCompensationNumberOfUnits                              INT            NULL
  , WorkersCompensationStatutoryRejected                          NVARCHAR(255)  NULL
  , WorkersCompensationText                                       NVARCHAR(2000) NULL
  , ETLCreatedDate                                                DATETIME2(7)   NOT NULL
        DEFAULT GETUTCDATE()
        CONSTRAINT PK_rpt_MarketResponseElementAttribute
        PRIMARY KEY
        (
            CarrierResponseId
          , MarketResponseId
        )
);