CREATE TABLE rpt.PlacementPrimaryRole (
    PlacementId                      BIGINT        NOT NULL
  , Client                           NVARCHAR(255) NULL
  , Broker                           NVARCHAR(255) NULL
  , AccountExecutive                 NVARCHAR(255) NULL
  , Insured                          NVARCHAR(255) NULL
  , StrategicBrokingAdvisor          NVARCHAR(255) NULL
  , ClientManager                    NVARCHAR(255) NULL
  , SeniorClientManager              NVARCHAR(255) NULL
  , PlacementCreator                 NVARCHAR(255) NULL
  , AccountManager                   NVARCHAR(255) NULL
  , AccountTechnician                NVARCHAR(255) NULL
  , Analyst                          NVARCHAR(255) NULL
  , AssistantClientServiceSpecialist NVARCHAR(255) NULL
  , ClientAdvocate                   NVARCHAR(255) NULL
  , Compliance                       NVARCHAR(255) NULL
  , GlobalClientAdvocate             NVARCHAR(255) NULL
  , Producer                         NVARCHAR(255) NULL
  , ProductGroupLeader               NVARCHAR(255) NULL
  , SupportBroker                    NVARCHAR(255) NULL
  , UmbrellaAndExcessBroker          NVARCHAR(255) NULL
  , BLU                              NVARCHAR(255) NULL
  , CONSTRAINT FK_rpt_PlacementPrimaryRole_dbo_Placement
        FOREIGN KEY
        (
            PlacementId
        )
        REFERENCES dbo.Placement
        (
            PlacementId
        )
);
GO

CREATE INDEX IX_rpt_PlacementPrimaryRole_PlacementId
ON rpt.PlacementPrimaryRole
(
    PlacementId
);
