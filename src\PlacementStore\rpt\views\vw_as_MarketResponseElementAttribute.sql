/*
Lineage
CarrierResponseId=rpt.MarketResponseElementAttribute.CarrierResponseId
AggregateLimitAmount=rpt.MarketResponseElementAttribute.AggregateLimitAmount
AggregateLimitAmountUSD=rpt.MarketResponseElementAttribute.AggregateLimitAmountUSD
AggregateLimitBasis=rpt.MarketResponseElementAttribute.AggregateLimitBasis
AggregateLimitCurrency=rpt.MarketResponseElementAttribute.AggregateLimitCurrency
AggregateLimitInclusionType=rpt.MarketResponseElementAttribute.AggregateLimitInclusionType
AggregateLimitNotApplicable=rpt.MarketResponseElementAttribute.AggregateLimitNotApplicable
AggregateLimitPercentage=rpt.MarketResponseElementAttribute.AggregateLimitPercentage
AggregateLimitText=rpt.MarketResponseElementAttribute.AggregateLimitText
AutoLiabilityEffectiveDate=rpt.MarketResponseElementAttribute.AutoLiabilityEffectiveDate
AutoLiabilityExpirationDate=rpt.MarketResponseElementAttribute.AutoLiabilityExpirationDate
AutoPhysicalDamageCollisionAmount=rpt.MarketResponseElementAttribute.AutoPhysicalDamageCollisionAmount
AutoPhysicalDamageCollisionAmountUSD=rpt.MarketResponseElementAttribute.AutoPhysicalDamageCollisionAmountUSD
AutoPhysicalDamageCollisionBasis=rpt.MarketResponseElementAttribute.AutoPhysicalDamageCollisionBasis
AutoPhysicalDamageCollisionCurrency=rpt.MarketResponseElementAttribute.AutoPhysicalDamageCollisionCurrency
AutoPhysicalDamageCollisionInclusionType=rpt.MarketResponseElementAttribute.AutoPhysicalDamageCollisionInclusionType
AutoPhysicalDamageCollisionText=rpt.MarketResponseElementAttribute.AutoPhysicalDamageCollisionText
AutoPhysicalDamageCollisionValuation=rpt.MarketResponseElementAttribute.AutoPhysicalDamageCollisionValuation
AutoPhysicalDamageComprehensiveAmount=rpt.MarketResponseElementAttribute.AutoPhysicalDamageComprehensiveAmount
AutoPhysicalDamageComprehensiveAmountUSD=rpt.MarketResponseElementAttribute.AutoPhysicalDamageComprehensiveAmountUSD
AutoPhysicalDamageComprehensiveBasis=rpt.MarketResponseElementAttribute.AutoPhysicalDamageComprehensiveBasis
AutoPhysicalDamageComprehensiveCurrency=rpt.MarketResponseElementAttribute.AutoPhysicalDamageComprehensiveCurrency
AutoPhysicalDamageComprehensiveInclusionType=rpt.MarketResponseElementAttribute.AutoPhysicalDamageComprehensiveInclusionType
AutoPhysicalDamageComprehensivePercentage=rpt.MarketResponseElementAttribute.AutoPhysicalDamageComprehensivePercentage
AutoPhysicalDamageComprehensiveText=rpt.MarketResponseElementAttribute.AutoPhysicalDamageComprehensiveText
AutoPhysicalDamageComprehensiveValuation=rpt.MarketResponseElementAttribute.AutoPhysicalDamageComprehensiveValuation
BasisofCoverBasis=rpt.MarketResponseElementAttribute.BasisofCoverBasis
BodilyInjuryByAccidentEachAccidentAmount=rpt.MarketResponseElementAttribute.BodilyInjuryByAccidentEachAccidentAmount
BodilyInjuryByAccidentEachAccidentAmountUSD=rpt.MarketResponseElementAttribute.BodilyInjuryByAccidentEachAccidentAmountUSD
BodilyInjuryByAccidentEachAccidentBasis=rpt.MarketResponseElementAttribute.BodilyInjuryByAccidentEachAccidentBasis
BodilyInjuryByAccidentEachAccidentCurrency=rpt.MarketResponseElementAttribute.BodilyInjuryByAccidentEachAccidentCurrency
BodilyInjuryByAccidentEachAccidentInclusionType=rpt.MarketResponseElementAttribute.BodilyInjuryByAccidentEachAccidentInclusionType
BodilyInjuryByAccidentEachAccidentPercentage=rpt.MarketResponseElementAttribute.BodilyInjuryByAccidentEachAccidentPercentage
BodilyInjuryByAccidentEachAccidentText=rpt.MarketResponseElementAttribute.BodilyInjuryByAccidentEachAccidentText
BodilyInjuryByDiseaseEachEmployeeAmount=rpt.MarketResponseElementAttribute.BodilyInjuryByDiseaseEachEmployeeAmount
BodilyInjuryByDiseaseEachEmployeeAmountUSD=rpt.MarketResponseElementAttribute.BodilyInjuryByDiseaseEachEmployeeAmountUSD
BodilyInjuryByDiseaseEachEmployeeBasis=rpt.MarketResponseElementAttribute.BodilyInjuryByDiseaseEachEmployeeBasis
BodilyInjuryByDiseaseEachEmployeeCurrency=rpt.MarketResponseElementAttribute.BodilyInjuryByDiseaseEachEmployeeCurrency
BodilyInjuryByDiseaseEachEmployeeInclusionType=rpt.MarketResponseElementAttribute.BodilyInjuryByDiseaseEachEmployeeInclusionType
BodilyInjuryByDiseaseEachEmployeeNumberOfUnits=rpt.MarketResponseElementAttribute.BodilyInjuryByDiseaseEachEmployeeNumberOfUnits
BodilyInjuryByDiseaseEachEmployeePercentage=rpt.MarketResponseElementAttribute.BodilyInjuryByDiseaseEachEmployeePercentage
BodilyInjuryByDiseaseEachEmployeeText=rpt.MarketResponseElementAttribute.BodilyInjuryByDiseaseEachEmployeeText
BodilyInjuryByDiseasePolicyLimitAmount=rpt.MarketResponseElementAttribute.BodilyInjuryByDiseasePolicyLimitAmount
BodilyInjuryByDiseasePolicyLimitAmountUSD=rpt.MarketResponseElementAttribute.BodilyInjuryByDiseasePolicyLimitAmountUSD
BodilyInjuryByDiseasePolicyLimitBasis=rpt.MarketResponseElementAttribute.BodilyInjuryByDiseasePolicyLimitBasis
BodilyInjuryByDiseasePolicyLimitCurrency=rpt.MarketResponseElementAttribute.BodilyInjuryByDiseasePolicyLimitCurrency
BodilyInjuryByDiseasePolicyLimitInclusionType=rpt.MarketResponseElementAttribute.BodilyInjuryByDiseasePolicyLimitInclusionType
BodilyInjuryByDiseasePolicyLimitPercentage=rpt.MarketResponseElementAttribute.BodilyInjuryByDiseasePolicyLimitPercentage
BodilyInjuryByDiseasePolicyLimitText=rpt.MarketResponseElementAttribute.BodilyInjuryByDiseasePolicyLimitText
ClaimsHandlingStructure=rpt.MarketResponseElementAttribute.ClaimsHandlingStructure
DamageToPremisesRentedToYouAmount=rpt.MarketResponseElementAttribute.DamageToPremisesRentedToYouAmount
DamageToPremisesRentedToYouAmountUSD=rpt.MarketResponseElementAttribute.DamageToPremisesRentedToYouAmountUSD
DamageToPremisesRentedToYouBasis=rpt.MarketResponseElementAttribute.DamageToPremisesRentedToYouBasis
DamageToPremisesRentedToYouCurrency=rpt.MarketResponseElementAttribute.DamageToPremisesRentedToYouCurrency
DamageToPremisesRentedToYouInclusionType=rpt.MarketResponseElementAttribute.DamageToPremisesRentedToYouInclusionType
DamageToPremisesRentedToYouText=rpt.MarketResponseElementAttribute.DamageToPremisesRentedToYouText
DeductibleAmount=rpt.MarketResponseElementAttribute.DeductibleAmount
DeductibleAmountUSD=rpt.MarketResponseElementAttribute.DeductibleAmountUSD
DeductibleBasis=rpt.MarketResponseElementAttribute.DeductibleBasis
DeductibleCurrency=rpt.MarketResponseElementAttribute.DeductibleCurrency
DeductibleNumberOfUnits=rpt.MarketResponseElementAttribute.DeductibleNumberOfUnits
DeductiblePercentage=rpt.MarketResponseElementAttribute.DeductiblePercentage
DeductibleText=rpt.MarketResponseElementAttribute.DeductibleText
EachOccurrenceLimitAmount=rpt.MarketResponseElementAttribute.EachOccurrenceLimitAmount
EachOccurrenceLimitAmountUSD=rpt.MarketResponseElementAttribute.EachOccurrenceLimitAmountUSD
EachOccurrenceLimitBasis=rpt.MarketResponseElementAttribute.EachOccurrenceLimitBasis
EachOccurrenceLimitCurrency=rpt.MarketResponseElementAttribute.EachOccurrenceLimitCurrency
EachOccurrenceLimitInclusionType=rpt.MarketResponseElementAttribute.EachOccurrenceLimitInclusionType
EachOccurrenceLimitNumberOfUnits=rpt.MarketResponseElementAttribute.EachOccurrenceLimitNumberOfUnits
EachOccurrenceLimitPercentage=rpt.MarketResponseElementAttribute.EachOccurrenceLimitPercentage
EachOccurrenceLimitText=rpt.MarketResponseElementAttribute.EachOccurrenceLimitText
EmployeeBenefitsLiabilityDeductibleRetentionAmountAmount=rpt.MarketResponseElementAttribute.EmployeeBenefitsLiabilityDeductibleRetentionAmountAmount
EmployeeBenefitsLiabilityDeductibleRetentionAmountAmountUSD=rpt.MarketResponseElementAttribute.EmployeeBenefitsLiabilityDeductibleRetentionAmountAmountUSD
EmployeeBenefitsLiabilityDeductibleRetentionAmountBasis=rpt.MarketResponseElementAttribute.EmployeeBenefitsLiabilityDeductibleRetentionAmountBasis
EmployeeBenefitsLiabilityDeductibleRetentionAmountCurrency=rpt.MarketResponseElementAttribute.EmployeeBenefitsLiabilityDeductibleRetentionAmountCurrency
EmployeeBenefitsLiabilityDeductibleRetentionAmountText=rpt.MarketResponseElementAttribute.EmployeeBenefitsLiabilityDeductibleRetentionAmountText
EmployersLiabilityEffectiveDate=rpt.MarketResponseElementAttribute.EmployersLiabilityEffectiveDate
EmployersLiabilityExpirationDate=rpt.MarketResponseElementAttribute.EmployersLiabilityExpirationDate
EstimatedClaimsHandlingCostAmount=rpt.MarketResponseElementAttribute.EstimatedClaimsHandlingCostAmount
EstimatedClaimsHandlingCostAmountUSD=rpt.MarketResponseElementAttribute.EstimatedClaimsHandlingCostAmountUSD
EstimatedClaimsHandlingCostCurrency=rpt.MarketResponseElementAttribute.EstimatedClaimsHandlingCostCurrency
EstimatedClaimsHandlingCostText=rpt.MarketResponseElementAttribute.EstimatedClaimsHandlingCostText
FormOfCollateral=rpt.MarketResponseElementAttribute.FormOfCollateral
GeneralAggregateAmount=rpt.MarketResponseElementAttribute.GeneralAggregateAmount
GeneralAggregateAmountUSD=rpt.MarketResponseElementAttribute.GeneralAggregateAmountUSD
GeneralAggregateBasis=rpt.MarketResponseElementAttribute.GeneralAggregateBasis
GeneralAggregateCurrency=rpt.MarketResponseElementAttribute.GeneralAggregateCurrency
GeneralAggregateInclusionType=rpt.MarketResponseElementAttribute.GeneralAggregateInclusionType
GeneralAggregateText=rpt.MarketResponseElementAttribute.GeneralAggregateText
GeneralLiabilityEffectiveDate=rpt.MarketResponseElementAttribute.GeneralLiabilityEffectiveDate
GeneralLiabilityExpirationDate=rpt.MarketResponseElementAttribute.GeneralLiabilityExpirationDate
LiabilityAmount=rpt.MarketResponseElementAttribute.LiabilityAmount
LiabilityAmountUSD=rpt.MarketResponseElementAttribute.LiabilityAmountUSD
LiabilityBasis=rpt.MarketResponseElementAttribute.LiabilityBasis
LiabilityCurrency=rpt.MarketResponseElementAttribute.LiabilityCurrency
LiabilityNumberOfUnits=rpt.MarketResponseElementAttribute.LiabilityNumberOfUnits
LiabilityPercentage=rpt.MarketResponseElementAttribute.LiabilityPercentage
LiabilityText=rpt.MarketResponseElementAttribute.LiabilityText
MedPayAmount=rpt.MarketResponseElementAttribute.MedPayAmount
MedPayAmountUSD=rpt.MarketResponseElementAttribute.MedPayAmountUSD
MedPayBasis=rpt.MarketResponseElementAttribute.MedPayBasis
MedPayCurrency=rpt.MarketResponseElementAttribute.MedPayCurrency
MedPayInclusionType=rpt.MarketResponseElementAttribute.MedPayInclusionType
MedPayText=rpt.MarketResponseElementAttribute.MedPayText
NumberOfExtraHeavyTrucks=rpt.MarketResponseElementAttribute.NumberOfExtraHeavyTrucks
NumberOfHeavyTrucks=rpt.MarketResponseElementAttribute.NumberOfHeavyTrucks
NumberOfLightTrucks=rpt.MarketResponseElementAttribute.NumberOfLightTrucks
NumberOfPowerUnits=rpt.MarketResponseElementAttribute.NumberOfPowerUnits
NumberOfPrivatePersonalTransportVehicles=rpt.MarketResponseElementAttribute.NumberOfPrivatePersonalTransportVehicles
NumberOfTrailers=rpt.MarketResponseElementAttribute.NumberOfTrailers
OtherStatesCoverage=rpt.MarketResponseElementAttribute.OtherStatesCoverage
PersonalAdvertisingInjuryPerPersonOrOrganizationAmount=rpt.MarketResponseElementAttribute.PersonalAdvertisingInjuryPerPersonOrOrganizationAmount
PersonalAdvertisingInjuryPerPersonOrOrganizationAmountUSD=rpt.MarketResponseElementAttribute.PersonalAdvertisingInjuryPerPersonOrOrganizationAmountUSD
PersonalAdvertisingInjuryPerPersonOrOrganizationBasis=rpt.MarketResponseElementAttribute.PersonalAdvertisingInjuryPerPersonOrOrganizationBasis
PersonalAdvertisingInjuryPerPersonOrOrganizationCurrency=rpt.MarketResponseElementAttribute.PersonalAdvertisingInjuryPerPersonOrOrganizationCurrency
PersonalAdvertisingInjuryPerPersonOrOrganizationInclusionType=rpt.MarketResponseElementAttribute.PersonalAdvertisingInjuryPerPersonOrOrganizationInclusionType
PersonalAdvertisingInjuryPerPersonOrOrganizationText=rpt.MarketResponseElementAttribute.PersonalAdvertisingInjuryPerPersonOrOrganizationText
PolicyEffectiveDate=rpt.MarketResponseElementAttribute.PolicyEffectiveDate
PolicyEffectiveDateCurrency=rpt.MarketResponseElementAttribute.PolicyEffectiveDateCurrency
PolicyEffectiveDateText=rpt.MarketResponseElementAttribute.PolicyEffectiveDateText
PolicyExpirationDate=rpt.MarketResponseElementAttribute.PolicyExpirationDate
PolicyExpirationDateCurrency=rpt.MarketResponseElementAttribute.PolicyExpirationDateCurrency
PolicyExpirationDateText=rpt.MarketResponseElementAttribute.PolicyExpirationDateText
PolicyFormReportingForm=rpt.MarketResponseElementAttribute.PolicyFormReportingForm
PolicyFormText=rpt.MarketResponseElementAttribute.PolicyFormText
PolicyLimitAmount=rpt.MarketResponseElementAttribute.PolicyLimitAmount
PolicyLimitAmountUSD=rpt.MarketResponseElementAttribute.PolicyLimitAmountUSD
PolicyLimitBasis=rpt.MarketResponseElementAttribute.PolicyLimitBasis
PolicyLimitCurrency=rpt.MarketResponseElementAttribute.PolicyLimitCurrency
PolicyLimitInclusionType=rpt.MarketResponseElementAttribute.PolicyLimitInclusionType
PolicyLimitNumberOfUnits=rpt.MarketResponseElementAttribute.PolicyLimitNumberOfUnits
PolicyLimitPercentage=rpt.MarketResponseElementAttribute.PolicyLimitPercentage
PolicyLimitText=rpt.MarketResponseElementAttribute.PolicyLimitText
PolicyTrigger=rpt.MarketResponseElementAttribute.PolicyTrigger
PolicyTriggerText=rpt.MarketResponseElementAttribute.PolicyTriggerText
ProductsCompletedOpsAggregateLimitAmount=rpt.MarketResponseElementAttribute.ProductsCompletedOpsAggregateLimitAmount
ProductsCompletedOpsAggregateLimitAmountUSD=rpt.MarketResponseElementAttribute.ProductsCompletedOpsAggregateLimitAmountUSD
ProductsCompletedOpsAggregateLimitBasis=rpt.MarketResponseElementAttribute.ProductsCompletedOpsAggregateLimitBasis
ProductsCompletedOpsAggregateLimitCurrency=rpt.MarketResponseElementAttribute.ProductsCompletedOpsAggregateLimitCurrency
ProductsCompletedOpsAggregateLimitInclusionType=rpt.MarketResponseElementAttribute.ProductsCompletedOpsAggregateLimitInclusionType
ProductsCompletedOpsAggregateLimitPercentage=rpt.MarketResponseElementAttribute.ProductsCompletedOpsAggregateLimitPercentage
ProductsCompletedOpsAggregateLimitText=rpt.MarketResponseElementAttribute.ProductsCompletedOpsAggregateLimitText
ProfessionalLiabilityEffectiveDate=rpt.MarketResponseElementAttribute.ProfessionalLiabilityEffectiveDate
ProfessionalLiabilityExpirationDate=rpt.MarketResponseElementAttribute.ProfessionalLiabilityExpirationDate
QuoteExpiryDate=rpt.MarketResponseElementAttribute.QuoteExpiryDate
QuotedExpirationDate=rpt.MarketResponseElementAttribute.QuotedExpirationDate
RevenueAmount=rpt.MarketResponseElementAttribute.RevenueAmount
RevenueAmountUSD=rpt.MarketResponseElementAttribute.RevenueAmountUSD
RevenueCurrency=rpt.MarketResponseElementAttribute.RevenueCurrency
StructureProgramStructure=rpt.MarketResponseElementAttribute.StructureProgramStructure
SurplusLinesTax=rpt.MarketResponseElementAttribute.SurplusLinesTax
SurplusLinesTaxAmount=rpt.MarketResponseElementAttribute.SurplusLinesTaxAmount
SurplusLinesTaxCurrency=rpt.MarketResponseElementAttribute.SurplusLinesTaxCurrency
ThirdPartyAdministrator=rpt.MarketResponseElementAttribute.ThirdPartyAdministrator
TotalCollateralRequirementAmount=rpt.MarketResponseElementAttribute.TotalCollateralRequirementAmount
TotalCollateralRequirementAmountUSD=rpt.MarketResponseElementAttribute.TotalCollateralRequirementAmountUSD
TotalCollateralRequirementCurrency=rpt.MarketResponseElementAttribute.TotalCollateralRequirementCurrency
TotalCollateralRequirementPerStateRequirements=rpt.MarketResponseElementAttribute.TotalCollateralRequirementPerStateRequirements
TotalCollateralRequirementText=rpt.MarketResponseElementAttribute.TotalCollateralRequirementText
TotalEstimatedPayrollDate=rpt.MarketResponseElementAttribute.TotalEstimatedPayrollDate
TotalEstimatedPayrollAmount=rpt.MarketResponseElementAttribute.TotalEstimatedPayrollAmount
TotalEstimatedPayrollAmountUSD=rpt.MarketResponseElementAttribute.TotalEstimatedPayrollAmountUSD
TotalEstimatedPayrollCurrency=rpt.MarketResponseElementAttribute.TotalEstimatedPayrollCurrency
TotalExcessLimitsAmount=rpt.MarketResponseElementAttribute.TotalExcessLimitsAmount
TotalExcessLimitsCurrency=rpt.MarketResponseElementAttribute.TotalExcessLimitsCurrency
TotalExcessLimitsPercentage=rpt.MarketResponseElementAttribute.TotalExcessLimitsPercentage
TotalExcessLimitsText=rpt.MarketResponseElementAttribute.TotalExcessLimitsText
TotalOutstandingActuarialLiabilityCarrierAmount=rpt.MarketResponseElementAttribute.TotalOutstandingActuarialLiabilityCarrierAmount
TotalOutstandingActuarialLiabilityCarrierAmountUSD=rpt.MarketResponseElementAttribute.TotalOutstandingActuarialLiabilityCarrierAmountUSD
TotalOutstandingActuarialLiabilityCarrierCurrency=rpt.MarketResponseElementAttribute.TotalOutstandingActuarialLiabilityCarrierCurrency
TotalOutstandingActuarialLiabilityCarrierText=rpt.MarketResponseElementAttribute.TotalOutstandingActuarialLiabilityCarrierText
TotalValueAmount=rpt.MarketResponseElementAttribute.TotalValueAmount
TotalValueAmountUSD=rpt.MarketResponseElementAttribute.TotalValueAmountUSD
TotalValueCurrency=rpt.MarketResponseElementAttribute.TotalValueCurrency
TreatmentOfAllocatedLossAdjustmentExpenses=rpt.MarketResponseElementAttribute.TreatmentOfAllocatedLossAdjustmentExpenses
TerrorismRiskInsuranceActPremiumAmount=rpt.MarketResponseElementAttribute.TerrorismRiskInsuranceActPremiumAmount
TerrorismRiskInsuranceActPremiumAmountUSD=rpt.MarketResponseElementAttribute.TerrorismRiskInsuranceActPremiumAmountUSD
TerrorismRiskInsuranceActPremiumCurrency=rpt.MarketResponseElementAttribute.TerrorismRiskInsuranceActPremiumCurrency
TerrorismRiskInsuranceActPremiumIncludedExcluded=rpt.MarketResponseElementAttribute.TerrorismRiskInsuranceActPremiumIncludedExcluded
WorkersCompensationAmount=rpt.MarketResponseElementAttribute.WorkersCompensationAmount
WorkersCompensationAmountUSD=rpt.MarketResponseElementAttribute.WorkersCompensationAmountUSD
WorkersCompensationBasis=rpt.MarketResponseElementAttribute.WorkersCompensationBasis
WorkersCompensationCurrency=rpt.MarketResponseElementAttribute.WorkersCompensationCurrency
WorkersCompensationNumberOfUnits=rpt.MarketResponseElementAttribute.WorkersCompensationNumberOfUnits
WorkersCompensationStatutoryRejected=rpt.MarketResponseElementAttribute.WorkersCompensationStatutoryRejected
WorkersCompensationText=rpt.MarketResponseElementAttribute.WorkersCompensationText
*/

CREATE VIEW rpt.vw_as_MarketResponseElementAttribute
AS
SELECT
    CarrierResponseId
  , AggregateLimitAmount
  , AggregateLimitAmountUSD
  , AggregateLimitBasis
  , AggregateLimitCurrency
  , AggregateLimitInclusionType
  , AggregateLimitNotApplicable
  , AggregateLimitPercentage
  , AggregateLimitText
  , AutoLiabilityEffectiveDate
  , AutoLiabilityExpirationDate
  , AutoPhysicalDamageCollisionAmount
  , AutoPhysicalDamageCollisionAmountUSD
  , AutoPhysicalDamageCollisionBasis
  , AutoPhysicalDamageCollisionCurrency
  , AutoPhysicalDamageCollisionInclusionType
  , AutoPhysicalDamageCollisionText
  , AutoPhysicalDamageCollisionValuation
  , AutoPhysicalDamageComprehensiveAmount
  , AutoPhysicalDamageComprehensiveAmountUSD
  , AutoPhysicalDamageComprehensiveBasis
  , AutoPhysicalDamageComprehensiveCurrency
  , AutoPhysicalDamageComprehensiveInclusionType
  , AutoPhysicalDamageComprehensivePercentage
  , AutoPhysicalDamageComprehensiveText
  , AutoPhysicalDamageComprehensiveValuation
  , BasisofCoverBasis
  , BodilyInjuryByAccidentEachAccidentAmount
  , BodilyInjuryByAccidentEachAccidentAmountUSD
  , BodilyInjuryByAccidentEachAccidentBasis
  , BodilyInjuryByAccidentEachAccidentCurrency
  , BodilyInjuryByAccidentEachAccidentInclusionType
  , BodilyInjuryByAccidentEachAccidentPercentage
  , BodilyInjuryByAccidentEachAccidentText
  , BodilyInjuryByDiseaseEachEmployeeAmount
  , BodilyInjuryByDiseaseEachEmployeeAmountUSD
  , BodilyInjuryByDiseaseEachEmployeeBasis
  , BodilyInjuryByDiseaseEachEmployeeCurrency
  , BodilyInjuryByDiseaseEachEmployeeInclusionType
  , BodilyInjuryByDiseaseEachEmployeeNumberOfUnits
  , BodilyInjuryByDiseaseEachEmployeePercentage
  , BodilyInjuryByDiseaseEachEmployeeText
  , BodilyInjuryByDiseasePolicyLimitAmount
  , BodilyInjuryByDiseasePolicyLimitAmountUSD
  , BodilyInjuryByDiseasePolicyLimitBasis
  , BodilyInjuryByDiseasePolicyLimitCurrency
  , BodilyInjuryByDiseasePolicyLimitInclusionType
  , BodilyInjuryByDiseasePolicyLimitPercentage
  , BodilyInjuryByDiseasePolicyLimitText
  , ClaimsHandlingStructure
  , DamageToPremisesRentedToYouAmount
  , DamageToPremisesRentedToYouAmountUSD
  , DamageToPremisesRentedToYouBasis
  , DamageToPremisesRentedToYouCurrency
  , DamageToPremisesRentedToYouInclusionType
  , DamageToPremisesRentedToYouText
  , DeductibleAmount
  , DeductibleAmountUSD
  , DeductibleBasis
  , DeductibleCurrency
  , DeductibleNumberOfUnits
  , DeductiblePercentage
  , DeductibleText
  , EachOccurrenceLimitAmount
  , EachOccurrenceLimitAmountUSD
  , EachOccurrenceLimitBasis
  , EachOccurrenceLimitCurrency
  , EachOccurrenceLimitInclusionType
  , EachOccurrenceLimitNumberOfUnits
  , EachOccurrenceLimitPercentage
  , EachOccurrenceLimitText
  , EmployeeBenefitsLiabilityDeductibleRetentionAmountAmount
  , EmployeeBenefitsLiabilityDeductibleRetentionAmountAmountUSD
  , EmployeeBenefitsLiabilityDeductibleRetentionAmountBasis
  , EmployeeBenefitsLiabilityDeductibleRetentionAmountCurrency
  , EmployeeBenefitsLiabilityDeductibleRetentionAmountText
  , EmployersLiabilityEffectiveDate
  , EmployersLiabilityExpirationDate
  , EstimatedClaimsHandlingCostAmount
  , EstimatedClaimsHandlingCostAmountUSD
  , EstimatedClaimsHandlingCostCurrency
  , EstimatedClaimsHandlingCostText
  , FormOfCollateral
  , GeneralAggregateAmount
  , GeneralAggregateAmountUSD
  , GeneralAggregateBasis
  , GeneralAggregateCurrency
  , GeneralAggregateInclusionType
  , GeneralAggregateText
  , GeneralLiabilityEffectiveDate
  , GeneralLiabilityExpirationDate
  , LiabilityAmount
  , LiabilityAmountUSD
  , LiabilityBasis
  , LiabilityCurrency
  , LiabilityNumberOfUnits
  , LiabilityPercentage
  , LiabilityText
  , MedPayAmount
  , MedPayAmountUSD
  , MedPayBasis
  , MedPayCurrency
  , MedPayInclusionType
  , MedPayText
  , NumberOfExtraHeavyTrucks
  , NumberOfHeavyTrucks
  , NumberOfLightTrucks
  , NumberOfPowerUnits
  , NumberOfPrivatePersonalTransportVehicles
  , NumberOfTrailers
  , OtherStatesCoverage
  , PersonalAdvertisingInjuryPerPersonOrOrganizationAmount
  , PersonalAdvertisingInjuryPerPersonOrOrganizationAmountUSD
  , PersonalAdvertisingInjuryPerPersonOrOrganizationBasis
  , PersonalAdvertisingInjuryPerPersonOrOrganizationCurrency
  , PersonalAdvertisingInjuryPerPersonOrOrganizationInclusionType
  , PersonalAdvertisingInjuryPerPersonOrOrganizationText
  , PolicyEffectiveDate
  , PolicyEffectiveDateCurrency
  , PolicyEffectiveDateText
  , PolicyExpirationDate
  , PolicyExpirationDateCurrency
  , PolicyExpirationDateText
  , PolicyFormReportingForm
  , PolicyFormText
  , PolicyLimitAmount
  , PolicyLimitAmountUSD
  , PolicyLimitBasis
  , PolicyLimitCurrency
  , PolicyLimitInclusionType
  , PolicyLimitNumberOfUnits
  , PolicyLimitPercentage
  , PolicyLimitText
  , PolicyTrigger
  , PolicyTriggerText
  , ProductsCompletedOpsAggregateLimitAmount
  , ProductsCompletedOpsAggregateLimitAmountUSD
  , ProductsCompletedOpsAggregateLimitBasis
  , ProductsCompletedOpsAggregateLimitCurrency
  , ProductsCompletedOpsAggregateLimitInclusionType
  , ProductsCompletedOpsAggregateLimitPercentage
  , ProductsCompletedOpsAggregateLimitText
  , ProfessionalLiabilityEffectiveDate
  , ProfessionalLiabilityExpirationDate
  , QuoteExpiryDate
  , QuotedExpirationDate
  , RevenueAmount
  , RevenueAmountUSD
  , RevenueCurrency
  , StructureProgramStructure
  , SurplusLinesTax
  , SurplusLinesTaxAmount
  , SurplusLinesTaxCurrency
  , ThirdPartyAdministrator
  , TotalCollateralRequirementAmount
  , TotalCollateralRequirementAmountUSD
  , TotalCollateralRequirementCurrency
  , TotalCollateralRequirementPerStateRequirements
  , TotalCollateralRequirementText
  , TotalEstimatedPayrollDate
  , TotalEstimatedPayrollAmount
  , TotalEstimatedPayrollAmountUSD
  , TotalEstimatedPayrollCurrency
  , TotalExcessLimitsAmount
  , TotalExcessLimitsCurrency
  , TotalExcessLimitsPercentage
  , TotalExcessLimitsText
  , TotalOutstandingActuarialLiabilityCarrierAmount
  , TotalOutstandingActuarialLiabilityCarrierAmountUSD
  , TotalOutstandingActuarialLiabilityCarrierCurrency
  , TotalOutstandingActuarialLiabilityCarrierText
  , TotalValueAmount
  , TotalValueAmountUSD
  , TotalValueCurrency
  , TreatmentOfAllocatedLossAdjustmentExpenses
  , TerrorismRiskInsuranceActPremiumAmount
  , TerrorismRiskInsuranceActPremiumAmountUSD
  , TerrorismRiskInsuranceActPremiumCurrency
  , TerrorismRiskInsuranceActPremiumIncludedExcluded
  , WorkersCompensationAmount
  , WorkersCompensationAmountUSD
  , WorkersCompensationBasis
  , WorkersCompensationCurrency
  , WorkersCompensationNumberOfUnits
  , WorkersCompensationStatutoryRejected
  , WorkersCompensationText
FROM
    rpt.MarketResponseElementAttribute;
GO

--Add View Level Detail

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'Provides information from Market Responses in the Broking Platform Trading Centre'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute';
GO

--Add Column Level Detail
EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The unique Id for the carrier response from Placement Store'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'CarrierResponseId';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The aggregate limit amount'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'AggregateLimitAmount';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The aggregate limit amount in US Dollars'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'AggregateLimitAmountUSD';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The aggregate limit basis'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'AggregateLimitBasis';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The aggregate limit currency'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'AggregateLimitCurrency';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The aggregate limit inclusion type'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'AggregateLimitInclusionType';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The not applicable attribute associated with the aggregate limit'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'AggregateLimitNotApplicable';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The aggregate limit percentage'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'AggregateLimitPercentage';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The text associated with the aggregate limit'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'AggregateLimitText';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The effective date of the auto liability value'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'AutoLiabilityEffectiveDate';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The expiration date of the auto liability value'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'AutoLiabilityExpirationDate';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The auto physical damage collision amount'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'AutoPhysicalDamageCollisionAmount';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The auto physical damage collision amount in US dollars'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'AutoPhysicalDamageCollisionAmountUSD';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The auto physical damage collision amount basis'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'AutoPhysicalDamageCollisionBasis';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The auto physical damage collision amount currency'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'AutoPhysicalDamageCollisionCurrency';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The auto physical damage collision amount inclusion type'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'AutoPhysicalDamageCollisionInclusionType';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'Text associated with the auto physical damage collision amount'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'AutoPhysicalDamageCollisionText';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The auto physical damage collision valuation'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'AutoPhysicalDamageCollisionValuation';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The auto physical damage comprehensive amount'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'AutoPhysicalDamageComprehensiveAmount';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The auto physical damage comprehensive amount in US dollars'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'AutoPhysicalDamageComprehensiveAmountUSD';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The auto physical damage comprehensive amount basis'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'AutoPhysicalDamageComprehensiveBasis';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The auto physical damage comprehensive amount currency'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'AutoPhysicalDamageComprehensiveCurrency';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The auto physical damage comprehensive amount inclusion type'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'AutoPhysicalDamageComprehensiveInclusionType';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The auto physical damage comprehensive percentage'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'AutoPhysicalDamageComprehensivePercentage';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The text associated with the auto physical damage comprehensive amount'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'AutoPhysicalDamageComprehensiveText';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The valuation associated with the auto physical damage comprehensive amount'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'AutoPhysicalDamageComprehensiveValuation';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The basis associated with the basis of cover'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'BasisofCoverBasis';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'Each accident amount from the bodily injury by accident category'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'BodilyInjuryByAccidentEachAccidentAmount';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'Each accident amount in US dollars from the bodily injury by accident category'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'BodilyInjuryByAccidentEachAccidentAmountUSD';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'Each accident basis from the bodily injury by accident category'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'BodilyInjuryByAccidentEachAccidentBasis';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'Each accident amount currency from the bodily injury by accident category'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'BodilyInjuryByAccidentEachAccidentCurrency';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'Each accident inclusion type from the bodily injury by accident category'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'BodilyInjuryByAccidentEachAccidentInclusionType';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'Each accident percentage from the bodily injury by accident category'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'BodilyInjuryByAccidentEachAccidentPercentage';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'Each accident text from the bodily injury by accident category'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'BodilyInjuryByAccidentEachAccidentText';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'Each employee amount from the bodily injury by disease category'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'BodilyInjuryByDiseaseEachEmployeeAmount';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'Each employee amount in US dollars from the bodily injury by disease category'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'BodilyInjuryByDiseaseEachEmployeeAmountUSD';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'Each employee basis from the bodily injury by disease category'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'BodilyInjuryByDiseaseEachEmployeeBasis';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'Each employee amount currency from the bodily injury by disease category'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'BodilyInjuryByDiseaseEachEmployeeCurrency';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'Each employee inclusion type from the bodily injury by disease category'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'BodilyInjuryByDiseaseEachEmployeeInclusionType';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'Each employee number of units from the bodily injury by disease category'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'BodilyInjuryByDiseaseEachEmployeeNumberOfUnits';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'Each employee percentage from the bodily injury by disease category'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'BodilyInjuryByDiseaseEachEmployeePercentage';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'Each employee text from the bodily injury by disease category'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'BodilyInjuryByDiseaseEachEmployeeText';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'Policy limit amount from the bodily injury by disease category'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'BodilyInjuryByDiseasePolicyLimitAmount';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'Policy limit amount in US dollars from the bodily injury by disease category'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'BodilyInjuryByDiseasePolicyLimitAmountUSD';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'Policy limit basis from the bodily injury by disease category'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'BodilyInjuryByDiseasePolicyLimitBasis';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'Policy limit amount currency from the bodily injury by disease category'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'BodilyInjuryByDiseasePolicyLimitCurrency';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'Policy limit inclusion type from the bodily injury by disease category'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'BodilyInjuryByDiseasePolicyLimitInclusionType';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'Policy limit percentage from the bodily injury by disease category'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'BodilyInjuryByDiseasePolicyLimitPercentage';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'Policy limit text from the bodily injury by disease category'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'BodilyInjuryByDiseasePolicyLimitText';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The claims handling structure'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'ClaimsHandlingStructure';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The amount associated with damage to premises rented to you'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'DamageToPremisesRentedToYouAmount';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The amount in US dollars associated with damage to premises rented to you'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'DamageToPremisesRentedToYouAmountUSD';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The basis associated with damage to premises rented to you'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'DamageToPremisesRentedToYouBasis';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The currency associated with damage to premises rented to you amount'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'DamageToPremisesRentedToYouCurrency';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The inclusion type associated with damage to premises rented to you'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'DamageToPremisesRentedToYouInclusionType';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The text associated with damage to premises rented to you'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'DamageToPremisesRentedToYouText';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The deductible amount'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'DeductibleAmount';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The deductible amount in US dollars'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'DeductibleAmountUSD';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The deductible basis'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'DeductibleBasis';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The deductible currency'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'DeductibleCurrency';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The deductible number of units'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'DeductibleNumberOfUnits';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The deductible percentage'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'DeductiblePercentage';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The deductible text'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'DeductibleText';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'Each occurrence limit amount'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'EachOccurrenceLimitAmount';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'Each occurrence limit amount in US dollars'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'EachOccurrenceLimitAmountUSD';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'Each occurrence limit basis'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'EachOccurrenceLimitBasis';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'Each occurrence limit currency'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'EachOccurrenceLimitCurrency';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'Each occurrence limit inclusion type'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'EachOccurrenceLimitInclusionType';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'Each occurrence limit number of units'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'EachOccurrenceLimitNumberOfUnits';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'Each occurrence limit percentage'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'EachOccurrenceLimitPercentage';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'Each occurrence limit text'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'EachOccurrenceLimitText';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The employee benefits liability deductible retention amount'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'EmployeeBenefitsLiabilityDeductibleRetentionAmountAmount';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The employee benefits liability deductible retention amount in US dollars'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'EmployeeBenefitsLiabilityDeductibleRetentionAmountAmountUSD';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The employee benefits liability deductible retention amount basis'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'EmployeeBenefitsLiabilityDeductibleRetentionAmountBasis';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The employee benefits liability deductible retention amount currency'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'EmployeeBenefitsLiabilityDeductibleRetentionAmountCurrency';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The employee benefits liability deductible retention amount text'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'EmployeeBenefitsLiabilityDeductibleRetentionAmountText';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The effective date of the employers liability'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'EmployersLiabilityEffectiveDate';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The expiration date of the employers liability'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'EmployersLiabilityExpirationDate';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The amount of the estimated claims handling cost'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'EstimatedClaimsHandlingCostAmount';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The amount in US dollars of the estimated claims handling cost'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'EstimatedClaimsHandlingCostAmountUSD';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The currency of the estimated claims handling cost'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'EstimatedClaimsHandlingCostCurrency';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'Text associated with the estimated claims handling cost'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'EstimatedClaimsHandlingCostText';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The form of collateral'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'FormOfCollateral';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The general aggregate amount'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'GeneralAggregateAmount';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The general aggregate amount in US dollars'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'GeneralAggregateAmountUSD';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The general aggregate basis'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'GeneralAggregateBasis';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The general aggregate currency'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'GeneralAggregateCurrency';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The general aggregate inclusion type'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'GeneralAggregateInclusionType';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'Text associated with the general aggregate amount'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'GeneralAggregateText';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The effective date of the general liability'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'GeneralLiabilityEffectiveDate';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The expiration date of the general liability'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'GeneralLiabilityExpirationDate';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The liability amount'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'LiabilityAmount';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The liability amount in US dollars'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'LiabilityAmountUSD';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The liability basis'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'LiabilityBasis';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The liability currency'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'LiabilityCurrency';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The liability number of units'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'LiabilityNumberOfUnits';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The liability percentage'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'LiabilityPercentage';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The liability text'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'LiabilityText';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The med pay amount'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'MedPayAmount';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The med pay amount in UD dollars'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'MedPayAmountUSD';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The med pay basis'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'MedPayBasis';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The med pay currency'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'MedPayCurrency';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The med pay inclusion type'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'MedPayInclusionType';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The med pay text'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'MedPayText';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The number of extra heavy trucks'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'NumberOfExtraHeavyTrucks';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The number of heavy trucks'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'NumberOfHeavyTrucks';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The number of light trucks'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'NumberOfLightTrucks';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The number of power units'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'NumberOfPowerUnits';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The number of private personal transport vehicles'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'NumberOfPrivatePersonalTransportVehicles';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The number of trailers'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'NumberOfTrailers';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'Other US states coverage'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'OtherStatesCoverage';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The personal advertising injury amount per person or organization'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'PersonalAdvertisingInjuryPerPersonOrOrganizationAmount';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The personal advertising injury amount in US dollars per person or organization'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'PersonalAdvertisingInjuryPerPersonOrOrganizationAmountUSD';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The personal advertising injury basis per person or organization'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'PersonalAdvertisingInjuryPerPersonOrOrganizationBasis';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The personal advertising injury currency per person or organization'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'PersonalAdvertisingInjuryPerPersonOrOrganizationCurrency';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The personal advertising injury inclusion type per person or organization'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'PersonalAdvertisingInjuryPerPersonOrOrganizationInclusionType';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'Text associated with the personal advertising injury per person or organization'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'PersonalAdvertisingInjuryPerPersonOrOrganizationText';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The policy effective date'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'PolicyEffectiveDate';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The currency associated with the policy effective date'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'PolicyEffectiveDateCurrency';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'Text associated with the policy effective date'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'PolicyEffectiveDateText';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The policy expiration date'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'PolicyExpirationDate';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The currency associated with the policy expiration date'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'PolicyExpirationDateCurrency';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'Text associated with the policy expiration date'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'PolicyExpirationDateText';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The policy form reporting form'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'PolicyFormReportingForm';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The policy form text'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'PolicyFormText';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The policy limit amount'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'PolicyLimitAmount';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The policy limit amount in US dollars'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'PolicyLimitAmountUSD';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The policy limit basis'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'PolicyLimitBasis';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The policy limit currency'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'PolicyLimitCurrency';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The policy limit inclusion type'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'PolicyLimitInclusionType';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The policy limit number of units'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'PolicyLimitNumberOfUnits';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The policy limit percentage'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'PolicyLimitPercentage';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'Text associated with the policy limit'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'PolicyLimitText';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The policy trigger'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'PolicyTrigger';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'Text associated with the policy trigger'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'PolicyTriggerText';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The products completed ops aggregate limit amount'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'ProductsCompletedOpsAggregateLimitAmount';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The products completed ops aggregate limit amount in US dollars'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'ProductsCompletedOpsAggregateLimitAmountUSD';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The products completed ops aggregate limit basis'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'ProductsCompletedOpsAggregateLimitBasis';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The products completed ops aggregate limit currency'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'ProductsCompletedOpsAggregateLimitCurrency';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The products completed ops aggregate limit inclusion type'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'ProductsCompletedOpsAggregateLimitInclusionType';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The products completed ops aggregate limit percentage'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'ProductsCompletedOpsAggregateLimitPercentage';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'Text associated with the products completed ops aggregate limit'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'ProductsCompletedOpsAggregateLimitText';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The effective date of the professional liability'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'ProfessionalLiabilityEffectiveDate';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The expiration date of the professional liability'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'ProfessionalLiabilityExpirationDate';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The quote expiry date'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'QuoteExpiryDate';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The quoted expiration date'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'QuotedExpirationDate';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The revenue amount'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'RevenueAmount';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The revenue amount in US dollars'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'RevenueAmountUSD';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The revenue amount currency'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'RevenueCurrency';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The program structure'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'StructureProgramStructure';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The surplus lines tax status'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'SurplusLinesTax';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The surplus lines tax amount'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'SurplusLinesTaxAmount';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The surplus lines tax currency'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'SurplusLinesTaxCurrency';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The third party administrator'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'ThirdPartyAdministrator';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The total collateral requirement amount'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'TotalCollateralRequirementAmount';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The total collateral requirement amount in US dollars'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'TotalCollateralRequirementAmountUSD';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The total collateral requirement currency'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'TotalCollateralRequirementCurrency';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The total collateral requirements per state'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'TotalCollateralRequirementPerStateRequirements';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The total collateral requirement text'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'TotalCollateralRequirementText';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The total estimated payroll date'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'TotalEstimatedPayrollDate';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The total estimated payroll amount'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'TotalEstimatedPayrollAmount';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The total estimated payroll amount in US dollars'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'TotalEstimatedPayrollAmountUSD';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The total estimated payroll currency'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'TotalEstimatedPayrollCurrency';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The total excess limit amount'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'TotalExcessLimitsAmount';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The total excess limit currency'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'TotalExcessLimitsCurrency';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The total excess limit percentage'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'TotalExcessLimitsPercentage';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'Text associated with the total excess limit'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'TotalExcessLimitsText';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The total outstanding actuarial liability carrier amount'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'TotalOutstandingActuarialLiabilityCarrierAmount';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The total outstanding actuarial liability carrier amount in US dollars'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'TotalOutstandingActuarialLiabilityCarrierAmountUSD';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The total outstanding actuarial liability carrier currency'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'TotalOutstandingActuarialLiabilityCarrierCurrency';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'Text associated with the total outstanding actuarial liability carrier'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'TotalOutstandingActuarialLiabilityCarrierText';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The total value amount'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'TotalValueAmount';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The total value amount in US dollars'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'TotalValueAmountUSD';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The total value currency'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'TotalValueCurrency';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The treatment of allocated loss adjustment expenses'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'TreatmentOfAllocatedLossAdjustmentExpenses';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The terrorism risk insurance act premium amount'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'TerrorismRiskInsuranceActPremiumAmount';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The terrorism risk insurance act premium amount in US dollars'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'TerrorismRiskInsuranceActPremiumAmountUSD';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The terrorism risk insurance act premium currency'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'TerrorismRiskInsuranceActPremiumCurrency';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'An indication of what is included and excluded in the terrorism risk insurance act premium'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'TerrorismRiskInsuranceActPremiumIncludedExcluded';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The workers compensation amount'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'WorkersCompensationAmount';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The workers compensation amount in US dollars'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'WorkersCompensationAmountUSD';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The workers compensation basis'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'WorkersCompensationBasis';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The workers compensation currency'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'WorkersCompensationCurrency';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The workers compensation number of units'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'WorkersCompensationNumberOfUnits';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The workers compensation statutory or rejected details'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'WorkersCompensationStatutoryRejected';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'Text associated with the workers compensation'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_MarketResponseElementAttribute'
  , @level2type = N'COLUMN'
  , @level2name = N'WorkersCompensationText';
GO