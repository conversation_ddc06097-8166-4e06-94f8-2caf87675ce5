/*
Lineage
PlacementId=rpt.PlacementPrimaryRole.PlacementId
Client=rpt.PlacementPrimaryRole.Client
Broker=rpt.PlacementPrimaryRole.Broker
AccountExecutive=rpt.PlacementPrimaryRole.AccountExecutive
Insured=rpt.PlacementPrimaryRole.Insured
StrategicBrokingAdvisor=rpt.PlacementPrimaryRole.StrategicBrokingAdvisor
ClientManager=rpt.PlacementPrimaryRole.ClientManager
SeniorClientManager=rpt.PlacementPrimaryRole.SeniorClientManager
PlacementCreator=rpt.PlacementPrimaryRole.PlacementCreator
AccountManager=rpt.PlacementPrimaryRole.AccountManager
AccountTechnician=rpt.PlacementPrimaryRole.AccountTechnician
Analyst=rpt.PlacementPrimaryRole.Analyst
AssistantClientServiceSpecialist=rpt.PlacementPrimaryRole.AssistantClientServiceSpecialist
ClientAdvocate=rpt.PlacementPrimaryRole.ClientAdvocate
Compliance=rpt.PlacementPrimaryRole.Compliance
GlobalClientAdvocate=rpt.PlacementPrimaryRole.GlobalClientAdvocate
Producer=rpt.PlacementPrimaryRole.Producer
ProductGroupLeader=rpt.PlacementPrimaryRole.ProductGroupLeader
SupportBroker=rpt.PlacementPrimaryRole.SupportBroker
UmbrellaAndExcessBroker=rpt.PlacementPrimaryRole.UmbrellaAndExcessBroker
BLU=rpt.PlacementPrimaryRole.BLU
*/

CREATE VIEW rpt.vw_as_PlacementPrimaryRole
AS
SELECT
    PlacementId
  , Client
  , Broker
  , AccountExecutive
  , Insured
  , StrategicBrokingAdvisor
  , ClientManager
  , SeniorClientManager
  , PlacementCreator
  , AccountManager
  , AccountTechnician
  , Analyst
  , AssistantClientServiceSpecialist
  , ClientAdvocate
  , Compliance
  , GlobalClientAdvocate
  , Producer
  , ProductGroupLeader
  , SupportBroker
  , UmbrellaAndExcessBroker
  , BLU
FROM
    rpt.PlacementPrimaryRole;
GO

--Add View Level Detail
EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'This table is used to identify the placements main roles.  In the case where there are multiple of a given role, one will be presented via this table.'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_PlacementPrimaryRole';
GO

--Add Column Level Detail
EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The unique Id for the placement'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_PlacementPrimaryRole'
  , @level2type = N'COLUMN'
  , @level2name = N'PlacementId';
GO

--Add Column Level Detail
EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'A Client named on a placement.'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_PlacementPrimaryRole'
  , @level2type = N'COLUMN'
  , @level2name = N'Client';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The Broker named on a placement'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_PlacementPrimaryRole'
  , @level2type = N'COLUMN'
  , @level2name = N'Broker';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The Account Executive named on a placement'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_PlacementPrimaryRole'
  , @level2type = N'COLUMN'
  , @level2name = N'AccountExecutive';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The Insured party named on a placement'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_PlacementPrimaryRole'
  , @level2type = N'COLUMN'
  , @level2name = N'Insured';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The Strategic Broking Advisor named on a placement'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_PlacementPrimaryRole'
  , @level2type = N'COLUMN'
  , @level2name = N'StrategicBrokingAdvisor';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The Client Manager named on a placement'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_PlacementPrimaryRole'
  , @level2type = N'COLUMN'
  , @level2name = N'ClientManager';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The senior client manager named on a placement'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_PlacementPrimaryRole'
  , @level2type = N'COLUMN'
  , @level2name = N'SeniorClientManager';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The creator of the placement (where manually created)'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_PlacementPrimaryRole'
  , @level2type = N'COLUMN'
  , @level2name = N'PlacementCreator';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The Account Manager named on a placement'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_PlacementPrimaryRole'
  , @level2type = N'COLUMN'
  , @level2name = N'AccountManager';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The Account Technician named on a placement'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_PlacementPrimaryRole'
  , @level2type = N'COLUMN'
  , @level2name = N'AccountTechnician';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The Analyst named on a placement'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_PlacementPrimaryRole'
  , @level2type = N'COLUMN'
  , @level2name = N'Analyst';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The Assistant Client Service Specialist named on a placement'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_PlacementPrimaryRole'
  , @level2type = N'COLUMN'
  , @level2name = N'AssistantClientServiceSpecialist';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The Client Advocate named on a placement'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_PlacementPrimaryRole'
  , @level2type = N'COLUMN'
  , @level2name = N'ClientAdvocate';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The Compliance team member named on a placement'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_PlacementPrimaryRole'
  , @level2type = N'COLUMN'
  , @level2name = N'Compliance';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The Global Client Advocate named on a placement'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_PlacementPrimaryRole'
  , @level2type = N'COLUMN'
  , @level2name = N'GlobalClientAdvocate';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The producer named on a placement'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_PlacementPrimaryRole'
  , @level2type = N'COLUMN'
  , @level2name = N'Producer';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The product group leader named on a placement'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_PlacementPrimaryRole'
  , @level2type = N'COLUMN'
  , @level2name = N'ProductGroupLeader';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The support broker named on a placement'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_PlacementPrimaryRole'
  , @level2type = N'COLUMN'
  , @level2name = N'SupportBroker';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The Umbrella And Excess Broker named on a placement'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_PlacementPrimaryRole'
  , @level2type = N'COLUMN'
  , @level2name = N'UmbrellaAndExcessBroker';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The BLU named on a placement'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_PlacementPrimaryRole'
  , @level2type = N'COLUMN'
  , @level2name = N'BLU';
GO