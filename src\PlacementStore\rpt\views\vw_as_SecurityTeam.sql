/*
Lineage
DataSourceInstanceID=dbo.Placement.ServicingPlatformId
TeamID=dbo.Scope.ScopeId
TeamID=dbo.Scope.ScopeItemId
TeamID=ods.vw_ref_Team.TeamId
TeamID=dbo.Placement.PlacementId
TeamName=ref.BrokingSegment.BrokingSegment
TeamName=ref.BrokingRegion.BrokingRegion
TeamName=ref.BrokingSubSegment.BrokingSubSegment
TeamName=dbo.Scope.IndustryId
TeamName=ref.PlacementStatus.PlacementStatus
TeamName=ods.vw_ref_Team.OrgLevel1
TeamName=dbo.Placement.PlacementName
ParentId=ods.vw_ref_Team.TeamId
ParentId=dbo.PlacementTeams.TeamId
OrgLevel1=ods.vw_ref_Team.OrgLevel1
OrgLevel2=ods.vw_ref_Team.OrgLevel2
OrgLevel2=dbo.Placement.PlacementName
OrgLevel2=ods.vw_ref_Team.OrgLevel1
OrgLevel3=ods.vw_ref_Team.OrgLevel3
OrgLevel3=dbo.Placement.PlacementName
OrgLevel3=ods.vw_ref_Team.OrgLevel1
OrgLevel4=ods.vw_ref_Team.OrgLevel4
OrgLevel4=dbo.Placement.PlacementName
OrgLevel4=ods.vw_ref_Team.OrgLevel1
OrgLevel5=ods.vw_ref_Team.OrgLevel5
OrgLevel5=dbo.Placement.PlacementName
OrgLevel5=ods.vw_ref_Team.OrgLevel1
OrgLevel6=ods.vw_ref_Team.OrgLevel6
OrgLevel6=dbo.Placement.PlacementName
OrgLevel6=ods.vw_ref_Team.OrgLevel1
LevelNum=ods.vw_ref_Team.LevelNum
Team=ods.vw_ref_Team.OrgLevel1
Team=ods.vw_ref_Team.OrgLevel2
Team=ods.vw_ref_Team.OrgLevel3
Team=ods.vw_ref_Team.OrgLevel4
Segment=ref.BrokingSegment.BrokingSegment
Region=ref.BrokingRegion.BrokingRegion
SubSegment=ref.BrokingSubSegment.BrokingSubSegment
Industry=dbo.Scope.IndustryId
PlacementStatusId=ref.PlacementStatus.PlacementStatusId
*/
CREATE VIEW rpt.vw_as_SecurityTeam
AS
SELECT
    DataSourceInstanceID = sc.DataSourceInstanceId
  , sc.TeamType
  , TeamID = CONCAT(sc.TeamId, '|', t.TeamId)
  , TeamName = ISNULL(sc.TeamName, 'Scope:' + t.OrgLevel1)
  , ParentId = t.TeamId
  , t.OrgLevel1
  , t.OrgLevel2
  , t.OrgLevel3
  , t.OrgLevel4
  , t.OrgLevel5
  , t.OrgLevel6
  , LevelNum = t.LevelNum
  , OrganisationRole = 'Scope'
  , Team = CONCAT(
               t.OrgLevel1
             , CASE WHEN t.OrgLevel2 IS NULL
                        THEN NULL
                    ELSE CONCAT(' > ', t.OrgLevel2) END
             , CASE WHEN t.OrgLevel3 IS NULL
                        THEN NULL
                    ELSE CONCAT(' > ', t.OrgLevel3) END
             , CASE WHEN t.OrgLevel4 IS NULL
                        THEN NULL
                    ELSE CONCAT(' > ', t.OrgLevel4) END
           )
  , Segment = sc.Segment
  , Region = sc.Region
  , SubSegment = sc.SubSegment
  , Industry = sc.Industry
  , PlacementStatusId = sc.PlacementStatusId
FROM
    ods.vw_ref_Team t
    INNER JOIN (
        --Scopes
        SELECT
            DataSourceInstanceId = 50366
          , TeamType = 'Scope'
          , TeamId = CAST(CAST(sc.ScopeId AS NVARCHAR(9)) + '|' + CAST(sc.ScopeItemId AS NVARCHAR(10)) AS NVARCHAR(20))
          , ParentId = CAST(t.TeamId AS NVARCHAR(200))
          , t.OrgNode
          , TeamName = CAST('Scope: Segment|' + ISNULL(bs.BrokingSegment, 'All') + ' | ' + 'Region|'
                            + ISNULL(br.BrokingRegion, 'All') + ' | ' + 'SubSegment|'
                            + ISNULL(pc.BrokingSubSegment, 'All') + ' | ' + 'Industry|'
                            + ISNULL(CAST(sc.IndustryId AS NVARCHAR(10)), 'All') + ' | ' + 'PlacementStatusID|'
                            + ISNULL(st.PlacementStatus, 'All') AS NVARCHAR(500))
          , Segment = ISNULL(bs.BrokingSegment, 'All')
          , Region = ISNULL(br.BrokingRegion, 'All')
          , SubSegment = ISNULL(pc.BrokingSubSegment, 'All')
          , Industry = ISNULL(CAST(sc.IndustryId AS NVARCHAR(10)), 'All')
          , PlacementStatusId = ISNULL(st.PlacementStatusId, -1)
        FROM
            dbo.Scope sc
            INNER JOIN ref.Team t
                ON sc.TeamId = t.TeamId

            LEFT JOIN ref.BrokingSegment bs
                ON sc.BrokingSegmentId = bs.BrokingSegmentId

            LEFT JOIN ref.BrokingRegion br
                ON sc.BrokingRegionId = br.BrokingRegionId

            LEFT JOIN ref.BrokingSubSegment pc
                ON sc.BrokingSubSegmentId = pc.BrokingSubSegmentId

            LEFT JOIN ref.PlacementStatus st
                ON sc.PlacementStatusId = st.PlacementStatusId
        WHERE
            sc.IsDeleted = 0
    ) sc
        ON t.OrgNode.IsDescendantOf(sc.OrgNode) = 1
UNION ALL
SELECT
    DataSourceInstanceID = pt.ServicingPlatformId
  , pt.TeamType
  , TeamID = pt.TeamId
  , TeamName = ISNULL(pt.TeamName, 'Placement:' + t.OrgLevel1)
  , pt.ParentId
  , t.OrgLevel1
  , CASE WHEN t.LevelNum + 1 = 2
             THEN ISNULL(pt.TeamName, 'Placement:' + t.OrgLevel1)
         ELSE t.OrgLevel2 END
  , CASE WHEN t.LevelNum + 1 = 3
             THEN ISNULL(pt.TeamName, 'Placement:' + t.OrgLevel1)
         ELSE t.OrgLevel3 END
  , CASE WHEN t.LevelNum + 1 = 4
             THEN ISNULL(pt.TeamName, 'Placement:' + t.OrgLevel1)
         ELSE t.OrgLevel4 END
  , CASE WHEN t.LevelNum + 1 = 5
             THEN ISNULL(pt.TeamName, 'Placement:' + t.OrgLevel1)
         ELSE t.OrgLevel5 END
  , CASE WHEN t.LevelNum + 1 = 6
             THEN ISNULL(pt.TeamName, 'Placement:' + t.OrgLevel1)
         ELSE t.OrgLevel6 END
  , LevelNum = t.LevelNum + 1
  , OrganisationRole = 'Placement'
  , Team = CONCAT(
               t.OrgLevel1
             , CASE WHEN t.OrgLevel2 IS NULL
                        THEN NULL
                    ELSE CONCAT(' > ', t.OrgLevel2) END
             , CASE WHEN t.OrgLevel3 IS NULL
                        THEN NULL
                    ELSE CONCAT(' > ', t.OrgLevel3) END
             , CASE WHEN t.OrgLevel4 IS NULL
                        THEN NULL
                    ELSE CONCAT(' > ', t.OrgLevel4) END
           )
  , Segment = NULL
  , Region = NULL
  , SubSegment = NULL
  , Industry = NULL
  , PlacementStatusId = NULL
FROM
    ods.vw_ref_Team t
    INNER JOIN (
        --Placement Teams
        SELECT DISTINCT
               pl.ServicingPlatformId
             , TeamType = 'Placement'
             , TeamId = CAST(pl.PlacementId AS NVARCHAR(20))
             , ParentId = (
                   SELECT pt.TeamId FROM dbo.PlacementTeams pt WHERE pt.PlacementId = pl.PlacementId AND pt.IsDeleted = 0
               )
             , TeamName = CAST('Placement:' + pl.PlacementName AS NVARCHAR(200))
        FROM
            dbo.PlacementTeamMember ptm
            INNER JOIN dbo.Placement pl
                ON ptm.PlacementId = pl.PlacementId

            LEFT JOIN dbo.PlacementTeams pt
                ON pl.PlacementId = pt.PlacementId
                   AND pt.IsDeleted = 0

            LEFT JOIN ref.Team t
                ON pt.TeamId = t.TeamId
        WHERE
            ptm.DataSourceInstanceId = 50366
            AND pl.PlacementSystemId IS NOT NULL
            AND pl.DataSourceInstanceId = 50366
    ) pt
        ON pt.ParentId = t.TeamId;

--Add View Level Detail
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'Internal view to provide security teams for a placement and user'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_SecurityTeam';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The source of the Data '
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_SecurityTeam'
  , @level2type = N'COLUMN'
  , @level2name = N'DataSourceInstanceID';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The type of security grouping '
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_SecurityTeam'
  , @level2type = N'COLUMN'
  , @level2name = N'TeamType';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The internal id of the team'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_SecurityTeam'
  , @level2type = N'COLUMN'
  , @level2name = N'TeamID';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The internal id of the parent team '
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_SecurityTeam'
  , @level2type = N'COLUMN'
  , @level2name = N'ParentId';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The name of the team '
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_SecurityTeam'
  , @level2type = N'COLUMN'
  , @level2name = N'TeamName';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The Org Level 1 for the team / security '
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_SecurityTeam'
  , @level2type = N'COLUMN'
  , @level2name = N'OrgLevel1';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The Org Level 2 for the team / security '
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_SecurityTeam'
  , @level2type = N'COLUMN'
  , @level2name = N'OrgLevel2';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The Org Level 3 for the team / security '
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_SecurityTeam'
  , @level2type = N'COLUMN'
  , @level2name = N'OrgLevel3';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The Org Level 4 for the team / security '
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_SecurityTeam'
  , @level2type = N'COLUMN'
  , @level2name = N'OrgLevel4';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The Org Level 5 for the team / security '
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_SecurityTeam'
  , @level2type = N'COLUMN'
  , @level2name = N'OrgLevel5';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The Org Level 6 for the team / security '
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_SecurityTeam'
  , @level2type = N'COLUMN'
  , @level2name = N'OrgLevel6';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The level in the organisation structure '
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_SecurityTeam'
  , @level2type = N'COLUMN'
  , @level2name = N'LevelNum';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The role of the team where appropriate '
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_SecurityTeam'
  , @level2type = N'COLUMN'
  , @level2name = N'OrganisationRole';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The full path of the team'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_SecurityTeam'
  , @level2type = N'COLUMN'
  , @level2name = N'Team';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The Segment to be applied for Security (All = unset)'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_SecurityTeam'
  , @level2type = N'COLUMN'
  , @level2name = N'Segment';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The Global Line Of Business (SubSegment) to be applied for Security (All = unset)'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_SecurityTeam'
  , @level2type = N'COLUMN'
  , @level2name = N'SubSegment';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The Region to be applied for Security (All = unset)'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_SecurityTeam'
  , @level2type = N'COLUMN'
  , @level2name = N'Region';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The Industry to be applied for Security (All = unset)'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_SecurityTeam'
  , @level2type = N'COLUMN'
  , @level2name = N'Industry';
GO

EXEC sys.sp_addextendedproperty
    @name = N'Description'
  , @value = N'The PlacementStatusId to be applied for Security (-1 = unset)'
  , @level0type = N'SCHEMA'
  , @level0name = N'rpt'
  , @level1type = N'VIEW'
  , @level1name = N'vw_as_SecurityTeam'
  , @level2type = N'COLUMN'
  , @level2name = N'PlacementStatusId';