﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Identity.Web.Resource;
using PsApi.Services;
using System.Net;
using Wtw.Crb.PlacementStore.Api.Model;

namespace PsApi.Controllers.ReferenceData;

/// <summary>The controller for looking up Policy Status information.</summary>
/// <seealso cref="AuthorizingControllerBase{T}"/>
[Authorize]
[ApiVersion("1")]
[Produces("application/json")]
[Route("api/v{version:apiVersion}/ref")]
[ApiController]
[Tags("Reference Data: Policy Status")]
public sealed class RefPolicyStatusController : AuthorizingControllerBase<RefPolicyStatusController>
{
    /// <summary>Initializes a new instance of the <see cref="EnterpriseReferenceData.PolicyStatusController"/> class.</summary>
    /// <param name="logger">The logger with which to record activity.</param>
    /// <param name="context">The DB context to use to access the database.</param>
    /// <param name="sharedKeyService">The service used to manage the mappings between AD Application IDs and Placement Store Shared Keys.</param>
    public RefPolicyStatusController(ILogger<RefPolicyStatusController> logger, PlacementStoreContext context, ISharedKeyService sharedKeyService) : base(
        logger, context, sharedKeyService)
    {
    }

    /// <summary>
    ///     Get a sequence of, at most <paramref name="limit"/>, policy statuses starting at the element specified by <paramref name="offset"/>, sorted
    ///     by <see cref="vw_ref_PolicyStatus.PolicyStatusId"/>.
    /// </summary>
    /// <remarks>
    ///     Warning: this can contain a large number of elements in the results, use
    ///     <paramref name="limit"/> and <paramref name="offset"/> to page results.
    /// </remarks>
    /// <param name="limit">The maximum number of entities to return.  Defaults to 100.</param>
    /// <param name="offset">The number of rows to skip before returning results.  Defaults to zero.</param>
    /// <param name="cancellationToken">The cancellation token with which to cleanly abort the operation.</param>
    /// <param name="includeDeprecated">
    ///     if set to <see langword="true"/> deprecated records are included in the results returned; otherwise, pass <see langword="false"/> to exclude
    ///     deprecated records from the results.  Defaults to <see langword="false"/>.
    /// </param>
    /// <returns>
    ///     Get a sequence of, at most <paramref name="limit"/>, policy statuses starting at the element specified by <paramref name="offset"/>, sorted
    ///     by <see cref="vw_ref_PolicyStatus.PolicyStatusId"/>.
    /// </returns>
    /// <response code="200">The request has been processed successfully and results returned.</response>
    /// <response code="400">The 'offset' argument was less than zero, or the 'limit' argument was less than one.</response>
    /// <response code="401">Could not determine the caller's application ID, or could not parse the callers application ID, or could not determine the callers permitted data source IDs.</response>
    /// <response code="403">The request was not authenticated using AD/OAuth2 bearer token.</response>
    /// <response code="500">An unexpected error occurred whilst processing the request.</response>
    [HttpGet("policy-statuses")]
    [RequiredScopeOrAppPermission(AcceptedAppPermission = new[] { AllForDataSource })]
    [ProducesResponseType(typeof(vw_ref_PolicyStatus[]), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(void), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(typeof(void), StatusCodes.Status403Forbidden)]
    [ProducesResponseType(typeof(void), StatusCodes.Status500InternalServerError)]
    public async Task<IEnumerable<vw_ref_PolicyStatus>?> GetPolicyStatusesAsync(CancellationToken cancellationToken, [FromQuery] int limit = 100,
        [FromQuery] int offset = 0, [FromQuery] bool includeDeprecated = false)
    {
        if(!await IsCallerAuthenticatedAsync(cancellationToken))
        {
            return null;
        }

        if(offset < 0)
        {
            await SetResponseAsync(HttpStatusCode.BadRequest, "'offset' must be zero or greater.", null, cancellationToken);
            return Enumerable.Empty<vw_ref_PolicyStatus>();
        }

        if(limit < 1)
        {
            await SetResponseAsync(HttpStatusCode.BadRequest, "'limit' must be greater than zero.", null, cancellationToken);
            return Enumerable.Empty<vw_ref_PolicyStatus>();
        }

        /*
         * The vw_ref_* and vw_ref_* views are reference data that should be available to all.  We check that a
         * registered data source has been supplied, but we do not filter.
         */

        var data = Context.vw_ref_PolicyStatuses.AsNoTracking().Where(c => includeDeprecated || !c.IsDeprecated).OrderBy(c => c.PolicyStatusId).Skip(offset).Take(limit);

        return data;
    }

    /// <summary>Gets the policy status identified by the policy status key specified.</summary>
    /// <param name="policyStatusKey">The key identifying the policy status for which to search.  String comparisons are case-insensitive.</param>
    /// <param name="cancellationToken">The cancellation token with which to cleanly abort the operation.</param>
    /// <returns>The policy status requested, or <see langword="null"/> if none found.</returns>
    /// <response code="200">The request has been processed successfully and results returned.</response>
    /// <response code="400">The ID or key supplied is zero-length or contains only whitespace characters.</response>
    /// <response code="404">No entities were found matching the criteria specified.</response>
    /// <response code="401">Could not determine the caller's application ID, or could not parse the callers application ID, or could not determine the callers permitted data source IDs.</response>
    /// <response code="403">The request was not authenticated using AD/OAuth2 bearer token.</response>
    /// <response code="500">An unexpected error occurred whilst processing the request.</response>
    [HttpGet("policy-status/by-policy-status-key/{policyStatusKey}")]
    [RequiredScopeOrAppPermission(AcceptedAppPermission = new[] { AllForDataSource })]
    [ProducesResponseType(typeof(vw_ref_PolicyStatus), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(void), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(typeof(void), StatusCodes.Status403Forbidden)]
    [ProducesResponseType(typeof(void), StatusCodes.Status404NotFound)]
    [ProducesResponseType(typeof(void), StatusCodes.Status500InternalServerError)]
    public async Task<vw_ref_PolicyStatus?> GetPolicyStatusByPolicyStatusKeyAsync([FromRoute] string policyStatusKey, CancellationToken cancellationToken)
    {
        if(!await IsCallerAuthenticatedAsync(cancellationToken))
        {
            return null;
        }

        /*
         * The vw_ref_* and vw_ref_* views are reference data that should be available to all.  We check that a
         * registered data source has been supplied, but we do not filter.
         */

        var data = Context.vw_ref_PolicyStatuses.AsNoTracking().FirstOrDefault(c => c.PolicyStatusKey == policyStatusKey);

        if(data is null)
        {
            await SetResponseAsync(HttpStatusCode.NotFound, "Not found.", null, cancellationToken);
            return null;
        }

        return data;
    }

    /// <summary>Gets the policy status identified by the policy status ID specified.</summary>
    /// <param name="policyStatusId">The ID identifying the policy status for which to search.  The comparison is not case sensitive.</param>
    /// <param name="cancellationToken">The cancellation token with which to cleanly abort the operation.</param>
    /// <returns>The policy status requested, or <see langword="null"/> if none found.</returns>
    /// <response code="200">The request has been processed successfully and results returned.</response>
    /// <response code="400">The ID or key supplied is zero-length or contains only whitespace characters.</response>
    /// <response code="404">No entities were found matching the criteria specified.</response>
    /// <response code="401">Could not determine the caller's application ID, or could not parse the callers application ID, or could not determine the callers permitted data source IDs.</response>
    /// <response code="403">The request was not authenticated using AD/OAuth2 bearer token.</response>
    /// <response code="500">An unexpected error occurred whilst processing the request.</response>
    [HttpGet("policy-status/by-policy-status-id/{policyStatusId}")]
    [Obsolete("This method is deprecated.")]
    [RequiredScopeOrAppPermission(AcceptedAppPermission = new[] { AllForDataSource })]
    [ProducesResponseType(typeof(vw_ref_PolicyStatus), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(void), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(typeof(void), StatusCodes.Status403Forbidden)]
    [ProducesResponseType(typeof(void), StatusCodes.Status404NotFound)]
    [ProducesResponseType(typeof(void), StatusCodes.Status500InternalServerError)]
    public async Task<vw_ref_PolicyStatus?> GetPolicyStatusByPolicyStatusIdAsync([FromRoute] int policyStatusId, CancellationToken cancellationToken)
    {
        if(!await IsCallerAuthenticatedAsync(cancellationToken))
        {
            return null;
        }

        /*
         * The vw_ref_* and vw_ref_* views are reference data that should be available to all.  We check that a
         * registered data source has been supplied, but we do not filter.
         */

        var data = Context.vw_ref_PolicyStatuses.AsNoTracking().FirstOrDefault(c => c.PolicyStatusId == policyStatusId);

        if(data is null)
        {
            await SetResponseAsync(HttpStatusCode.NotFound, "Not found.", null, cancellationToken);
            return null;
        }

        return data;
    }

    /// <summary>Gets the policy status identified by the PACT policy status id specified.</summary>
    /// <param name="pactPolicyStatusId">The PACT ID of the policy status for which to search.</param>
    /// <param name="cancellationToken">The cancellation token with which to cleanly abort the operation.</param>
    /// <returns>The policy status requested, or <see langword="null"/> if none found.</returns>
    /// <response code="200">The request has been processed successfully and results returned.</response>
    /// <response code="400">The ID or key supplied is zero-length or contains only whitespace characters.</response>
    /// <response code="404">No entities were found matching the criteria specified.</response>
    /// <response code="401">Could not determine the caller's application ID, or could not parse the callers application ID, or could not determine the callers permitted data source IDs.</response>
    /// <response code="403">The request was not authenticated using AD/OAuth2 bearer token.</response>
    /// <response code="500">An unexpected error occurred whilst processing the request.</response>
    [HttpGet("policy-status/by-pact-policy-status-id/{pactPolicyStatusId}")]
    [RequiredScopeOrAppPermission(AcceptedAppPermission = new[] { AllForDataSource })]
    [ProducesResponseType(typeof(vw_ref_PolicyStatus), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(void), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(typeof(void), StatusCodes.Status403Forbidden)]
    [ProducesResponseType(typeof(void), StatusCodes.Status404NotFound)]
    [ProducesResponseType(typeof(void), StatusCodes.Status500InternalServerError)]
    public async Task<vw_ref_PolicyStatus?> GetPolicyStatusByPACTPolicyStatusIdAsync([FromRoute] int pactPolicyStatusId, CancellationToken cancellationToken)
    {
        if(!await IsCallerAuthenticatedAsync(cancellationToken))
        {
            return null;
        }

        /*
         * The vw_ref_* and vw_ref_* views are reference data that should be available to all.  We check that a
         * registered data source has been supplied, but we do not filter.
         */

        var data = Context.vw_ref_PolicyStatuses.AsNoTracking().FirstOrDefault(c => c.PACTPolicyStatusId == pactPolicyStatusId);

        if(data is null)
        {
            await SetResponseAsync(HttpStatusCode.NotFound, "Not found.", null, cancellationToken);
            return null;
        }

        return data;
    }

    /// <summary>
    /// Gets the policy statuses identified by the reference policy status id specified
    /// </summary>
    /// <remarks>
    ///     Warning: this can contain a large number of elements in the results, use <paramref name="limit"/>
    ///     and <paramref name="offset"/> to page results.
    /// </remarks>
    /// <param name="refPolicyStatusId">The name of the policy status for which to search.  The comparison is not case sensitive.</param>
    /// <param name="limit">The maximum number of entities to return.  Defaults to 100.</param>
    /// <param name="offset">The number of rows to skip before returning results.  Defaults to zero.</param>
    /// <param name="cancellationToken">The cancellation token with which to cleanly abort the operation.</param>
    /// <param name="includeDeprecated">
    ///     if set to <see langword="true"/> deprecated records are included in the results returned; otherwise, pass <see langword="false"/> to exclude
    ///     deprecated records from the results.  Defaults to <see langword="false"/>.
    /// </param>
    /// <returns>
    /// Get a sequence of, at most <paramref name="limit"/>, policy statuses with the reference policy status id specified starting at the element specified by <paramref name="offset"/>, sorted by <see cref="vw_ref_PolicyStatus.PolicyStatusId"/>.
    /// </returns>
    /// <response code="200">The request has been processed successfully and results returned.</response>
    /// <response code="400">The 'offset' argument was less than zero, or the 'limit' argument was less than one.</response>
    /// <response code="401">Could not determine the caller's application ID, or could not parse the callers application ID, or could not determine the callers permitted data source IDs.</response>
    /// <response code="403">The request was not authenticated using AD/OAuth2 bearer token.</response>
    /// <response code="500">An unexpected error occurred whilst processing the request.</response>
    /// <response code="404">No entities were found matching the criteria specified.</response>
    [HttpGet("policy-status/by-ref-policy-status-id{refPolicyStatusId}")]
    [RequiredScopeOrAppPermission(AcceptedAppPermission = new[] { AllForDataSource })]
    [ProducesResponseType(typeof(vw_ref_PolicyStatus[]), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(void), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(typeof(void), StatusCodes.Status403Forbidden)]
    [ProducesResponseType(typeof(void), StatusCodes.Status500InternalServerError)]
    [ProducesResponseType(typeof(void), StatusCodes.Status404NotFound)]
    public async Task<IEnumerable<vw_ref_PolicyStatus>?> GetPolicyStatusesByRefPolicyStatusIdAsync([FromRoute] int refPolicyStatusId, CancellationToken cancellationToken, [FromQuery] int limit = 100,
    [FromQuery] int offset = 0, [FromQuery] bool includeDeprecated = false)
    {
        if(!await IsCallerAuthenticatedAsync(cancellationToken))
        {
            return null;
        }

        if(offset < 0)
        {
            await SetResponseAsync(HttpStatusCode.BadRequest, "'offset' must be zero or greater.", null, cancellationToken);
            return Enumerable.Empty<vw_ref_PolicyStatus>();
        }

        if(limit < 1)
        {
            await SetResponseAsync(HttpStatusCode.BadRequest, "'limit' must be greater than zero.", null, cancellationToken);
            return Enumerable.Empty<vw_ref_PolicyStatus>();
        }

        var data = Context.vw_ref_PolicyStatuses.AsNoTracking().Where(c => (includeDeprecated || !c.IsDeprecated) && c.RefPolicyStatusId == refPolicyStatusId).OrderBy(c => c.PolicyStatusId).Skip(offset).Take(limit);

        if(!data.Any())
        {
            await SetResponseAsync(HttpStatusCode.NotFound, "Not found.", null, cancellationToken);
            return null;
        }

        return data;
    }

}