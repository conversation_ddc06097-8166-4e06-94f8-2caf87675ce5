﻿using Microsoft.ApplicationInsights.Extensibility;
using Microsoft.AspNetCore.Builder;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Azure;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using PsFunc;
using PsFunc.Helper;
using System.Diagnostics;
using Wtw.Crb.Common.HealthCheck.EventHub;
using Wtw.Crb.Common.HealthCheck.ServiceBus;
using Wtw.Crb.Common.HealthCheck.Sql;
using Wtw.Crb.Common.Identity;

var host = new HostBuilder()
    .ConfigureFunctionsWebApplication()
    .ConfigureServices((Action<IServiceCollection>)(services =>
    {

#if DEBUG
        if(Debugger.IsAttached)
        {
            // Attempt to reduce network issue when testing on developers' desktops.
            HttpClient.DefaultProxy = new NoProxy();
        }
#endif

        // Below change is done for Test Automation
        // Reload on change environment variable which is set to true by DotNet runtime, so if that environment variable
        // is set to false then reload on change would be set to false which is required for running UI Test on Linux
        // refer https://github.com/dotnet/AspNetCore.Docs/issues/19814
        var reloadOnChangeEnv = Environment.GetEnvironmentVariable("DOTNET_HOSTBUILDER__RELOADCONFIGONCHANGE");
        var reloadOnChange = !(!string.IsNullOrEmpty(reloadOnChangeEnv) && reloadOnChangeEnv.Equals("false"));

        // Get current directory for either local or Azure
        var config = ConfigureConfiguration(Environment.CurrentDirectory, reloadOnChange);

        // Register Azure Clients
        services.AddAzureClients(builder =>
        {
            builder
                .AddServiceBusClientWithNamespace(config[AzureConstants.ServiceBusConnectionString])
                .WithName(AzureConstants.ServiceBusClientName);
            builder
                .AddEventHubProducerClientWithNamespace(config[AzureConstants.EventHubConnectionString], config[AzureConstants.EventHubName])
                .WithName(AzureConstants.EventHubClientName);
            builder
                .UseCredential(TokenUtil.TokenCredential);
        });

        services.AddScoped<IServiceBusMessageHelper, ServiceBusMessageHelper>();
        services.AddScoped<IEventHubHelper, EventHubHelper>();
        ConfigureLogging(services, config);

        services.AddHsts(options => { options.Preload = true; });
        services.AddSingleton(config);
        services.AddSingleton<ITelemetryInitializer, LogLevelTelemetryInitializer>();
        // added custom telemetry processor to remove dependency logging
        services.AddApplicationInsightsTelemetryProcessor<CustomTelemetryProcessor>();
        services.AddHttpClient();

        ConfigureHealthChecks(services, config);

    }))
    .ConfigureFunctionsWorkerDefaults(worker =>
    {
        worker.ConfigureServices(services =>
        {
            services.Configure<ServiceBusOptions>(options =>
            {
                options.MaxAutoLockRenewalDuration = TimeSpan.FromHours(2);
            });
        });
    })
    .Build();

await host.RunAsync();

static IConfigurationRoot ConfigureConfiguration(string currentDirectory, bool reloadOnChange)
{
    // Load configuration and set up DI
    var config = new ConfigurationBuilder()
        .SetBasePath(currentDirectory)
        .AddJsonFile("appsettings.json", false)
        .AddJsonFile("local.settings.json", optional: true, reloadOnChange: reloadOnChange)
        .AddEnvironmentVariables()
        .AddUserSecrets<Program>()
        .Build();

    return config;
}

void ConfigureLogging(IServiceCollection services, IConfigurationRoot config)
{
    /*
     *     * Configure the logging manually to allow us to add our custom telemetry.
     *         * This has to be done manually because the order in which things happen is significant.
     *             */
    services.AddLogging(logging =>
    {
        Debug.Assert(logging != null, nameof(logging) + " != null");

        // ReSharper disable PossibleNullReferenceException
        logging.AddConfiguration(config.GetSection(@"Logging"));
#if DEBUG
        if(Debugger.IsAttached)
        {
            // Add debugger logging only when a debugger is attached.
            logging.AddConsole();
        }
#endif

        // Custom telemetry for application insights logging
        logging.Services.AddApplicationInsightsTelemetryWorkerService(o =>
        {
            o.ApplicationVersion = typeof(Program).Assembly.GetName().Version!.ToString();
            o.ConnectionString = config["APPLICATIONINSIGHTS_CONNECTION_STRING"];
            o.DeveloperMode = Debugger.IsAttached;
            o.EnableDebugLogger = Debugger.IsAttached;
            o.EnableAdaptiveSampling = true;
            o.EnableAppServicesHeartbeatTelemetryModule = true;
            o.EnableAzureInstanceMetadataTelemetryModule = true;
            o.EnableDependencyTrackingTelemetryModule = false;
            o.EnableEventCounterCollectionModule = true;
            o.EnableHeartbeat = true;
            o.EnablePerformanceCounterCollectionModule = true;
            o.EnableQuickPulseMetricStream = true;
        });
        logging.Services.ConfigureFunctionsApplicationInsights();
        logging.Services.Configure<LoggerFilterOptions>(options =>
        {
            // The Application Insights SDK adds a default logging filter that instructs ILogger to capture only Warning and more severe logs. Application Insights requires an explicit override.
            var toRemove = options.Rules.FirstOrDefault(rule => rule.ProviderName == "Microsoft.Extensions.Logging.ApplicationInsights.ApplicationInsightsLoggerProvider");

            if(toRemove is not null)
            {
                options.Rules.Remove(toRemove);
            }
        });
    });
}

void ConfigureHealthChecks(IServiceCollection services, IConfigurationRoot configuration)
{
    services.AddHealthChecks()
        .AddAzureServiceBusTopics(
            clientName: AzureConstants.ServiceBusClientName,
            topicSubscriptions: new Dictionary<string, IEnumerable<string>>() {
                {
                    "Placement",
                    new List<string>() {
                        "PlacementEvents"
                    }
                },
                {
                    "psadf-to-psfuncapp-event",
                    new List<string>() {
                        "bpstaging"
                    }
                }
            })
        .AddSqlServer(configurationRoot: configuration, connectionName: "Database")
        .AddSqlServer(configurationRoot: configuration, connectionName: "BKPDatabase", isExternal: true)
        .AddSqlServer(configurationRoot: configuration, connectionName: "ServiceHub", isExternal: true)
        .AddSqlServer(configurationRoot: configuration, connectionName: "ServiceHubMetadata", isExternal: true)
        .AddEventHub(hubName: AzureConstants.EventHubClientName, isExternal: true)
        .AddApplicationInsightsPublisher();
}