﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PsDb.Tests.APIv1.StoredProcedures;
public class UpdatePlacementSystemStatusTests : PlacementStoreTestBase
{
    [Fact]
    public void UpdatePlacementSystemStatusTest()
    {
        dynamic placementListenerRecord = CreateRow(tableName: "dbo.PlacementListener", values: new { PlacementSystemStatusId = 1, IsReadyToSend = true });

        ExecuteStoredProcedureWithoutResult(storedProcedureName: "APIv1.UpdatePlacementSystemStatus", values: new
        {
            @PlacementSystemData = $"{{\"PlacementID\":\"{placementListenerRecord.PlacementListenerId}\",\"PlacementSystemStatus\":\"Renewing\",\"PlacementSystemID\":\"66\"}}",
            @MarkAsSent = true
        });

        CheckSprocExecutionLog(sprocName: "APIv1.UpdatePlacementSystemStatus", updatedCount: 1);

        dynamic row = GetResultRow(tableName: "dbo.PlacementListener");
        Assert.Equal(expected: 4, actual: row.PlacementSystemStatusId);
        Assert.Equal(expected: false, actual: row.IsReadyToSend);
        Assert.Equal(expected: "66", actual: row.PlacementSystemID);
        Assert.True(row.ValidFrom > DateTime.UtcNow.AddMinutes(-1));
        Assert.Equal(expected: "APIv1.UpdatePlacementSystemStatus", actual: row.LastUpdatedUser);
        dynamic executionLogRow = GetResultRow(tableName: "ADF.StoredProcLog", whereClause: $"StoredProcName = 'APIv1.UpdatePlacementSystemStatus'");
        Assert.Equal(expected:$"Update dbo.PlacementListener for 1 placement listener IDs.", actual: executionLogRow.ActionDescription);
    }

    public UpdatePlacementSystemStatusTests(DatabaseFixture fixture) : base(fixture)
    {
    }
}
