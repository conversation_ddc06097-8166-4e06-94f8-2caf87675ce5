﻿using PsDb.Tests.PlacementStoreHelpers;
using Xunit.Abstractions;

namespace PsDb.Tests.BPStaging.StoredProcedures;
public class LoadContractSectionTests : PlacementStoreSystemVersionedLoadProcedureTestBase
{

    protected override dynamic GetLogicalDeletionValue(dynamic row)
    {
        return row.IsDeleted;
    }

    protected override dynamic GetSourceUpdatedDateValue(dynamic row)
    {
        return row.ValidFrom;
    }

    protected override dynamic GetUpdatedDateValue(dynamic row)
    {
        return row.LastUpdatedUTCDate;
    }
    protected override dynamic GetCreatedDateValue(dynamic row)
    {
        return row.CreatedUTCDate;
    }

    protected override void SetUpExtraRecords(TestType testType)
    {
        dynamic placementRecord = CreateRow(tableName: "dbo.Placement", values: new
        {
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            PlacementSystemId = 123456,
            PlacementStatusId = 1
        });

        dynamic contractRecord = CreateRow(tableName: "PS.Contract", values: new
        {
            PlacementId = placementRecord.PlacementId,
            IsExpiring = 0,
            DisplayIndex = 0,
            IsMultiSection = 0,
            SourceUpdatedDate = DateTime.UtcNow.AddDays(-1),
            ETLCreatedDate = DateTime.UtcNow.AddDays(-1),
            ETLUpdatedDate = DateTime.UtcNow.AddDays(-1),
            IsDeleted = false
        });
    }
    protected override dynamic CreateExistingRecord(TestType testType, dynamic stagingRecord)
    {
        return new
        {
            ContractSectionId = stagingRecord.id,
            DisplayIndex = stagingRecord.DisplayIndex,
            ShortName = "asd",
            ValidFrom = stagingRecord.ValidFrom,
            CreatedUTCDate = DateTime.UtcNow.AddDays(-1),
            LastUpdatedUTCDate = stagingRecord.validFrom,
            IsDeleted = false
        };
    }

    protected override dynamic CreateStagingRecord(TestType testType, DateTime validFrom, DateTime validTo, bool changeSomething)
    {
        return new
        {

            Id = 2,
            DisplayIndex = 234,
            ShortName = changeSomething ? "sad" : "asd",
            ValidFrom = validFrom,
            ValidTo = validTo
        };
    }

    protected override void CheckTargetRecordValues(TestType testType, dynamic stagingRecord, dynamic targetResult)
    {
        Assert.Equal(expected: stagingRecord.Id, actual: targetResult.ContractSectionId);
        Assert.Equal(expected: stagingRecord.DisplayIndex, actual: targetResult.DisplayIndex);
        Assert.Equal(expected: stagingRecord.ShortName, actual: targetResult.ShortName);
    }

    public LoadContractSectionTests(DatabaseFixture fixture, ITestOutputHelper output) : base(fixture: fixture, output: output)
    {
    }
}
