﻿using PsDb.Tests.PlacementStoreHelpers;
using System.ComponentModel.DataAnnotations;
using System.Diagnostics.CodeAnalysis;
using Xunit.Abstractions;

namespace PsDb.Tests.BPStaging.StoredProcedures;
[ExcludeFromCodeCoverage]
public class LoadPlacementOpportunityTests : PlacementStoreSystemVersionedLoadProcedureTestBase
{
    dynamic? _placementRecord;

    /* Additional tests on top of those inherited from PlacementStoreSystemVersionedLoadProcedureTestBase */

    /// <summary>
    /// Because it would cause duplicate rows and doesn't happen (at the moment)
    /// The PlacementOpportunity is constrained to prevent a PlacementId appearing more
    /// than once.
    /// Test that it fails if this happens.
    /// This table holds when the Placement was generated by a CRM opportunity.
    /// OpportunityId is currently really just an integer.
    /// </summary>
    [Fact]
    public void OnlyAllowedOneOpportunityPerPlacementTest()
    {
        _placementRecord = CreateRow(
            tableName: "dbo.Placement",
            values: new
            {
                DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
                PlacementSystemId = 13579
            });

        CreateRow(
            tableName: StagingTableName,
            values: new
            {
                Id = 1,
                PlacementId = _placementRecord.PlacementSystemId,
                OpportunityId = "1000000",
                ValidFrom = DateTime.UtcNow.AddMinutes(-20),
                ValidTo = ValidToOpen
            });

        CreateRow(
            tableName: StagingTableName,
            values: new
            {
                Id = 2,
                PlacementId = _placementRecord.PlacementSystemId,
                OpportunityId = "2000000",
                ValidFrom = DateTime.UtcNow.AddMinutes(-20),
                ValidTo = ValidToOpen
            });

        // Error expected.
        dynamic result = ExecuteStoredProcedureWithResultRow(storedProcedureName: StoredProcedureName);
        var errorMessage = IfRejectedGetSprocError(output: output, rejectedCount: result.RejectedCount, storedProcedureName: StoredProcedureName);
        Assert.True(result.RejectedCount == 1, $"Expected result.RejectedCount to be 1. It was {result.RejectedCount}.");
        Assert.Contains(expectedSubstring: @"Cannot insert duplicate key row in object 'dbo.PlacementOpportunity' with unique index 'IXU_dbo_PlacementOpportunity_PlacementId'", actualString: errorMessage);

        // No row stored.
        dynamic row = GetResultRow(tableName: TargetTableName);
        Assert.Null(row);
    }


    #region Inherited Test Configuration
    protected override void SetUpExtraRecords(TestType testType)
    {
        _placementRecord = CreateRow(
            tableName: "dbo.Placement",
            values: new
            {
                DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
                PlacementSystemId = 13579
            });
    }

    protected override void CheckTargetRecordValues(TestType testType, dynamic stagingRecord, dynamic targetResult)
    {
        Assert.Equal(expected: _placementRecord.PlacementId, actual: targetResult.PlacementId);
        Assert.Equal(expected: stagingRecord.OpportunityId, actual: targetResult.OpportunityId);
    }

    protected override dynamic CreateExistingRecord(TestType testType, dynamic stagingRecord)
    {
        return new
        {
            PlacementOpportunityKey = stagingRecord.Id,
            PlacementId = _placementRecord.PlacementId,
            OpportunityId = "1",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            ETLCreatedDate = DateTime.UtcNow.AddDays(-1),
            ETLUpdatedDate = DateTime.UtcNow.AddDays(-1),
            IsDeleted = false
        };
        ;
    }

    protected override dynamic CreateStagingRecord(TestType testType, DateTime validFrom, DateTime validTo, bool changeSomething)
    {
        return new
        {
            PlacementId = _placementRecord.PlacementSystemId,
            OpportunityId = changeSomething ? "2" : "1",
            ValidFrom = validFrom,
            ValidTo = validTo
        };
    }

    #endregion
    #region Constructor

    /// <summary>
    /// Constructor
    /// </summary>
    /// <param name="fixture"></param>
    /// <param name="output"></param>
    /// <param name="targetTableName"></param>
    /// <param name="storedProcedureName"></param>
    public LoadPlacementOpportunityTests(DatabaseFixture fixture, ITestOutputHelper output) :
        base(fixture, output, stagingTableName: "BPStaging.OpportunityPlacement")
    {
    }
    #endregion

}
