﻿using PsDb.Tests.PlacementStoreHelpers;
using System.ComponentModel.DataAnnotations;
using System.Diagnostics.CodeAnalysis;
using Xunit.Abstractions;

namespace PsDb.Tests.BPStaging.StoredProcedures;

[ExcludeFromCodeCoverage]
public class LoadPlacementPremiumTests : PlacementStoreSystemVersionedLoadProcedureTestBase
{
    private dynamic _placementRecord;

    /// <summary>
    /// If all the amounts are NULL no record is inserted.
    /// </summary>
    [Fact]
    public void IfNoAmountsNoRecordIsInsertedTest()
    {
        SetUpExtraRecords(TestType.InsertTest);

        CreateRow(tableName: StagingTableName,
            values: new
            {
                Id = _placementRecord.PlacementSystemId,
                ValidFrom = DateTime.UtcNow.AddMinutes(-30),
                ValidTo = ValidToOpen
            });

        dynamic result = ExecuteStoredProcedureWithResultRow(storedProcedureName: StoredProcedureName);
        IfRejectedGetSprocError(output: output, rejectedCount: result.RejectedCount, storedProcedureName: StoredProcedureName);
        Assert.True(result.RejectedCount == 0, $"Expected result.RejectedCount to be 0. It was {result.RejectedCount}.");
        Assert.True(result.InsertedCount == 0, $"Expected result.InsertedCount to be 0. It was {result.InsertedCount}.");

        CheckSprocExecutionLog(sprocName: StoredProcedureName);

        dynamic row = GetResultRow(tableName: TargetTableName);
        Assert.Null(row);
    }

    /// <summary>
    /// However if all the staged amounts are NULL, if there is a record it will be updated and the value set to null.
    /// Not quite the same as before which would have deleted it. But as the only use I can find does a left join if the
    /// values are NULL it will behave the same.
    /// </summary>
    [Fact]
    public void IfNoAmountsStagedItWillUpdateTest()
    {
        SetUpExtraRecords(TestType.UpdateTest);

        dynamic stagingRecord = CreateRow(tableName: StagingTableName,
            values: new
            {
                Id = _placementRecord.PlacementSystemId,
                ValidFrom = DateTime.UtcNow.AddMinutes(-30),
                ValidTo = ValidToOpen
            });

        CreateExistingRecordWrapper(TestType.UpdateTest, stagingRecord);

        dynamic result = ExecuteStoredProcedureWithResultRow(storedProcedureName: StoredProcedureName);
        IfRejectedGetSprocError(output: output, rejectedCount: result.RejectedCount, storedProcedureName: StoredProcedureName);
        Assert.True(result.RejectedCount == 0, $"Expected result.RejectedCount to be 0. It was {result.RejectedCount}.");
        Assert.True(result.InsertedCount == 0, $"Expected result.InsertedCount to be 0. It was {result.InsertedCount}.");
        Assert.True(result.UpdatedCount == 1, $"Expected result.UpdatedCount to be 1. It was {result.InsertedCount}.");

        CheckSprocExecutionLog(sprocName: StoredProcedureName, updatedCount: 1);

        dynamic row = GetResultRow(tableName: TargetTableName);
        Assert.NotNull(row);
        Assert.Equal(expected: DBNull.Value, actual: row.DepositPremium);
    }

    #region Inherited Test Configuration
    /// <summary>
    /// Need to seed some values to make these tests work.
    /// </summary>
    /// <param name="testType"></param>
    protected override void SetUpExtraRecords(TestType testType)
    {
        PopulateRefPlacementStatus();

        _placementRecord = CreateRow(tableName: "dbo.Placement", values: new
        {
            PlacementSystemId = 45345,
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
        });
    }

    protected override dynamic CreateStagingRecord(TestType testType, DateTime validFrom, DateTime validTo, bool changeSomething)
    {
        return new
        {
            Id = _placementRecord.PlacementSystemId,
            DepositPremium = changeSomething ? 101.0 : 100.0,
            ValidFrom = validFrom,
            ValidTo = validTo
        };
    }

    protected override dynamic CreateExistingRecord(TestType testType, dynamic stagingRecord)
    {
        return new
        {
            PlacementId = _placementRecord.PlacementId,
            DepositPremium = 100.0,
            CreatedUTCDate = DateTime.UtcNow.AddDays(-1),
            CreatedUser = "FMAUser",
            LastUpdatedUTCDate = DateTime.UtcNow.AddDays(-1),
            LastUpdatedUser = "FMAUser",
        };
    }

    protected override void CheckTargetRecordValues(TestType testType, dynamic stagingRecord, dynamic targetResult)
    {
        Assert.Equal(expected: _placementRecord.PlacementId, actual: targetResult.PlacementId);
        Assert.Equal(expected: "FMAUser", actual: targetResult.CreatedUser);
        Assert.Equal(expected: "FMAUser", actual: targetResult.LastUpdatedUser);
        Assert.Equal(expected: Convert.ToDecimal(stagingRecord.DepositPremium), actual: Convert.ToDecimal(targetResult.DepositPremium));
    }

    protected override dynamic GetCreatedDateValue(dynamic row)
    {
        return row.CreatedUTCDate;
    }

    protected override dynamic GetUpdatedDateValue(dynamic row)
    {
        return row.LastUpdatedUTCDate;
    }
    #endregion

    #region Constructor

    /// <summary>
    /// Constructor - Do not change.
    /// </summary>
    /// <param name="fixture"></param>
    /// <param name="output"></param>
    public LoadPlacementPremiumTests(DatabaseFixture fixture, ITestOutputHelper output) : base(fixture: fixture, output: output, stagingTableName: "BPStaging.Placement")
    {
    }
    #endregion
}
