﻿using PsDb.Tests.PlacementStoreHelpers;
using System.Diagnostics.CodeAnalysis;
using Xunit.Abstractions;

namespace PsDb.Tests.BPStaging.StoredProcedures;

[ExcludeFromCodeCoverage]
public class LoadPlacementTests : PlacementStoreSystemVersionedLoadProcedureTestBase
{
    /* Additional tests on top of those inherited from PlacementStoreSystemVersionedLoadProcedureTestBase */

    /// <summary>
    /// If a servicing platform id is passed it is overridden.
    /// If nothing else matches changed to 50366
    /// </summary>
    [Fact]
    public void OverridesServicingPlatformIdTest()
    {
        SetUpExtraRecords(testType: TestType.InsertTest);

        dynamic stagingRecord = CreateRow(
            tableName: StagingTableName,
            values: new
            {
                Id = 9639,
                PlacementStatusId = (int)PlacementStatus.NotStarted,
                Description = "Description of Placement",
                ServicingPlatformId = (int)DataSourceInstance.EpicCanada,
                ValidFrom = DateTime.UtcNow.AddMinutes(-30),
                ValidTo = ValidToOpen,
            });

        dynamic result = ExecuteStoredProcedureWithResultRow(storedProcedureName: StoredProcedureName);
        IfRejectedGetSprocError(output: output, rejectedCount: result.RejectedCount, storedProcedureName: StoredProcedureName);

        dynamic row = GetResultRow(tableName: TargetTableName);
        Assert.Equal(expected: (int)DataSourceInstance.BrokingPlatform, actual: row.ServicingPlatformId);
    }

    /// <summary>
    /// Servicing platform ID is overridden by region.
    /// Only RegionId match
    /// </summary>
    [Fact]
    public void OverridesServicingPlatformIdFromRegionMinimumMatchTest()
    {
        SetUpExtraRecords(testType: TestType.InsertTest);

        dynamic dataSourceInstanceRecord = CreateRow(tableName: "Reference.DataSourceInstance", values: new { DataSourceInstanceId = (int)DataSourceInstance.BrazilCOL });

        dynamic stagingRecord = CreateRow(
            tableName: StagingTableName,
            values: new
            {
                Id = 9639,
                PlacementStatusId = (int)PlacementStatus.NotStarted,
                Description = "Description of Placement",
                ServicingPlatformId = (int)DataSourceInstance.EpicCanada,
                RegionId = 97531,
                ValidFrom = DateTime.UtcNow.AddMinutes(-30),
                ValidTo = ValidToOpen,
            });

        dynamic regionRecord = CreateRow(
            tableName: "dbo.DataSourceInstanceIdMapping",
            values: new
            {
                BrokingRegionID = stagingRecord.RegionId,
                BrokingSegmentID = -999999,
                BrokingSubSegmentID = -999999,
                InsuranceTypeID = -999999,
                DataSourceInstanceID = (int)DataSourceInstance.BrazilCOL
            });

        dynamic result = ExecuteStoredProcedureWithResultRow(storedProcedureName: StoredProcedureName);
        IfRejectedGetSprocError(output: output, rejectedCount: result.RejectedCount, storedProcedureName: StoredProcedureName);

        dynamic row = GetResultRow(tableName: TargetTableName);
        Assert.Equal(expected: regionRecord.DataSourceInstanceId, actual: row.ServicingPlatformId);
    }

    /// <summary>
    /// Servicing platform ID is overridden by region.
    /// All columns used in match
    /// </summary>
    [Fact]
    public void OverridesServicingPlatformIdFromRegionMaximumMatchTest()
    {
        SetUpExtraRecords(testType: TestType.InsertTest);

        dynamic dataSourceInstanceRecord = CreateRow(tableName: "Reference.DataSourceInstance", values: new { DataSourceInstanceId = (int)DataSourceInstance.BrazilCOL });
        dynamic refInsuranceTypeRecord = CreateRow(tableName: "ref.RefInsuranceType", values: new { });

        dynamic stagingRecord = CreateRow(
            tableName: StagingTableName,
            values: new
            {
                Id = 9639,
                PlacementStatusId = (int)PlacementStatus.NotStarted,
                Description = "Description of Placement",
                ServicingPlatformId = (int)DataSourceInstance.EpicCanada,
                RegionId = 97531,
                BrokingSegmentId = 8642,
                BrokingSubSegmentId = 1379,
                InsuranceTypeId = refInsuranceTypeRecord.RefInsuranceTypeId,
                ValidFrom = DateTime.UtcNow.AddMinutes(-30),
                ValidTo = ValidToOpen,
            });

        dynamic regionRecord = CreateRow(
            tableName: "dbo.DataSourceInstanceIdMapping",
            values: new
            {
                BrokingRegionID = stagingRecord.RegionId,
                BrokingSegmentID = stagingRecord.BrokingSegmentId,
                BrokingSubSegmentID = stagingRecord.BrokingSubSegmentId,
                InsuranceTypeID = stagingRecord.InsuranceTypeId,
                DataSourceInstanceID = (int)DataSourceInstance.BrazilCOL
            });

        dynamic result = ExecuteStoredProcedureWithResultRow(storedProcedureName: StoredProcedureName);
        IfRejectedGetSprocError(output: output, rejectedCount: result.RejectedCount, storedProcedureName: StoredProcedureName);

        dynamic row = GetResultRow(tableName: TargetTableName);
        Assert.Equal(expected: regionRecord.DataSourceInstanceId, actual: row.ServicingPlatformId);
    }
    [Fact]
    public void OverridesServicingPlatformIdFromTeamMaximumMatchTest()
    {
        SetUpExtraRecords(testType: TestType.InsertTest);

        dynamic dataSourceInstanceRecord = CreateRow(tableName: "Reference.DataSourceInstance", values: new { DataSourceInstanceId = (int)DataSourceInstance.BrazilCOL });
        dynamic refInsuranceTypeRecord = CreateRow(tableName: "ref.RefInsuranceType", values: new { });

        dynamic stagingRecord = CreateRow(
            tableName: StagingTableName,
            values: new
            {
                Id = 9639,
                PlacementStatusId = (int)PlacementStatus.NotStarted,
                Description = "Description of Placement",
                RegionId = 97531,
                BrokingSegmentId = 8642,
                BrokingSubSegmentId = 1379,
                InsuranceTypeId = refInsuranceTypeRecord.RefInsuranceTypeId,

                ValidFrom = DateTime.UtcNow.AddMinutes(-30),
                ValidTo = ValidToOpen,
            });

        dynamic PlacementRecord = CreateRow(
            tableName: "dbo.Placement",
            values: new
            {
                PlacementSystemId = stagingRecord.Id,
                DataSourceInstanceID = 50366,
                ServicingPlatFormId = (int)DataSourceInstance.EpicCanada
            });
        dynamic TeamRecord = CreateRow(
            tableName: "ref.Team",
            values: new
            {
                TeamId = 1,
                TeamKey = "1",
                DataSourceInstanceID = dataSourceInstanceRecord.DataSourceInstanceID
            });

        dynamic PlacementTeamRecord = CreateRow(
            tableName: "dbo.PlacementTeams",
            values: new
            {
                PlacementId = PlacementRecord.PlacementId,
                TeamId = TeamRecord.TeamId
            });
        dynamic result = ExecuteStoredProcedureWithResultRow(storedProcedureName: StoredProcedureName);
        IfRejectedGetSprocError(output: output, rejectedCount: result.RejectedCount, storedProcedureName: StoredProcedureName);

        dynamic row = GetResultRow(tableName: TargetTableName);
        Assert.Equal(expected: TeamRecord.DataSourceInstanceId, actual: row.ServicingPlatformId);
    }
    [Fact]
    public void LoadPlacementVerticalIndustryTest()
    {
        dynamic referenceVerticalIndustry = CreateRow(tableName: "reference.VerticalIndustry", values: new
        {
            VerticalIndustryId = 1,
            VerticalIndustryKey = "SIC87VI|1",
            VerticalIndustry = "Existing Aerospace",
            DataSourceInstanceId = (int)DataSourceInstance.ReferenceData,
            IsDeprecated = 0
        });

        dynamic placementStatusRecord = CreateRow(tableName: "ref.PlacementStatus", values: new
        {
            PlacementStatus = "Not Started",
            DataSourceInstanceId = 50366,
            PlacementStatusKey = "1"
        });

        dynamic stagingPlacement = CreateRow(tableName: "BPStaging.Placement", values: new
        {
            Id = 9639,
            PlacementStatusId = 1,
            ValidFrom = DateTime.UtcNow.AddMinutes(-120),
            ValidTo = ValidToOpen,
            VerticalIndustryId = referenceVerticalIndustry.VerticalIndustryId
        });

        dynamic result = ExecuteStoredProcedureWithResultRow(storedProcedureName: "BPStaging.LoadPlacement");
        Assert.Equal(expected: 1, actual: result.InsertedCount);
        Assert.Equal(expected: 0, actual: result.UpdatedCount);
        Assert.Equal(expected: 0, actual: result.DeletedCount);
        Assert.Equal(expected: 0, actual: result.RejectedCount);

        // Expect a log row but without error.
        CheckSprocExecutionLog(sprocName: "BPStaging.LoadPlacement", insertedCount: 1);

        dynamic row = GetResultRow(tableName: TargetTableName);
        Assert.NotNull(row);
        Assert.Equal(expected: (int)DataSourceInstance.BrokingPlatform, actual: row.DataSourceInstanceId);
        Assert.Equal(expected: stagingPlacement.Id, actual: row.PlacementSystemId);
        Assert.Equal(expected: false, actual: row.IsDeleted);
        Assert.Equal(expected: "FMAImport", actual: row.CreatedUser);
        Assert.Equal(expected: "FMAImport", actual: row.LastUpdatedUser);
        Assert.Equal(expected: stagingPlacement.VerticalIndustryId, actual: row.VerticalIndustryId);

        // BAD. This is using the date from the BP not the time the record was inserted into the PS.
        Assert.True(row.CreatedUTCDate > DateTime.UtcNow.AddMinutes(-1));
        Assert.True(row.LastUpdatedUTCDate > DateTime.UtcNow.AddMinutes(-1));
    }

    /// <summary>
    /// The PlacementName is limited to 255 characters.
    /// Ensure it is truncated.
    /// </summary>
    [Fact]
    public void PlacementDescriptionTruncatedTo255Test()
    {
        SetUpExtraRecords(testType: TestType.InsertTest);

        dynamic stagingRecord = CreateRow(
            tableName: StagingTableName,
            values: new
            {
                Id = 9639,
                PlacementStatusId = LookupRefPlacementStatusId(DataSourceInstance.BrokingPlatform, PlacementStatus.NotStarted),
                Description = "0123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789" +
                                "0123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789" +
                                "0123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789",
                ValidFrom = DateTime.UtcNow.AddMinutes(-30),
                ValidTo = ValidToOpen,
            });

        dynamic result = ExecuteStoredProcedureWithResultRow(storedProcedureName: StoredProcedureName);
        IfRejectedGetSprocError(output: output, rejectedCount: result.RejectedCount, storedProcedureName: StoredProcedureName);

        dynamic row = GetResultRow(tableName: TargetTableName);
        Assert.Equal(expected: 300, actual: stagingRecord.Description.Length);
        Assert.Equal(expected: stagingRecord.Description.Substring(0, 255), actual: row.PlacementName);
        Assert.Equal(expected: 255, actual: row.PlacementName.Length);
    }

    /// <summary>
    /// If you have 2 placements and one is renewd from the other, then the renewed 2
    /// is just the other way round.
    /// </summary>
    [Fact]
    public void RenewedToAndFromPlacementIfExistingPlacementTest()
    {
        SetUpExtraRecords(testType: TestType.InsertTest);

        dynamic renewedFromPlacementRecord = CreateRow(
            tableName: TargetTableName,
            values: new
            {
                DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
                PlacementSystemId = 97531
            });

        dynamic stagingRecord = CreateRow(
            tableName: StagingTableName,
            values: new
            {
                Id = 9639,
                PlacementStatusId = LookupRefPlacementStatusId(DataSourceInstance.BrokingPlatform, PlacementStatus.NotStarted),
                Description = "Description of Placement",
                RenewedFromPlacementId = renewedFromPlacementRecord.PlacementSystemId,
                ValidFrom = DateTime.UtcNow.AddMinutes(-30),
                ValidTo = ValidToOpen,
            });

        dynamic result = ExecuteStoredProcedureWithResultRow(storedProcedureName: StoredProcedureName);
        IfRejectedGetSprocError(output: output, rejectedCount: result.RejectedCount, storedProcedureName: StoredProcedureName);

        dynamic row1 = GetResultRow(tableName: TargetTableName, whereClause: $"PlacementId <> {renewedFromPlacementRecord.PlacementId}");
        dynamic row2 = GetResultRow(tableName: TargetTableName, whereClause: $"PlacementId = {renewedFromPlacementRecord.PlacementId}");
        Assert.Equal(expected: row1.PlacementId, actual: row2.RenewedToPlacementId);
        Assert.Equal(expected: row2.PlacementId, actual: row1.RenewedFromPlacementId);
        Assert.NotEqual(expected: row2.PlacementId, actual: row2.RenewedToPlacementId);
        Assert.NotEqual(expected: row1.PlacementId, actual: row1.RenewedFromPlacementId);
    }

    [Fact]
    public void RenewedToAndFromPlacementIfExistingPlacementUpdatesValueTest()
    {
        SetUpExtraRecords(testType: TestType.InsertTest);

        dynamic renewedFromPlacementRecord = CreateRow(
            tableName: TargetTableName,
            values: new
            {
                DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
                PlacementSystemId = 97531,
                RenewedToPlacementId = 666
            });

        dynamic stagingRecord = CreateRow(
            tableName: StagingTableName,
            values: new
            {
                Id = 9639,
                PlacementStatusId = LookupRefPlacementStatusId(DataSourceInstance.BrokingPlatform, PlacementStatus.NotStarted),
                Description = "Description of Placement",
                RenewedFromPlacementId = renewedFromPlacementRecord.PlacementSystemId,
                ValidFrom = DateTime.UtcNow.AddMinutes(-30),
                ValidTo = ValidToOpen,
            });

        dynamic result = ExecuteStoredProcedureWithResultRow(storedProcedureName: StoredProcedureName);
        IfRejectedGetSprocError(output: output, rejectedCount: result.RejectedCount, storedProcedureName: StoredProcedureName);

        dynamic row1 = GetResultRow(tableName: TargetTableName, whereClause: $"PlacementId <> {renewedFromPlacementRecord.PlacementId}");
        dynamic row2 = GetResultRow(tableName: TargetTableName, whereClause: $"PlacementId = {renewedFromPlacementRecord.PlacementId}");
        Assert.Equal(expected: row1.PlacementId, actual: row2.RenewedToPlacementId);
        Assert.Equal(expected: row2.PlacementId, actual: row1.RenewedFromPlacementId);
        Assert.NotEqual(expected: row2.PlacementId, actual: row2.RenewedToPlacementId);
        Assert.NotEqual(expected: row1.PlacementId, actual: row1.RenewedFromPlacementId);
    }

    /// <summary>
    /// For this one even if both records are coming in via staging this will still
    /// link them together.
    /// </summary>
    [Fact]
    public void RenewedToAndFromPlacementIdBothStagedPlacementTest()
    {
        SetUpExtraRecords(testType: TestType.InsertTest);

        dynamic stagingRecord1 = CreateRow(
            tableName: StagingTableName,
            values: new
            {
                Id = 9639,
                PlacementStatusId = LookupRefPlacementStatusId(DataSourceInstance.BrokingPlatform, PlacementStatus.NotStarted),
                Description = "Description of Placement 1",
                RenewedFromPlacementId = 7010,
                ValidFrom = DateTime.UtcNow.AddMinutes(-30),
                ValidTo = ValidToOpen,
            });
        dynamic stagingRecord2 = CreateRow(
            tableName: StagingTableName,
            values: new
            {
                Id = 7010,
                PlacementStatusId = LookupRefPlacementStatusId(DataSourceInstance.BrokingPlatform, PlacementStatus.Complete),
                Description = "Description of Placement 2",
                ValidFrom = DateTime.UtcNow.AddMinutes(-40),
                ValidTo = ValidToOpen,
            });

        dynamic result = ExecuteStoredProcedureWithResultRow(storedProcedureName: StoredProcedureName);
        IfRejectedGetSprocError(output: output, rejectedCount: result.RejectedCount, storedProcedureName: StoredProcedureName);
        Assert.Equal(expected: 2, actual: result.InsertedCount);

        dynamic row1 = GetResultRow(tableName: TargetTableName, whereClause: $"PlacementSystemId = {stagingRecord1.Id}");
        dynamic row2 = GetResultRow(tableName: TargetTableName, whereClause: $"PlacementSystemId = {stagingRecord2.Id}");
        Assert.Equal(expected: row1.PlacementId, actual: row2.RenewedToPlacementId);
        Assert.Equal(expected: row2.PlacementId, actual: row1.RenewedFromPlacementId);
        Assert.NotEqual(expected: row2.PlacementId, actual: row2.RenewedToPlacementId);
        Assert.NotEqual(expected: row1.PlacementId, actual: row1.RenewedFromPlacementId);
        Assert.Equal(expected: DBNull.Value, actual: row1.RenewedToPlacementId);
        Assert.Equal(expected: DBNull.Value, actual: row2.RenewedFromPlacementId);
    }

    /// <summary>
    /// If No other link to the RenewedFromPlacementId but PsId has a value use that.
    /// </summary>
    [Fact]
    public void RenewedFromPlacementIdFromPsIdTest()
    {
        SetUpExtraRecords(testType: TestType.InsertTest);

        dynamic stagingRecord = CreateRow(
            tableName: StagingTableName,
            values: new
            {
                Id = 9639,
                PlacementStatusId = LookupRefPlacementStatusId(DataSourceInstance.BrokingPlatform, PlacementStatus.NotStarted),
                Description = "Description of Placement",
                PsId = 987321,
                ValidFrom = DateTime.UtcNow.AddMinutes(-30),
                ValidTo = ValidToOpen,
            });

        dynamic result = ExecuteStoredProcedureWithResultRow(storedProcedureName: StoredProcedureName);
        IfRejectedGetSprocError(output: output, rejectedCount: result.RejectedCount, storedProcedureName: StoredProcedureName);

        dynamic row = GetResultRow(tableName: TargetTableName);
        Assert.Equal(expected: stagingRecord.PsId, actual: row.RenewedFromPlacementId);
    }

    /// <summary>
    /// We need to hold a time value of 24:00 for a business reason. A TIME column can't hold it
    /// so we have to put it in a text column. Using an NCHAR(5).
    /// This checks that we can push in values we support
    /// </summary>
    /// <param name="inceptionTime"></param>
    /// <param name="expiryTime"></param>
    [Theory]
    [InlineData("13:00", "14:00")]
    [InlineData("00:00", "00:00")]
    [InlineData("24:00", "24:00")]
    public void InceptionAndExpiryVariousTimesTest(string inceptionTime, string expiryTime)
    {
        SetUpExtraRecords(testType: TestType.InsertTest);

        dynamic stagingRecord = CreateRow(
            tableName: StagingTableName,
            values: new
            {
                Id = 9639,
                PlacementStatusId = LookupRefPlacementStatusId(DataSourceInstance.BrokingPlatform, PlacementStatus.NotStarted),
                Description = "Description of Placement",
                PsId = 987321,
                InceptionStartTime = inceptionTime,
                ExpiryStartTime = expiryTime,
                ValidFrom = DateTime.UtcNow.AddMinutes(-30),
                ValidTo = ValidToOpen,
            });

        dynamic result = ExecuteStoredProcedureWithResultRow(storedProcedureName: StoredProcedureName);
        IfRejectedGetSprocError(output: output, rejectedCount: result.RejectedCount, storedProcedureName: StoredProcedureName);

        dynamic row = GetResultRow(tableName: TargetTableName);
        Assert.Equal(expected: stagingRecord.PsId, actual: row.RenewedFromPlacementId);
        Assert.Equal(expected: inceptionTime.Substring(startIndex: 0, length: 5), actual: row.InceptionStartTime);
        Assert.Equal(expected: expiryTime.Substring(startIndex: 0, length: 5), actual: row.ExpiryStartTime);
    }

    /// <summary>
    /// We need to hold a time value of 24:00 for a business reason. A TIME column can't hold it
    /// so we have to put it in a text column. Using an NCHAR(5). This checks that we can't push various
    /// invalid values into it.
    /// </summary>
    /// <param name="inceptionTime"></param>
    /// <param name="expiryTime"></param>
    [Theory]
    [InlineData("24:01", "00:00")]
    [InlineData("aa:aa", "00:00")]
    [InlineData("99:99", "00:00")]
    [InlineData("trash", "00:00")]
    [InlineData("0000", "00:00")]
    [InlineData("00:60", "00:00")]
    [InlineData("00:00", "24:01")]
    [InlineData("00:00", "aa:aa")]
    [InlineData("00:00", "99:99")]
    [InlineData("00:00", "trash")]
    [InlineData("00:00", "0000")]
    [InlineData("00:00", "00:60")]
    public void InceptionAndExpiryVariousFailingTimesTest(string inceptionTime, string expiryTime)
    {
        SetUpExtraRecords(testType: TestType.InsertTest);

        dynamic stagingRecord = CreateRow(
            tableName: StagingTableName,
            values: new
            {
                Id = 9639,
                PlacementStatusId = LookupRefPlacementStatusId(DataSourceInstance.BrokingPlatform, PlacementStatus.NotStarted),
                Description = "Description of Placement",
                PsId = 987321,
                InceptionStartTime = inceptionTime,
                ExpiryStartTime = expiryTime,
                ValidFrom = DateTime.UtcNow.AddMinutes(-30),
                ValidTo = ValidToOpen,
            });

        dynamic result = ExecuteStoredProcedureWithResultRow(storedProcedureName: StoredProcedureName);
        var errorMessage = IfRejectedGetSprocError(output: output, rejectedCount: result.RejectedCount, storedProcedureName: StoredProcedureName);
        //dynamic executionLogRow = GetResultRow(tableName: "ADF.StoredProcLog", whereClause: $"StoredProcName = '{StoredProcedureName}'");
        Assert.Equal(expected: 1, actual: result.RejectedCount);
        Assert.Contains(expectedSubstring: @"The MERGE statement conflicted with the CHECK constraint", actualString: errorMessage);

        dynamic row = GetResultRow(tableName: TargetTableName);
        Assert.Null(row);
    }

    /// <summary>
    /// Ensure that the MTACreatedFromPlacementId is updated for Placements not yet created
    /// in PlacementStore and linking it to itself
    /// </summary>
    [Fact]
    public void MTACreatedFromPlacementIdTest()
    {
        SetUpExtraRecords(testType: TestType.InsertTest);

        dynamic stagingRecord1 = CreateRow(
           tableName: StagingTableName,
           values: new
           {
               Id = 10000,
               PlacementStatusId = LookupRefPlacementStatusId(DataSourceInstance.BrokingPlatform, PlacementStatus.NotStarted),
               Description = "MTA Placement from New Placement",
               MTACreatedFromPlacementId = 10001,
               MTATypeId = 1,
               ValidFrom = DateTime.UtcNow.AddMinutes(-30),
               ValidTo = ValidToOpen,
           });
        dynamic stagingRecord2 = CreateRow(
            tableName: StagingTableName,
            values: new
            {
                Id = 10001,
                PlacementStatusId = LookupRefPlacementStatusId(DataSourceInstance.BrokingPlatform, PlacementStatus.Complete),
                Description = "New Placement",
                ValidFrom = DateTime.UtcNow.AddMinutes(-40),
                ValidTo = ValidToOpen,
            });

        dynamic result = ExecuteStoredProcedureWithResultRow(storedProcedureName: StoredProcedureName);
        IfRejectedGetSprocError(output: output, rejectedCount: result.RejectedCount, storedProcedureName: StoredProcedureName);
        Assert.Equal(expected: 2, actual: result.InsertedCount);

        dynamic row1 = GetResultRow(tableName: TargetTableName, whereClause: $"PlacementSystemId = {stagingRecord1.Id}");
        dynamic row2 = GetResultRow(tableName: TargetTableName, whereClause: $"PlacementSystemId = {stagingRecord2.Id}");
        Assert.Equal(expected: row2.PlacementId, actual: row1.MTACreatedFromPlacementId);
        Assert.Equal(expected: DBNull.Value, actual: row2.MTACreatedFromPlacementId);
    }

    #region Inherited Test Configuration
    /// <summary>
    /// Need to seed some values to make these tests work.
    /// </summary>
    /// <param name="testType"></param>
    protected override void SetUpExtraRecords(TestType testType)
    {
        PopulateRefPlacementStatus();
    }

    protected override dynamic CreateStagingRecord(TestType testType, DateTime validFrom, DateTime validTo, bool changeSomething)
    {
        return new
        {
            Id = 9639,
            PlacementStatusId = (int)PlacementStatus.NotStarted,
            Description = changeSomething ? "Description of Placement" : "Old Description",
            ValidFrom = validFrom,
            ValidTo = validTo,
        };
    }

    protected override dynamic CreateExistingRecord(TestType testType, dynamic stagingRecord)
    {
        return new
        {
            PlacementName = "Old Description",
            PlacementSystemId = stagingRecord.Id,
            PlacementStatusId = LookupRefPlacementStatusId(dataSourceInstance: DataSourceInstance.BrokingPlatform, placementStatusKey: stagingRecord.PlacementStatusId),
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            CreatedUTCDate = DateTime.UtcNow.AddDays(-1),
            LastUpdatedUTCDate = DateTime.UtcNow.AddDays(-1),
        };
    }

    protected override void CheckTargetRecordValues(TestType testType, dynamic stagingRecord, dynamic targetResult)
    {
        Assert.Equal(expected: (int)DataSourceInstance.BrokingPlatform, actual: targetResult.DataSourceInstanceId);
        Assert.Equal(expected: stagingRecord.Id, actual: targetResult.PlacementSystemId);
        Assert.Equal(expected: "FMAImport", actual: targetResult.LastUpdatedUser);
        Assert.Equal(expected: stagingRecord.Description, actual: targetResult.PlacementName);
        Assert.Equal(expected: LookupRefPlacementStatusId(dataSourceInstance: DataSourceInstance.BrokingPlatform, placementStatusKey: stagingRecord.PlacementStatusId), actual: targetResult.PlacementStatusId);
    }

    protected override dynamic GetCreatedDateValue(dynamic row)
    {
        return row.CreatedUTCDate;
    }

    protected override dynamic GetUpdatedDateValue(dynamic row)
    {
        return row.LastUpdatedUTCDate;
    }

    /// <summary>
    /// There are no dependencies for these tables as they are scripted.
    /// </summary>
    /// <returns></returns>
    protected override IList<string> GetCoreLoadProcedureCheckADFDependencyTestTablesToExclude()
    {
        return new List<string> { "dbo.DataSourceInstanceIdMapping"
            , "dbo.PlacementCompletionStatus"
            , "dbo.PlacementTeams" /* Technically a circular reference as PlacementTeams is also dependent on Placement. */
            , "ref.RenewableOption"
            , "Reference.NotRemarketingReason" };
    }

    #endregion

    #region Constructor

    /// <summary>
    /// Constructor - Do not change.
    /// </summary>
    /// <param name="fixture"></param>
    /// <param name="output"></param>
    public LoadPlacementTests(DatabaseFixture fixture, ITestOutputHelper output) : base(fixture, output)
    {
    }
    #endregion
}
