﻿using PsDb.Tests.PlacementStoreHelpers;
using System.Diagnostics.CodeAnalysis;
using Xunit.Abstractions;

namespace PsDb.Tests.BPStaging.StoredProcedures;

[ExcludeFromCodeCoverage]
public class Load_BP_AffirmationQuestionTests : PlacementStoreSystemVersionedLoadProcedureTestBase
{
    protected override void CheckTargetRecordValues(TestType testType, dynamic stagingRecord, dynamic targetResult)
    {
        Assert.Equal(expected: stagingRecord.Id, actual: targetResult.Id);
        Assert.Equal(expected: stagingRecord.AffirmationQuestionSetId, actual: targetResult.AffirmationQuestionSetId);
        Assert.Equal(expected: stagingRecord.LabelTranslationDescription, actual: targetResult.LabelTranslationDescription);
        Assert.Equal(expected: stagingRecord.Url, actual: targetResult.Url);
        Assert.Equal(expected: stagingRecord.UrlCaptionTranslationText, actual: targetResult.UrlCaptionTranslationText);
        Assert.Equal(expected: stagingRecord.RequiredAnswer, actual: targetResult.RequiredAnswer);
    }

    protected override void SetUpExtraRecords(TestType testType)
    {

    }

    protected override dynamic CreateExistingRecord(TestType testType, dynamic stagingRecord)
    {
        return new
            {
                Id = 1,
                AffirmationQuestionSetId = 1,
                LabelTranslationDescription = "Question?",
                Url = stagingRecord.Url,
                UrlCaptionTranslationText = stagingRecord.UrlCaptionTranslationText,
                RequiredAnswer = stagingRecord.RequiredAnswer,
                SourceUpdatedDate = stagingRecord.ValidFrom,
                ETLCreatedDate = DateTime.UtcNow.AddDays(-1),
                ETLUpdatedDate = DateTime.UtcNow.AddDays(-1),
                IsDeprecated = false
            };
    }

    protected override dynamic CreateStagingRecord(TestType testType, DateTime validFrom, DateTime validTo, bool changeSomething)
    {
        return new
             {
                 Id = 1,
                 AffirmationQuestionSetId = 1,
                 LabelTranslationDescription = changeSomething ? "Question reworded?" : "Question?",
                 RequiredAnswer = true,
                 Url = "https://example.com",
                 UrlCaptionTranslationText = "Example",
                 IsDeprecated = false,
                 ValidFrom = validFrom,
                 ValidTo = validTo
             };
    }

    protected override object GetLogicalDeletionValue(dynamic row)
    {
        return row.IsDeprecated;
    }

    public Load_BP_AffirmationQuestionTests(DatabaseFixture fixture, ITestOutputHelper output, string? targetTableName = null, string? storedProcedureName = null, string? stagingTableName = null) : base(fixture, output, targetTableName, storedProcedureName, stagingTableName)
    {
    }

}
