﻿using PsDb.Tests.PlacementStoreHelpers;
using System.Diagnostics.CodeAnalysis;
using Xunit.Abstractions;

namespace PsDb.Tests.BPStaging.StoredProcedures;
[ExcludeFromCodeCoverage]
public class Load_BP_StrategyProposedSignedLineTests : PlacementStoreSystemVersionedLoadProcedureTestBase
{
    protected override void SetUpExtraRecords(TestType testType)
    {
        dynamic placementRecord = CreateRow(tableName: "dbo.Placement", values: new
        {
            PlacementId = 0
        });

        CreateRow(tableName: "BP.Strategy", values: new
        {
            Id = 0,
            PlacementId = placementRecord.PlacementId
        });

        CreateRow(tableName: "PS.MarketQuoteResponse", values: new
        {
            MarketQuoteResponseId = 0
        });
    }
    protected override dynamic CreateExistingRecord(TestType testType, dynamic stagingRecord)
    {
        return new
        {
            Id = stagingRecord.Id,
            StrategyId = 0,
            MarketQuoteResponseId = 0,
            SignedLine = 1.2345,
            SignedLineRate = 1.234567,
            SignedLineRateOfOrder = 1.234567,
            SourceUpdatedDate = stagingRecord.ValidFrom,
            ETLCreatedDate = DateTime.UtcNow.AddDays(-1),
            ETLUpdatedDate = DateTime.UtcNow.AddDays(-1),
            IsDeleted = false
        };
    }

    protected override dynamic CreateStagingRecord(TestType testType, DateTime validFrom, DateTime validTo, bool changeSomething)
    {
        return new
        {

            Id = 15,
            SignedLine = changeSomething ? 5.4321 : 1.2345,
            SignedLineRate = changeSomething ? 7.654321 : 1.234567,
            SignedLineRateOfOrder = changeSomething ? 7.654321 : 1.234567,
            ValidFrom = validFrom,
            ValidTo = validTo
        };
    }

    protected override void CheckTargetRecordValues(TestType testType, dynamic stagingRecord, dynamic targetResult)
    {
        Assert.Equal(expected: stagingRecord.Id, actual: targetResult.Id);
        Assert.Equal(expected: (decimal) stagingRecord.SignedLine, actual: (decimal) targetResult.SignedLine);
        Assert.Equal(expected: (decimal) stagingRecord.SignedLineRate, actual: (decimal) targetResult.SignedLineRate);
        Assert.Equal(expected: (decimal) stagingRecord.SignedLineRateOfOrder, actual: (decimal) targetResult.SignedLineRateOfOrder);
    }

    public Load_BP_StrategyProposedSignedLineTests(DatabaseFixture fixture, ITestOutputHelper output) : base(fixture: fixture, output: output)
    {
    }
}
