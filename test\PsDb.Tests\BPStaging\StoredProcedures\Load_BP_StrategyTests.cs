﻿using PsDb.Tests.PlacementStoreHelpers;
using System.Diagnostics.CodeAnalysis;
using Xunit.Abstractions;

namespace PsDb.Tests.BPStaging.StoredProcedures;
[ExcludeFromCodeCoverage]
public class Load_BP_StrategyTests : PlacementStoreSystemVersionedLoadProcedureTestBase
{
    protected override void SetUpExtraRecords(TestType testType)
    {
        CreateRow(tableName: "dbo.Placement", values: new
        {
            PlacementId = 0
        });
    }
    protected override dynamic CreateExistingRecord(TestType testType, dynamic stagingRecord)
    {
        return new
        {
            Id = stagingRecord.Id,
            PlacementId = 0,
            Name = "Top Strat",
            DisplayIndex = 5,
            Accepted = false,
            SourceUpdatedDate = stagingRecord.ValidFrom,
            ETLCreatedDate = DateTime.UtcNow.AddDays(-1),
            ETLUpdatedDate = DateTime.UtcNow.AddDays(-1),
            IsDeleted = false
        };
    }

    protected override dynamic CreateStagingRecord(TestType testType, DateTime validFrom, DateTime validTo, bool changeSomething)
    {
        return new
        {

            Id = 15,
            Name = changeSomething ? "diff Strat" : "Top Strat",
            DisplayIndex = changeSomething ? 2 : 5,
            Accepted = changeSomething ? true : false,
            ValidFrom = validFrom,
            ValidTo = validTo
        };
    }

    protected override void CheckTargetRecordValues(TestType testType, dynamic stagingRecord, dynamic targetResult)
    {
        Assert.Equal(expected: stagingRecord.Id, actual: targetResult.Id);
        Assert.Equal(expected: stagingRecord.Name, actual: targetResult.Name);
        Assert.Equal(expected: stagingRecord.DisplayIndex, actual: targetResult.DisplayIndex);
        Assert.Equal(expected: stagingRecord.Accepted, actual: targetResult.Accepted);
    }

    public Load_BP_StrategyTests(DatabaseFixture fixture, ITestOutputHelper output) : base(fixture: fixture, output: output)
    {
    }
}
