﻿using PsDb.Tests.PlacementStoreHelpers;
using System.Diagnostics.CodeAnalysis;
using Xunit.Abstractions;

namespace PsDb.Tests.BPStaging.StoredProcedures;

[ExcludeFromCodeCoverage]
public class Load_BP_UserEnhancedBrokerDetailsTests : PlacementStoreFullLoadProcedureTestBase
{
    static string storedProcedureName = "BPStaging.Load_BP_UserEnhancedBrokerDetails";

    [Fact]
    public void Load_BP_UserEnhancedBrokerDetailsNoDataTest()
    {
        dynamic result = ExecuteStoredProcedureWithResultRow(storedProcedureName: storedProcedureName);
        Assert.NotNull(result);
        Assert.Equal(expected: 0, actual: result.RejectedCount);
        Assert.Equal(expected: 0, actual: result.DeletedCount);
        Assert.Equal(expected: 0, actual: result.InsertedCount);
        Assert.Equal(expected: 0, actual: result.UpdatedCount);
        CheckSprocExecutionLog(sprocName: storedProcedureName);
    }

    protected override dynamic CreateStagingRecord(TestType testType, DateTime sourceUpdatedDate, bool changeSomething)
    {
        return new
        {
            UserId = 1234,
            BrokerNumber = changeSomething ? "B000083307" : "B000083306",
            BrokerRegistrationNumber = changeSomething ? "000282224" : "000282225",
            BrokerRegistrationDate = changeSomething ? DateTime.Parse("2024-12-31") : DateTime.Parse("2025-01-05"),
            BrokerRegistrationType = changeSomething ? "A" : "E",
            BrokerQualification = changeSomething ? "Broking Certified" : "Broking Specialist",
            TelephoneNumber = changeSomething ? "+290246687375" : "+390247787374",
        };
    }

    protected override dynamic CreateExistingRecord(TestType testType, dynamic stagingRecord)
    {
        return new
        {
            UserId = 1234,
            BrokerNumber = "B000083306",
            BrokerRegistrationNumber = "000282225",
            BrokerRegistrationDate = DateTime.Parse("2025-01-05"),
            BrokerRegistrationType = "E",
            BrokerQualification = "Broking Specialist",
            TelephoneNumber = "+390247787374",
            ETLCreatedDate = DateTime.UtcNow.AddHours(-1),
            ETLUpdatedDate = DateTime.UtcNow.AddHours(-1),
            IsDeleted = false,
        };
    }
    protected override void CheckTargetRecordValues(TestType testType, dynamic stagingRecord, dynamic targetResult)
    {
    }

    protected override object GetLogicalDeletionValue(dynamic row)
    {
        return row.IsDeleted;
    }

    [Fact]
    public void CreatesAUserEnhancedBrokerDetailsTest()
    {
        dynamic stagedUserEnhancedBrokerDetailsRecord = CreateRow(tableName: "BPStaging.UserEnhancedBrokerDetails", values: new
        {
            UserId = 1234,
            BrokerNumber = "B000083306",
            BrokerRegistrationNumber = "000282225",
            BrokerRegistrationDate = DateTime.Parse("2025-01-05"),
            BrokerRegistrationType = "E",
            BrokerQualification = "Broking Specialist",
            TelephoneNumber = "+390247787374"
        });

        dynamic spResult = ExecuteStoredProcedureWithResultRow(storedProcedureName: "BPStaging.Load_BP_UserEnhancedBrokerDetails");
        Assert.Equal(expected: 1, actual: spResult.InsertedCount);
        Assert.Equal(expected: 0, actual: spResult.UpdatedCount);
        Assert.Equal(expected: 0, actual: spResult.DeletedCount);
        Assert.Equal(expected: 0, actual: spResult.RejectedCount);
        CheckSprocExecutionLog(sprocName: "BPStaging.Load_BP_UserEnhancedBrokerDetails", insertedCount: 1);

        dynamic row = GetResultRow(tableName: "BP.UserEnhancedBrokerDetails");
        Assert.NotNull(row);
        Assert.Equal(expected: stagedUserEnhancedBrokerDetailsRecord.UserId, actual: row.UserId);
        Assert.Equal(expected: stagedUserEnhancedBrokerDetailsRecord.BrokerNumber, actual: row.BrokerNumber);
        Assert.Equal(expected: stagedUserEnhancedBrokerDetailsRecord.BrokerRegistrationNumber, actual: row.BrokerRegistrationNumber);
        Assert.Equal(expected: stagedUserEnhancedBrokerDetailsRecord.BrokerRegistrationDate, actual: row.BrokerRegistrationDate);
        Assert.Equal(expected: stagedUserEnhancedBrokerDetailsRecord.BrokerRegistrationType, actual: row.BrokerRegistrationType);
        Assert.Equal(expected: stagedUserEnhancedBrokerDetailsRecord.BrokerQualification, actual: row.BrokerQualification);
        Assert.Equal(expected: stagedUserEnhancedBrokerDetailsRecord.TelephoneNumber, actual: row.TelephoneNumber);
        Assert.True(row.ETLCreatedDate > DateTime.UtcNow.AddMinutes(-1));
        Assert.True(row.ETLUpdatedDate > DateTime.UtcNow.AddMinutes(-1));
        Assert.Equal(expected: false, actual: row.IsDeleted);

    }

    [Fact]
    public void UpdatesAUserEnhancedBrokerDetailsTest()
    {
        dynamic stagedUserEnhancedBrokerDetailsRecord = CreateRow(tableName: "BPStaging.UserEnhancedBrokerDetails", values: new
        {
            UserId = 1234,
            BrokerNumber = "B000083306",
            BrokerRegistrationNumber = "000282225",
            BrokerRegistrationDate = DateTime.Parse("2025-01-05"),
            BrokerRegistrationType = "E",
            BrokerQualification = "Broking Specialist",
            TelephoneNumber = "+390247787374"
        });

        dynamic userEnhancedBrokerDetailsRecord = CreateRow(tableName: "BP.UserEnhancedBrokerDetails", values: new
        {
            UserId = 1234,
            BrokerNumber = DBNull.Value,
            BrokerRegistrationNumber = DBNull.Value,
            BrokerRegistrationDate = DBNull.Value,
            BrokerRegistrationType = DBNull.Value,
            BrokerQualification = DBNull.Value,
            TelephoneNumber = DBNull.Value,
            IsDeleted = false
        });

        dynamic spResult = ExecuteStoredProcedureWithResultRow(storedProcedureName: "BPStaging.Load_BP_UserEnhancedBrokerDetails");
        Assert.Equal(expected: 0, actual: spResult.InsertedCount);
        Assert.Equal(expected: 1, actual: spResult.UpdatedCount);
        Assert.Equal(expected: 0, actual: spResult.DeletedCount);
        Assert.Equal(expected: 0, actual: spResult.RejectedCount);
        CheckSprocExecutionLog(sprocName: "BPStaging.Load_BP_UserEnhancedBrokerDetails", updatedCount: 1);

        dynamic row = GetResultRow(tableName: "BP.UserEnhancedBrokerDetails");
        Assert.NotNull(row);
        Assert.Equal(expected: stagedUserEnhancedBrokerDetailsRecord.UserId, actual: row.UserId);
        Assert.Equal(expected: stagedUserEnhancedBrokerDetailsRecord.BrokerNumber, actual: row.BrokerNumber);
        Assert.Equal(expected: stagedUserEnhancedBrokerDetailsRecord.BrokerRegistrationNumber, actual: row.BrokerRegistrationNumber);
        Assert.Equal(expected: stagedUserEnhancedBrokerDetailsRecord.BrokerRegistrationDate, actual: row.BrokerRegistrationDate);
        Assert.Equal(expected: stagedUserEnhancedBrokerDetailsRecord.BrokerRegistrationType, actual: row.BrokerRegistrationType);
        Assert.Equal(expected: stagedUserEnhancedBrokerDetailsRecord.BrokerQualification, actual: row.BrokerQualification);
        Assert.Equal(expected: stagedUserEnhancedBrokerDetailsRecord.TelephoneNumber, actual: row.TelephoneNumber);
        Assert.True(row.ETLUpdatedDate > DateTime.UtcNow.AddMinutes(-1));
        Assert.Equal(expected: false, actual: row.IsDeleted);
    }

    [Fact]
    public void DeletesAUserEnhancedBrokerDetailsTest()
    {
        dynamic stagedUserEnhancedBrokerDetailsRecord = CreateRow(tableName: "BPStaging.UserEnhancedBrokerDetails", values: new
        {
            UserId = 12345,
            BrokerNumber = "B000083306",
            BrokerRegistrationNumber = "000282225",
            BrokerRegistrationDate = "2025-01-05",
            BrokerRegistrationType = "E",
            BrokerQualification = "Broking Specialist",
            TelephoneNumber = "+390247787374"
        });

        dynamic userEnhancedBrokerDetailsRecord = CreateRow(tableName: "BP.UserEnhancedBrokerDetails", values: new
        {
            UserId = 1234,
            IsDeleted = false
        });

        dynamic spResult = ExecuteStoredProcedureWithResultRow(storedProcedureName: "BPStaging.Load_BP_UserEnhancedBrokerDetails");
        Assert.Equal(expected: 1, actual: spResult.InsertedCount);
        Assert.Equal(expected: 1, actual: spResult.UpdatedCount);
        Assert.Equal(expected: 0, actual: spResult.DeletedCount);
        Assert.Equal(expected: 0, actual: spResult.RejectedCount);
        CheckSprocExecutionLog(sprocName: "BPStaging.Load_BP_UserEnhancedBrokerDetails", insertedCount: 1, updatedCount: 1);

        dynamic row = GetResultRow(tableName: "BP.UserEnhancedBrokerDetails", whereClause: "UserId = " + userEnhancedBrokerDetailsRecord.UserId);
        Assert.NotNull(row);
        Assert.True(row.ETLUpdatedDate > DateTime.UtcNow.AddMinutes(-1));
        Assert.Equal(expected: true, actual: row.IsDeleted);
    }

    #region Constructor
    public Load_BP_UserEnhancedBrokerDetailsTests(DatabaseFixture fixture, ITestOutputHelper output, string? targetTableName = @"BP.UserEnhancedBrokerDetails", string? storedProcedureName = null, string? stagingTableName = null) : base(fixture, output, targetTableName, storedProcedureName, stagingTableName)
    {
    }
    #endregion
}
