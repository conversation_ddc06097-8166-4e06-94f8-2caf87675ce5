﻿using PsDb.Tests.PlacementStoreHelpers;
using System.Diagnostics.CodeAnalysis;

namespace PsDb.Tests.BPStaging.StoredProcedures;

[ExcludeFromCodeCoverage]
public class Load_PS_ContractStatusHistoryTests : PlacementStoreTestBase
{
    [Fact]
    public void UpdatedFromContractStagingTest()
    {
        CreateRow(tableName: "ref.ContractStatus", values: new
        {
            ContractStatusId = (int)ContractStatus.Draft,
            ContractStatusKey = (int)ContractStatus.Draft,
            ContractStatus = "Draft",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform
        });

        dynamic contractRecord = CreateRow(
            tableName: "PS.Contract",
            values: new
            {
                DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform
            });

        dynamic contractStagingRecord = CreateRow(
            tableName: "BPStaging.Contract",
            values: new
            {
                Id = contractRecord.ContractID,
                ContractStatusId = (int)ContractStatus.Draft,
                ValidFrom = DateTime.UtcNow.AddMinutes(-20).WithPrecision(2),
                ValidTo = ValidToOpen
            });

        dynamic result = ExecuteStoredProcedureWithResultRow(storedProcedureName: "BPStaging.Load_PS_ContractStatusHistory");
        Assert.Equal(expected: 0, actual: result.RejectedCount);
        Assert.Equal(expected: 1, actual: result.InsertedCount);


        CheckSprocExecutionLog(sprocName: "BPStaging.Load_PS_ContractStatusHistory", insertedCount: 1);

        dynamic row = GetResultRow(tableName: "PS.ContractStatusHistory");
        Assert.Equal(expected: (int)DataSourceInstance.BrokingPlatform, actual: row.DataSourceInstanceId);
        Assert.Equal(expected: contractRecord.ContractId, actual: row.ContractId);
        Assert.Equal(expected: contractStagingRecord.ValidFrom, actual: row.SourceUpdatedDate);
        Assert.Equal(expected: LookupRefContractStatusId(DataSourceInstance.BrokingPlatform, (int)contractStagingRecord.ContractStatusId), actual: row.ContractStatusId);
        Assert.True(row.ETLUpdatedDate > DateTime.UtcNow.AddMinutes(-1));
    }

    [Theory]
    [InlineData(true, true, true)]
    [InlineData(false, true, true)]
    [InlineData(true, false, true)]
    [InlineData(false, false, true)]
    [InlineData(true, true, false)]
    [InlineData(false, true, false)]
    [InlineData(true, false, false)]
    [InlineData(false, false, false)]
    public void SameOrSimilarTimeSameStatusNoUpdateTest(bool sourceUpdatedDateIsValidFrom, bool hasRecordBefore, bool hasRecordAfterwards)
    {
        PopulateRefContractStatus();

        dynamic contractRecord = CreateRow(
            tableName: "PS.Contract",
            values: new
            {
                DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform
            });

        dynamic contractStagingRecord = CreateRow(
            tableName: "BPStaging.Contract",
            values: new
            {
                Id = contractRecord.ContractId,
                ContractStatusId = (int)ContractStatus.Draft,
                ValidFrom = DateTime.UtcNow.AddDays(-1).AddMinutes(-20).WithPrecision(2),
                ValidTo = ValidToOpen
            });

        if(hasRecordBefore)
        {
            dynamic contractStatusHistoryRecord3 = CreateRow(
                tableName: "PS.ContractStatusHistory",
                values: new
                {
                    DataSourceInstanceId = contractRecord.DataSourceInstanceId,
                    ContractId = contractRecord.ContractId,
                    ContractStatusId = LookupRefContractStatusId(DataSourceInstance.BrokingPlatform, (int)ContractStatus.Cancelled),
                    SourceUpdatedDate = DateTime.UtcNow.AddDays(-3).WithPrecision(2),
                    ETLCreatedDate = DateTime.UtcNow.AddDays(-3),
                    ETLUpdatedDate = DateTime.UtcNow.AddDays(-3),
                });
        }

        dynamic contractStatusHistoryRecord = CreateRow(
            tableName: "PS.ContractStatusHistory",
            values: new
            {
                DataSourceInstanceId = contractRecord.DataSourceInstanceId,
                ContractId = contractRecord.ContractId,
                ContractStatusId = LookupRefContractStatusId(DataSourceInstance.BrokingPlatform, (int)contractStagingRecord.ContractStatusId),
                SourceUpdatedDate = sourceUpdatedDateIsValidFrom ? contractStagingRecord.ValidFrom : DateTime.UtcNow.AddDays(-2).WithPrecision(2),
                ETLCreatedDate = DateTime.UtcNow.AddDays(-2),
                ETLUpdatedDate = DateTime.UtcNow.AddDays(-2),
            });

        if(hasRecordAfterwards)
        {
            dynamic contractStatusHistoryRecord2 = CreateRow(
                tableName: "PS.ContractStatusHistory",
                values: new
                {
                    DataSourceInstanceId = contractRecord.DataSourceInstanceId,
                    ContractId = contractRecord.ContractId,
                    ContractStatusId = LookupRefContractStatusId(DataSourceInstance.BrokingPlatform, (int)ContractStatus.ContractBound),
                    SourceUpdatedDate = DateTime.UtcNow.AddDays(-1).WithPrecision(2),
                    ETLCreatedDate = DateTime.UtcNow.AddDays(-1),
                    ETLUpdatedDate = DateTime.UtcNow.AddDays(-1),
                });
        }
        dynamic result = ExecuteStoredProcedureWithResultRow(storedProcedureName: "BPStaging.Load_PS_ContractStatusHistory");
        Assert.Equal(expected: 0, actual: result.InsertedCount);

        CheckSprocExecutionLog(sprocName: "BPStaging.Load_PS_ContractStatusHistory", insertedCount: 0);

        dynamic row = GetResultRow(tableName: "PS.ContractStatusHistory", whereClause: $"ContractStatusHistoryId = {contractStatusHistoryRecord.ContractStatusHistoryId}");
        Assert.Equal(expected: (int)DataSourceInstance.BrokingPlatform, actual: row.DataSourceInstanceId);
        Assert.Equal(expected: contractRecord.ContractId, actual: row.ContractId);
        Assert.Equal(expected: contractStatusHistoryRecord.SourceUpdatedDate, actual: row.SourceUpdatedDate);
        Assert.Equal(expected: contractStatusHistoryRecord.ContractStatusId, actual: row.ContractStatusId);
        Assert.True(row.ETLUpdatedDate < DateTime.UtcNow.AddMinutes(-1));
    }

    [Theory]
    [InlineData(true, true)]
    [InlineData(false, true)]
    [InlineData(true, false)]
    [InlineData(false, false)]
    public void SimilarTimeDifferentStatusUpdateTest(bool hasRecordBefore, bool hasRecordAfterwards)
    {
        PopulateRefContractStatus();

        dynamic contractRecord = CreateRow(
            tableName: "PS.Contract",
            values: new
            {
                DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform
            });

        dynamic contractStagingRecord = CreateRow(
            tableName: "BPStaging.Contract",
            values: new
            {
                Id = contractRecord.ContractId,
                ContractStatusId = (int)ContractStatus.MarketSubmissionInProgress,
                ValidFrom = DateTime.UtcNow.AddMinutes(-20).WithPrecision(2),
                ValidTo = ValidToOpen
            });

        if(hasRecordBefore)
        {
            dynamic contractStatusHistoryRecord3 = CreateRow(
                tableName: "PS.ContractStatusHistory",
                values: new
                {
                    DataSourceInstanceId = contractRecord.DataSourceInstanceId,
                    ContractId = contractRecord.ContractId,
                    ContractStatusId = LookupRefContractStatusId(DataSourceInstance.BrokingPlatform, (int)ContractStatus.Cancelled),
                    SourceUpdatedDate = DateTime.UtcNow.AddDays(-3).WithPrecision(2),
                    ETLCreatedDate = DateTime.UtcNow.AddDays(-3),
                    ETLUpdatedDate = DateTime.UtcNow.AddDays(-3),
                });
        }

        dynamic contractStatusHistoryRecord = CreateRow(
            tableName: "PS.ContractStatusHistory",
            values: new
            {
                DataSourceInstanceId = contractRecord.DataSourceInstanceId,
                ContractId = contractRecord.ContractId,
                ContractStatusId = LookupRefContractStatusId(DataSourceInstance.BrokingPlatform, (int)ContractStatus.Draft),
                SourceUpdatedDate = DateTime.UtcNow.AddDays(-2).WithPrecision(2),
                ETLCreatedDate = DateTime.UtcNow.AddDays(-2),
                ETLUpdatedDate = DateTime.UtcNow.AddDays(-2),
            });

        if(hasRecordAfterwards)
        {
            dynamic contractStatusHistoryRecord2 = CreateRow(
                tableName: "PS.ContractStatusHistory",
                values: new
                {
                    DataSourceInstanceId = contractRecord.DataSourceInstanceId,
                    ContractId = contractRecord.ContractId,
                    ContractStatusId = LookupRefContractStatusId(DataSourceInstance.BrokingPlatform, (int)ContractStatus.ContractBound),
                    SourceUpdatedDate = DateTime.UtcNow.AddDays(-1).WithPrecision(2),
                    ETLCreatedDate = DateTime.UtcNow.AddDays(-1),
                    ETLUpdatedDate = DateTime.UtcNow.AddDays(-1),
                });
        }

        dynamic result = ExecuteStoredProcedureWithResultRow(storedProcedureName: "BPStaging.Load_PS_ContractStatusHistory");
        Assert.Equal(expected: 0, actual: result.RejectedCount);
        Assert.Equal(expected: 1, actual: result.InsertedCount);

        CheckSprocExecutionLog(sprocName: "BPStaging.Load_PS_ContractStatusHistory", insertedCount: 1);

        dynamic row = GetResultRow(tableName: "PS.ContractStatusHistory", whereClause: $"ETLUpdatedDate > '{DateTime.UtcNow.AddMinutes(-1):O}'");
        Assert.Equal(expected: (int)DataSourceInstance.BrokingPlatform, actual: row.DataSourceInstanceId);
        Assert.Equal(expected: contractRecord.ContractId, actual: row.ContractId);
        Assert.Equal(expected: contractStagingRecord.ValidFrom, actual: row.SourceUpdatedDate);
        Assert.Equal(expected: contractStagingRecord.ContractStatusId, actual: row.ContractStatusId);
        Assert.True(row.ETLUpdatedDate > DateTime.UtcNow.AddMinutes(-1));
    }

    [Fact]
    public void SameTimeDifferentStatusErrorsTest()
    {
        PopulateRefContractStatus();

        dynamic contractRecord = CreateRow(
            tableName: "PS.Contract",
            values: new
            {
                DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform
            });

        dynamic contractStagingRecord = CreateRow(
            tableName: "BPStaging.Contract",
            values: new
            {
                Id = contractRecord.ContractId,
                ContractStatusId = (int)ContractStatus.ReadyForMarketSubmission,
                ValidFrom = DateTime.UtcNow.AddMinutes(-20).WithPrecision(2),
                ValidTo = ValidToOpen
            });

        dynamic contractStatusHistoryRecord = CreateRow(
            tableName: "PS.ContractStatusHistory",
            values: new
            {
                DataSourceInstanceId = contractRecord.DataSourceInstanceId,
                ContractId = contractRecord.ContractId,
                ContractStatusId = LookupRefContractStatusId(DataSourceInstance.BrokingPlatform, (int)ContractStatus.Draft),
                SourceUpdatedDate = contractStagingRecord.ValidFrom,
                ETLCreatedDate = DateTime.UtcNow.AddDays(-1),
                ETLUpdatedDate = DateTime.UtcNow.AddDays(-1),
            });

        dynamic result = ExecuteStoredProcedureWithResultRow(storedProcedureName: "BPStaging.Load_PS_ContractStatusHistory");
        Assert.Equal(expected: 1, actual: result.RejectedCount);
        Assert.Equal(expected: 0, actual: result.InsertedCount);

        dynamic row = GetResultRow(tableName: "ADF.StoredProcLog", whereClause: $"StoredProcName = 'BPStaging.Load_PS_ContractStatusHistory'");
        Assert.Contains(expectedSubstring: "Cannot insert duplicate key row in object", actualString: row.ErrorMessage);
    }

    [Fact]
    public void UpdatedFromContractStagingMultipleChangesTest()
    {
        PopulateRefContractStatus();

        dynamic contractRecord = CreateRow(
            tableName: "PS.Contract",
            values: new
            {
                DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform
            });

        dynamic contractStagingRecord = CreateRow(
            tableName: "BPStaging.Contract",
            values: new
            {
                Id = contractRecord.ContractId,
                ContractStatusId = (int)ContractStatus.Draft,
                ValidFrom = DateTime.UtcNow.AddMinutes(-20).WithPrecision(2),
                ValidTo = DateTime.UtcNow.AddMinutes(-15).WithPrecision(2)
            });
        dynamic contractStagingRecord2 = CreateRow(
            tableName: "BPStaging.Contract",
            values: new
            {
                Id = contractRecord.ContractId,
                ContractStatusId = (int)ContractStatus.MarketSubmissionInProgress,
                ValidFrom = DateTime.UtcNow.AddMinutes(-15).WithPrecision(2),
                ValidTo = ValidToOpen
            });

        dynamic result = ExecuteStoredProcedureWithResultRow(storedProcedureName: "BPStaging.Load_PS_ContractStatusHistory");
        Assert.Equal(expected: 0, actual: result.RejectedCount);
        Assert.Equal(expected: 2, actual: result.InsertedCount);

        CheckSprocExecutionLog(sprocName: "BPStaging.Load_PS_ContractStatusHistory", insertedCount: 2);

        // Now going to have more than 1 record in the target to check.
        dynamic row = GetResultRow(tableName: "PS.ContractStatusHistory", whereClause: $"SourceUpdatedDate = '{contractStagingRecord.ValidFrom:O}'");
        Assert.Equal(expected: (int)DataSourceInstance.BrokingPlatform, actual: row.DataSourceInstanceId);
        Assert.Equal(expected: contractRecord.ContractId, actual: row.ContractId);
        Assert.Equal(expected: LookupRefContractStatusId(DataSourceInstance.BrokingPlatform, (int)contractStagingRecord.ContractStatusId), actual: row.ContractStatusId);
        Assert.True(row.ETLUpdatedDate > DateTime.UtcNow.AddMinutes(-1));
        row = GetResultRow(tableName: "PS.ContractStatusHistory", whereClause: $"SourceUpdatedDate = '{contractStagingRecord2.ValidFrom:O}'");
        Assert.Equal(expected: (int)DataSourceInstance.BrokingPlatform, actual: row.DataSourceInstanceId);
        Assert.Equal(expected: contractRecord.ContractId, actual: row.ContractId);
        Assert.Equal(expected: LookupRefContractStatusId(DataSourceInstance.BrokingPlatform, (int)contractStagingRecord2.ContractStatusId), actual: row.ContractStatusId);
        Assert.True(row.ETLUpdatedDate > DateTime.UtcNow.AddMinutes(-1));
    }

    [Fact]
    public void TakesFirstDateIfMultipleRecordsStagedWithSameStatusTest()
    {
        PopulateRefContractStatus();

        dynamic contractRecord = CreateRow(
            tableName: "PS.Contract",
            values: new
            {
                DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform
            });

        dynamic contractStagingRecord = CreateRow(
            tableName: "BPStaging.Contract",
            values: new
            {
                Id = contractRecord.ContractId,
                ContractStatusId = (int)ContractStatus.Draft,
                ValidFrom = DateTime.UtcNow.AddMinutes(-30).WithPrecision(2),
                ValidTo = DateTime.UtcNow.AddMinutes(-20).WithPrecision(2)
            });
        // This one repeats the previous status so must be ignored.
        dynamic contractStagingRecord2 = CreateRow(
            tableName: "BPStaging.Contract",
            values: new
            {
                Id = contractRecord.ContractId,
                ContractStatusId = (int)ContractStatus.Draft,
                ValidFrom = DateTime.UtcNow.AddMinutes(-20).WithPrecision(2),
                ValidTo = DateTime.UtcNow.AddMinutes(-15).WithPrecision(2)
            });
        dynamic contractStagingRecord3 = CreateRow(
            tableName: "BPStaging.Contract",
            values: new
            {
                Id = contractRecord.ContractId,
                ContractStatusId = (int)ContractStatus.MarketSubmissionInProgress,
                ValidFrom = DateTime.UtcNow.AddMinutes(-15).WithPrecision(2),
                ValidTo = ValidToOpen
            });

        dynamic result = ExecuteStoredProcedureWithResultRow(storedProcedureName: "BPStaging.Load_PS_ContractStatusHistory");
        Assert.Equal(expected: 0, actual: result.RejectedCount);
        Assert.Equal(expected: 2, actual: result.InsertedCount);

        CheckSprocExecutionLog(sprocName: "BPStaging.Load_PS_ContractStatusHistory", insertedCount: 2);

        // Now going to have more than 1 record in the target to check.
        dynamic row = GetResultRow(tableName: "PS.ContractStatusHistory", whereClause: $"SourceUpdatedDate = '{contractStagingRecord.ValidFrom:O}'");
        Assert.Equal(expected: (int)DataSourceInstance.BrokingPlatform, actual: row.DataSourceInstanceId);
        Assert.Equal(expected: contractRecord.ContractId, actual: row.ContractId);
        Assert.Equal(expected: LookupRefContractStatusId(DataSourceInstance.BrokingPlatform, (int)contractStagingRecord.ContractStatusId), actual: row.ContractStatusId);

        Assert.True(row.ETLUpdatedDate > DateTime.UtcNow.AddMinutes(-1));
        row = GetResultRow(tableName: "PS.ContractStatusHistory", whereClause: $"SourceUpdatedDate = '{contractStagingRecord3.ValidFrom:O}'");
        Assert.Equal(expected: (int)DataSourceInstance.BrokingPlatform, actual: row.DataSourceInstanceId);
        Assert.Equal(expected: contractRecord.ContractId, actual: row.ContractId);
        Assert.Equal(expected: LookupRefContractStatusId(DataSourceInstance.BrokingPlatform, (int)contractStagingRecord3.ContractStatusId), actual: row.ContractStatusId);
        Assert.True(row.ETLUpdatedDate > DateTime.UtcNow.AddMinutes(-1));
    }

    /// <summary>
    /// There seems to be an issue where if the last record has the same status as
    /// an existing record it stops anything matching that status being stored.
    /// It is just supposed to prevent the same status being stored as the next timed change rather than all.
    /// </summary>
    [Fact]
    public void TestBug271832ScenarioTest()
    {
        PopulateRefContractStatus();

        dynamic contractRecord = CreateRow(
            tableName: "PS.Contract",
            values: new
            {
                ContractId = 1688660,
                ContractStatusId = (int)ContractStatus.ContractBound,
                DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            });
        _ = CreateRow(
                tableName: "PS.ContractStatusHistory",
                columnNames: new[] { "ContractId", "ContractStatusId", "PlacementServiceBusId", "DataSourceInstanceId", "SourceUpdatedDate" },
                values: new[] {
                    new object?[] { 1688660, (int)ContractStatus.ContractBound, 836955, contractRecord.DataSourceInstanceId, "2024-01-24 07:33:50"},
                    new object?[] { 1688660, (int)ContractStatus.MarketSubmissionInProgress, null, contractRecord.DataSourceInstanceId, "2024-01-24 07:33:26"}
                });
        _ = CreateRow(
                tableName: "BPStaging.Contract",
                columnNames: new[] { "Id", "ContractStatusId", "ValidFrom", "ValidTo" },
                values: new[] {
                            new object?[] { 1688660, (int)ContractStatus.ContractBound, "2024-04-12 11:14:53", "9999-12-31 23:59:59"},
                            new object?[] { 1688660, (int)ContractStatus.ContractBound, "2024-03-26 14:13:47", "2024-04-12 11:14:32"},
                            new object?[] { 1688660, (int)ContractStatus.MarketSubmissionInProgress, "2024-04-12 11:14:32", "2024-04-12 11:14:53"},
                });

        // I'm expecting this to insert 3 of the 2 records.
        dynamic result = ExecuteStoredProcedureWithResultRow(storedProcedureName: "BPStaging.Load_PS_ContractStatusHistory");
        Assert.Equal(expected: 0, actual: result.RejectedCount);
        Assert.Equal(expected: 2, actual: result.InsertedCount);

        CheckSprocExecutionLog(sprocName: "BPStaging.Load_PS_ContractStatusHistory", insertedCount: 2);

        // And check they are the two records they should be.
        dynamic row = GetResultRow(tableName: "PS.ContractStatusHistory", whereClause: $"SourceUpdatedDate = '2024-04-12 11:14:53'");
        Assert.Equal(expected: (int)ContractStatus.ContractBound, actual: row.ContractStatusId);
        row = GetResultRow(tableName: "PS.ContractStatusHistory", whereClause: $"SourceUpdatedDate = '2024-04-12 11:14:32'");
        Assert.Equal(expected: (int)ContractStatus.MarketSubmissionInProgress, actual: row.ContractStatusId);
    }


    public Load_PS_ContractStatusHistoryTests(DatabaseFixture fixture) : base(fixture)
    {
    }
}
