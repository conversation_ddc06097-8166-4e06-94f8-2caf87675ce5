﻿using System.Reflection;
using Xunit.Abstractions;

namespace PsDb.Tests.BPStaging.StoredProcedures;
public class Load_PS_ElementTagSummaryTests : PlacementStoreTestBase
{
    [Fact]
    public void Load_PS_ElementTagSummaryNoDataTest()
    {
        NoDataStoredProcedureTest(storedProcedureTestMethod: MethodBase.GetCurrentMethod(), schema: "BPStaging", values: new { @LastUpdatedDate = DateTime.UtcNow.AddDays(-1) });
    }

    [Fact]
    public void Load_PS_ElementTagSummaryInsertTest()
    {
        dynamic elementTagGroupRecord;
        dynamic elementTagTypeRecord;
        dynamic elementTagCacheRecord;

        SetupData(elementTagGroupRecord: out elementTagGroupRecord, elementTagTypeRecord: out elementTagTypeRecord, elementTagCacheRecord: out elementTagCacheRecord);

        dynamic result = ExecuteStoredProcedureWithResultRow(storedProcedureName: "BPStaging.Load_PS_ElementTagSummary", values: new { @LastUpdatedDate = DateTime.UtcNow.AddDays(-1) });
        Assert.Equal(0, result.RejectedCount);
        Assert.Equal(0, result.DeletedCount);
        Assert.Equal(1, result.InsertedCount);
        Assert.Equal(0, result.UpdatedCount);
        Assert.NotNull(result.MaxLastUpdatedUTCDate);

        CheckSprocExecutionLog(sprocName: @"BPStaging.Load_PS_ElementTagSummary", insertedCount: 1);

        dynamic row = GetResultRow(tableName: "PS.ElementTagSummary");
        Assert.NotNull(row);
        Assert.Equal(expected: 1, actual: row.ElementGroupRowNumber);
        Assert.Equal(expected: false, actual: row.IsDeleted);
        Assert.Equal(expected: elementTagGroupRecord.DataSourceInstanceId, actual: row.DataSourceInstanceId);
        Assert.Equal(expected: elementTagGroupRecord.ElementTagGroupKey, actual: row.ElementTagGroupKey);
        Assert.Equal(expected: elementTagTypeRecord.ElementTagTypeKey, actual: row.ElementTagTypeKey);
        Assert.True(row.ETLCreatedDate >= DateTime.UtcNow.AddMinutes(-1));
        Assert.True(row.ETLUpdatedDate >= DateTime.UtcNow.AddMinutes(-1));
    }

    [Fact]
    public void Load_PS_ElementTagSummaryUpdateTest()
    {
        dynamic elementTagGroupRecord;
        dynamic elementTagTypeRecord;
        dynamic elementTagCacheRecord;
        SetupData(elementTagGroupRecord: out elementTagGroupRecord, elementTagTypeRecord: out elementTagTypeRecord, elementTagCacheRecord: out elementTagCacheRecord);

        CreateElementTagSummary(elementTagGroupRecord, elementTagTypeRecord, elementTagCacheRecord);

        dynamic result = ExecuteStoredProcedureWithResultRow(storedProcedureName: "BPStaging.Load_PS_ElementTagSummary", values: new { @LastUpdatedDate = DateTime.UtcNow.AddDays(-1) });
        Assert.Equal(0, result.RejectedCount);
        Assert.Equal(0, result.DeletedCount);
        Assert.Equal(0, result.InsertedCount);
        Assert.Equal(1, result.UpdatedCount);
        Assert.NotNull(result.MaxLastUpdatedUTCDate);

        CheckSprocExecutionLog(sprocName: @"BPStaging.Load_PS_ElementTagSummary", updatedCount: 1);

        dynamic row = GetResultRow(tableName: "PS.ElementTagSummary");
        Assert.NotNull(row);
        Assert.Equal(expected: 1, actual: row.ElementGroupRowNumber);
        Assert.Equal(expected: false, actual: row.IsDeleted);
        Assert.Equal(expected: elementTagGroupRecord.DataSourceInstanceId, actual: row.DataSourceInstanceId);
        Assert.Equal(expected: elementTagGroupRecord.ElementTagGroupKey, actual: row.ElementTagGroupKey);
        Assert.Equal(expected: elementTagTypeRecord.ElementTagTypeKey, actual: row.ElementTagTypeKey);
        Assert.True(row.ETLCreatedDate < DateTime.UtcNow.AddMinutes(-1));
        Assert.True(row.ETLUpdatedDate >= DateTime.UtcNow.AddMinutes(-1));
    }

    [Fact]
    public void Load_PS_ElementTagSummaryDeleteTest()
    {
        dynamic elementTagGroupRecord;
        dynamic elementTagTypeRecord;
        dynamic elementTagCacheRecord;
        SetupData(elementTagGroupRecord: out elementTagGroupRecord, elementTagTypeRecord: out elementTagTypeRecord, elementTagCacheRecord: out elementTagCacheRecord, isDeleted: true);

        CreateElementTagSummary(elementTagGroupRecord, elementTagTypeRecord, elementTagCacheRecord);

        dynamic result = ExecuteStoredProcedureWithResultRow(storedProcedureName: "BPStaging.Load_PS_ElementTagSummary", values: new { @LastUpdatedDate = DateTime.UtcNow.AddDays(-1) });
        Assert.Equal(0, result.RejectedCount);
        Assert.Equal(0, result.DeletedCount);
        Assert.Equal(0, result.InsertedCount);
        Assert.Equal(1, result.UpdatedCount);
        Assert.NotNull(result.MaxLastUpdatedUTCDate);

        CheckSprocExecutionLog(sprocName: @"BPStaging.Load_PS_ElementTagSummary", updatedCount: 1);

        dynamic row = GetResultRow(tableName: "PS.ElementTagSummary");
        Assert.NotNull(row);
        Assert.Equal(expected: true, actual: row.IsDeleted);
        Assert.True(row.ETLCreatedDate < DateTime.UtcNow.AddMinutes(-1));
        Assert.True(row.ETLUpdatedDate >= DateTime.UtcNow.AddMinutes(-1));
    }

    /// <summary>
    /// No update expected because changes happened too early
    /// </summary>
    [Fact]
    public void Load_PS_ElementTagSummaryNoUpdateAsUpdatedDateTooLateTest()
    {
        dynamic elementTagGroupRecord;
        dynamic elementTagTypeRecord;
        dynamic elementTagCacheRecord;
        SetupData(elementTagGroupRecord: out elementTagGroupRecord, elementTagTypeRecord: out elementTagTypeRecord, elementTagCacheRecord: out elementTagCacheRecord);

        CreateElementTagSummary(elementTagGroupRecord, elementTagTypeRecord, elementTagCacheRecord);

        dynamic result = ExecuteStoredProcedureWithResultRow(storedProcedureName: "BPStaging.Load_PS_ElementTagSummary", values: new { @LastUpdatedDate = DateTime.UtcNow.AddMinutes(-1) });
        Assert.Equal(0, result.RejectedCount);
        Assert.Equal(0, result.DeletedCount);
        Assert.Equal(0, result.InsertedCount);
        Assert.Equal(0, result.UpdatedCount);
        Assert.NotNull(result.MaxLastUpdatedUTCDate);

        CheckSprocExecutionLog(sprocName: @"BPStaging.Load_PS_ElementTagSummary");

        dynamic row = GetResultRow(tableName: "PS.ElementTagSummary");
        Assert.NotNull(row);
        Assert.Equal(expected: false, actual: row.IsDeleted);
        Assert.True(row.ETLCreatedDate < DateTime.UtcNow.AddMinutes(-1));
        Assert.True(row.ETLUpdatedDate < DateTime.UtcNow.AddMinutes(-1));
    }

    /// <summary>
    /// There was a production incident INC6568997 with duplicates in Load_PS_RiskProfile
    /// This seems to have been caused because Row Numbers were changing for rows without an update so the new row number wasn't being inserted.
    /// This led to duplicate row numbers.
    /// </summary>
    [Fact]
    public void Load_PS_ElementTagSummaryUpdateCausedByRowNumberChangeTest()
    {
        dynamic elementTagGroupRecord;
        dynamic elementTagTypeRecord;
        dynamic elementTagCacheRecord;
        SetupData(elementTagGroupRecord: out elementTagGroupRecord, elementTagTypeRecord: out elementTagTypeRecord, elementTagCacheRecord: out elementTagCacheRecord);

        CreateElementTagSummary(elementTagGroupRecord, elementTagTypeRecord, elementTagCacheRecord);

        // Some additional records which should cause a change as these should shift the row numbers
        dynamic elementTagTypeRecord2 = CreateRow("ref.ElementTagType", values: new
        {
            ElementTagTypeId = 2,
            DataSourceInstanceId = (int)DataSourceInstance.Eclipse,
            ElementTagTypeKey = "elementTagTypeKey2",
            ElementTagGroupId = elementTagGroupRecord.ElementTagGroupId,
            ETLCreatedDate = DateTime.UtcNow.AddHours(-1),
            ETLUpdatedDate = DateTime.UtcNow.AddHours(-1),
            SourceUpdatedDate = DateTime.UtcNow.AddHours(-1),
            IsDeprecated = 0
        });

        // tag
        dynamic elementTagCacheRecord2 = CreateRow("dbo.ElementTagCache", values: new
        {
            RootElementId = 1,
            ElementBranchId = 1,
            ElementId = 1,
            ElementTagId = 2,
            ElementTagDeltaId = 2,
            ElementTagTypeId = elementTagTypeRecord2.ElementTagTypeId,
            ETLCreatedDate = DateTime.UtcNow.AddHours(-1),
            ETLUpdatedDate = DateTime.UtcNow.AddHours(-1),
            IsDeleted = false
        });

        // This is slightly false as the LastUpdatedDate would normally be back enough to pick up the new record
        // But this is intended to test that the update happens to the existing record because of the Row Number change.
        dynamic result = ExecuteStoredProcedureWithResultRow(storedProcedureName: "BPStaging.Load_PS_ElementTagSummary", values: new { @LastUpdatedDate = DateTime.UtcNow.AddMinutes(-1) });
        Assert.Equal(0, result.RejectedCount);
        Assert.Equal(0, result.DeletedCount);
        Assert.Equal(0, result.InsertedCount);
        Assert.Equal(1, result.UpdatedCount);
        Assert.NotNull(result.MaxLastUpdatedUTCDate);

        CheckSprocExecutionLog(sprocName: @"BPStaging.Load_PS_ElementTagSummary", updatedCount: 1);

        dynamic row = GetResultRow(tableName: "PS.ElementTagSummary");
        Assert.NotNull(row);
        Assert.Equal(expected: false, actual: row.IsDeleted);
        Assert.Equal(expected: 2, actual: row.ElementGroupRowNumber); /* This was 1 before */
        Assert.True(row.ETLCreatedDate < DateTime.UtcNow.AddMinutes(-1));
        Assert.True(row.ETLUpdatedDate > DateTime.UtcNow.AddMinutes(-1));
    }

    private void SetupData(out dynamic elementTagGroupRecord, out dynamic elementTagTypeRecord, out dynamic elementTagCacheRecord, bool isDeleted = false)
    {
        dynamic dataSourceInstanceId = CreateRow(tableName: "Reference.DataSourceInstance", values: new
        {
            DataSourceInstanceId = (int)DataSourceInstance.Eclipse
        });

        // tg
        elementTagGroupRecord = CreateRow("ref.ElementTagGroup", values: new
        {
            ElementTagGroupId = 1,
            DataSourceInstanceId = dataSourceInstanceId.DataSourceInstanceId,
            ElementTagGroupKey = "tagGroupKey",
            ETLCreatedDate = DateTime.UtcNow.AddHours(-1),
            ETLUpdatedDate = DateTime.UtcNow.AddHours(-1),
            IsDeprecated = 0,
            SourceUpdatedDate = DateTime.UtcNow.AddHours(-2)
        });

        // tt
        elementTagTypeRecord = CreateRow("ref.ElementTagType", values: new
        {
            ElementTagTypeId = 1,
            DataSourceInstanceId = (int)DataSourceInstance.Eclipse,
            ElementTagTypeKey = "elementTagTypeKey",
            ElementTagGroupId = elementTagGroupRecord.ElementTagGroupId,
            ETLCreatedDate = DateTime.UtcNow.AddHours(-1),
            ETLUpdatedDate = DateTime.UtcNow.AddHours(-1),
            SourceUpdatedDate = DateTime.UtcNow.AddHours(-2),
            IsDeprecated = 0
        });

        // tag
        elementTagCacheRecord = CreateRow("dbo.ElementTagCache", values: new
        {
            RootElementId = 1,
            ElementBranchId = 1,
            ElementId = 1,
            ElementTagId = 1,
            ElementTagDeltaId = 1,
            ElementTagTypeId = elementTagTypeRecord.ElementTagTypeId,
            ETLCreatedDate = DateTime.UtcNow.AddHours(-1),
            ETLUpdatedDate = DateTime.UtcNow.AddHours(-1),
            IsDeleted = isDeleted
        });
    }

    private void CreateElementTagSummary(dynamic elementTagGroupRecord, dynamic elementTagTypeRecord, dynamic elementTagCacheRecord)
    {
        dynamic x = CreateRow(tableName: "PS.ElementTagSummary", values: new
        {
            ElementGroupRowNumber = 1,
            ElementTypeGroupRowNumber = 1,
            ElementId = elementTagCacheRecord.ElementId,
            ElementBranchId = elementTagCacheRecord.ElementBranchId,
            ElementTagTypeId = elementTagTypeRecord.ElementTagTypeId,
            ElementTagGroupId = elementTagGroupRecord.ElementTagGroupId,
            ETLCreatedDate = DateTime.UtcNow.AddHours(-1),
            ETLUpdatedDate = DateTime.UtcNow.AddHours(-1),
        });
    }

    #region Constructor
    public Load_PS_ElementTagSummaryTests(DatabaseFixture fixture, ITestOutputHelper output) : base(fixture, output)
    {
    }
    #endregion
}