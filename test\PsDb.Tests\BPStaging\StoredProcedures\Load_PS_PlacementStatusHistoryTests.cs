﻿using PsDb.Tests.PlacementStoreHelpers;
using System.Diagnostics.CodeAnalysis;

namespace PsDb.Tests.BPStaging.StoredProcedures;

/// <summary>
/// This is loaded from the Placement which is updated directly so cannot be obtained from staging,
/// and from the broking Platform. As the status may go through several states between extracts
/// the information is obtained from the Placement Timeline rather than Placement updates directly.
/// The LastUpdatedUTCDate on the Placement is usually (wrongly) updated with the update date on the BP
/// Placement. In this case this does us a favour.
/// </summary>
[ExcludeFromCodeCoverage]
public class Load_PS_PlacementStatusHistoryTests : PlacementStoreTestBase
{
    /// <summary>
    /// We have changed Placement to be incremental from a System Versioned table.
    /// This means that although not used by Placement we will have the full set of
    /// changes since the last run. Meaning we will be able to find out when the
    /// status last changed.
    /// </summary>
    [Fact]
    public void UpdatedFromPlacementStagingTest()
    {
        PopulateRefPlacementStatus();

        dynamic placementRecord = CreateRow(
            tableName: "dbo.Placement",
            values: new
            {
                PlacementSystemId = 13579,
                DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform
            });

        dynamic placementStagingRecord = CreateRow(
            tableName: "BPStaging.Placement",
            values: new
            {
                Id = placementRecord.PlacementSystemId,
                PlacementStatusId = (int)PlacementStatus.NotStarted,
                ValidFrom = DateTime.UtcNow.AddMinutes(-20).WithPrecision(2), 
                ValidTo = ValidToOpen
            });

        dynamic result = ExecuteStoredProcedureWithResultRow( storedProcedureName: "BPStaging.Load_PS_PlacementStatusHistory");
        Assert.Equal(expected: 0, actual: result.RejectedCount);
        Assert.Equal(expected: 1, actual: result.InsertedCount);


        CheckSprocExecutionLog(sprocName: "BPStaging.Load_PS_PlacementStatusHistory", insertedCount: 1);

        dynamic row = GetResultRow(tableName: "PS.PlacementStatusHistory");
        Assert.Equal(expected: (int)DataSourceInstance.BrokingPlatform, actual: row.DataSourceInstanceId);
        Assert.Equal(expected: placementRecord.PlacementId, actual: row.PlacementId);
        Assert.Equal(expected: placementStagingRecord.ValidFrom, actual: row.SourceUpdatedDate);
        Assert.Equal(expected: LookupRefPlacementStatusId(DataSourceInstance.BrokingPlatform, (int)placementStagingRecord.PlacementStatusId), actual: row.PlacementStatusId);
        Assert.True(row.ETLUpdatedDate > DateTime.UtcNow.AddMinutes(-1));
    }

    /// <summary>
    /// Need to know that no updates will occur if we already have a matching record.
    /// Similar if the date is not quite the same, but the most recent record has the same status.
    /// Does a check when there is no other record to confuse, and when there is a different status afterwards.
    /// </summary>
    [Theory]
    [InlineData(true, true, true)]
    [InlineData(false, true, true)]
    [InlineData(true, false, true)]
    [InlineData(false, false, true)]
    [InlineData(true, true, false)]
    [InlineData(false, true, false)]
    [InlineData(true, false, false)]
    [InlineData(false, false, false)]
    public void SameOrSimilarTimeSameStatusNoUpdateTest(bool sourceUpdatedDateIsValidFrom, bool hasRecordBefore, bool hasRecordAfterwards)
    {
        PopulateRefPlacementStatus();

        dynamic placementRecord = CreateRow(
            tableName: "dbo.Placement",
            values: new
            {
                PlacementSystemId = 13579,
                DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform
            });

        dynamic placementStagingRecord = CreateRow(
            tableName: "BPStaging.Placement",
            values: new
            {
                Id = placementRecord.PlacementSystemId,
                PlacementStatusId = (int)PlacementStatus.InProgress,
                ValidFrom = DateTime.UtcNow.AddDays(-1).AddMinutes(-20).WithPrecision(2),
                ValidTo = ValidToOpen
            });

        if(hasRecordBefore)
        {
            dynamic placementStatusHistoryRecord3 = CreateRow(
                tableName: "PS.PlacementStatusHistory",
                values: new
                {
                    DataSourceInstanceId = placementRecord.DataSourceInstanceId,
                    PlacementId = placementRecord.PlacementId,
                    PlacementStatusId = LookupRefPlacementStatusId(DataSourceInstance.BrokingPlatform, PlacementStatus.Cancelled),
                    SourceUpdatedDate = DateTime.UtcNow.AddDays(-3).WithPrecision(2),
                    ETLCreatedDate = DateTime.UtcNow.AddDays(-3),
                    ETLUpdatedDate = DateTime.UtcNow.AddDays(-3),
                });
        }

        dynamic placementStatusHistoryRecord = CreateRow(
            tableName: "PS.PlacementStatusHistory",
            values: new
            {
                DataSourceInstanceId= placementRecord.DataSourceInstanceId,
                PlacementId = placementRecord.PlacementId,
                PlacementStatusId = LookupRefPlacementStatusId(DataSourceInstance.BrokingPlatform, (int)placementStagingRecord.PlacementStatusId),
                SourceUpdatedDate = sourceUpdatedDateIsValidFrom ? placementStagingRecord.ValidFrom : DateTime.UtcNow.AddDays(-2).WithPrecision(2),
                ETLCreatedDate = DateTime.UtcNow.AddDays(-2),
                ETLUpdatedDate = DateTime.UtcNow.AddDays(-2),
            });

        if(hasRecordAfterwards)
        {
            dynamic placementStatusHistoryRecord2 = CreateRow(
                tableName: "PS.PlacementStatusHistory",
                values: new
                {
                    DataSourceInstanceId = placementRecord.DataSourceInstanceId,
                    PlacementId = placementRecord.PlacementId,
                    PlacementStatusId = LookupRefPlacementStatusId(DataSourceInstance.BrokingPlatform, PlacementStatus.Complete),
                    SourceUpdatedDate = DateTime.UtcNow.AddDays(-1).WithPrecision(2),
                    ETLCreatedDate = DateTime.UtcNow.AddDays(-1),
                    ETLUpdatedDate = DateTime.UtcNow.AddDays(-1),
                });
        }

        dynamic result = ExecuteStoredProcedureWithResultRow(storedProcedureName: "BPStaging.Load_PS_PlacementStatusHistory");
//        Assert.Equal(expected: 0, actual: result.RejectedCount);
        Assert.Equal(expected: 0, actual: result.InsertedCount);
        
        CheckSprocExecutionLog(sprocName: "BPStaging.Load_PS_PlacementStatusHistory", insertedCount: 0);

        dynamic row = GetResultRow(tableName: "PS.PlacementStatusHistory", whereClause: $"PlacementStatusHistoryId = {placementStatusHistoryRecord.PlacementStatusHistoryId}");
        Assert.Equal(expected: (int)DataSourceInstance.BrokingPlatform, actual: row.DataSourceInstanceId);
        Assert.Equal(expected: placementRecord.PlacementId, actual: row.PlacementId);
        Assert.Equal(expected: placementStatusHistoryRecord.SourceUpdatedDate, actual: row.SourceUpdatedDate);
        Assert.Equal(expected: placementStatusHistoryRecord.PlacementStatusId, actual: row.PlacementStatusId);
        Assert.True(row.ETLUpdatedDate < DateTime.UtcNow.AddMinutes(-1));
    }

    [Theory]
    [InlineData(true, true)]
    [InlineData(false, true)]
    [InlineData(true, false)]
    [InlineData(false, false)]
    public void SimilarTimeDifferentStatusUpdateTest(bool hasRecordBefore, bool hasRecordAfterwards)
    {
        PopulateRefPlacementStatus();

        dynamic placementRecord = CreateRow(
            tableName: "dbo.Placement",
            values: new
            {
                PlacementSystemId = 13579,
                DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform
            });

        dynamic placementStagingRecord = CreateRow(
            tableName: "BPStaging.Placement",
            values: new
            {
                Id = placementRecord.PlacementSystemId,
                PlacementStatusId = (int)PlacementStatus.InProgress,
                ValidFrom = DateTime.UtcNow.AddMinutes(-20).WithPrecision(2),
                ValidTo = ValidToOpen
            });

        if(hasRecordBefore)
        {
            dynamic placementStatusHistoryRecord3 = CreateRow(
                tableName: "PS.PlacementStatusHistory",
                values: new
                {
                    DataSourceInstanceId = placementRecord.DataSourceInstanceId,
                    PlacementId = placementRecord.PlacementId,
                    PlacementStatusId = LookupRefPlacementStatusId(DataSourceInstance.BrokingPlatform, PlacementStatus.Cancelled),
                    SourceUpdatedDate = DateTime.UtcNow.AddDays(-3).WithPrecision(2),
                    ETLCreatedDate = DateTime.UtcNow.AddDays(-3),
                    ETLUpdatedDate = DateTime.UtcNow.AddDays(-3),
                });
        }

        dynamic placementStatusHistoryRecord = CreateRow(
            tableName: "PS.PlacementStatusHistory",
            values: new
            {
                DataSourceInstanceId = placementRecord.DataSourceInstanceId,
                PlacementId = placementRecord.PlacementId,
                PlacementStatusId = LookupRefPlacementStatusId(DataSourceInstance.BrokingPlatform, PlacementStatus.NotStarted),
                SourceUpdatedDate = DateTime.UtcNow.AddDays(-2).WithPrecision(2),
                ETLCreatedDate = DateTime.UtcNow.AddDays(-2),
                ETLUpdatedDate = DateTime.UtcNow.AddDays(-2),
            });

        if(hasRecordAfterwards)
        {
            dynamic placementStatusHistoryRecord2 = CreateRow(
                tableName: "PS.PlacementStatusHistory",
                values: new
                {
                    DataSourceInstanceId = placementRecord.DataSourceInstanceId,
                    PlacementId = placementRecord.PlacementId,
                    PlacementStatusId = LookupRefPlacementStatusId(DataSourceInstance.BrokingPlatform, PlacementStatus.Complete),
                    SourceUpdatedDate = DateTime.UtcNow.AddDays(-1).WithPrecision(2),
                    ETLCreatedDate = DateTime.UtcNow.AddDays(-1),
                    ETLUpdatedDate = DateTime.UtcNow.AddDays(-1),
                });
        }

        dynamic result = ExecuteStoredProcedureWithResultRow(storedProcedureName: "BPStaging.Load_PS_PlacementStatusHistory");
        Assert.Equal(expected: 0, actual: result.RejectedCount);
        Assert.Equal(expected: 1, actual: result.InsertedCount);

        CheckSprocExecutionLog(sprocName: "BPStaging.Load_PS_PlacementStatusHistory", insertedCount: 1);

        dynamic row = GetResultRow(tableName: "PS.PlacementStatusHistory", whereClause: $"ETLUpdatedDate > '{DateTime.UtcNow.AddMinutes(-1):O}'");
        Assert.Equal(expected: (int)DataSourceInstance.BrokingPlatform, actual: row.DataSourceInstanceId);
        Assert.Equal(expected: placementRecord.PlacementId, actual: row.PlacementId);
        Assert.Equal(expected: placementStagingRecord.ValidFrom, actual: row.SourceUpdatedDate);
        Assert.Equal(expected: placementStagingRecord.PlacementStatusId, actual: row.PlacementStatusId);
        Assert.True(row.ETLUpdatedDate > DateTime.UtcNow.AddMinutes(-1));
    }

    /// <summary>
    /// We can't have status updates at exactly the same time for a different status.
    /// The same status is allowed but ignored as it is seen as a repeat of the same data.
    /// </summary>
    [Fact]
    public void SameTimeDifferentStatusErrorsTest()
    {
        PopulateRefPlacementStatus();

        dynamic placementRecord = CreateRow(
            tableName: "dbo.Placement",
            values: new
            {
                PlacementSystemId = 13579,
                DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform
            });

        dynamic placementStagingRecord = CreateRow(
            tableName: "BPStaging.Placement",
            values: new
            {
                Id = placementRecord.PlacementSystemId,
                PlacementStatusId = (int)PlacementStatus.InProgress,
                ValidFrom = DateTime.UtcNow.AddMinutes(-20).WithPrecision(2),
                ValidTo = ValidToOpen
            });

        dynamic placementStatusHistoryRecord = CreateRow(
            tableName: "PS.PlacementStatusHistory",
            values: new
            {
                DataSourceInstanceId = placementRecord.DataSourceInstanceId,
                PlacementId = placementRecord.PlacementId,
                PlacementStatusId = LookupRefPlacementStatusId(DataSourceInstance.BrokingPlatform, PlacementStatus.NotStarted),
                SourceUpdatedDate = placementStagingRecord.ValidFrom,
                ETLCreatedDate = DateTime.UtcNow.AddDays(-1),
                ETLUpdatedDate = DateTime.UtcNow.AddDays(-1),
            });

        dynamic result = ExecuteStoredProcedureWithResultRow(storedProcedureName: "BPStaging.Load_PS_PlacementStatusHistory");
        Assert.Equal(expected: 1, actual: result.RejectedCount);
        Assert.Equal(expected: 0, actual: result.InsertedCount);

        dynamic row = GetResultRow(tableName: "ADF.StoredProcLog", whereClause: $"StoredProcName = 'BPStaging.Load_PS_PlacementStatusHistory'");
        Assert.Contains(expectedSubstring: "Cannot insert duplicate key row in object", actualString: row.ErrorMessage);
    }

    [Fact]
    public void UpdatedFromPlacementStagingMultipleChangesTest()
    {
        PopulateRefPlacementStatus();

        dynamic placementRecord = CreateRow(
            tableName: "dbo.Placement",
            values: new
            {
                PlacementSystemId = 13579,
                DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform
            });

        dynamic placementStagingRecord = CreateRow(
            tableName: "BPStaging.Placement",
            values: new
            {
                Id = placementRecord.PlacementSystemId,
                PlacementStatusId = (int)PlacementStatus.NotStarted,
                ValidFrom = DateTime.UtcNow.AddMinutes(-20).WithPrecision(2), 
                ValidTo = DateTime.UtcNow.AddMinutes(-15).WithPrecision(2)
            });
        dynamic placementStagingRecord2 = CreateRow(
            tableName: "BPStaging.Placement",
            values: new
            {
                Id = placementRecord.PlacementSystemId,
                PlacementStatusId = (int)PlacementStatus.InProgress,
                ValidFrom = DateTime.UtcNow.AddMinutes(-15).WithPrecision(2), 
                ValidTo = ValidToOpen
            });

        dynamic result = ExecuteStoredProcedureWithResultRow(storedProcedureName: "BPStaging.Load_PS_PlacementStatusHistory");
        Assert.Equal(expected: 0, actual: result.RejectedCount);
        Assert.Equal(expected: 2, actual: result.InsertedCount);

        CheckSprocExecutionLog(sprocName: "BPStaging.Load_PS_PlacementStatusHistory", insertedCount: 2);

        // Now going to have more than 1 record in the target to check.
        dynamic row = GetResultRow(tableName: "PS.PlacementStatusHistory", whereClause: $"SourceUpdatedDate = '{placementStagingRecord.ValidFrom:O}'");
        Assert.Equal(expected: (int)DataSourceInstance.BrokingPlatform, actual: row.DataSourceInstanceId);
        Assert.Equal(expected: placementRecord.PlacementId, actual: row.PlacementId);
        Assert.Equal(expected: LookupRefPlacementStatusId(DataSourceInstance.BrokingPlatform, (int)placementStagingRecord.PlacementStatusId), actual: row.PlacementStatusId);
        Assert.True(row.ETLUpdatedDate > DateTime.UtcNow.AddMinutes(-1));
        row = GetResultRow(tableName: "PS.PlacementStatusHistory", whereClause: $"SourceUpdatedDate = '{placementStagingRecord2.ValidFrom:O}'");
        Assert.Equal(expected: (int)DataSourceInstance.BrokingPlatform, actual: row.DataSourceInstanceId);
        Assert.Equal(expected: placementRecord.PlacementId, actual: row.PlacementId);
        Assert.Equal(expected: LookupRefPlacementStatusId(DataSourceInstance.BrokingPlatform, (int)placementStagingRecord2.PlacementStatusId), actual: row.PlacementStatusId);
        Assert.True(row.ETLUpdatedDate > DateTime.UtcNow.AddMinutes(-1));
    }

    /// <summary>
    /// Needed a test to help develop this.
    /// The Placement may have multiple changes staged but there may not be any status change.
    /// Not only that we may already have the status stored from a previous run so we can just store
    /// or update the record if we already have the status change.
    /// </summary>
    [Fact]
    public void TakesFirstDateIfMultipleRecordsStagedWithSameStatusTest()
    {
        PopulateRefPlacementStatus();

        dynamic placementRecord = CreateRow(
            tableName: "dbo.Placement",
            values: new
            {
                PlacementSystemId = 13579,
                DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform
            });

        dynamic placementStagingRecord = CreateRow(
            tableName: "BPStaging.Placement",
            values: new
            {
                Id = placementRecord.PlacementSystemId,
                PlacementStatusId = (int)PlacementStatus.NotStarted,
                ValidFrom = DateTime.UtcNow.AddMinutes(-30).WithPrecision(2), 
                ValidTo = DateTime.UtcNow.AddMinutes(-20).WithPrecision(2)
            });
        // This one repeats the previous status so must be ignored.
        dynamic placementStagingRecord2 = CreateRow(
            tableName: "BPStaging.Placement",
            values: new
            {
                Id = placementRecord.PlacementSystemId,
                PlacementStatusId = (int)PlacementStatus.NotStarted,
                ValidFrom = DateTime.UtcNow.AddMinutes(-20).WithPrecision(2), 
                ValidTo = DateTime.UtcNow.AddMinutes(-15).WithPrecision(2)
            });
        dynamic placementStagingRecord3 = CreateRow(
            tableName: "BPStaging.Placement",
            values: new
            {
                Id = placementRecord.PlacementSystemId,
                PlacementStatusId = (int)PlacementStatus.InProgress,
                ValidFrom = DateTime.UtcNow.AddMinutes(-15).WithPrecision(2), 
                ValidTo = ValidToOpen
            });

        dynamic result = ExecuteStoredProcedureWithResultRow(storedProcedureName: "BPStaging.Load_PS_PlacementStatusHistory");
        Assert.Equal(expected: 0, actual: result.RejectedCount);
        Assert.Equal(expected: 2, actual: result.InsertedCount);

        CheckSprocExecutionLog(sprocName: "BPStaging.Load_PS_PlacementStatusHistory", insertedCount: 2);

        // Now going to have more than 1 record in the target to check.
        dynamic row = GetResultRow(tableName: "PS.PlacementStatusHistory", whereClause: $"SourceUpdatedDate = '{placementStagingRecord.ValidFrom:O}'");
        Assert.Equal(expected: (int)DataSourceInstance.BrokingPlatform, actual: row.DataSourceInstanceId);
        Assert.Equal(expected: placementRecord.PlacementId, actual: row.PlacementId);
        Assert.Equal(expected: LookupRefPlacementStatusId(DataSourceInstance.BrokingPlatform, (int)placementStagingRecord.PlacementStatusId), actual: row.PlacementStatusId);

        Assert.True(row.ETLUpdatedDate > DateTime.UtcNow.AddMinutes(-1));
        row = GetResultRow(tableName: "PS.PlacementStatusHistory", whereClause: $"SourceUpdatedDate = '{placementStagingRecord3.ValidFrom:O}'");
        Assert.Equal(expected: (int)DataSourceInstance.BrokingPlatform, actual: row.DataSourceInstanceId);
        Assert.Equal(expected: placementRecord.PlacementId, actual: row.PlacementId);
        Assert.Equal(expected: LookupRefPlacementStatusId(DataSourceInstance.BrokingPlatform, (int)placementStagingRecord3.PlacementStatusId), actual: row.PlacementStatusId);
        Assert.True(row.ETLUpdatedDate > DateTime.UtcNow.AddMinutes(-1));
    }

    /// <summary>
    /// There seems to be an issue where if the last record has the same status as
    /// an existing record it stops anything matching that status being stored.
    /// It is just supposed to prevent the same status being stored as the next timed change rather than all.
    /// </summary>
    [Fact]
    public void TestBug271832ScenarioTest()
    {
        PopulateRefPlacementStatus();

        dynamic placementRecord = CreateRow(
            tableName: "dbo.Placement", 
            values: new {
                PlacementId = 1688660,
                PlacementSystemId = 17633257,
                PlacementStatusId = (int)PlacementStatus.Complete,
                DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            });
        _ = CreateRow(
                tableName: "PS.PlacementStatusHistory", 
                columnNames: new[] { "PlacementId", "PlacementStatusId", "PlacementServiceBusId", "DataSourceInstanceId", "SourceUpdatedDate" },
                values: new[] { 
                    new object?[] { 1688660, (int)PlacementStatus.Complete, 836955, placementRecord.DataSourceInstanceId, "2024-01-24 07:33:50"},
                    new object?[] { 1688660, (int)PlacementStatus.InProgress, null, placementRecord.DataSourceInstanceId, "2024-01-24 07:33:26"} 
                });
        _ = CreateRow(
                tableName: "BPStaging.Placement",
                columnNames: new[] { "Id", "PlacementStatusId", "ValidFrom", "ValidTo" },
                values: new[] {
                            new object?[] { 17633257, (int)PlacementStatus.Complete, "2024-04-12 11:14:53", "9999-12-31 23:59:59"},
                            new object?[] { 17633257, (int)PlacementStatus.Complete, "2024-03-26 14:13:47", "2024-04-12 11:14:32"},
                            new object?[] { 17633257, (int)PlacementStatus.InProgress, "2024-04-12 11:14:32", "2024-04-12 11:14:53"},
                });

        // I'm expecting this to insert 3 of the 2 records.
        dynamic result = ExecuteStoredProcedureWithResultRow(storedProcedureName: "BPStaging.Load_PS_PlacementStatusHistory");
        Assert.Equal(expected: 0, actual: result.RejectedCount);
        Assert.Equal(expected: 2, actual: result.InsertedCount);

        CheckSprocExecutionLog(sprocName: "BPStaging.Load_PS_PlacementStatusHistory", insertedCount: 2);

        // And check they are the two records they should be.
        dynamic row = GetResultRow(tableName: "PS.PlacementStatusHistory", whereClause: $"SourceUpdatedDate = '2024-04-12 11:14:53'");
        Assert.Equal(expected: (int)PlacementStatus.Complete, actual: row.PlacementStatusId);
        row = GetResultRow(tableName: "PS.PlacementStatusHistory", whereClause: $"SourceUpdatedDate = '2024-04-12 11:14:32'");
        Assert.Equal(expected: (int)PlacementStatus.InProgress, actual: row.PlacementStatusId);
    }

    #region Constructor
    /// <summary>
    /// Constructor - Do not change.
    /// </summary>
    /// <param name="fixture"></param>
    public Load_PS_PlacementStatusHistoryTests(DatabaseFixture fixture) : base(fixture)
    {
    }
    #endregion
}
