﻿using System.Diagnostics.CodeAnalysis;
using Xunit.Abstractions;

namespace PsDb.Tests.BPStaging.StoredProcedures;
[ExcludeFromCodeCoverage]
public class Load_PS_SubmissionPortalSummaryTests : PlacementStoreTestBase
{
    [Fact]
    public void NoDataTest()
    {
        dynamic result = ExecuteStoredProcedureWithResultRow(storedProcedureName: "BPStaging.Load_PS_SubmissionPortalSummary");
        Assert.NotNull(result);
        Assert.Equal(expected: 0, actual: result.RejectedCount);
        Assert.Equal(expected: 0, actual: result.DeletedCount);
        Assert.Equal(expected: 0, actual: result.InsertedCount);
        Assert.Equal(expected: 0, actual: result.UpdatedCount);
        CheckSprocExecutionLog(sprocName: "BPStaging.Load_PS_SubmissionPortalSummary");
    }

    [Fact]
    public void InsertTest()
    {
        dynamic NegotiaionRecord = CreateRow("PS.Negotiation", values: new
        {
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            NegotiationId = 123,
            NegotiationKey = "SUBC|1"
        });

        dynamic NegotiationMarket = CreateRow("PS.NegotiationMarket", values: new
        {
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            NegotiationMarketId = 123,
            NegotiationId = 123,
            NegotiationMarketKey = "SUBCONMKT|1"
        });
        dynamic stagingRecord = CreateRow("BP.SubmissionPortalUser", values: new
        {
            Id = 1,
            SubmissionId = 1,
            SubmissionContainerMarketId = 1,
            PortalUserId = 1,
            NotificationSent = 1,
            NotificationSentTimestamp = DateTime.UtcNow.AddMinutes(-120),
            SubmissionOpened = 1,
            SubmissionOpenedTimestamp = DateTime.UtcNow.AddMinutes(-120),
        });

        dynamic SubmissionRecord = CreateRow("BP.Submission", values: new
        {
            Id = 1,
            SubmissionContainerId = 2,
            Sent = "2019-10-11 14:09:09.033"
        });
        dynamic result = ExecuteStoredProcedureWithResultRow(storedProcedureName: "BPStaging.Load_PS_SubmissionPortalSummary");
        Assert.NotNull(result);
        Assert.Equal(expected: 0, actual: result.RejectedCount);
        Assert.Equal(expected: 0, actual: result.DeletedCount);
        Assert.Equal(expected: 1, actual: result.InsertedCount);
        Assert.Equal(expected: 0, actual: result.UpdatedCount);
        CheckSprocExecutionLog(sprocName: "BPStaging.Load_PS_SubmissionPortalSummary", insertedCount: 1);

        dynamic row = GetResultRow(tableName: "PS.SubmissionPortalSummary");
        Assert.NotNull(row);
        Assert.Equal(expected: stagingRecord.SubmissionId, actual: row.SubmissionId);
        Assert.Equal(expected: stagingRecord.SubmissionContainerMarketId, actual: row.SubmissionContainerMarketId);
        Assert.Equal(expected: 1, actual: row.NotifiedCount);
        Assert.Equal(expected: 1, actual: row.OpenedCount);
    }


    [Fact]
    public void UpdateTest()
    {
        dynamic NegotiaionRecord = CreateRow("PS.Negotiation", values: new
        {
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            NegotiationId = 123,
            NegotiationKey = "SUBC|1"
        });

        dynamic NegotiationMarket = CreateRow("PS.NegotiationMarket", values: new
        {
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            NegotiationMarketId = 123,
            NegotiationId = 123,
            NegotiationMarketKey = "SUBCONMKT|1"
        });
        dynamic stagingRecord1 = CreateRow("BP.SubmissionPortalUser", values: new
        {
            Id = 1,
            SubmissionId = 1,
            SubmissionContainerMarketId = 1,
            PortalUserId = 1,
            NotificationSent = 1,
            NotificationSentTimestamp = DateTime.UtcNow.AddMinutes(-120),
            SubmissionOpened = 1,
            SubmissionOpenedTimestamp = DateTime.UtcNow.AddMinutes(-120),
        });
        dynamic stagingRecord2 = CreateRow("BP.SubmissionPortalUser", values: new
        {
            Id = 2,
            SubmissionId = 1,
            SubmissionContainerMarketId = 1,
            PortalUserId = 2,
            NotificationSent = 1,
            NotificationSentTimestamp = DateTime.UtcNow.AddMinutes(-60),
            SubmissionOpened = 1,
            SubmissionOpenedTimestamp = DateTime.UtcNow.AddMinutes(-60),
        });

        dynamic SubmissionRecord = CreateRow("BP.Submission", values: new
        {
            Id = 1,
            SubmissionContainerId = 2,
            Sent = "2019-10-11 14:09:09.033"
        });
        dynamic SubmissionCoverageGroup = CreateRow("PS.SubmissionPortalSummary", values: new
        {
            SubmissionId = 1,
            SubmissionContainerMarketId = 1,
            SubmissionCount = 1,
            NumberOfPortalUsers = 1,
            NotifiedCount = 1,
            OpenedCount = 1
        });

        dynamic result = ExecuteStoredProcedureWithResultRow(storedProcedureName: "BPStaging.Load_PS_SubmissionPortalSummary");
        Assert.NotNull(result);
        Assert.Equal(expected: 0, actual: result.RejectedCount);
        Assert.Equal(expected: 0, actual: result.DeletedCount);
        Assert.Equal(expected: 0, actual: result.InsertedCount);
        Assert.Equal(expected: 1, actual: result.UpdatedCount);
        CheckSprocExecutionLog(sprocName: "BPStaging.Load_PS_SubmissionPortalSummary", updatedCount: 1);

        dynamic row = GetResultRow(tableName: "PS.SubmissionPortalSummary");
        Assert.NotNull(row);
        Assert.Equal(expected: 2, actual: row.SubmissionCount);
        Assert.Equal(expected: 2, actual: row.NumberOfPortalUsers);
        Assert.Equal(expected: 2, actual: row.NotifiedCount);
        Assert.Equal(expected: 2, actual: row.OpenedCount);
        Assert.Equal(expected: stagingRecord1.SubmissionOpenedTimestamp, actual: row.FirstOpened);
        Assert.Equal(expected: stagingRecord2.SubmissionOpenedTimestamp, actual: row.LastOpened);
        Assert.Equal(expected: stagingRecord1.NotificationSentTimestamp, actual: row.FirstNotified);
        Assert.Equal(expected: stagingRecord2.NotificationSentTimestamp, actual: row.LastNotified);
    }
    [Fact]
    public void DeleteTest()
    {
        dynamic NegotiaionRecord = CreateRow("PS.Negotiation", values: new
        {
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            NegotiationId = 123,
            NegotiationKey = "SUBC|2"
        });

        dynamic NegotiationMarket = CreateRow("PS.NegotiationMarket", values: new
        {
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            NegotiationMarketId = 123,
            NegotiationId = 123,
            NegotiationMarketKey = "SUBCONMKT|2"
        });
        dynamic stagingRecord = CreateRow("BP.SubmissionPortalUser", values: new
        {
            Id = 1,
            SubmissionId = 1,
            SubmissionContainerMarketId = 1,
            PortalUserId = 1,
            NotificationSent = 1,
            NotificationSentTimestamp = DateTime.UtcNow.AddMinutes(-120),
            SubmissionOpened = 1,
            SubmissionOpenedTimestamp = DateTime.UtcNow.AddMinutes(-120),
        });

        dynamic SubmissionRecord = CreateRow("BP.Submission", values: new
        {
            Id = 1,
            SubmissionContainerId = 2,
            Sent = "2019-10-11 14:09:09.033"
        });
        dynamic SubmissionCoverageGroup = CreateRow("PS.SubmissionPortalSummary", values: new
        {
            SubmissionId = 2,
            SubmissionContainerMarketId = 2,
            SubmissionCount = 1,
            NumberOfPortalUsers = 1,
            NotifiedCount = 17,
            OpenedCount = 0
        });

        dynamic result = ExecuteStoredProcedureWithResultRow(storedProcedureName: "BPStaging.Load_PS_SubmissionPortalSummary");
        Assert.NotNull(result);
        Assert.Equal(expected: 0, actual: result.RejectedCount);
        Assert.Equal(expected: 1, actual: result.DeletedCount);
        Assert.Equal(expected: 0, actual: result.InsertedCount);
        Assert.Equal(expected: 0, actual: result.UpdatedCount);
        CheckSprocExecutionLog(sprocName: "BPStaging.Load_PS_SubmissionPortalSummary", deletedCount: 1);

        dynamic row = GetResultRow(tableName: "PS.SubmissionPortalSummary", whereClause: "SubmissionId = 2");
        Assert.Null(row);
    }
    public Load_PS_SubmissionPortalSummaryTests(DatabaseFixture fixture) : base(fixture)
    {
    }
}
