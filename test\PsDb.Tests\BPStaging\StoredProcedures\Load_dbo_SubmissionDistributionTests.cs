﻿using System.Diagnostics.CodeAnalysis;
using Xunit.Abstractions;

namespace PsDb.Tests.BPStaging.StoredProcedures;
[ExcludeFromCodeCoverage]
public class Load_dbo_SubmissionDistributionTests : PlacementStoreTestBase
{
    [Fact]
    public void NoDataTest()
    {
        dynamic result = ExecuteStoredProcedureWithResultRow(storedProcedureName: "BPStaging.Load_dbo_SubmissionDistribution");
        Assert.NotNull(result);
        Assert.Equal(expected: 0, actual: result.RejectedCount);
        Assert.Equal(expected: 0, actual: result.DeletedCount);
        Assert.Equal(expected: 0, actual: result.InsertedCount);
        Assert.Equal(expected: 0, actual: result.UpdatedCount);
        CheckSprocExecutionLog(sprocName: "BPStaging.Load_dbo_SubmissionDistribution");
    }


    [Fact]
    public void SubmissionDistributionInsertTest()
    {
        dynamic negotiationRecord = CreateRow("PS.Negotiation", values: new
        {
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            NegotiationId = 1,
            NegotiationKey = "SUBC|1"
        });
        dynamic NegotiationMarket1 = CreateRow("PS.NegotiationMarket", values: new
        {
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            NegotiationMarketId = 123,
            NegotiationId = negotiationRecord.NegotiationId,
            NegotiationMarketKey = "SUBCONMKT|2"
        });
        dynamic negotiationRecord2 = CreateRow("PS.Negotiation", values: new
        {
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            NegotiationId = 2,
            NegotiationKey = "PLAC|2"
        });
        dynamic NegotiationMarket2 = CreateRow("PS.NegotiationMarket", values: new
        {
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            NegotiationMarketId = 124,
            NegotiationId = negotiationRecord2.NegotiationId,
            NegotiationMarketKey = "EXPRESMKT|2"
        });

        dynamic PlacementSystemUser1 = CreateRow("dbo.PlacementSystemUser", values: new
        {
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            UserID = 1,
            UserPrincipalName = "test"
        });

        dynamic stagingRecord1 = CreateRow("BP.SubmissionMarket", values: new
        {
            Id = 1,
            SubmissionId = 1,
            SubmissionContainerMarketId = 2
        });

        dynamic SubmissionRecord1 = CreateRow("BP.Submission", values: new
        {
            Id = 1,
            SubmissionContainerId = 2,
            Sent = "2019-10-11 14:09:09.033"
        });
        dynamic result = ExecuteStoredProcedureWithResultRow(storedProcedureName: "BPStaging.Load_dbo_SubmissionDistribution");
        Assert.NotNull(result);
        Assert.Equal(expected: 0, actual: result.RejectedCount);
        Assert.Equal(expected: 0, actual: result.DeletedCount);
        Assert.Equal(expected: 1, actual: result.InsertedCount);
        Assert.Equal(expected: 0, actual: result.UpdatedCount);
        CheckSprocExecutionLog(sprocName: "BPStaging.Load_dbo_SubmissionDistribution", insertedCount: 1);

        dynamic row = GetResultRow(tableName: "dbo.SubmissionDistribution");
        Assert.NotNull(row);
        Assert.Equal(expected: stagingRecord1.Id, actual: row.SubmissionMarketId);
        Assert.Equal(expected: stagingRecord1.SubmissionContainerMarketId, actual: row.SubmissionContainerMarketId);

    }

    [Fact]
    public void SubmissionDistributionUpdateTest()
    {
        dynamic NegotiationMarket1 = CreateRow("PS.NegotiationMarket", values: new
        {
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            NegotiationMarketId = 123,
            NegotiationId = 123,
            NegotiationMarketKey = "SUBCONMKT|2"
        });
        dynamic stagingRecord = CreateRow("BP.SubmissionMarket", values: new
        {
            Id = 1,
            SubmissionId = 1,
            SubmissionContainerMarketId = 2
        });
        dynamic SubmissionDistribution = CreateRow("dbo.SubmissionDistribution", values: new
        {
            SubmissionMarketId = 1,
            SubmissionContainerMarketId = 1
        });

        dynamic PlacementSystemUser1 = CreateRow("dbo.PlacementSystemUser", values: new
        {
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            UserID = 1,
            UserPrincipalName = "test"
        });

        dynamic SubmissionRecord1 = CreateRow("BP.Submission", values: new
        {
            Id = 1,
            SubmissionContainerId = 2,
            Sent = "2019-10-11 14:09:09.033"
        });
        dynamic result = ExecuteStoredProcedureWithResultRow(storedProcedureName: "BPStaging.Load_dbo_SubmissionDistribution");
        Assert.NotNull(result);
        Assert.Equal(expected: 0, actual: result.RejectedCount);
        Assert.Equal(expected: 0, actual: result.DeletedCount);
        Assert.Equal(expected: 0, actual: result.InsertedCount);
        Assert.Equal(expected: 1, actual: result.UpdatedCount);
        CheckSprocExecutionLog(sprocName: "BPStaging.Load_dbo_SubmissionDistribution", updatedCount: 1);
        dynamic row = GetResultRow(tableName: "dbo.SubmissionDistribution");
        Assert.NotNull(row);
        Assert.Equal(expected: stagingRecord.SubmissionContainerMarketId, actual: row.SubmissionContainerMarketId);
    }
    [Fact]
    public void SubmissionDistributionDeleteTest()
    {
        dynamic NegotiaionRecord = CreateRow("PS.Negotiation", values: new
        {
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            NegotiationId = 123,
            NegotiationKey = "SUBC|2"
        });
        dynamic NegotiationMarket1 = CreateRow("PS.NegotiationMarket", values: new
        {
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            NegotiationMarketId = 123,
            NegotiationId = 123,
            NegotiationMarketKey = "SUBCONMKT|3"
        });
        dynamic stagingRecord = CreateRow("BP.SubmissionMarket", values: new
        {
            Id = 2,
            SubmissionId = 1,
            SubmissionContainerMarketId = 2
        });
        dynamic SubmissionRecord1 = CreateRow("BP.Submission", values: new
        {
            Id = 1,
            SubmissionContainerId = 2,
            Sent = "2019-10-11 14:09:09.033"
        });
        dynamic SubmissionDistribution = CreateRow("dbo.SubmissionDistribution", values: new
        {
            SubmissionMarketId = 1,
            SubmissionContainerMarketId = 2
        });

        dynamic result = ExecuteStoredProcedureWithResultRow(storedProcedureName: "BPStaging.Load_dbo_SubmissionDistribution");
        Assert.NotNull(result);
        Assert.Equal(expected: 0, actual: result.RejectedCount);
        Assert.Equal(expected: 1, actual: result.DeletedCount);
        Assert.Equal(expected: 0, actual: result.InsertedCount);
        Assert.Equal(expected: 0, actual: result.UpdatedCount);
        CheckSprocExecutionLog(sprocName: "BPStaging.Load_dbo_SubmissionDistribution", deletedCount: 1);
        dynamic row = GetResultRow(tableName: "dbo.SubmissionDistribution", whereClause: "SubmissionMarketId = 1");
        Assert.Null(row);
    }

    [Fact]
    public void CheckADFInterdependencies()
    {
        CheckToSeeIfInterdependenciesExistForStoredProcedure(
            storedProcedureName: @"BPStaging.Load_dbo_SubmissionDistribution"
        );
    }

    #region Constructor
    public Load_dbo_SubmissionDistributionTests(DatabaseFixture fixture, ITestOutputHelper output) : base(fixture, output)
    {
    }
    #endregion
}
