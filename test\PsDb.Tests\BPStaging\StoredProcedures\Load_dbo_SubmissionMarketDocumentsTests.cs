﻿using Microsoft.VisualStudio.TestPlatform.Utilities;
using System.Data;
using System.Diagnostics.CodeAnalysis;
using System.Text;
using Xunit.Abstractions;

namespace PsDb.Tests.BPStaging.StoredProcedures;
[ExcludeFromCodeCoverage]
public class Load_dbo_SubmissionMarketDocumentsTests : PlacementStoreTestBase
{
    private readonly ITestOutputHelper? output;

    [Fact]
    public void NoDataTest()
    {
        dynamic result = ExecuteStoredProcedureWithResultRow(storedProcedureName: "BPStaging.Load_dbo_SubmissionMarketDocuments");
        Assert.NotNull(result);
        Assert.Equal(expected: 0, actual: result.RejectedCount);
        Assert.Equal(expected: 0, actual: result.DeletedCount);
        Assert.Equal(expected: 0, actual: result.InsertedCount);
        Assert.Equal(expected: 0, actual: result.UpdatedCount);
        CheckSprocExecutionLog(sprocName: "BPStaging.Load_dbo_SubmissionMarketDocuments");
    }

    [Fact]
    public void InsertTest()
    {
        dynamic NegotiaionRecord = CreateRow("PS.Negotiation", values: new
        {
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            NegotiationId = 123,
            NegotiationKey = "SUBC|1"
        });

        dynamic NegotiationMarket = CreateRow("PS.NegotiationMarket", values: new
        {
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            NegotiationMarketId = 123,
            NegotiationId = 123,
            NegotiationMarketKey = "SUBCONMKT|1"
        });
        dynamic stagingRecord = CreateRow("BP.SubmissionDocument", values: new
        {
            SubmissionId = 1,
            LastModified = DateTime.Parse("2020-02-03 15:53:29.0000000")
        });
        dynamic stagingSubmissionPortalUserRecord = CreateRow("BP.SubmissionPortalUser", values: new
        {
            SubmissionId = stagingRecord.SubmissionId,
            SubmissionContainerMarketId = 1
        });
        dynamic stagingPortalUserDocumentRecord = CreateRow("BP.PortalUserDocument", values: new
        {
            SubmissionDocumentId = stagingRecord.Id,
            SubmissionPortalUserId = stagingSubmissionPortalUserRecord.Id,
            DocumentDownloaded = false
        });
        

        dynamic SubmissionRecord = CreateRow("BP.Submission", values: new
        {
            Id = 1,
            SubmissionContainerId = 2,
            Sent = "2019-10-11 14:09:09.033"
        });
        dynamic result = ExecuteStoredProcedureWithResultRow(storedProcedureName: "BPStaging.Load_dbo_SubmissionMarketDocuments");
        Assert.NotNull(result);
        Assert.Equal(expected: 0, actual: result.RejectedCount);
        Assert.Equal(expected: 0, actual: result.DeletedCount);
        Assert.Equal(expected: 1, actual: result.InsertedCount);
        Assert.Equal(expected: 0, actual: result.UpdatedCount);
        CheckSprocExecutionLog(sprocName: "BPStaging.Load_dbo_SubmissionMarketDocuments", insertedCount: 1);

        dynamic row = GetResultRow(tableName: "dbo.SubmissionMarketDocuments");
        Assert.NotNull(row);
        Assert.Equal(expected: stagingRecord.SubmissionId, actual: row.SubmissionId);
        Assert.Equal(expected: stagingSubmissionPortalUserRecord.SubmissionContainerMarketId, actual: row.SubmissionContainerMarketId);
    }


    [Fact]
    public void UpdateTest()
    {
        dynamic NegotiaionRecord = CreateRow("PS.Negotiation", values: new
        {
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            NegotiationId = 123,
            NegotiationKey = "SUBC|1"
        });

        dynamic NegotiationMarket = CreateRow("PS.NegotiationMarket", values: new
        {
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            NegotiationMarketId = 123,
            NegotiationId = 123,
            NegotiationMarketKey = "SUBCONMKT|1"
        });
        dynamic stagingRecord = CreateRow("BP.SubmissionDocument", values: new
        {
            SubmissionId = 1,
            LastModified = DateTime.Parse("2020-02-03 15:53:29.0000000")
        });
        dynamic stagingSubmissionPortalUserRecord = CreateRow("BP.SubmissionPortalUser", values: new
        {
            SubmissionId = stagingRecord.SubmissionId,
            SubmissionContainerMarketId = 1,

        });
        dynamic stagingPortalUserDocumentRecord = CreateRow("BP.PortalUserDocument", values: new
        {
            SubmissionDocumentId = stagingRecord.Id,
            SubmissionPortalUserId = stagingSubmissionPortalUserRecord.Id,
            DocumentDownloaded = false
        });

        dynamic SubmissionRecord = CreateRow("BP.Submission", values: new
        {
            Id = 1,
            SubmissionContainerId = 2,
            Sent = "2019-10-11 14:09:09.033"
        });
        dynamic SubmissionCoverageGroup = CreateRow("dbo.SubmissionMarketDocuments", values: new
        {
            SubmissionId = 1,
            SubmissionContainerMarketId = 1,
            NumberOfDocuments = 2,
            NumberOfPortalUsers = 2,
            DocumentNotDownloaded = 2
        });

        dynamic result = ExecuteStoredProcedureWithResultRow(storedProcedureName: "BPStaging.Load_dbo_SubmissionMarketDocuments");
        Assert.NotNull(result);
        Assert.Equal(expected: 0, actual: result.RejectedCount);
        Assert.Equal(expected: 0, actual: result.DeletedCount);
        Assert.Equal(expected: 0, actual: result.InsertedCount);
        Assert.Equal(expected: 1, actual: result.UpdatedCount);
        CheckSprocExecutionLog(sprocName: "BPStaging.Load_dbo_SubmissionMarketDocuments", updatedCount: 1);

        dynamic row = GetResultRow(tableName: "dbo.SubmissionMarketDocuments");
        Assert.NotNull(row);
        Assert.Equal(expected: 1, actual: row.NumberOfDocuments);
        Assert.Equal(expected: 1, actual: row.NumberOfPortalUsers);
        Assert.Equal(expected: 1, actual: row.DocumentNotDownloaded);

    }
    [Fact]
    public void DeleteTest()
    {
        dynamic NegotiaionRecord = CreateRow("PS.Negotiation", values: new
        {
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            NegotiationId = 123,
            NegotiationKey = "SUBC|2"
        });

        dynamic NegotiationMarket = CreateRow("PS.NegotiationMarket", values: new
        {
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            NegotiationMarketId = 123,
            NegotiationId = 123,
            NegotiationMarketKey = "SUBCONMKT|2"
        });
        dynamic stagingRecord = CreateRow("BP.SubmissionDocument", values: new
        {
            SubmissionId = 1,
            LastModified = DateTime.Parse("2020-02-03 15:53:29.0000000")
        });

        dynamic SubmissionRecord = CreateRow("BP.Submission", values: new
        {
            Id = 1,
            SubmissionContainerId = 2,
            Sent = "2019-10-11 14:09:09.033"
        });
        dynamic SubmissionCoverageGroup = CreateRow("dbo.SubmissionMarketDocuments", values: new
        {
            SubmissionId = 2,
            SubmissionContainerMarketId = 2,
            NumberOfDocuments = 2,
            NumberOfPortalUsers = 2,
            DocumentNotDownloaded = 1
        });

        dynamic result = ExecuteStoredProcedureWithResultRow(storedProcedureName: "BPStaging.Load_dbo_SubmissionMarketDocuments");
        Assert.NotNull(result);
        Assert.Equal(expected: 0, actual: result.RejectedCount);
        Assert.Equal(expected: 1, actual: result.DeletedCount);
        Assert.Equal(expected: 0, actual: result.InsertedCount);
        Assert.Equal(expected: 0, actual: result.UpdatedCount);

        CheckSprocExecutionLog(sprocName: "BPStaging.Load_dbo_SubmissionMarketDocuments", deletedCount: 1);

        dynamic row = GetResultRow(tableName: "dbo.SubmissionMarketDocuments", whereClause: "SubmissionId = 2");
        Assert.Null(row);
    }

    /// <summary>
    /// A test which checks to see if the stored procedure is dependent on all the tables it uses.
    /// Also checks dependencies for foreign keys.
    /// </summary>
    [Fact]
    public void CheckADFInterdependencies()
    {
        CheckToSeeIfInterdependenciesExistForStoredProcedure(storedProcedureName: @"BPStaging.Load_dbo_SubmissionMarketDocuments");
    }

    #region Constructor

    public Load_dbo_SubmissionMarketDocumentsTests(DatabaseFixture fixture, ITestOutputHelper output) : base(fixture, output)
    {
        this.output = output;
    }
    
    #endregion
}
