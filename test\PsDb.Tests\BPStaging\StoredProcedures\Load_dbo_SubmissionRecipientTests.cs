﻿using System.Diagnostics.CodeAnalysis;

namespace PsDb.Tests.BPStaging.StoredProcedures;
[ExcludeFromCodeCoverage]
public class Load_dbo_SubmissionRecipientTests : PlacementStoreTestBase
{
    [Fact]
    public void NoDataTest()
    {
        dynamic result = ExecuteStoredProcedureWithResultRow(storedProcedureName: "BPStaging.Load_dbo_SubmissionRecipient");
        Assert.NotNull(result);
        Assert.Equal(expected: 0, actual: result.RejectedCount);
        Assert.Equal(expected: 0, actual: result.DeletedCount);
        Assert.Equal(expected: 0, actual: result.InsertedCount);
        Assert.Equal(expected: 0, actual: result.UpdatedCount);
        CheckSprocExecutionLog(sprocName: "BPStaging.Load_dbo_SubmissionRecipient");
    }

    [Fact]
    public void SubmissionRecipientInsertTest()
    {
        dynamic NegotiaionRecord1 = CreateRow("PS.Negotiation", values: new
        {
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            NegotiationId = 123,
            NegotiationKey = "SUBC|2"
        });

        dynamic NegotiationMarket1 = CreateRow("PS.NegotiationMarket", values: new
        {
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            NegotiationMarketId = 123,
            NegotiationId = 123,
            NegotiationMarketKey = "SUBCONMKT|2"
        });

        dynamic stagingRecord1 = CreateRow("BP.SubmissionMarketRecipient", values: new
        {
            Id = 1,
            SubmissionMarketId = 1,
            Email = "test"
        });

        dynamic PlacementSystemUser = CreateRow("dbo.PlacementSystemUser", values: new
        {
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            UserID = 1,
            UserPrincipalName = "test"
        });
        dynamic SubmissionDistribution = CreateRow("dbo.SubmissionDistribution", values: new
        {
            SubmissionMarketId = 1,
            SubmissionContainerMarketId = 2
        });
        dynamic result = ExecuteStoredProcedureWithResultRow(storedProcedureName: "BPStaging.Load_dbo_SubmissionRecipient");
        Assert.NotNull(result);
        Assert.Equal(expected: 0, actual: result.RejectedCount);
        Assert.Equal(expected: 0, actual: result.DeletedCount);
        Assert.Equal(expected: 1, actual: result.InsertedCount);
        Assert.Equal(expected: 0, actual: result.UpdatedCount);
        CheckSprocExecutionLog(sprocName: "BPStaging.Load_dbo_SubmissionRecipient", insertedCount: 1);

        dynamic row = GetResultRow(tableName: "dbo.SubmissionRecipient");
        Assert.NotNull(row);
        Assert.Equal(expected: stagingRecord1.SubmissionMarketId, actual: row.SubmissionMarketId);
        Assert.Equal(expected: stagingRecord1.Id, actual: row.RecipientId);

    }

    [Fact]
    public void SubmissionRecipientUpdateTest()
    {
        dynamic NegotiaionRecord = CreateRow("PS.Negotiation", values: new
        {
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            NegotiationId = 123,
            NegotiationKey = "SUBC|2"
        });

        dynamic NegotiationMarket1 = CreateRow("PS.NegotiationMarket", values: new
        {
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            NegotiationMarketId = 123,
            NegotiationId = 123,
            NegotiationMarketKey = "SUBCONMKT|2"
        });


        dynamic PlacementSystemUser = CreateRow("dbo.PlacementSystemUser", values: new
        {
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            UserID = 1,
            UserPrincipalName = "test"
        });
        dynamic stagingRecord = CreateRow("BP.SubmissionMarketRecipient", values: new
        {
            Id = 1,
            SubmissionMarketId = 1,
            Email = "test"
        });
        dynamic SubmissionDistribution = CreateRow("dbo.SubmissionDistribution", values: new
        {
            SubmissionMarketId = 1,
            SubmissionContainerMarketId = 2
        });

        dynamic SubmissionRecipient = CreateRow("dbo.SubmissionRecipient", values: new
        {
            SubmissionMarketId = 2,
            RecipientId = 1
        });

        dynamic SubmissionRecord1 = CreateRow("BP.Submission", values: new
        {
            Id = 1,
            UserId = 1,
            SubmissionContainerId = 2,
            Sent = "2019-10-11 14:09:09.033"
        });

        dynamic result = ExecuteStoredProcedureWithResultRow(storedProcedureName: "BPStaging.Load_dbo_SubmissionRecipient");
        Assert.NotNull(result);
        Assert.Equal(expected: 0, actual: result.RejectedCount);
        Assert.Equal(expected: 0, actual: result.DeletedCount);
        Assert.Equal(expected: 0, actual: result.InsertedCount);
        Assert.Equal(expected: 1, actual: result.UpdatedCount);

        CheckSprocExecutionLog(sprocName: "BPStaging.Load_dbo_SubmissionRecipient", updatedCount: 1);


        dynamic row = GetResultRow(tableName: "dbo.SubmissionRecipient");
        Assert.NotNull(row);
        Assert.Equal(expected: stagingRecord.SubmissionMarketId, actual: row.SubmissionMarketId);
        Assert.Equal(expected: stagingRecord.Email, actual: row.RecipientEmail);

    }
    [Fact]
    public void SubmissionRecipientDeleteTest()
    {
        dynamic stagingRecord = CreateRow("BP.SubmissionMarketRecipient", values: new
        {
            Id = 1,
            SubmissionMarketId = 2,
            Email = "test"
        });
        dynamic SubmissionRecipient = CreateRow("dbo.SubmissionRecipient", values: new
        {
            SubmissionMarketId = 2,
            RecipientId = 2,
            RecipientEmail = "test"
        });
        dynamic result = ExecuteStoredProcedureWithResultRow(storedProcedureName: "BPStaging.Load_dbo_SubmissionRecipient");
        Assert.NotNull(result);
        Assert.Equal(expected: 0, actual: result.RejectedCount);
        Assert.Equal(expected: 1, actual: result.DeletedCount);
        Assert.Equal(expected: 1, actual: result.InsertedCount);
        Assert.Equal(expected: 0, actual: result.UpdatedCount);
        CheckSprocExecutionLog(sprocName: "BPStaging.Load_dbo_SubmissionRecipient", deletedCount: 1, insertedCount: 1);

        dynamic row = GetResultRow(tableName: "dbo.SubmissionRecipient", whereClause: "RecipientId = 2");
        Assert.Null(row);
    }
    public Load_dbo_SubmissionRecipientTests(DatabaseFixture fixture) : base(fixture)
    {
    }
}
