﻿using PsDb.Tests.PlacementStoreHelpers;
using System.Diagnostics.CodeAnalysis;
using Xunit.Abstractions;

namespace PsDb.Tests.BPStaging.StoredProcedures;
[ExcludeFromCodeCoverage]
public class Load_ref_BasisOfCoverTests : PlacementStoreFullLoadProcedureTestBase
{
    protected override object GetLogicalDeletionValue(dynamic row)
    {
        return row.IsDeprecated;
    }

    protected override object GetStagingUpdatedDateValue(dynamic row)
    {
        return row.ValidFrom;
    }

    protected override dynamic CreateStagingRecord(TestType testType, DateTime sourceUpdatedDate, bool changeSomething)
    {
        return new
        {
            Id = 17,
            TranslationKey = "15",
            Text = "test",
            ScopeId = changeSomething ? 5 : 15,
            ValidFrom = sourceUpdatedDate,
            ValidTo = ValidToOpen,
            IsDeprecated = testType == TestType.LogicalDeleteTest ? true : false,
        };
    }

    protected override dynamic CreateExistingRecord(TestType testType, dynamic stagingRecord)
    {
        return new
        {
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            BasisOfCoverKey = stagingRecord.Id.ToString(),
            TranslationKey = "15",
            BasisOfCover = "test",
            ScopeId = 15,
            ETLCreatedDate = DateTime.UtcNow.AddHours(-1),
            ETLUpdatedDate = DateTime.UtcNow.AddHours(-1),
            SourceUpdatedDate = stagingRecord.ValidFrom,
            IsDeprecated = false,
        };
    }

    protected override void CheckTargetRecordValues(TestType testType, dynamic stagingRecord, dynamic targetResult)
    {
        Assert.Equal(expected: (int)DataSourceInstance.BrokingPlatform, actual: targetResult.DataSourceInstanceId);
        Assert.Equal(expected: stagingRecord.TranslationKey, actual: targetResult.TranslationKey);
        Assert.Equal(expected: stagingRecord.Text, actual: targetResult.BasisOfCover);
        Assert.Equal(expected: stagingRecord.ScopeId, actual: targetResult.ScopeId);
    }

    public Load_ref_BasisOfCoverTests(DatabaseFixture fixture, ITestOutputHelper output) : base(fixture, output)
    {
    }

}
