﻿//#define ShowEachQuery
using System.Text.Json.Nodes;
using Xunit.Abstractions;

namespace PsDb.Tests;

public class CheckModelQueriesTests : PlacementStoreTestBase
{
    private readonly ITestOutputHelper _output;

    /// <summary>
    /// This test checks a BIM file against the PS database.
    /// It assumes it can extract a query from a partition and then
    /// runs the query against the placement store database.
    /// No data but will do a basic syntax check.
    /// </summary>
    /// <param name="modelName"></param>
    /// <param name="modelPath"></param>
    [Theory]
    [InlineData("Carrier", @"..\..\..\..\..\src\ReportingLayer\Carrier.bim")]
    [InlineData("GA", @"..\..\..\..\..\src\ReportingLayer\GA.bim")]
    [InlineData("Usage", @"..\..\..\..\..\src\ReportingLayer\Usage.bim")]
    [Trait("Category", "IgnoreForNow")]
    public void CheckModelQueriesTest(string modelName, string modelPath)
    {
        var json = JsonObject.Parse(File.ReadAllText(modelPath));

        foreach(var table in json["model"]["tables"].AsArray())
        {
            var tableName = table["name"].AsValue().ToString();
#if ShowEachQuery
            _output.WriteLine($"Checking model '{modelName}' - Table : '{tableName}'");
#endif
            var tableType = table["partitions"][0]["source"]["type"].AsValue().ToString();
            if(tableType == "query")
            {
                var dataSource = table["partitions"][0]["source"]["dataSource"].AsValue().ToString();

                // As we can only handle queries against Placement Store.
                Assert.Equal(expected: "PlacementStore", actual: dataSource, ignoreCase: true);

                // Might be a single line query.
                if(table["partitions"][0]["source"]["query"].GetValueKind() == System.Text.Json.JsonValueKind.String)
                {
                    var query = table["partitions"][0]["source"]["query"].ToString();
#if ShowEachQuery
                    _output.WriteLine($"Query: {query}");
#endif
                    Assert.NotEmpty(query);
                    try
                    {
                        ExecuteSQLStatementWithResult(tSQL: query);
                    }
                    catch(Exception ex)
                    {
                        _output.WriteLine($"Checking model '{modelName}' - Table : '{tableName}'");
                        _output.WriteLine($"Query: {query}");
                        Assert.Fail($"Model: '{modelName}'. Table: '{tableName}'. '{ex.Message}'");
                    }
                }
                else if(table["partitions"][0]["source"]["query"].GetValueKind() == System.Text.Json.JsonValueKind.Array)
                {
                    // Query is an array of lines.
                    var query = string.Join("\r\n", table["partitions"][0]["source"]["query"].AsArray());
#if ShowEachQuery
                    _output.WriteLine($"Query: {query}");
#endif
                    Assert.NotEmpty(query);
                    try
                    {
                        ExecuteSQLStatementWithResult(tSQL: query);
                    }
                    catch(Exception ex)
                    {
                        _output.WriteLine($"Checking model '{modelName}' - Table : '{tableName}'");
                        _output.WriteLine($"Query: {query}");
                        Assert.Fail($"Model: '{modelName}'. Table: '{tableName}'. '{ex.Message}'");
                    }
                }
                else
                {
                    // Hopefully we can't get this.
                    Assert.Fail(message: $"Not a supported type: {table["partitions"][0]["source"]["query"].GetValueKind()}");
                }
            }
        }
    }

    #region Constructor

    /// <summary>
    /// Constructor - Do not change.
    /// </summary>
    /// <param name="fixture"></param>
    public CheckModelQueriesTests(DatabaseFixture fixture, ITestOutputHelper output) : base(fixture, output)
    {
        _output = output;
    }

    #endregion
}
