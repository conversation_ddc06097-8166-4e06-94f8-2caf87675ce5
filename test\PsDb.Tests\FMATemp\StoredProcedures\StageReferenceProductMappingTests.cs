﻿namespace PsDb.Tests.FMATemp.StoredProcedures;

public class StageReferenceDataProductMappingTests : PlacementStoreTestBase
{
    [Fact]
    public void NoDataTest()
    {
        dynamic result = ExecuteStoredProcedureWithResultRow(storedProcedureName: "FMATemp.StageReferenceDataProductMapping", values: new
        {
            @LastUpdatedDate = DateTime.UtcNow.AddMinutes(-120)
        });
        Assert.Equal(expected: 0, actual: result.InsertedCount);
        Assert.Equal(expected: 0, actual: result.RejectedCount);
        dynamic row = GetResultRow(tableName: "FMATemp.staging_vw_ReferenceDataProductMapping");
        Assert.Null(row);
    }

    [Fact]
    public void SendsValidClassLineReferenceProductMappingTest()
    {
        dynamic productRecord = CreateRow(tableName: "dbo.Product", values: new
        {
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            ProductKey = "classOfBusiness_Class1|lineOfBusiness_Line1",
            ReferenceProductId = 12345,
            CreatedUTCDate = DateTime.UtcNow.AddMinutes(-10),
            LastUpdatedUTCDate = DateTime.UtcNow.AddMinutes(-10),
            IsDeleted = false
        });

        dynamic productHierarchyRecord = CreateRow(tableName: "rpt.ProductHierarchy", values: new
        {
            ProductId = productRecord.ReferenceProductId,
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            ProductName = "ProductName1",
            LevelNum = 1,
            ETLCreatedDate = DateTime.UtcNow.AddMinutes(-10),
            ETLUpdatedDate = DateTime.UtcNow.AddMinutes(-10),
            IsDeprecated = false
        });

        dynamic result = ExecuteStoredProcedureWithResultRow(storedProcedureName: "FMATemp.StageReferenceDataProductMapping", values: new
        {
            @LastUpdatedDate = DateTime.UtcNow.AddMinutes(-120)
        });
        Assert.Equal(expected: 1, actual: result.InsertedCount);
        Assert.Equal(expected: 0, actual: result.RejectedCount);

        dynamic row = GetResultRow(tableName: "FMATemp.staging_vw_ReferenceDataProductMapping");
        Assert.NotNull(row);
        Assert.Equal(expected: productRecord.ProductId, actual: row.Id);
        Assert.Equal(expected: productHierarchyRecord.ProductId, actual: row.ProductId);
        Assert.Equal(expected: productRecord.IsDeleted, actual: row.IsDeprecated);

    }

    [Fact]
    public void SendsValidLineOnlyReferenceProductMappingTest()
    {
        dynamic productRecord = CreateRow(tableName: "dbo.Product", values: new
        {
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            ProductKey = "|lineOfBusiness_Line1",
            ReferenceProductId = 12345,
            CreatedUTCDate = DateTime.UtcNow.AddMinutes(-10),
            LastUpdatedUTCDate = DateTime.UtcNow.AddMinutes(-10),
            IsDeleted = false
        });

        dynamic productHierarchyRecord = CreateRow(tableName: "rpt.ProductHierarchy", values: new
        {
            ProductId = productRecord.ReferenceProductId,
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            ProductName = "ProductName1",
            LevelNum = 1,
            ETLCreatedDate = DateTime.UtcNow.AddMinutes(-10),
            ETLUpdatedDate = DateTime.UtcNow.AddMinutes(-10),
            IsDeprecated = false
        });

        dynamic result = ExecuteStoredProcedureWithResultRow(storedProcedureName: "FMATemp.StageReferenceDataProductMapping", values: new
        {
            @LastUpdatedDate = DateTime.UtcNow.AddMinutes(-120)
        });
        Assert.Equal(expected: 1, actual: result.InsertedCount);
        Assert.Equal(expected: 0, actual: result.RejectedCount);

        dynamic row = GetResultRow(tableName: "FMATemp.staging_vw_ReferenceDataProductMapping");
        Assert.NotNull(row);
        Assert.Equal(expected: productRecord.ProductId, actual: row.Id);
        Assert.Equal(expected: productHierarchyRecord.ProductId, actual: row.ProductId);
        Assert.Equal(expected: productRecord.IsDeleted, actual: row.IsDeprecated);

    }

    [Fact]
    public void DontSendInvalidReferenceProductMappingTest()
    {
        dynamic productRecord = CreateRow(tableName: "dbo.Product", values: new
        {
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            ProductKey = "Invalid",
            ReferenceProductId = 12345,
            CreatedUTCDate = DateTime.UtcNow.AddMinutes(-10),
            LastUpdatedUTCDate = DateTime.UtcNow.AddMinutes(-10),
            IsDeleted = false
        });

        dynamic productHierarchyRecord = CreateRow(tableName: "rpt.ProductHierarchy", values: new
        {
            ProductId = productRecord.ReferenceProductId,
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            ProductName = "ProductName1",
            LevelNum = 1,
            ETLCreatedDate = DateTime.UtcNow.AddMinutes(-10),
            ETLUpdatedDate = DateTime.UtcNow.AddMinutes(-10),
            IsDeprecated = false
        });

        dynamic result = ExecuteStoredProcedureWithResultRow(storedProcedureName: "FMATemp.StageReferenceDataProductMapping", values: new
        {
            @LastUpdatedDate = DateTime.UtcNow.AddMinutes(-120)
        });
        Assert.Equal(expected: 0, actual: result.InsertedCount);
        Assert.Equal(expected: 0, actual: result.RejectedCount);

        dynamic row = GetResultRow(tableName: "FMATemp.staging_vw_ReferenceDataProductMapping");
        Assert.Null(row);

    }

    public StageReferenceDataProductMappingTests(DatabaseFixture fixture) : base(fixture)
    {
    }


}
