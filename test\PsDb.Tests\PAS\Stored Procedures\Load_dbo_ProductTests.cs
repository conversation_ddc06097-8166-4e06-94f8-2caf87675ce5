﻿using System.Diagnostics.CodeAnalysis;
using Xunit.Abstractions;

namespace PsDb.Tests.PAS.StoredProcedures;
[ExcludeFromCodeCoverage]
public class Load_dbo_ProductTests : PlacementStoreTestBase
{
    [Fact]
    public void NoStageDataTest()
    {
        dynamic spResult = ExecuteStoredProcedureWithResultRow(storedProcedureName: "PAS.Load_dbo_Product");

        dynamic row = GetResultRow(tableName: "dbo.Product", whereClause: "DataSourceInstanceId <> " + (int)DataSourceInstance.BrokingPlatform);
        Assert.Null(row);
    }

    [Theory]
    [InlineData((int)DataSource.Eclipse, (int)DataSourceInstance.Eclipse, "PRDKEY1", null, 1, 100000, "Airline Hull", "Aerospace", "Aviation", null, null, false)]
    [InlineData((int)DataSource.Eclipse, (int)DataSourceInstance.Eclipse, "PRDKEY2", "PARENTPRDKEY2", 2, 200000, "RE PropTtyAcct", "Agg", "PropertyRe", null, null, false)]
    [InlineData((int)DataSource.eGlobal, (int)DataSourceInstance.eGlobalAustralia, "PRDKEY1", null, 1, 100000, "AVIATION", "AVI EXCESSAVIATIONLI", null, "0", "0", false)]
    [InlineData((int)DataSource.eGlobal, (int)DataSourceInstance.eGlobalAustralia, "PRDKEY1", null, 1, 100000, "AVIATION", "AVI EXCESSAVIATIONLI", null, "1", "1", false)]
    [InlineData((int)DataSource.eGlobal, (int)DataSourceInstance.eGlobalAustralia, "PRDKEY1", null, 1, 100000, "AVIATION", "AVI EXCESSAVIATIONLI", null, "-1", "-1", false)]
    [InlineData((int)DataSource.eGlobal, (int)DataSourceInstance.eGlobalAustralia, "PRDKEY2", "PARENTPRDKEY2", 2, 200000, "ACCHEALTHTRAVEL", "AHT ACTIVEASSAILANT", null, "0", "0", false)]
    [InlineData((int)DataSource.eGlobal, (int)DataSourceInstance.eGlobalAustralia, "PRDKEY2", "PARENTPRDKEY2", 2, 200000, "ACCHEALTHTRAVEL", "AHT ACTIVEASSAILANT", null, "1", "1", false)]
    [InlineData((int)DataSource.eGlobal, (int)DataSourceInstance.eGlobalAustralia, "PRDKEY2", "PARENTPRDKEY2", 2, 200000, "ACCHEALTHTRAVEL", "AHT ACTIVEASSAILANT", null, "-1", "-1", false)]
    [InlineData((int)DataSource.eGlobal, (int)DataSourceInstance.eGlobalNetherlands, "PRDKEY1", null, 1, 100000, "CONSTRUCTION", "BOUW- EN OPERATIONEL", "Build & Operate cover", "0", "0", false)]
    [InlineData((int)DataSource.eGlobal, (int)DataSourceInstance.eGlobalNetherlands, "PRDKEY1", null, 1, 100000, "FINEX", "BEROEPSAANSPRAKELIJ", "Professional Indemnity Insurance", "1", "1", false)]
    [InlineData((int)DataSource.eGlobal, (int)DataSourceInstance.eGlobalNetherlands, "PRDKEY1", null, 1, 100000, "FLEET", "VERKEERSRISICO", "Legal Aid/ Traffic Risks Insurance", "-1", "-1", false)]
    [InlineData((int)DataSource.eGlobal, (int)DataSourceInstance.eGlobalNetherlands, "PRDKEY2", "PARENTPRDKEY2", 2, 200000, "CONSTRUCTION", "BOUW- EN OPERATIONEL", "Build & Operate cover", "0", "0", false)]
    [InlineData((int)DataSource.eGlobal, (int)DataSourceInstance.eGlobalNetherlands, "PRDKEY2", "PARENTPRDKEY2", 2, 200000, "FINEX", "BEROEPSAANSPRAKELIJ", "Professional Indemnity Insurance", "1", "1", false)]
    [InlineData((int)DataSource.eGlobal, (int)DataSourceInstance.eGlobalNetherlands, "PRDKEY2", "PARENTPRDKEY2", 2, 200000, "FLEET", "VERKEERSRISICO", "Legal Aid/ Traffic Risks Insurance", "-1", "-1", false)]
    [InlineData((int)DataSource.BrazilCOL, (int)DataSourceInstance.BrazilCOL, "PRDKEY1", null, 1, 100000, "PROPERTY", "RISCOS DIVERSOS - INATIVO", "W EMPRESA", null, null, false)]
    [InlineData((int)DataSource.BrazilCOL, (int)DataSourceInstance.BrazilCOL, "CELPRDKEY1", null, 1, 100000, "PROPERTY", "RISCOS DIVERSOS - INATIVO", "W EMPRESA", null, null, false)]
    [InlineData((int)DataSource.BrazilCOL, (int)DataSourceInstance.BrazilCOL, "RAMPRDKEY1", null, 1, 100000, "PROPERTY", "RISCOS DIVERSOS - INATIVO", "W EMPRESA", null, null, false)]
    [InlineData((int)DataSource.BrazilCOL, (int)DataSourceInstance.BrazilCOL, "PRDKEY2", null, 2, 200000, "PROPERTY", "RISCOS DIVERSOS - INATIVO", null, null, null, false)]
    [InlineData((int)DataSource.BrazilCOL, (int)DataSourceInstance.BrazilCOL, "CELPRDKEY2", null, 2, 200000, "PROPERTY", "RISCOS DIVERSOS - INATIVO", null, null, null, false)]
    [InlineData((int)DataSource.BrazilCOL, (int)DataSourceInstance.BrazilCOL, "RAMPRDKEY2", null, 2, 200000, "PROPERTY", "RISCOS DIVERSOS - INATIVO", null, null, null, false)]
    [InlineData((int)DataSource.BrazilCOL, (int)DataSourceInstance.BrazilCOL, "PRDKEY3", null, 3, 300000, "PROPERTY", null, null, null, null, false)]
    [InlineData((int)DataSource.BrazilCOL, (int)DataSourceInstance.BrazilCOL, "CELPRDKEY3", null, 3, 300000, "PROPERTY", null, null, null, null, false)]
    [InlineData((int)DataSource.BrazilCOL, (int)DataSourceInstance.BrazilCOL, "RAMPRDKEY3", null, 3, 300000, "PROPERTY", null, null, null, null, false)]
    [InlineData((int)DataSource.EPIC, (int)DataSourceInstance.EpicUS, "PRDKEY1", null, 1, 100000, "Agriculture Package", null, null, null, null, false)]
    [InlineData((int)DataSource.EPIC, (int)DataSourceInstance.EpicUS, "PRDKEY2", "PARENTPRDKEY2", 2, 200000, "Commercial Package", null, null, null, null, false)]
    [InlineData((int)DataSource.EPIC, (int)DataSourceInstance.EpicCanada, "PRDKEY1", null, 1, 100000, "Agriculture Package", null, null, null, null, false)]
    [InlineData((int)DataSource.EPIC, (int)DataSourceInstance.EpicCanada, "PRDKEY2", "PARENTPRDKEY2", 2, 200000, "Commercial Package", null, null, null, null, false)]
    [InlineData((int)DataSource.WIBS, (int)DataSourceInstance.WIBS, "PRDKEY1", null, 1, 100000, "Motor", "Motor Description", null, null, null, false)]
    [InlineData((int)DataSource.WIBS, (int)DataSourceInstance.WIBS, "PRDKEY2", "PARENTPRDKEY2", 2, 200000, "Financial Lines", "Financial Lines Description", null, null, null, false)]
    [InlineData((int)DataSource.ASYS, (int)DataSourceInstance.ASYSGermanyAndAustria, "PRDKEY1", null, 1, 100000, "Deleted", "Deleted", "Deleted", null, null, true)]
    [InlineData((int)DataSource.ASYS, (int)DataSourceInstance.ASYSGermanyAndAustria, "PRDKEY2", "PARENTPRDKEY2", 2, 200000, "Deleted", "Deleted", "Deleted", null, null, true)]

    public void CreateProductTest(int dataSourceId, int dataSourceInstanceId, string productKey, string? parentProductKey, int pasProductId, int globalProductId, string? productAttribute1, string? productAttribute2, string? productAttribute3, string? isMultiRisk, string? isInactive, bool isDeleted)
    {
        dynamic dataSourceInstanceRecord, pasProductRecord, pasProductAttributeRecord, refProductMappingRecord, parentProductRecord;

        SetUpData(dataSourceId, dataSourceInstanceId, productKey, parentProductKey, pasProductId, globalProductId, productAttribute1, productAttribute2, productAttribute3, isMultiRisk, isInactive, isDeleted
            , out dataSourceInstanceRecord, out pasProductRecord, out pasProductAttributeRecord, out refProductMappingRecord, out parentProductRecord);

        dynamic result = ExecuteStoredProcedureWithResultRow(storedProcedureName: "PAS.Load_dbo_Product");
        Assert.NotNull(result);
        Assert.Equal(expected: 0, actual: result.RejectedCount);
        Assert.Equal(expected: 1, actual: result.InsertedCount);

        dynamic row = GetResultRow(tableName: "dbo.Product", whereClause: " SourceProductId = " + pasProductRecord.PASProductId + " AND DataSourceInstanceId = " + dataSourceInstanceRecord.DataSourceInstanceId + " AND SourcedFromBrokingPlatform = 0");
        Assert.NotNull(row);
        Assert.Equal(expected: pasProductRecord.ProductKey, actual: row.ProductKey);
        if(dataSourceInstanceId == (int)DataSourceInstance.Eclipse)
        {
            Assert.Equal(expected: pasProductAttributeRecord.EclipseProduct, actual: row.ProductLine);
            Assert.Equal(expected: pasProductAttributeRecord.EclipseProductClass, actual: row.ProductClass);
            Assert.Equal(expected: pasProductAttributeRecord.EclipseBusinessClass, actual: row.SourceProductName);
            Assert.Equal(expected: pasProductRecord.ProductDescription, actual: row.SourceProductDescription);
            Assert.Equal(expected: refProductMappingRecord.SourceProductName, actual: row.ReferenceSourceProductName);
        }
        if(dataSourceInstanceId == (int)DataSourceInstance.BrazilCOL)
        {
            if(productAttribute3 != null && pasProductRecord.ProductKey.Substring(0, 3) != "CEL" && pasProductRecord.ProductKey.Substring(0, 3) != "RAM")
            {
                Assert.Equal(expected: productAttribute2, actual: row.ProductLine);
            }
            if(productAttribute3 == null && productAttribute2 != null && pasProductRecord.ProductKey.Substring(0, 3) != "CEL" && pasProductRecord.ProductKey.Substring(0, 3) != "RAM")
            {
                Assert.Equal(expected: productAttribute1, actual: row.ProductLine);
            }
            if(productAttribute3 == null && productAttribute2 == null && productAttribute1 != null && pasProductRecord.ProductKey.Substring(0, 3) == "RAM")
            {
                Assert.Equal(expected: productAttribute1, actual: row.ProductLine);
            }

            if(productAttribute3 != null && pasProductRecord.ProductKey.Substring(0, 3) != "CEL" && pasProductRecord.ProductKey.Substring(0, 3) != "RAM")
            {
                Assert.Equal(expected: productAttribute1, actual: row.ProductClass);
            }
            if(productAttribute3 == null && productAttribute2 == null && productAttribute1 != null && pasProductRecord.ProductKey.Substring(0, 3) == "CEL")
            {
                Assert.Equal(expected: productAttribute1, actual: row.ProductClass);
            }

            if(productAttribute3 != null)
            {
                Assert.Equal(expected: productAttribute3, actual: row.SourceProductName);
            }
            if(productAttribute3 == null && productAttribute2 != null && pasProductRecord.ProductKey.Substring(0, 3) != "CEL" && pasProductRecord.ProductKey.Substring(0, 3) != "RAM")
            {
                Assert.Equal(expected: productAttribute2, actual: row.SourceProductName);
            }
            if(productAttribute3 == null && productAttribute2 == null && productAttribute1 != null && pasProductRecord.ProductKey.Substring(0, 3) != "CEL" && pasProductRecord.ProductKey.Substring(0, 3) != "RAM")
            {
                Assert.Equal(expected: productAttribute1, actual: row.SourceProductName);
            }

            Assert.Equal(expected: pasProductRecord.ProductDescription, actual: row.SourceProductDescription);
            Assert.Equal(expected: refProductMappingRecord.SourceProductName, actual: row.ReferenceSourceProductName);
        }
        if(dataSourceId == (int)DataSource.eGlobal)
        {
            Assert.Equal(expected: pasProductAttributeRecord.eGlobalTBL_FULLNAME, actual: row.ProductClass);
            if(dataSourceInstanceId != (int)DataSourceInstance.eGlobalNetherlands)
            {
                Assert.Equal(expected: pasProductAttributeRecord.eGlobalRI_ABBRNAME, actual: row.SourceProductName);
            }
            else
            {
                Assert.Equal(expected: pasProductAttributeRecord.eGlobalRI_DESCRIPTIONA, actual: row.SourceProductName);
            }
            if(isMultiRisk == "-1")
            {
                Assert.True(row.IsMultiRisk);
            }
            else
            {
                Assert.Equal(expected: DBNull.Value, actual: row.IsMultiRisk);
            }
            if(isInactive == "-1")
            {
                Assert.True(row.IsInactive);
            }
            else
            {
                Assert.Equal(expected: DBNull.Value, actual: row.IsInactive);
            }
            Assert.Equal(expected: pasProductRecord.ProductDescription, actual: row.SourceProductDescription);
            Assert.Equal(expected: refProductMappingRecord.SourceProductName, actual: row.ReferenceSourceProductName);
            Assert.Equal(expected: refProductMappingRecord.SourceProductDescription, actual: row.ReferenceSourceProductDescription);
        }
        if(dataSourceId == (int)DataSource.EPIC)
        {
            Assert.Equal(expected: pasProductAttributeRecord.EpicPolicyLineType, actual: row.ProductLine);
            Assert.Equal(expected: pasProductRecord.ProductDescription, actual: row.SourceProductName);
            Assert.Equal(expected: pasProductRecord.ProductDescription, actual: row.SourceProductDescription);
            if(dataSourceInstanceId == (int)DataSourceInstance.EpicUS)
            {
                Assert.Equal(expected: pasProductAttributeRecord.EpicPolicyLineType, actual: row.ReferenceSourceProductName);
            }
            else
            {
                Assert.Equal(expected: refProductMappingRecord.SourceProductName, actual: row.ReferenceSourceProductName);
            }
        }
        if(dataSourceInstanceId == (int)DataSourceInstance.WIBS)
        {
            Assert.Equal(expected: pasProductAttributeRecord.WIBSAreaDescription, actual: row.ProductClass);
            Assert.Equal(expected: pasProductRecord.Product, actual: row.SourceProductName);
            Assert.Equal(expected: pasProductRecord.ProductDescription, actual: row.SourceProductDescription);
            Assert.Equal(expected: pasProductAttributeRecord.WIBSDescription, actual: row.ReferenceSourceProductName);
        }
        Assert.Equal(expected: pasProductRecord.GlobalProductId, actual: row.ReferenceProductId);
        Assert.Equal(expected: refProductMappingRecord.SourceProductDescription, actual: row.ReferenceSourceProductDescription);
        Assert.Equal(expected: parentProductKey != null ? parentProductRecord.ProductId : DBNull.Value, actual: row.ParentProductId);
        Assert.Equal(expected: pasProductRecord.ETLUpdatedDate, actual: row.SourceLastUpdateDate);
        Assert.Equal(expected: pasProductRecord.IsDeleted, actual: row.IsDeleted);
    }

    [Theory]
    [InlineData((int)DataSource.Eclipse, (int)DataSourceInstance.Eclipse, "PRDKEY1", null, 1, 100000, "Airline Hull", "Aerospace", "Aviation", null, null, false)]
    [InlineData((int)DataSource.Eclipse, (int)DataSourceInstance.Eclipse, "PRDKEY2", "PARENTPRDKEY2", 2, 200000, "RE PropTtyAcct", "Agg", "PropertyRe", null, null, false)]
    [InlineData((int)DataSource.eGlobal, (int)DataSourceInstance.eGlobalAustralia, "PRDKEY1", null, 1, 100000, "AVIATION", "AVI EXCESSAVIATIONLI", null, "0", "0", false)]
    [InlineData((int)DataSource.eGlobal, (int)DataSourceInstance.eGlobalAustralia, "PRDKEY1", null, 1, 100000, "AVIATION", "AVI EXCESSAVIATIONLI", null, "1", "1", false)]
    [InlineData((int)DataSource.eGlobal, (int)DataSourceInstance.eGlobalAustralia, "PRDKEY1", null, 1, 100000, "AVIATION", "AVI EXCESSAVIATIONLI", null, "-1", "-1", false)]
    [InlineData((int)DataSource.eGlobal, (int)DataSourceInstance.eGlobalAustralia, "PRDKEY2", "PARENTPRDKEY2", 2, 200000, "ACCHEALTHTRAVEL", "AHT ACTIVEASSAILANT", null, "0", "0", false)]
    [InlineData((int)DataSource.eGlobal, (int)DataSourceInstance.eGlobalAustralia, "PRDKEY2", "PARENTPRDKEY2", 2, 200000, "ACCHEALTHTRAVEL", "AHT ACTIVEASSAILANT", null, "1", "1", false)]
    [InlineData((int)DataSource.eGlobal, (int)DataSourceInstance.eGlobalAustralia, "PRDKEY2", "PARENTPRDKEY2", 2, 200000, "ACCHEALTHTRAVEL", "AHT ACTIVEASSAILANT", null, "-1", "-1", false)]
    [InlineData((int)DataSource.eGlobal, (int)DataSourceInstance.eGlobalNetherlands, "PRDKEY1", null, 1, 100000, "CONSTRUCTION", "BOUW- EN OPERATIONEL", "Build & Operate cover", "0", "0", false)]
    [InlineData((int)DataSource.eGlobal, (int)DataSourceInstance.eGlobalNetherlands, "PRDKEY1", null, 1, 100000, "FINEX", "BEROEPSAANSPRAKELIJ", "Professional Indemnity Insurance", "1", "1", false)]
    [InlineData((int)DataSource.eGlobal, (int)DataSourceInstance.eGlobalNetherlands, "PRDKEY1", null, 1, 100000, "FLEET", "VERKEERSRISICO", "Legal Aid/ Traffic Risks Insurance", "-1", "-1", false)]
    [InlineData((int)DataSource.eGlobal, (int)DataSourceInstance.eGlobalNetherlands, "PRDKEY2", "PARENTPRDKEY2", 2, 200000, "CONSTRUCTION", "BOUW- EN OPERATIONEL", "Build & Operate cover", "0", "0", false)]
    [InlineData((int)DataSource.eGlobal, (int)DataSourceInstance.eGlobalNetherlands, "PRDKEY2", "PARENTPRDKEY2", 2, 200000, "FINEX", "BEROEPSAANSPRAKELIJ", "Professional Indemnity Insurance", "1", "1", false)]
    [InlineData((int)DataSource.eGlobal, (int)DataSourceInstance.eGlobalNetherlands, "PRDKEY2", "PARENTPRDKEY2", 2, 200000, "FLEET", "VERKEERSRISICO", "Legal Aid/ Traffic Risks Insurance", "-1", "-1", false)]
    [InlineData((int)DataSource.BrazilCOL, (int)DataSourceInstance.BrazilCOL, "PRDKEY1", null, 1, 100000, "PROPERTY", "RISCOS DIVERSOS - INATIVO", "W EMPRESA", null, null, false)]
    [InlineData((int)DataSource.BrazilCOL, (int)DataSourceInstance.BrazilCOL, "CELPRDKEY1", null, 1, 100000, "PROPERTY", "RISCOS DIVERSOS - INATIVO", "W EMPRESA", null, null, false)]
    [InlineData((int)DataSource.BrazilCOL, (int)DataSourceInstance.BrazilCOL, "RAMPRDKEY1", null, 1, 100000, "PROPERTY", "RISCOS DIVERSOS - INATIVO", "W EMPRESA", null, null, false)]
    [InlineData((int)DataSource.BrazilCOL, (int)DataSourceInstance.BrazilCOL, "PRDKEY2", null, 2, 200000, "PROPERTY", "RISCOS DIVERSOS - INATIVO", null, null, null, false)]
    [InlineData((int)DataSource.BrazilCOL, (int)DataSourceInstance.BrazilCOL, "CELPRDKEY2", null, 2, 200000, "PROPERTY", "RISCOS DIVERSOS - INATIVO", null, null, null, false)]
    [InlineData((int)DataSource.BrazilCOL, (int)DataSourceInstance.BrazilCOL, "RAMPRDKEY2", null, 2, 200000, "PROPERTY", "RISCOS DIVERSOS - INATIVO", null, null, null, false)]
    [InlineData((int)DataSource.BrazilCOL, (int)DataSourceInstance.BrazilCOL, "PRDKEY3", null, 3, 300000, "PROPERTY", null, null, null, null, false)]
    [InlineData((int)DataSource.BrazilCOL, (int)DataSourceInstance.BrazilCOL, "CELPRDKEY3", null, 3, 300000, "PROPERTY", null, null, null, null, false)]
    [InlineData((int)DataSource.BrazilCOL, (int)DataSourceInstance.BrazilCOL, "RAMPRDKEY3", null, 3, 300000, "PROPERTY", null, null, null, null, false)]
    [InlineData((int)DataSource.EPIC, (int)DataSourceInstance.EpicUS, "PRDKEY1", null, 1, 100000, "Agriculture Package", null, null, null, null, false)]
    [InlineData((int)DataSource.EPIC, (int)DataSourceInstance.EpicUS, "PRDKEY2", "PARENTPRDKEY2", 2, 200000, "Commercial Package", null, null, null, null, false)]
    [InlineData((int)DataSource.EPIC, (int)DataSourceInstance.EpicCanada, "PRDKEY1", null, 1, 100000, "Agriculture Package", null, null, null, null, false)]
    [InlineData((int)DataSource.EPIC, (int)DataSourceInstance.EpicCanada, "PRDKEY2", "PARENTPRDKEY2", 2, 200000, "Commercial Package", null, null, null, null, false)]
    [InlineData((int)DataSource.WIBS, (int)DataSourceInstance.WIBS, "PRDKEY1", null, 1, 100000, "Motor", "Motor Description", null, null, null, false)]
    [InlineData((int)DataSource.WIBS, (int)DataSourceInstance.WIBS, "PRDKEY2", "PARENTPRDKEY2", 2, 200000, "Financial Lines", "Financial Lines Description", null, null, null, false)]
    [InlineData((int)DataSource.ASYS, (int)DataSourceInstance.ASYSGermanyAndAustria, "PRDKEY1", null, 1, 100000, "Deleted", "Deleted", "Deleted", null, null, true)]
    [InlineData((int)DataSource.ASYS, (int)DataSourceInstance.ASYSGermanyAndAustria, "PRDKEY2", "PARENTPRDKEY2", 2, 200000, "Deleted", "Deleted", "Deleted", null, null, true)]

    public void UpdateProductTest(int dataSourceId, int dataSourceInstanceId, string productKey, string? parentProductKey, int pasProductId, int globalProductId, string? productAttribute1, string? productAttribute2, string? productAttribute3, string? isMultiRisk, string? isInactive, bool isDeleted)
    {
        dynamic dataSourceInstanceRecord, pasProductRecord, pasProductAttributeRecord, refProductMappingRecord, parentProductRecord;

        SetUpData(dataSourceId, dataSourceInstanceId, productKey, parentProductKey, pasProductId, globalProductId, productAttribute1, productAttribute2, productAttribute3, isMultiRisk, isInactive, isDeleted
            , out dataSourceInstanceRecord, out pasProductRecord, out pasProductAttributeRecord, out refProductMappingRecord, out parentProductRecord);

        SetUpExistingData(dataSourceInstanceId, productKey, pasProductId);

        dynamic result = ExecuteStoredProcedureWithResultRow(storedProcedureName: "PAS.Load_dbo_Product");
        Assert.NotNull(result);
        if(parentProductKey != null)
        {
            Assert.Equal(expected: 0, actual: result.RejectedCount);
            Assert.Equal(expected: 0, actual: result.InsertedCount);
            Assert.Equal(expected: 3, actual: result.UpdatedCount);
        }
        else
        {
            Assert.Equal(expected: 0, actual: result.RejectedCount);
            Assert.Equal(expected: 0, actual: result.InsertedCount);
            Assert.Equal(expected: 1, actual: result.UpdatedCount);
        }


        dynamic row = GetResultRow(tableName: "dbo.Product", whereClause: " SourceProductId = " + pasProductRecord.PASProductId + " AND DataSourceInstanceId = " + dataSourceInstanceRecord.DataSourceInstanceId + " AND SourcedFromBrokingPlatform = 0");
        Assert.NotNull(row);
        Assert.Equal(expected: pasProductRecord.ProductKey, actual: row.ProductKey);
        if(dataSourceInstanceId == (int)DataSourceInstance.Eclipse)
        {
            Assert.Equal(expected: pasProductAttributeRecord.EclipseProduct, actual: row.ProductLine);
            Assert.Equal(expected: pasProductAttributeRecord.EclipseProductClass, actual: row.ProductClass);
            Assert.Equal(expected: pasProductAttributeRecord.EclipseBusinessClass, actual: row.SourceProductName);
            Assert.Equal(expected: pasProductRecord.ProductDescription, actual: row.SourceProductDescription);
            Assert.Equal(expected: refProductMappingRecord.SourceProductName, actual: row.ReferenceSourceProductName);
        }
        if(dataSourceInstanceId == (int)DataSourceInstance.BrazilCOL)
        {
            if(productAttribute3 != null && pasProductRecord.ProductKey.Substring(0, 3) != "CEL" && pasProductRecord.ProductKey.Substring(0, 3) != "RAM")
            {
                Assert.Equal(expected: productAttribute2, actual: row.ProductLine);
            }
            if(productAttribute3 == null && productAttribute2 != null && pasProductRecord.ProductKey.Substring(0, 3) != "CEL" && pasProductRecord.ProductKey.Substring(0, 3) != "RAM")
            {
                Assert.Equal(expected: productAttribute1, actual: row.ProductLine);
            }
            if(productAttribute3 == null && productAttribute2 == null && productAttribute1 != null && pasProductRecord.ProductKey.Substring(0, 3) == "RAM")
            {
                Assert.Equal(expected: productAttribute1, actual: row.ProductLine);
            }

            if(productAttribute3 != null && pasProductRecord.ProductKey.Substring(0, 3) != "CEL" && pasProductRecord.ProductKey.Substring(0, 3) != "RAM")
            {
                Assert.Equal(expected: productAttribute1, actual: row.ProductClass);
            }
            if(productAttribute3 == null && productAttribute2 == null && productAttribute1 != null && pasProductRecord.ProductKey.Substring(0, 3) == "CEL")
            {
                Assert.Equal(expected: productAttribute1, actual: row.ProductClass);
            }

            if(productAttribute3 != null)
            {
                Assert.Equal(expected: productAttribute3, actual: row.SourceProductName);
            }
            if(productAttribute3 == null && productAttribute2 != null && pasProductRecord.ProductKey.Substring(0, 3) != "CEL" && pasProductRecord.ProductKey.Substring(0, 3) != "RAM")
            {
                Assert.Equal(expected: productAttribute2, actual: row.SourceProductName);
            }
            if(productAttribute3 == null && productAttribute2 == null && productAttribute1 != null && pasProductRecord.ProductKey.Substring(0, 3) != "CEL" && pasProductRecord.ProductKey.Substring(0, 3) != "RAM")
            {
                Assert.Equal(expected: productAttribute1, actual: row.SourceProductName);
            }

            Assert.Equal(expected: pasProductRecord.ProductDescription, actual: row.SourceProductDescription);
            Assert.Equal(expected: refProductMappingRecord.SourceProductName, actual: row.ReferenceSourceProductName);
        }
        if(dataSourceId == (int)DataSource.eGlobal)
        {
            Assert.Equal(expected: pasProductAttributeRecord.eGlobalTBL_FULLNAME, actual: row.ProductClass);
            if(dataSourceInstanceId != (int)DataSourceInstance.eGlobalNetherlands)
            {
                Assert.Equal(expected: pasProductAttributeRecord.eGlobalRI_ABBRNAME, actual: row.SourceProductName);
            }
            else
            {
                Assert.Equal(expected: pasProductAttributeRecord.eGlobalRI_DESCRIPTIONA, actual: row.SourceProductName);
            }
            if(isMultiRisk == "-1")
            {
                Assert.True(row.IsMultiRisk);
            }
            else
            {
                Assert.Equal(expected: DBNull.Value, actual: row.IsMultiRisk);
            }
            if(isInactive == "-1")
            {
                Assert.True(row.IsInactive);
            }
            else
            {
                Assert.Equal(expected: DBNull.Value, actual: row.IsInactive);
            }
            Assert.Equal(expected: pasProductRecord.ProductDescription, actual: row.SourceProductDescription);
            Assert.Equal(expected: refProductMappingRecord.SourceProductName, actual: row.ReferenceSourceProductName);
            Assert.Equal(expected: refProductMappingRecord.SourceProductDescription, actual: row.ReferenceSourceProductDescription);
        }
        if(dataSourceId == (int)DataSource.EPIC)
        {
            Assert.Equal(expected: pasProductAttributeRecord.EpicPolicyLineType, actual: row.ProductLine);
            Assert.Equal(expected: pasProductRecord.ProductDescription, actual: row.SourceProductName);
            Assert.Equal(expected: pasProductRecord.ProductDescription, actual: row.SourceProductDescription);
            if(dataSourceInstanceId == (int)DataSourceInstance.EpicUS)
            {
                Assert.Equal(expected: pasProductAttributeRecord.EpicPolicyLineType, actual: row.ReferenceSourceProductName);
            }
            else
            {
                Assert.Equal(expected: refProductMappingRecord.SourceProductName, actual: row.ReferenceSourceProductName);
            }
        }
        if(dataSourceInstanceId == (int)DataSourceInstance.WIBS)
        {
            Assert.Equal(expected: pasProductAttributeRecord.WIBSAreaDescription, actual: row.ProductClass);
            Assert.Equal(expected: pasProductRecord.Product, actual: row.SourceProductName);
            Assert.Equal(expected: pasProductRecord.ProductDescription, actual: row.SourceProductDescription);
            Assert.Equal(expected: pasProductAttributeRecord.WIBSDescription, actual: row.ReferenceSourceProductName);
        }
        Assert.Equal(expected: pasProductRecord.GlobalProductId, actual: row.ReferenceProductId);
        Assert.Equal(expected: refProductMappingRecord.SourceProductDescription, actual: row.ReferenceSourceProductDescription);
        Assert.Equal(expected: parentProductKey != null ? parentProductRecord.ProductId : DBNull.Value, actual: row.ParentProductId);
        Assert.Equal(expected: pasProductRecord.ETLUpdatedDate, actual: row.SourceLastUpdateDate);
        Assert.Equal(expected: pasProductRecord.IsDeleted, actual: row.IsDeleted);
    }

    [Theory]
    [InlineData((int)DataSource.Eclipse, (int)DataSourceInstance.Eclipse, "PRDKEY1", null, 1, 100000, "Airline Hull", "Aerospace", "Aviation", null, null, true)]
    [InlineData((int)DataSource.eGlobal, (int)DataSourceInstance.eGlobalAustralia, "PRDKEY1", null, 1, 100000, "AVIATION", "AVI EXCESSAVIATIONLI", null, "-1", "-1", true)]
    [InlineData((int)DataSource.eGlobal, (int)DataSourceInstance.eGlobalNetherlands, "PRDKEY1", null, 1, 100000, "CONSTRUCTION", "BOUW- EN OPERATIONEL", "Build & Operate cover", "0", "0", true)]
    [InlineData((int)DataSource.EPIC, (int)DataSourceInstance.EpicUS, "PRDKEY1", null, 1, 100000, "Agriculture Package", null, null, null, null, true)]
    [InlineData((int)DataSource.EPIC, (int)DataSourceInstance.EpicCanada, "PRDKEY1", null, 1, 100000, "Agriculture Package", null, null, null, null, true)]
    [InlineData((int)DataSource.WIBS, (int)DataSourceInstance.WIBS, "PRDKEY1", null, 1, 100000, "Motor", "Motor Description", null, null, null, true)]
    [InlineData((int)DataSource.ASYS, (int)DataSourceInstance.ASYSGermanyAndAustria, "PRDKEY1", null, 1, 100000, "Deleted", "Deleted", "Deleted", null, null, true)]

    public void DoesNotuseDeletedProductAttributesTest(int dataSourceId, int dataSourceInstanceId, string productKey, string? parentProductKey, int pasProductId, int globalProductId, string? productAttribute1, string? productAttribute2, string? productAttribute3, string? isMultiRisk, string? isInactive, bool isDeleted)
    {
        dynamic dataSourceInstanceRecord, pasProductRecord, pasProductAttributeRecord, refProductMappingRecord, parentProductRecord;

        SetUpData(dataSourceId, dataSourceInstanceId, productKey, parentProductKey, pasProductId, globalProductId, productAttribute1, productAttribute2, productAttribute3, isMultiRisk, isInactive, isDeleted
            , out dataSourceInstanceRecord, out pasProductRecord, out pasProductAttributeRecord, out refProductMappingRecord, out parentProductRecord);

        dynamic result = ExecuteStoredProcedureWithResultRow(storedProcedureName: "PAS.Load_dbo_Product");
        Assert.NotNull(result);
        Assert.Equal(expected: 0, actual: result.RejectedCount);
        Assert.Equal(expected: 1, actual: result.InsertedCount);

        dynamic row = GetResultRow(tableName: "dbo.Product", whereClause: " SourceProductId = " + pasProductRecord.PASProductId + " AND DataSourceInstanceId = " + dataSourceInstanceRecord.DataSourceInstanceId + " AND SourcedFromBrokingPlatform = 0");
        Assert.NotNull(row);
        Assert.Equal(expected: pasProductRecord.ProductKey, actual: row.ProductKey);
        if(dataSourceInstanceId == (int)DataSourceInstance.Eclipse)
        {
            Assert.Equal(expected: DBNull.Value, actual: row.ProductLine);
            Assert.Equal(expected: DBNull.Value, actual: row.ProductClass);
            Assert.Equal(expected: DBNull.Value, actual: row.SourceProductName);
        }
        if(dataSourceId == (int)DataSource.eGlobal)
        {
            Assert.Equal(expected: DBNull.Value, actual: row.ProductClass);
            Assert.Equal(expected: DBNull.Value, actual: row.SourceProductName);
            Assert.Equal(expected: DBNull.Value, actual: row.IsMultiRisk);
            Assert.Equal(expected: DBNull.Value, actual: row.IsInactive);

        }
        if(dataSourceId == (int)DataSource.EPIC)
        {
            Assert.Equal(expected: DBNull.Value, actual: row.ProductLine);
            if(dataSourceInstanceId == (int)DataSourceInstance.EpicUS)
            {
                Assert.Equal(expected: DBNull.Value, actual: row.ReferenceSourceProductName);
            }
        }
        if(dataSourceInstanceId == (int)DataSourceInstance.WIBS)
        {
            Assert.Equal(expected: DBNull.Value, actual: row.ProductClass);
            Assert.Equal(expected: DBNull.Value, actual: row.ReferenceSourceProductName);
        }
    }

    private void SetUpData(int dataSourceId, int dataSourceInstanceId, string productKey, string? parentProductKey, int pasProductId, int globalProductId, string? productAttribute1, string? productAttribute2, string? productAttribute3, string? isMultiRisk, string? isInactive, bool isDeleted
        , out dynamic dataSourceInstanceRecord, out dynamic pasProductRecord, out dynamic? pasProductAttributeRecord, out dynamic refProductMappingRecord, out dynamic parentProductRecord)
    {
        pasProductAttributeRecord = null;
        parentProductRecord = null;

        dynamic dataSourceRecord = CreateRow(tableName: "Reference.DataSource", values: new
        {
            DataSourceId = dataSourceId
        });

        dataSourceInstanceRecord = CreateRow(tableName: "Reference.DataSourceInstance", values: new
        {
            DataSourceId = dataSourceRecord.DataSourceId,
            DataSourceInstanceId = dataSourceInstanceId
        });

        if(parentProductKey != null)
        {
            dynamic pasParentProductRecord = CreateRow(tableName: "PAS.Product", values: new
            {
                ProductKey = parentProductKey,
                DataSourceInstanceId = dataSourceInstanceRecord.DataSourceInstanceId,
                ProductDescription = parentProductKey + "DESC",
                PASProductId = 1000,
                GlobalProductId = globalProductId,
                ETLUpdatedDate = DateTime.UtcNow.AddMinutes(-30),
                IsDeleted = isDeleted
            });

            parentProductRecord = CreateRow(tableName: "dbo.Product", values: new
            {
                ProductKey = parentProductKey,
                DataSourceInstanceId = dataSourceInstanceRecord.DataSourceInstanceId,
                SourceProductId = pasParentProductRecord.PASProductId,
                SourcedFromBrokingPlatform = 0,
                IsDeleted = isDeleted
            });
        }

        if(dataSourceInstanceId == (int)DataSourceInstance.BrazilCOL)
        {
            dynamic productDesc = productAttribute1;
            if(productAttribute2 != null)
            {
                productDesc = productDesc + ";" + productAttribute2;
            }
            if(productAttribute3 != null)
            {
                productDesc = productDesc + ";" + productAttribute3;
            }

            pasProductRecord = CreateRow(tableName: "PAS.Product", values: new
            {
                ProductKey = productKey,
                DataSourceInstanceId = dataSourceInstanceRecord.DataSourceInstanceId,
                Product = productKey + " Name",
                ProductDescription = productDesc,
                ParentKey = parentProductKey != null ? parentProductKey : null,
                PASProductId = pasProductId,
                GlobalProductId = globalProductId,
                ETLUpdatedDate = DateTime.UtcNow.AddMinutes(-30),
                IsDeleted = isDeleted
            });
        }
        else
        {
            pasProductRecord = CreateRow(tableName: "PAS.Product", values: new
            {
                ProductKey = productKey,
                DataSourceInstanceId = dataSourceInstanceRecord.DataSourceInstanceId,
                Product = productKey + " Name",
                ProductDescription = productKey + "DESC",
                ParentKey = parentProductKey != null ? parentProductKey : null,
                PASProductId = pasProductId,
                GlobalProductId = globalProductId,
                ETLUpdatedDate = DateTime.UtcNow.AddMinutes(-30),
                IsDeleted = isDeleted
            });
        }

        if(dataSourceInstanceId == (int)DataSourceInstance.Eclipse)
        {
            pasProductAttributeRecord = CreateRow(tableName: "PAS.ProductAttribute", values: new
            {
                ProductKey = pasProductRecord.ProductKey,
                DataSourceInstanceId = dataSourceInstanceRecord.DataSourceInstanceId,
                EclipseBusinessClass = productAttribute1,
                EclipseProductClass = productAttribute2,
                EclipseProduct = productAttribute3,
                IsDeleted = isDeleted
            });
        }

        if(dataSourceId == (int)DataSource.eGlobal)
        {
            pasProductAttributeRecord = CreateRow(tableName: "PAS.ProductAttribute", values: new
            {
                ProductKey = pasProductRecord.ProductKey,
                DataSourceInstanceId = dataSourceInstanceRecord.DataSourceInstanceId,
                eGlobalTBL_FULLNAME = productAttribute1,
                eGlobalRI_ABBRNAME = productAttribute2,
                eGlobalRI_DESCRIPTIONA = productAttribute3 != null ? productAttribute3 : null,
                eGlobalRI_MULTIRISK = isMultiRisk != null ? isMultiRisk : null,
                eGlobalRI_INACTIVERISK = isInactive != null ? isInactive : null,
                IsDeleted = isDeleted
            });
        }

        if(dataSourceId == (int)DataSource.EPIC)
        {
            pasProductAttributeRecord = CreateRow(tableName: "PAS.ProductAttribute", values: new
            {
                ProductKey = pasProductRecord.ProductKey,
                DataSourceInstanceId = dataSourceInstanceRecord.DataSourceInstanceId,
                EpicPolicyLineType = productAttribute1,
                IsDeleted = isDeleted
            });
        }

        if(dataSourceId == (int)DataSource.WIBS)
        {
            pasProductAttributeRecord = CreateRow(tableName: "PAS.ProductAttribute", values: new
            {
                ProductKey = pasProductRecord.ProductKey,
                DataSourceInstanceId = dataSourceInstanceRecord.DataSourceInstanceId,
                WIBSAreaDescription = productAttribute1,
                WIBSDescription = productAttribute2,
                IsDeleted = isDeleted
            });
        }

        refProductMappingRecord = CreateRow(tableName: "Reference.ProductMapping", values: new
        {
            SourceProductKey = pasProductRecord.ProductKey,
            DataSourceInstanceId = pasProductRecord.DataSourceInstanceId,
            SourceProductName = productKey + "Name Mapped",
            SourceProductDescription = productKey + "Desc Mapped"
        });

        dynamic refProductRecord = CreateRow(tableName: "Reference.Product", values: new
        {
            ProductId = pasProductRecord.GlobalProductId
        });

    }

    private void SetUpExistingData(int dataSourceInstanceId, string productKey, int pasProductId)
    {
        dynamic productRecord = CreateRow(tableName: "dbo.Product", values: new
        {
            ProductKey = productKey,
            DataSourceInstanceId = dataSourceInstanceId,
            SourceProductId = pasProductId,
            SourcedFromBrokingPlatform = 0,
            IsDeleted = false
        });
    }

    public Load_dbo_ProductTests(DatabaseFixture fixture, ITestOutputHelper output) : base(fixture: fixture, output: output)
    {

    }
}
