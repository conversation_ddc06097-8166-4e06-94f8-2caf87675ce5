﻿using System.Diagnostics.CodeAnalysis;
using System.Text.RegularExpressions;
using Xunit.Abstractions;

namespace PsDb.Tests.PlacementStoreHelpers;

/// <summary>
/// This class helps to test load stored procedures. But you aren't expected to inherit directly from this one.
/// You should inherit from either PlacementStoreSystemVersionedTest or PlacementStoreLoadProcedureTest which
/// will provide a standard starter set of tests
/// The main test class inherits the tests in here and has to provide the appropriate data and
/// checks. But the main tests are defined here for consistency. You can also add additional tests.
/// Tests in here are valid for all Load/Merge stored procedures.
/// </summary>
[ExcludeFromCodeCoverage]
public abstract class PlacementStoreCoreLoadProcedureTestBase : PlacementStoreTestBase
{
    protected readonly ITestOutputHelper output;

    /// <summary>
    /// An enum passed to the sub-class to be used if it needs to change the data from certain tests.
    /// </summary>
    protected enum TestType
    {
        InsertTest,
        UpdateTest,
        LogicalDeleteTest,
        UseMostRecentRecordTest,
        NoChangeTest,
        NoExtraDeleteTest
    }

    public string TargetTableName { get; private set; }
    public string StoredProcedureName { get; private set; }
    public string StagingTableName { get; private set; }

    // These are the methods to be  overridden in the sub-class to create and check the data.
    // testType can be used to tell which test called the method.
    // This is needed to make the data change between UpdateTest and NoChangeTest so one triggers an
    // update, and the other does not.

    /// <summary>
    /// Create an existing record. Can return an anonymous object and the record will be created automatically.
    /// Or you can call CreateRow and it won't (But why would you!)
    /// </summary>
    /// <param name="testType"></param>
    /// <param name="stagingRecord"></param>
    /// <returns></returns>
    protected abstract dynamic CreateExistingRecord(TestType testType, dynamic stagingRecord);



    /// <summary>
    /// Override this and check additional values specific to this target table.
    /// </summary>
    /// <param name="testType"></param>
    /// <param name="stagingRecord"></param>
    /// <param name="targetResult"></param>
    protected abstract void CheckTargetRecordValues(TestType testType, dynamic stagingRecord, dynamic targetResult);

    /// <summary>
    /// This should be ETLCreatedDate but can be overridden if not.
    /// As this will appear in the sub-class it will be obvious if not
    /// </summary>
    /// <param name="row"></param>
    /// <returns></returns>
    protected virtual object GetCreatedDateValue(dynamic row)
    {
        return row.ETLCreatedDate;
    }

    /// <summary>
    /// This should be ETLUpdatedDate but can be overridden if not.
    /// As this will appear in the sub-class it will be obvious if not
    /// </summary>
    /// <param name="row"></param>
    /// <returns></returns>
    protected virtual object GetUpdatedDateValue(dynamic row)
    {
        return row.ETLUpdatedDate;
    }

    /// <summary>
    /// Override this if the logical deletion flag isn't IsDeleted
    /// </summary>
    /// <param name="row"></param>
    /// <returns></returns>
    protected virtual object GetLogicalDeletionValue(dynamic row)
    {
        return row.IsDeleted;
    }

    /// <summary>
    /// Override this if the SourceUpdatedDate column has a
    /// different name.
    /// Anything new should be using this.
    /// </summary>
    /// <param name="row"></param>
    /// <returns></returns>
    protected virtual object GetSourceUpdatedDateValue(dynamic row)
    {
        return row.SourceUpdatedDate;
    }

    protected abstract object GetStagingUpdatedDateValue(dynamic record);

    /// <summary>
    /// If you need additional records to make this work you can override this and create the records you need.
    /// When not overridden this version does nothing.
    /// </summary>
    protected virtual void SetUpExtraRecords(TestType testType)
    {

    }

    /// <summary>
    /// Create a staging record. Can return an anonymous object and the record will be created automatically.
    /// Or you can call CreateRow and it won't (But why would you!)
    /// It provides the ValidTo and ValidFrom as appropriate to all you do is just assign them.
    /// </summary>
    /// <param name="testType"></param>
    /// <param name="validFrom"></param>
    /// <param name="validTo"></param>
    /// <returns></returns>
    protected abstract dynamic CreateStagingRecord(TestType testType, DateTime validFrom, DateTime validTo, bool changeSomething);

    /// <summary>
    /// Some things don't apply for a full load. Defaults to not a full load.
    /// Override if it should be true
    /// </summary>
    /// <returns></returns>
    protected virtual bool AlwaysFullLoad()
    {
        return false;
    }

    /// <summary>
    /// There are tests where we create a dummy record without any real data.
    /// In most cases this works but there are some edge cases when we need to
    /// provide values. This allows us to change it if we need to.
    /// </summary>
    /// <param name="testType"></param>
    /// <param name="stagingRecord"></param>
    /// <returns></returns>
    protected virtual dynamic CreateDummyExistingRecord(TestType testType)
    {
        return CreateRow(
            tableName: TargetTableName,
            values: new
            {
                IsDeleted = false,
                IsDeprecated = false,
                IsActive = true,
                DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform
            },
            checkValueColumns: false /* Dodgy parameter to allow us to specify parameters that don't exist. */
        );
    }

    /// <summary>
    /// It's possible you need to apply a filter to exclude some records.
    /// This can be overridden to allow that.
    /// </summary>
    /// <returns></returns>
    protected virtual dynamic? GetResultRowOverride()
    {
        return GetResultRow(tableName: TargetTableName);
    }

    /// <summary>
    /// This wraps the CreateStagingRecord abstract method to allow it to be used in two different ways.
    /// The first more traditional is where you call CreateRow. However this is still a bit tedious.
    /// It also supports return and anonymous type the same as you would pass into CreateRow.
    /// If it detects that it will call CreateRow for you - it knows the name of the staging table.
    /// </summary>
    /// <param name="testType"></param>
    /// <param name="validFrom"></param>
    /// <param name="validTo"></param>
    /// <returns></returns>
    protected ColumnValueCollection CreateStagingRecordWrapper(TestType testType, DateTime validFrom, DateTime validTo, bool changeSomething)
    {
        dynamic result = CreateStagingRecord(testType: testType, validFrom: validFrom, validTo: validTo, changeSomething: changeSomething);
        if(result is ColumnValueCollection) /* Simpler than checking for an anonymous object */
        {
            // Already been through CreateRow
            return (ColumnValueCollection)result;
        }
        else
        {
            // Needs a CreateRow.
            return CreateRow(tableName: StagingTableName, values: result);
        }
    }

    /// <summary>
    /// Wraps the CreateExistingRecord virtual method to allow it to either call CreateRow, or to provide
    /// an anonymous class with the columns and this wrapper will then call CreateRow automatically.
    /// </summary>
    /// <param name="testType"></param>
    /// <param name="stagingRecord"></param>
    /// <returns></returns>
    protected ColumnValueCollection CreateExistingRecordWrapper(TestType testType, dynamic stagingRecord)
    {
        dynamic result = CreateExistingRecord(testType: testType, stagingRecord: stagingRecord);
        if(result is ColumnValueCollection) /* Simpler than checking for an anonymous object */
        {
            // Already been through CreateRow
            return (ColumnValueCollection)result;
        }
        else
        {
            // Needs a CreateRow.
            return CreateRow(tableName: TargetTableName, values: result);
        }
    }

    protected ColumnValueCollection CreateDummyExistingRecordWrapper(TestType testType)
    {
        dynamic result = CreateDummyExistingRecord(testType: testType);
        if(result is ColumnValueCollection) /* Simpler than checking for an anonymous object */
        {
            // Already been through CreateRow
            return (ColumnValueCollection)result;
        }
        else
        {
            // Needs a CreateRow.
            return CreateRow(tableName: TargetTableName, values: result);
        }
    }

    /// <summary>
    /// Sometimes there are tables that we might be dependent on but are loaded
    /// by script - or we have some other reason to exclude.
    /// </summary>
    /// <returns></returns>
    protected virtual IList<string>? GetCoreLoadProcedureCheckADFDependencyTestTablesToExclude()
    {
        return null;
    }

    #region Standard Tests
    /// <summary>
    /// Checks the insert where we have a staging record and
    /// no target record.
    /// A new record should be created with the right records.
    /// </summary>
    [Fact]
    public void CoreLoadProcedureInsertTest()
    {
        SetUpExtraRecords(testType: TestType.InsertTest);

        dynamic stagingRecord = CreateStagingRecordWrapper(testType: TestType.InsertTest, validFrom: DateTime.UtcNow.AddMinutes(-10).WithPrecision(2), validTo: ValidToOpen, changeSomething: false);

        dynamic result = ExecuteStoredProcedureWithResultRow(storedProcedureName: StoredProcedureName);
        IfRejectedGetSprocError(output: output, rejectedCount: result.RejectedCount, storedProcedureName: StoredProcedureName);
        Assert.True(result.RejectedCount == 0, $"Expected result.RejectedCount to be 0. It was {result.RejectedCount}.");
        Assert.True(result.InsertedCount == ExpectedInsertedCount, $"Expected result.InsertedCount to be {ExpectedInsertedCount}. It was {result.InsertedCount}.");

        CheckSprocExecutionLog(sprocName: StoredProcedureName, insertedCount: ExpectedInsertedCount);

        dynamic row = GetResultRowOverride();
        Assert.NotNull(row);
        Assert.True(GetUpdatedDateValue(row) > DateTime.UtcNow.AddMinutes(-1));
        Assert.True(GetCreatedDateValue(row) > DateTime.UtcNow.AddMinutes(-1));
        Assert.False(condition: Convert.ToBoolean(GetLogicalDeletionValue(row)), userMessage: $"Logical delete value expected to be false it was {GetLogicalDeletionValue(row)}.");
        if(!AlwaysFullLoad())
        {
            //Assert.Equal(expected: GetStagingUpdatedDateValue(stagingRecord), actual: GetSourceUpdatedDateValue(row));
        }

        CheckTargetRecordValues(testType: TestType.InsertTest, stagingRecord: stagingRecord, targetResult: row);
    }

    /// <summary>
    /// Checks that the staging record will cause an update of the target record.
    /// </summary>
    [Fact]
    public void CoreLoadProcedureUpdateTest()
    {
        SetUpExtraRecords(testType: TestType.UpdateTest);

        dynamic stagingRecord = CreateStagingRecordWrapper(testType: TestType.UpdateTest, validFrom: DateTime.UtcNow.AddMinutes(-10).WithPrecision(2), validTo: ValidToOpen, changeSomething: true);

        dynamic targetRecord = CreateExistingRecordWrapper(testType: TestType.UpdateTest, stagingRecord: stagingRecord);

        dynamic result = ExecuteStoredProcedureWithResultRow(storedProcedureName: StoredProcedureName);
        IfRejectedGetSprocError(output: output, rejectedCount: result.RejectedCount, storedProcedureName: StoredProcedureName);
        Assert.True(result.RejectedCount == 0, $"Expected result.RejectedCount to be 0. It was {result.RejectedCount}.");
        Assert.True(result.InsertedCount == 0, $"Expected result.InsertedCount to be 0. It was {result.InsertedCount}.");
        Assert.True(result.UpdatedCount == 1, $"Expected result.UpdatedCount to be 1. It was {result.UpdatedCount}.");

        CheckSprocExecutionLog(sprocName: StoredProcedureName, updatedCount: 1);

        dynamic row = GetResultRowOverride();
        Assert.NotNull(row);
        Assert.True(condition: GetUpdatedDateValue(row) > DateTime.UtcNow.AddMinutes(-1), userMessage: "Expected Updated Date to have been changed.");
        Assert.True(condition: GetCreatedDateValue(row) < DateTime.UtcNow.AddMinutes(-1), userMessage: "Created Date was not expected to change.");
        Assert.False(condition: Convert.ToBoolean(GetLogicalDeletionValue(row)), userMessage: $"Logical delete value expected to be false it was {GetLogicalDeletionValue(row)}.");
        if(!AlwaysFullLoad())
        {
            Assert.Equal(expected: GetStagingUpdatedDateValue(stagingRecord), actual: GetSourceUpdatedDateValue(row));
        }

        CheckTargetRecordValues(testType: TestType.UpdateTest, stagingRecord: stagingRecord, targetResult: row);
    }

    /// <summary>
    /// Check that if nothing has changed in the staging record that no update occurs.
    /// </summary>
    [Fact]
    public void CoreLoadProcedureNoChangeTest()
    {
        SetUpExtraRecords(testType: TestType.NoChangeTest);

        dynamic stagingRecord = CreateStagingRecordWrapper(testType: TestType.NoChangeTest, validFrom: DateTime.UtcNow.AddMinutes(-10).WithPrecision(2), validTo: ValidToOpen, changeSomething: false);

        dynamic targetRecord = CreateExistingRecordWrapper(testType: TestType.NoChangeTest, stagingRecord: stagingRecord);

        dynamic result = ExecuteStoredProcedureWithResultRow(storedProcedureName: StoredProcedureName);
        IfRejectedGetSprocError(output: output, rejectedCount: result.RejectedCount, storedProcedureName: StoredProcedureName);
        Assert.True(result.RejectedCount == 0, $"Expected result.RejectedCount to be 0. It was {result.RejectedCount}.");
        Assert.True(result.InsertedCount == 0, $"Expected result.InsertedCount to be 0. It was {result.InsertedCount}.");
        Assert.True(result.UpdatedCount == 0, $"Expected result.UpdatedCount to be 0. It was {result.UpdatedCount}.");

        CheckSprocExecutionLog(sprocName: StoredProcedureName);

        dynamic row = GetResultRowOverride();
        Assert.NotNull(row);
        Assert.True(GetUpdatedDateValue(row) < DateTime.UtcNow.AddMinutes(-1));
        Assert.True(GetCreatedDateValue(row) < DateTime.UtcNow.AddMinutes(-1));
    }

    /// <summary>
    /// This test just ensure that running the stored procedure does not physically delete an existing record in
    /// the target table if there are no staging records.
    /// </summary>
    [Fact]
    public void CoreLoadProcedureNoDataDoesNotDeleteExistingTest()
    {
        dynamic targetRecord = CreateDummyExistingRecordWrapper(testType: TestType.NoChangeTest);

        dynamic result = ExecuteStoredProcedureWithResultRow(storedProcedureName: StoredProcedureName);
        IfRejectedGetSprocError(output: output, rejectedCount: result.RejectedCount, storedProcedureName: StoredProcedureName);
        Assert.True(result.RejectedCount == 0, $"Expected result.RejectedCount to be 0. It was {result.RejectedCount}.");
        Assert.True(result.InsertedCount == 0, $"Expected result.InsertedCount to be 0. It was {result.InsertedCount}.");
        Assert.True(result.UpdatedCount == 0, $"Expected result.UpdatedCount to be 0. It was {result.UpdatedCount}.");
        Assert.True(result.DeletedCount == 0, $"Expected result.DeletedCount to be 0. It was {result.DeletedCount}.");

        CheckSprocExecutionLog(sprocName: StoredProcedureName);

        string[] primaryKeyColumns = GetPrimaryKeyColumns(tableName: TargetTableName);
        Assert.NotEmpty(primaryKeyColumns);

        var where = string.Join(" AND ", primaryKeyColumns.Select(pcc => { if(targetRecord[pcc] == null) { return $"{pcc} IS NULL"; } else { return $"{pcc} = '{targetRecord[pcc]}'"; } }));
        dynamic row = GetResultRow(tableName: TargetTableName, whereClause: where);
        Assert.NotNull(row);
        Assert.Equal(expected: false, actual: GetLogicalDeletionValue(row));
    }

    /// <summary>
    /// Check the configuration for the stored procedure.
    /// We should be changing the stored procedures so that the ResultType option isn't being used to provide a DummyRow.
    /// </summary>
    [Fact]
    public void CoreLoadProcedureCheckingADFProcessStoredProcedureConfigurationTest()
    {
        dynamic row = GetResultRow(sql: @$"
                SELECT
                    TargetConnection = ISNULL(JSON_VALUE(JSONConfig, '$.TargetConnection'), '')
                  , ResultType = ISNULL(JSON_VALUE(JSONConfig, '$.ResultType'), '')
                  , RawSQL = ISNULL(JSON_VALUE(JSONConfig, '$.RawSQL'), '')
                  , Parameters = ISNULL(JSON_VALUE(JSONConfig, '$.Parameters'), '')
                  , TargetTable = ISNULL(TargetTable, '')
                FROM
                    ADF.Process
                WHERE
                    ProcessTypeId = 2
                    AND JSON_VALUE(JSONConfig, '$.StoredProcedure') = '{StoredProcedureName}'
                    AND IsDeleted = 0;");

        if (row == null || 
            row.TargetConnection != "PlacementStore" || 
            (!string.IsNullOrEmpty(row.ResultType) && row.ResultType != "None") || 
            !string.IsNullOrEmpty(row.RawSQL) || 
            string.IsNullOrEmpty(row.Parameters) ||
            row.TargetTable != TargetTableName
            )
        {
            // Not looking good so suggest a starter
            output.WriteLine("Suggested line for ADF.Process:");
            output.WriteLine($"  , (@LoadType_Intraday, '{StoredProcedureName}', @ProcessType_SP, '{{\"StoredProcedure\":\"{StoredProcedureName}\", \"TargetConnection\":\"PlacementStore\"}}', '{TargetTableName}')");

        }
        Assert.True(row != null, $"Unable to find a row in ADF.Process for stored procedure '{StoredProcedureName}'.");
        Assert.True(row.TargetConnection == "PlacementStore", $"The TargetConnection was expected to be 'PlacementStore'. It was '{row.TargetConnection}'.");
        Assert.True(row.TargetTable == TargetTableName, $"The Target Table was expected to be '{TargetTableName}'. It was '{row.TargetTable}'.");
        Assert.True(string.IsNullOrEmpty(row.ResultType) || row.ResultType == "None", $"The ResultType was expected to be 'None' or not provided. It was '{row.ResultType}'.");
        Assert.True(string.IsNullOrEmpty(row.RawSQL), $"The RawSQL option wasn't expected. It was '{row.RawSQL}'.");
        Assert.True(string.IsNullOrEmpty(row.Parameters), $"The Parameters option wasn't expected. It was '{row.Parameters}'.");
    }

    /// <summary>
    /// Should be configuration for the stored procedure we are testing too.
    /// </summary>
    [Fact]
    public void CoreLoadLoadProcedureCheckingADFProcessConfigurationForStoredProcedureTest()
    {
        dynamic row = GetResultRow(sql: @$"
                SELECT
                    Name
                FROM
                    ADF.Process
                WHERE
                    ProcessTypeId = 2
                    AND JSON_VALUE(JSONConfig, '$.StoredProcedure') = '{StoredProcedureName}'
                    AND IsDeleted = 0;");
        Assert.True(row != null, $"Unable to find a row in ADF.Process for stored procedure '{StoredProcedureName}'.");
        Assert.True(row.Name == StoredProcedureName, $"Expected the name to match the stored procedure name '{StoredProcedureName}'. It is '{row.Name}'");
    }

    /// <summary>
    /// At the least there should be a dependency between the stored procedure and the staging load.
    /// </summary>
    [Fact]
    public void CoreLoadLoadProcedureCheckingForDependencyTest()
    {
        dynamic row = GetResultRow(sql: @$"
                SELECT pind.InterdependencyType
                FROM
                    ADF.ProcessInterdependency pind
                    INNER JOIN ADF.Process pstage
                        ON pstage.ProcessId = pind.PrerequisiteProcessId

                    INNER JOIN ADF.Process pload
                        ON pload.ProcessId = pind.ProcessId
                WHERE
                    pstage.Name = '{StagingTableName}'
                    AND pload.Name = '{StoredProcedureName}'
                    AND pind.IsDeleted = 0
                    AND pload.IsDeleted = 0
                    AND pstage.IsDeleted = 0;");

        if (row == null)
        {
            // Simple for a 1 to 1. But once more are required a bit more difficult.
            output.WriteLine("Suggested row for Interdependency. This is probably just a starter. If you have any foreign key constraints think of adding those too:");
            output.WriteLine($", ('{StoredProcedureName}', 'Uses {StagingTableName}', '[\"{StagingTableName}\"]')");
        }
        Assert.True(row != null, $"Unable to find a row in ADF.ProcessInterdependency for dependency between stored procedure '{StoredProcedureName}' and staging load for '{StagingTableName}'.");
    }

    /// <summary>
    /// Checks that the stored procedure executes with no data and without error.
    /// </summary>
    [Fact]
    public void CoreLoadProcedureNoDataTest()
    {
        // Execute stored procedure
        ExecuteStoredProcedureWithoutResult(storedProcedureName: StoredProcedureName);

        // Expect a log row but without error.
        CheckSprocExecutionLog(sprocName: StoredProcedureName, insertedCount: 0, updatedCount: 0, deletedCount: 0, rejectedCount: 0);
    }

    /// <summary>
    /// Checks to see if the stored procedure dependencies are all defined.
    /// Extra dependencies are ignored.
    /// If the are tables a particular test needs to exclude use the GetCoreLoadProcedureCheckADFDependencyTestTablesToExclude
    /// override to return them as a List.
    /// </summary>
    [Fact]
    public void CoreLoadProcedureCheckADFDependencyTest()
    {
        CheckToSeeIfInterdependenciesExistForStoredProcedure(
            storedProcedureName: StoredProcedureName, 
            excludedTables: GetCoreLoadProcedureCheckADFDependencyTestTablesToExclude()
            );
    }

    #endregion

    #region Constructor

    /// <summary>
    /// Constructor - Do not change.
    /// If no targetTableName, storeProcedureName or stagingTableName are passed in it
    /// will try and compute the names from the test class name.
    /// If these have to be set it suggests that something isn't quite right.
    /// Tests for new functionality should not have these.
    /// </summary>
    /// <param name="fixture"></param>
    /// <param name="output"></param>
    /// <param name="targetTableName"></param>
    /// <param name="storedProcedureName"></param>
    /// <param name="stagingTableName"></param>
    public PlacementStoreCoreLoadProcedureTestBase(
        DatabaseFixture fixture,
        ITestOutputHelper output,
        string? targetTableName = null,
        string? storedProcedureName = null,
        string? stagingTableName = null
       ) : base(fixture, output)
    {
        this.output = output;

        if(storedProcedureName == null || targetTableName == null || stagingTableName == null)
        {
            // Ideally the name should allow us to compute the names
            // If this doesn't work have you got a name wrong?
            string className = GetType().Name;
            if(!className.EndsWith("Tests"))
            {
                throw new ApplicationException(message: $"The class name needs to end with \"Tests\"");
            }
            if(!className.StartsWith("Load"))
            {
                throw new ApplicationException(message: $"The class name needs to start with \"Load\"");
            }
            string fullClassName = GetType().FullName;
            var re = Regex.Match(fullClassName, @"\.(\w+)\.(\w+)\.Load_(\w+)_(\w+)Tests$");
            if(re.Success)
            {
                string sourceSchema = re.Groups[1].Value;
                string codeType = re.Groups[2].Value;
                string targetSchema = re.Groups[3].Value;
                string tableName = re.Groups[4].Value;

                TargetTableName = targetTableName ?? $"{targetSchema}.{tableName}";

                if(stagingTableName == null && (sourceSchema == "PASStaging" || sourceSchema == "ReferenceStaging"))
                {
                    StoredProcedureName = storedProcedureName ?? sourceSchema + "." + className.Substring(startIndex: 0, length: className.Length - 5);
                    StagingTableName = $"{sourceSchema}.rpt_vw{tableName}";
                }
                else
                {
                    StoredProcedureName = storedProcedureName ?? sourceSchema + "." + className.Substring(startIndex: 0, length: className.Length - 5);
                    StagingTableName = stagingTableName ?? $"{sourceSchema}.{tableName}";
                }
            }
            else
            {
                StoredProcedureName = storedProcedureName ?? "BPStaging." + className.Substring(startIndex: 0, length: className.Length - 5);
                TargetTableName = targetTableName ?? $"dbo.{className.Substring(startIndex: 4, length: className.Length - 9)}";
                StagingTableName = stagingTableName ?? $"BPStaging.{className.Substring(startIndex: 4, length: className.Length - 9)}";
            }
            output.WriteLine($"Information: Using Stored Procedure '{StoredProcedureName}', Target Table '{TargetTableName}' and Staging Table '{StagingTableName}' names.");
        }
        else
        {
            // When all are provided then don't try to work them out.
            StoredProcedureName = storedProcedureName;
            TargetTableName = targetTableName;
            StagingTableName = stagingTableName;

            output.WriteLine($"Warning: Passed Stored Procedure '{storedProcedureName}', Target Table '{targetTableName}' and Staging Table '{stagingTableName}' names. The likely means that the names are not standard as the test class should suggest the correct names.");
        }
    }
    #endregion
}
