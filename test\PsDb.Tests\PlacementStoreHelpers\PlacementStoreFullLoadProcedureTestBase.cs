﻿using System.Diagnostics.CodeAnalysis;
using Xunit.Abstractions;

namespace PsDb.Tests.PlacementStoreHelpers;

/// <summary>
/// Inherit from this to get a base set of tests for a stored procedure that implements a full load.
/// The main test class inherits the tests in here and has to provide the appropriate data and
/// checks. But the main tests are defined here for consistency. You can also add additional tests.
/// However if you are using this you should be asking if the code can be changed to be incremental.
/// </summary>
[ExcludeFromCodeCoverage]
public abstract class PlacementStoreFullLoadProcedureTestBase : PlacementStoreCoreLoadProcedureTestBase
{
    /// <summary>
    /// This wraps the CreateStagingRecord abstract method to allow it to be used in two different ways.
    /// The first more traditional is where you call CreateRow. However this is still a bit tedious./// 
    /// </summary>
    /// <param name="testType"></param>
    /// <param name="sourceUpdatedDate">Ensure that the sourceUpdatedDate value is used rather than providing.</param>
    /// <returns></returns>
    protected abstract dynamic CreateStagingRecord(TestType testType, DateTime sourceUpdatedDate, bool changeSomething);

    /// <summary>
    /// For non-system versioned stored procedures we don't have (in most cases) a ValidTo, and it has no
    /// importance, and the ValidTo column may have different name, especially from sources other 
    /// than Broking Platform. So change it to sourceUpdatedDate.
    /// </summary>
    /// <param name="testType"></param>
    /// <param name="validFrom"></param>
    /// <param name="validTo"></param>
    /// <returns></returns>
    protected override dynamic CreateStagingRecord(TestType testType, DateTime validFrom, DateTime validTo, bool changeSomething)
    {
        return CreateStagingRecord(testType: testType, sourceUpdatedDate: validFrom, changeSomething: changeSomething);
    }

    /// <summary>
    /// This test implements a full load so set the flag
    /// </summary>
    /// <returns></returns>
    protected override bool AlwaysFullLoad()
    {
        return true;
    }

    /// <summary>
    /// Full load isn't expect to have this date. If we do then we could probably make it incremental.
    /// </summary>
    /// <param name="record"></param>
    /// <returns></returns>
    /// <exception cref="NotImplementedException"></exception>
    protected override object GetStagingUpdatedDateValue(dynamic record)
    {
        throw new NotImplementedException();
    }

    #region Tests
    /// <summary>
    /// Check the configuration for the staging table.
    /// The main expectation is that AlwaysFullLoad is explicitly there and set to true.
    /// Other things may force it to be a full load but if the code assumes it then it should be set.
    /// </summary>
    [Fact]
    public void FullLoadProcedureCheckingADFProcessStagingTableConfigurationTest()
    {
        dynamic row = GetResultRow(sql: @$"
                SELECT
                    AlwaysFullLoad = CAST(ISNULL(JSON_VALUE(JSONConfig, '$.AlwaysFullLoad'), 0) AS BIT)
                  , SystemVersioned = CAST(ISNULL(JSON_VALUE(JSONConfig, '$.SystemVersioned'), 0) AS BIT)
                FROM
                    ADF.Process
                WHERE
                    ProcessTypeId = 1
                    AND JSON_VALUE(JSONConfig, '$.TargetTable') = '{StagingTableName}'
                    AND IsDeleted = 0;");
        Assert.True(row != null, $"Unable to find a row in ADF.Process for staging table '{StagingTableName}'.");
        Assert.False(row.SystemVersioned, $"The SystemVersioned was expected to be false. It was '{row.SystemVersioned}'.");
        Assert.True(row.AlwaysFullLoad, $"The AlwaysFullLoad was expected to be true. It was '{row.AlwaysFullLoad}'.");
    }

    /// <summary>
    /// A full load is expected to mark any records that aren't staged as deleted or deprecated.
    /// We need something in staging to force pass the empty staging table does nothing
    /// so if the "No Change" tests fails so will this.
    /// </summary>
    [Fact]
    public void FullLoadProcedureLogicallyDeletesRecordsTest()
    {
        SetUpExtraRecords(testType: TestType.NoChangeTest);

        dynamic stagingRecord = CreateStagingRecordWrapper(testType: TestType.NoChangeTest, validFrom: DateTime.UtcNow.AddMinutes(-10).WithPrecision(2), validTo: ValidToOpen, changeSomething: false);

        dynamic targetRecord = CreateExistingRecordWrapper(testType: TestType.NoChangeTest, stagingRecord: stagingRecord);

        dynamic targetRecord2 = CreateDummyExistingRecordWrapper(testType: TestType.NoChangeTest);


        dynamic result = ExecuteStoredProcedureWithResultRow(storedProcedureName: StoredProcedureName);
        IfRejectedGetSprocError(output: output, rejectedCount: result.RejectedCount, storedProcedureName: StoredProcedureName);
        Assert.True(result.RejectedCount == 0, $"Expected result.RejectedCount to be 0. It was {result.RejectedCount}.");
        Assert.True(result.InsertedCount == 0, $"Expected result.InsertedCount to be 0. It was {result.InsertedCount}.");
        Assert.True(result.UpdatedCount == 1, $"Expected result.UpdatedCount to be 1. It was {result.UpdatedCount}.");
        Assert.True(result.DeletedCount == 0, $"Expected result.DeletedCount to be 0. It was {result.DeletedCount}.");

        CheckSprocExecutionLog(sprocName: StoredProcedureName, updatedCount: 1);

        // We have two records in the target table. I only care about the one I expect to mark as deleted
        // so I need to find the primary key column and use that to find the right record.
        // If there is no primary key then that is a problem in itself.
        string[] primaryKeyColumns = GetPrimaryKeyColumns(tableName: TargetTableName);
        Assert.NotEmpty(primaryKeyColumns);

        var where = string.Join(" AND ", primaryKeyColumns.Select(pcc => { if(targetRecord2[pcc] == null) { return $"{pcc} IS NULL"; } else { return $"{pcc} = '{targetRecord2[pcc]}'"; } }));
        dynamic row = GetResultRow(tableName: TargetTableName, whereClause: where);
        Assert.NotNull(row);
        Assert.Equal(expected: true, actual: GetLogicalDeletionValue(row));
    }

    /// <summary>
    /// Check the configuration
    /// </summary>
    [Fact]
    public void FullLoadProcedureCheckingADFProcessConfigurationForStagingLoadTest()
    {
        dynamic row = GetResultRow(sql: @$"
                SELECT
                    AlwaysFullLoad = CAST(ISNULL(JSON_VALUE(JSONConfig, '$.AlwaysFullLoad'), 0) AS BIT)
                  , SystemVersioned = CAST(ISNULL(JSON_VALUE(JSONConfig, '$.SystemVersioned'), 0) AS BIT)
                  , SourceSQL = ISNULL(JSON_VALUE(JSONConfig, '$.SourceSQL'), '')
                FROM
                    ADF.Process
                WHERE
                    ProcessTypeId = 1
                    AND JSON_VALUE(JSONConfig, '$.TargetTable') = '{StagingTableName}'
                    AND IsDeleted = 0;");
        Assert.True(row != null, $"Unable to find a row in ADF.Process for staging table '{StagingTableName}'.");
        Assert.False(row.SystemVersioned, $"The SystemVersioned was expected to be false. It was '{row.SystemVersioned}'.");
        Assert.True(row.AlwaysFullLoad, $"The AlwaysFullLoad was expected to be true. It was '{row.AlwaysFullLoad}'.");
    }

    #endregion

    #region Constructor
    /// <summary>
    /// Constructor
    /// </summary>
    /// <param name="fixture"></param>
    /// <param name="output"></param>
    /// <param name="targetTableName"></param>
    /// <param name="storedProcedureName"></param>
    /// <param name="stagingTableName"></param>
    protected PlacementStoreFullLoadProcedureTestBase(DatabaseFixture fixture, ITestOutputHelper output, string? targetTableName = null, string? storedProcedureName = null, string? stagingTableName = null) 
        : base(fixture, output, targetTableName, storedProcedureName, stagingTableName)
    {
    }
    #endregion
}
