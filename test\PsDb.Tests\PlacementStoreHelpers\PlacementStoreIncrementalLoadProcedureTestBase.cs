﻿using System;
using System.Diagnostics;
using System.Diagnostics.CodeAnalysis;
using System.Text.RegularExpressions;
using Xunit.Abstractions;

namespace PsDb.Tests.PlacementStoreHelpers;

/// <summary>
/// This class helps to test load stored procedures.
/// If you want to test one that loads from a System Versioned table you should be
/// inheriting from PlacementStoreSystemVersionedLoadProcedureTestBase.
/// If you want to tests a full load you should inherit from PlacementStoreFullLoadProcedureTestBase.
/// The main test class inherits the tests in here and has to provide the appropriate data and
/// checks. But the main tests are defined here for consistency. You can also add additional tests.
/// </summary>
[ExcludeFromCodeCoverage]
public abstract class PlacementStoreIncrementalLoadProcedureTestBase : PlacementStoreCoreLoadProcedureTestBase
{
    // In CreateStagingRecord ensure that the sourceUpdatedDate value is used rather than providing.

    /// <summary>
    /// Create a staging record
    /// </summary>
    /// <param name="testType">The type of the test if you need to know.</param>
    /// <param name="sourceUpdatedDate">The value to put into the updated date column in the staging record</param>
    /// <param name="isDeleted">The value to set IsDeleted or IsDeprecated to</param>
    /// <param name="changeSomething">If true a value needs to be different to the real record to check the update works.</param>
    /// <returns></returns>
    protected abstract dynamic CreateStagingRecord(TestType testType, DateTime sourceUpdatedDate, bool isDeleted, bool changeSomething);

    /// <summary>
    /// For non-system versioned stored procedures we don't have (in most cases) a ValidTo, and it has no
    /// importance, and the ValidTo column may have different name, especially from sources other 
    /// than Broking Platform. So change it to sourceUpdatedDate.
    /// </summary>
    /// <param name="testType"></param>
    /// <param name="validFrom"></param>
    /// <param name="validTo"></param>
    /// <returns></returns>
    protected override dynamic CreateStagingRecord(TestType testType, DateTime validFrom, DateTime validTo, bool changeSomething)
    {
        return CreateStagingRecord(testType: testType, sourceUpdatedDate: validFrom, isDeleted: (validTo < ValidToOpen), changeSomething: changeSomething);
    }

    /// <summary>
    /// Override if the staging table is using a different column name.
    /// </summary>
    /// <param name="row"></param>
    /// <returns></returns>
    protected override object GetStagingUpdatedDateValue(dynamic row)
    {
        return row.ETLUpdatedDate;
    }

    /// <summary>
    /// Creates a dummy test target record that can be used to help test that an incorrect record isn't deleted.
    /// Normally this works but provided an override for when it doesn't.
    /// </summary>
    /// <returns></returns>
    protected virtual object IncrementalLoadProcedureCreateDummyTargetRecordToCheckDoesntDeleteWrongOne()
    {
        dynamic targetRecord = CreateRow(
          tableName: TargetTableName,
          values: new
          {
              IsDeleted = false,
              IsDeprecated = false,
              DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform
          },
          checkValueColumns: false /* Dodgy parameter to allow us to specify parameters that don't exist. */
        );
        return targetRecord;
    }

    #region Tests
    /// <summary>
    /// Check the configuration
    /// </summary>
    [Fact]
    public void IncrementalLoadProcedureCheckingADFProcessConfigurationForStagingLoadTest()
    {
        dynamic row = GetResultRow(sql: @$"
                SELECT
                    AlwaysFullLoad = CAST(ISNULL(JSON_VALUE(JSONConfig, '$.AlwaysFullLoad'), 0) AS BIT)
                  , SystemVersioned = CAST(ISNULL(JSON_VALUE(JSONConfig, '$.SystemVersioned'), 0) AS BIT)
                  , SourceSQL = ISNULL(JSON_VALUE(JSONConfig, '$.SourceSQL'), '')
                  , IncrementalUpdatedDateColumn = ISNULL(JSON_VALUE(JSONConfig, N'$.IncrementalUpdatedDateColumn'), '')
                FROM
                    ADF.Process
                WHERE
                    ProcessTypeId = 1
                    AND JSON_VALUE(JSONConfig, '$.TargetTable') = '{StagingTableName}'
                    AND IsDeleted = 0;");
        Assert.True(row != null, $"Unable to find a row in ADF.Process for staging table '{StagingTableName}'.");
        Assert.False(String.IsNullOrEmpty(row.IncrementalUpdatedDateColumn), $"The IncrementalUpdatedDateColumn was not expected to be empty. It was '{row.IncrementalUpdatedDateColumn}'.");
        Assert.False(row.SystemVersioned, $"The SystemVersioned was expected to be false. It was '{row.SystemVersioned}'.");
        Assert.True(!row.AlwaysFullLoad, $"The AlwaysFullLoad was expected to be false. It was '{row.AlwaysFullLoad}'.");
    }

    /// <summary>
    /// If the code has a check that it doesn't update if nothing is stage we
    /// want to test that if a record is staged that doesn't match the current
    /// records it doesn't do a logical or physical delete.
    /// It mustn't have a "WHEN NOT MATCHED BY SOURCE" clause.
    /// Hard to test as as soon as we add a staging record it will insert that.
    /// So inserted count will be 1. Nothing should be deleted.
    /// </summary>
    [Fact]
    public void IncrementalLoadProcedureDoesntDeleteIfNoMatchingStagedRecordTest()
    {
        SetUpExtraRecords(testType: TestType.NoExtraDeleteTest);

        CreateStagingRecordWrapper(testType: TestType.NoExtraDeleteTest, validFrom: DateTime.UtcNow.AddMinutes(-10).WithPrecision(2), validTo: DateTime.UtcNow.AddMinutes(-1).WithPrecision(2), changeSomething: false);
        dynamic targetRecord = IncrementalLoadProcedureCreateDummyTargetRecordToCheckDoesntDeleteWrongOne();

        dynamic result = ExecuteStoredProcedureWithResultRow(storedProcedureName: StoredProcedureName);
        IfRejectedGetSprocError(output: output, rejectedCount: result.RejectedCount, storedProcedureName: StoredProcedureName);
        Assert.True(result.RejectedCount == 0, $"Expected result.RejectedCount to be 0. It was {result.RejectedCount}.");
        Assert.True(result.InsertedCount == 1, $"Expected result.InsertedCount to be 1. It was {result.InsertedCount}.");
        Assert.True(result.UpdatedCount == 0, $"Expected result.UpdatedCount to be 0. It was {result.UpdatedCount}.");
        Assert.True(result.DeletedCount == 0, $"Expected result.DeletedCount to be 0. It was {result.DeletedCount}.");

        CheckSprocExecutionLog(sprocName: StoredProcedureName, insertedCount: 1);

        string[] primaryKeyColumns = GetPrimaryKeyColumns(tableName: TargetTableName);
        Assert.NotEmpty(primaryKeyColumns);

        var where = string.Join(" AND ", primaryKeyColumns.Select(pcc => { if(targetRecord[pcc] == null) { return $"{pcc} IS NULL"; } else { return $"{pcc} = '{targetRecord[pcc]}'"; } }));
        dynamic row = GetResultRow(tableName: TargetTableName, whereClause: where);
        Assert.NotNull(row);
        Assert.Equal(expected: false, actual: GetLogicalDeletionValue(row));
    }

    /// <summary>
    /// Checks if the ValidTo is not 9999-12-31 that the target record is marked as deprecated or deleted.
    /// </summary>
    [Fact]
    public void IncrementalLoadProcedureLogicalDeleteTest()
    {
        SetUpExtraRecords(testType: TestType.LogicalDeleteTest);

        dynamic stagingRecord = CreateStagingRecordWrapper(testType: TestType.LogicalDeleteTest, validFrom: DateTime.UtcNow.AddMinutes(-10).WithPrecision(2), validTo: DateTime.UtcNow.AddMinutes(-1).WithPrecision(2), changeSomething: false);

        dynamic targetRecord = CreateExistingRecordWrapper(testType: TestType.LogicalDeleteTest, stagingRecord: stagingRecord);

        dynamic result = ExecuteStoredProcedureWithResultRow(storedProcedureName: StoredProcedureName);
        IfRejectedGetSprocError(output: output, rejectedCount: result.RejectedCount, storedProcedureName: StoredProcedureName);
        Assert.True(result.RejectedCount == 0, $"Expected result.RejectedCount to be 0. It was {result.RejectedCount}.");
        Assert.True(result.UpdatedCount == 1, $"Expected result.UpdatedCount to be 1. It was {result.UpdatedCount}.");

        CheckSprocExecutionLog(sprocName: StoredProcedureName, updatedCount: 1);

        dynamic row = GetResultRowOverride();
        Assert.NotNull(row);
        Assert.True(condition: GetUpdatedDateValue(row) > DateTime.UtcNow.AddMinutes(-1), userMessage: "Expected Updated Date to have been changed.");
        Assert.True(condition: GetCreatedDateValue(row) < DateTime.UtcNow.AddMinutes(-1), userMessage: "Created Date was not expected to change.");
        Assert.True(condition: Convert.ToBoolean(GetLogicalDeletionValue(row)), userMessage: $"Logical delete value expected to be true it was {GetLogicalDeletionValue(row)}.");
        Assert.Equal(expected: GetStagingUpdatedDateValue(stagingRecord), actual: GetSourceUpdatedDateValue(row));

        CheckTargetRecordValues(testType: TestType.LogicalDeleteTest, stagingRecord: stagingRecord, targetResult: row);
    }

    #endregion
    #region Constructor

    /// <summary>
    /// Constructor - Do not change.
    /// If no targetTableName, storeProcedureName or stagingTableName are passed in it 
    /// will try and compute the names from the test class name.
    /// If these have to be set it suggests that something isn't quite right.
    /// Tests for new functionality should not have these.
    /// </summary>
    /// <param name="fixture"></param>
    /// <param name="output"></param>
    /// <param name="targetTableName"></param>
    /// <param name="storedProcedureName"></param>
    /// <param name="stagingTableName"></param>
    public PlacementStoreIncrementalLoadProcedureTestBase(
        DatabaseFixture fixture,
        ITestOutputHelper output,
        string? targetTableName = null,
        string? storedProcedureName = null,
        string? stagingTableName = null
       ) : base(fixture, output, targetTableName, storedProcedureName, stagingTableName)
    {
    }
    #endregion
}
