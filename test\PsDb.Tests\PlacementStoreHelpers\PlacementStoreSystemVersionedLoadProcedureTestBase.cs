﻿using System.Diagnostics;
using System.Diagnostics.CodeAnalysis;
using System.Text.RegularExpressions;
using Xunit.Abstractions;

namespace PsDb.Tests.PlacementStoreHelpers;

/// <summary>
/// This class helps to test system versioned records.
/// If you want to tests a stored procedure that isn't using a system versioned table you should 
/// use PlacementStoreIncrementalLoadProcedureTestBase to inherit from for an incremental load,
/// or PlacementStoreFullLoadProcedureTestBase for a full load.
/// The main test class inherits the tests in here and has to provide the appropriate data and
/// checks. But the main tests are defined here for consistency. You can also add additional tests.
/// </summary>
[ExcludeFromCodeCoverage]
public abstract class PlacementStoreSystemVersionedLoadProcedureTestBase : PlacementStoreCoreLoadProcedureTestBase
{
    // In CreateStagingRecord ensure that the ValidTo and ValidFrom values are used rather than providing them.
    //protected abstract dynamic CreateStagingRecord(TestType testType, DateTime validFrom, DateTime validTo);

    /// <summary>
    /// Staging table should have ValidFrom.
    /// </summary>
    /// <param name="row"></param>
    /// <returns></returns>
    protected override object GetStagingUpdatedDateValue(dynamic record)
    {
        return record.ValidFrom;
    }

    #region Tests
    /// <summary>
    /// Checks if the ValidTo is not 9999-12-31 that the target record is marked as deprecated or deleted.
    /// </summary>
    [Fact]
    public void SystemVersionedLoadProcedureLogicalDeleteTest()
    {
        SetUpExtraRecords(testType: TestType.LogicalDeleteTest);

        dynamic stagingRecord = CreateStagingRecordWrapper(testType: TestType.LogicalDeleteTest, validFrom: DateTime.UtcNow.AddMinutes(-10).WithPrecision(2), validTo: DateTime.UtcNow.AddMinutes(-1).WithPrecision(2), changeSomething: false);

        dynamic targetRecord = CreateExistingRecordWrapper(testType: TestType.LogicalDeleteTest, stagingRecord: stagingRecord);

        dynamic result = ExecuteStoredProcedureWithResultRow(storedProcedureName: StoredProcedureName);
        IfRejectedGetSprocError(output: output, rejectedCount: result.RejectedCount, storedProcedureName: StoredProcedureName);
        Assert.True(result.RejectedCount == 0, $"Expected result.RejectedCount to be 0. It was {result.RejectedCount}.");
        Assert.True(result.UpdatedCount == 1, $"Expected result.UpdatedCount to be 1. It was {result.UpdatedCount}.");

        CheckSprocExecutionLog(sprocName: StoredProcedureName, updatedCount: 1);

        dynamic row = GetResultRow(tableName: TargetTableName);
        Assert.NotNull(row);
        Assert.True(GetUpdatedDateValue(row) > DateTime.UtcNow.AddMinutes(-1));
        Assert.True(GetCreatedDateValue(row) < DateTime.UtcNow.AddMinutes(-1));
        Assert.True(condition: Convert.ToBoolean(GetLogicalDeletionValue(row)), userMessage: $"Logical delete value expected to be true it was {GetLogicalDeletionValue(row)}.");
        if(!AlwaysFullLoad())
        {
            // For a Full Load we shouldn't be updating SourceUpdatedDate so don't check it.
            // No source record to get it from by definition
            Assert.Equal(expected: stagingRecord.ValidTo, actual: GetSourceUpdatedDateValue(row));
        }

        CheckTargetRecordValues(testType: TestType.LogicalDeleteTest, stagingRecord: stagingRecord, targetResult: row);
    }


    /// <summary>
    /// Checks if the ValidTo is not 9999-12-31 that the target record is marked as deprecated or deleted.
    /// And if there are multiple records in the staging table ensure that the deleted record is used.
    /// There could be two records with the same ValidFrom so the ValidTo date is also required in the ORDER BY
    /// </summary>
    [Fact]
    public void SystemVersionedLoadProcedureLogicalDeleteWithMultipleStagedTest()
    {
        SetUpExtraRecords(testType: TestType.LogicalDeleteTest);

        var validFrom = DateTime.UtcNow.AddMinutes(-10).WithPrecision(2);
        dynamic stagingRecord = CreateStagingRecordWrapper(testType: TestType.LogicalDeleteTest, validFrom: validFrom, validTo: ValidToOpen, changeSomething: true);
        dynamic stagingRecord2 = CreateStagingRecordWrapper(testType: TestType.LogicalDeleteTest, validFrom: validFrom, validTo: DateTime.UtcNow.AddMinutes(-1).WithPrecision(2), changeSomething: false);

        // For this test to have value the SourceUpdatedDate can't have the same value as a staging record.
        stagingRecord2["ValidFrom"] = DateTime.UtcNow.AddDays(-1);
        dynamic targetRecord = CreateExistingRecordWrapper(testType: TestType.LogicalDeleteTest, stagingRecord: stagingRecord2);

        dynamic result = ExecuteStoredProcedureWithResultRow(storedProcedureName: StoredProcedureName);
        IfRejectedGetSprocError(output: output, rejectedCount: result.RejectedCount, storedProcedureName: StoredProcedureName);
        Assert.True(result.RejectedCount == 0, $"Expected result.RejectedCount to be 0. It was {result.RejectedCount}.");
        Assert.True(result.UpdatedCount == 1, $"Expected result.UpdatedCount to be 1. It was {result.UpdatedCount}.");

        CheckSprocExecutionLog(sprocName: StoredProcedureName, updatedCount: 1);

        dynamic row = GetResultRow(tableName: TargetTableName);
        Assert.NotNull(row);
        Assert.True(GetUpdatedDateValue(row) > DateTime.UtcNow.AddMinutes(-1));
        Assert.True(GetCreatedDateValue(row) < DateTime.UtcNow.AddMinutes(-1));
        Assert.Equal(expected: true, actual: GetLogicalDeletionValue(row));
        Assert.Equal(expected: stagingRecord2.ValidTo, actual: GetSourceUpdatedDateValue(row));

        CheckTargetRecordValues(testType: TestType.LogicalDeleteTest, stagingRecord: stagingRecord2, targetResult: row);
    }

    /// <summary>
    /// Because the BP is system versioned it is possible that it will send multiple records and we need to use the latest.
    /// </summary>
    [Fact]
    public void SystemVersionedLoadProcedureUseMostRecentRecordTest()
    {
        SetUpExtraRecords(testType: TestType.UseMostRecentRecordTest);

        dynamic stagingRecord = CreateStagingRecordWrapper(testType: TestType.UseMostRecentRecordTest, validFrom: DateTime.UtcNow.AddMinutes(-50).WithPrecision(2), validTo: ValidToOpen, changeSomething: true);
        dynamic stagingRecord2 = CreateStagingRecordWrapper(testType: TestType.UseMostRecentRecordTest, validFrom: DateTime.UtcNow.AddMinutes(-10).WithPrecision(2), validTo: ValidToOpen, changeSomething: true);

        // For this test to have value the SourceUpdatedDate can't have the same value as a staging record.
        stagingRecord["ValidFrom"] = DateTime.UtcNow.AddDays(-1);
        dynamic targetRecord = CreateExistingRecordWrapper(testType: TestType.UseMostRecentRecordTest, stagingRecord: stagingRecord);

        dynamic result = ExecuteStoredProcedureWithResultRow(storedProcedureName: StoredProcedureName);
        IfRejectedGetSprocError(output: output, rejectedCount: result.RejectedCount, storedProcedureName: StoredProcedureName);
        Assert.True(result.RejectedCount == 0, $"Expected result.RejectedCount to be 0. It was {result.RejectedCount}.");
        Assert.True(result.UpdatedCount == 1, $"Expected result.UpdatedCount to be 1. It was {result.UpdatedCount}.");

        CheckSprocExecutionLog(sprocName: StoredProcedureName, updatedCount: 1);

        dynamic row = GetResultRow(tableName: TargetTableName);
        Assert.NotNull(row);
        Assert.True(GetUpdatedDateValue(row) > DateTime.UtcNow.AddMinutes(-1));
        Assert.True(GetCreatedDateValue(row) < DateTime.UtcNow.AddMinutes(-1));
        Assert.Equal(expected: false, actual: GetLogicalDeletionValue(row));
        Assert.Equal(expected: stagingRecord2.ValidFrom, actual: GetSourceUpdatedDateValue(row));

        CheckTargetRecordValues(testType: TestType.UseMostRecentRecordTest, stagingRecord: stagingRecord2, targetResult: row);
    }

    [Fact]
    public void SystemVersionedLoadProcedureCheckingADFProcessConfigurationTest()
    {
        dynamic row = GetResultRow(sql: @$"
                SELECT
                    AlwaysFullLoad = CAST(ISNULL(JSON_VALUE(JSONConfig, '$.AlwaysFullLoad'), 0) AS BIT)
                  , SystemVersioned = CAST(ISNULL(JSON_VALUE(JSONConfig, '$.SystemVersioned'), 0) AS BIT)
                  , SourceSQL = ISNULL(JSON_VALUE(JSONConfig, '$.SourceSQL'), '')
                  , IncrementalUpdatedDateColumn = ISNULL(JSON_VALUE(JSONConfig, N'$.IncrementalUpdatedDateColumn'), '')
                FROM
                    ADF.Process
                WHERE
                    ProcessTypeId = 1
                    AND JSON_VALUE(JSONConfig, '$.TargetTable') = '{StagingTableName}'
                    AND IsDeleted = 0;");

        if (row == null || 
            row.IncrementalUpdatedDateColumn != "ValidFrom" || 
            !row.SystemVersioned || row.AlwaysFullLoad
            )
        {
            // Not looking good so suggest what it could be like. Still bits to be filled in.:
            output.WriteLine("Suggested line for ADF.Process:");
            output.WriteLine($"  , (@LoadType_Intraday, '{StagingTableName}', @ProcessType_Copy, '{{\"SourceConnection\":\"--- ? ---\", \"SourceTable\":\"--- ? ---\", \"SourceSQL\":\"\", \"TargetTable\":\"{StagingTableName}\", \"TruncateTargetTable\": true, \"TargetConnection\":\"PlacementStore\", \"SystemVersioned\":true, \"AlwaysFullLoad\":false, \"IncrementalUpdatedDateColumn\":\"ValidFrom\"}}', '{StagingTableName}')");
        }

        Assert.True(row != null, $"Unable to find a row in ADF.Process for staging table '{StagingTableName}'.");
        Assert.True(row.IncrementalUpdatedDateColumn == "ValidFrom", $"The IncrementalUpdatedDateColumn was expected to be 'ValidFrom'. It was '{row.IncrementalUpdatedDateColumn}'.");
        Assert.True(row.SystemVersioned, $"The SystemVersioned was expected to be true. It was '{row.AlwaysFullLoad}'.");
        Assert.True(!row.AlwaysFullLoad, $"The AlwaysFullLoad was expected to be false. It was '{row.AlwaysFullLoad}'.");

    }

    /// <summary>
    /// If the code has a check that it doesn't update if nothing is stage we
    /// want to test that if a record is staged that doesn't match the current
    /// records it doesn't do a logical or physical delete.
    /// It mustn't have a "WHEN NOT MATCHED BY SOURCE" clause.
    /// Hard to test as as soon as we add a staging record it will insert that.
    /// So inserted count will be 1. Nothing should be deleted.
    /// </summary>
    [Fact]
    public void SystemVersionedLoadProcedureCheckDoesntDeleteIfNoMatchingStagedRecordTest()
    {
        SetUpExtraRecords(testType: TestType.NoExtraDeleteTest);

        CreateStagingRecordWrapper(testType: TestType.NoExtraDeleteTest, validFrom: DateTime.UtcNow.AddMinutes(-10).WithPrecision(2), validTo: ValidToOpen, changeSomething: false);

        dynamic targetRecord = CreateRow(
            tableName: TargetTableName,
            values: new
            {
                IsDeleted = false,
                IsDeprecated = false,
                DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform
            },
            checkValueColumns: false /* Dodgy parameter to allow us to specify parameters that don't exist. */
        );

        dynamic result = ExecuteStoredProcedureWithResultRow(storedProcedureName: StoredProcedureName);
        IfRejectedGetSprocError(output: output, rejectedCount: result.RejectedCount, storedProcedureName: StoredProcedureName);
        Assert.True(result.RejectedCount == 0, $"Expected result.RejectedCount to be 0. It was {result.RejectedCount}.");
        Assert.True(result.InsertedCount == ExpectedInsertedCount, $"Expected result.InsertedCount to be {ExpectedInsertedCount}. It was {result.InsertedCount}.");
        Assert.True(result.UpdatedCount == 0, $"Expected result.UpdatedCount to be 0. It was {result.UpdatedCount}.");
        Assert.True(result.DeletedCount == 0, $"Expected result.DeletedCount to be 0. It was {result.DeletedCount}.");

        CheckSprocExecutionLog(sprocName: StoredProcedureName, insertedCount: ExpectedInsertedCount);

        string[] primaryKeyColumns = GetPrimaryKeyColumns(tableName: TargetTableName);
        Assert.NotEmpty(primaryKeyColumns);
        var where = string.Join(" AND ", primaryKeyColumns.Select(pcc => { if(targetRecord[pcc] == null) { return $"{pcc} IS NULL"; } else { return $"{pcc} = '{targetRecord[pcc]}'"; } }));
        dynamic row = GetResultRow(tableName: TargetTableName, whereClause: where);
        Assert.NotNull(row);
        Assert.Equal(expected: false, actual: GetLogicalDeletionValue(row));
    }

    #endregion
    #region Constructor

    /// <summary>
    /// Constructor - Do not change.
    /// If no targetTableName, storeProcedureName or stagingTableName are passed in it 
    /// will try and compute the names from the test class name.
    /// If these have to be set it suggests that something isn't quite right.
    /// Tests for new functionality should not have these.
    /// </summary>
    /// <param name="fixture"></param>
    /// <param name="output"></param>
    /// <param name="targetTableName"></param>
    /// <param name="storedProcedureName"></param>
    /// <param name="stagingTableName"></param>
    public PlacementStoreSystemVersionedLoadProcedureTestBase(
        DatabaseFixture fixture,
        ITestOutputHelper output,
        string? targetTableName = null, 
        string? storedProcedureName = null,
        string? stagingTableName = null
       ) : base(fixture, output, targetTableName, storedProcedureName, stagingTableName)
    {
    }
    #endregion
}
