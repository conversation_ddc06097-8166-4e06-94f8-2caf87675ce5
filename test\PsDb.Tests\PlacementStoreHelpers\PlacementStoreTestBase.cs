﻿#define OutputLineageIfWrongOrEmpty
/* The next line should be commented out in a PR */
// #define FixLineageIfWrongOrEmpty // Only if you know what you are doing
// #define FixKeyAndConstraintNames // Only if you know what you are doing

using Microsoft.IdentityModel.Tokens;
using PS.LineageForPurview;
using PsDb.Tests.DatabaseHelpers;
using PsDb.Tests.PlacementStoreHelpers;
using System.Data;
using System.Diagnostics.CodeAnalysis;
using System.Reflection;
using System.Security.AccessControl;
using System.Text;
using System.Text.RegularExpressions;
using Xunit.Abstractions;

namespace PsDb.Tests;

[ExcludeFromCodeCoverage]
[Collection("Database Test Collection")]
public class PlacementStoreTestBase : DatabaseTestBase
{
    private readonly ITestOutputHelper? output;

    /// <summary>
    /// This is provided to give the value for the ValidTo column
    /// when the record is not deleted. Microsoft call the row "Open".
    /// </summary>
    protected DateTime ValidToOpen { get { return new DateTime(year: 9999, month: 12, day: 31); } }

    /// <summary>
    /// To simplify the tests here use reflection to derive the name of the stored procedure to be tested
    /// with no data being passed in.
    /// </summary>
    /// <param name="storedProcedureTestMethod"></param>
    protected void NoDataStoredProcedureTest(MethodBase? storedProcedureTestMethod, object? values = null, string schema = "rpt")
    {
        string storedProcedureName = $"{schema}.{storedProcedureTestMethod.Name.Replace("NoDataTest", "")}";

        ExecuteStoredProcedureWithoutResult(storedProcedureName: storedProcedureName, values: values);

        // Expect a log row but without error.
        CheckSprocExecutionLog(sprocName: storedProcedureName);
    }

    /// <summary>
    /// Helper to check that the PactConfig.SprocExecutionLog table is set correctly.
    /// </summary>
    /// <param name="sprocName"></param>
    /// <param name="insertedCount"></param>
    /// <param name="updatedCount"></param>
    /// <param name="deletedCount"></param>
    /// <param name="rejectedCount"></param>
    protected void CheckSprocExecutionLog(string sprocName, int insertedCount = 0, int updatedCount = 0, int deletedCount = 0, int rejectedCount = 0)
    {
        dynamic row = GetResultRow(tableName: "ADF.StoredProcLog", whereClause: $"StoredProcName = '{sprocName}'");
        if(!(row.ErrorMessage is DBNull))
        {
            output?.WriteLine($"{row.ErrorMessage}");
            Assert.Equal(expected: "", actual: row.ErrorMessage);
        }
        Assert.Equal(expected: sprocName, actual: row.StoredProcName);
        Assert.True(row.StartTime > DateTime.UtcNow.AddMinutes(-1));
        // Equals shows the values but doesn't make it clear what the error was.
        // By using True I can display a more helpful message
        Assert.True(row.RejectedCount == rejectedCount, $"Expected row.InsertedCount to be  {rejectedCount} . It was  {row.RejectedCount}.");
        Assert.True(row.InsertedCount == insertedCount, $"Expected row.InsertedCount to be {insertedCount}. It was {row.InsertedCount}.");
        Assert.True(row.UpdatedCount == updatedCount, $"Expected row.UpdatedCount to be {updatedCount}. It was {row.UpdatedCount}.");
        Assert.True(row.DeletedCount == deletedCount, $"Expected row.DeletedCount to be {deletedCount}. It was {row.DeletedCount}.");
    }

    /// <summary>
    /// Help which attempts to get any error message from the SprocLog if rejected count suggests there is an error.
    /// The error is returned in case the caller needs it.
    /// </summary>
    /// <param name="output"></param>
    /// <param name="rejectedCount"></param>
    protected string? IfRejectedGetSprocError(ITestOutputHelper output, int rejectedCount, string storedProcedureName)
    {
        if(rejectedCount != 0)
        {
            dynamic row = GetResultRow(tableName: "ADF.StoredProcLog", whereClause: $"StoredProcName = '{storedProcedureName}'");
            if(row != null && !(row.ErrorMessage is DBNull))
            {
                output?.WriteLine($"StoredProcLog Error: '{row.ErrorMessage}'");
                return row.ErrorMessage;
            }
        }
        return null;
    }

    /// <summary>
    /// This attempts to get the updated and read tables.
    /// Sometimes it just has a list of tables and none of them go where you would like.
    /// </summary>
    /// <param name="storedProcedure"></param>
    /// <param name="updated"></param>
    /// <param name="selected"></param>
    protected void GetInputAndOutputTables(string storedProcedure, out IEnumerable<string> updated, out IEnumerable<string> selected)
    {
        DataTable rows = GetResultRows(sql: $"SELECT DISTINCT CONCAT(referenced_schema_name, '.', referenced_entity_name) AS [name], is_updated, is_selected FROM sys.dm_sql_referenced_entities ('{storedProcedure}', 'OBJECT') WHERE is_updated = 1 OR is_selected = 1;");
        updated = rows.Select(filterExpression: "is_updated = 1 AND is_selected = 0").Select((x) => $"{x["name"]}").Distinct();
        selected = rows.Select(filterExpression: "is_updated = 0 AND is_selected = 1").Select((x) => $"{x["name"]}").Distinct().Except(updated);

        // At the moment if more than 1 table is feeding into this we can't handle it.
        //Assert.Single(selected);
    }

    /// <summary>
    /// Returns the fist column name that indciates it is deleted/deprecated.
    /// </summary>
    /// <param name="columns"></param>
    /// <returns></returns>
    protected string? GetLogicallyDeletedColumn(IEnumerable<string> columns)
    {
        return columns.FindFirstOf(new string[] { "IsDeleted", "IsDeprecated" });
    }

    /// <summary>
    /// Returns the name of a column in the table that usually holds the time the row was last updated.
    /// </summary>
    /// <param name="columns"></param>
    /// <returns></returns>
    protected string? GetUpdatedAtColumn(IEnumerable<string> columns)
    {
        return columns.FindFirstOf(new string[] { "ETLUpdatedDate", "LastUpdatedDate", "LastUpdatedUTCDate", "UpdatedDate", "UpdatedDateTime" });
    }

    /// <summary>
    /// Returns true if the table contains ValidTo and ValidFrom columns.
    /// This should mean it is system versioned.
    /// </summary>
    /// <param name="columns"></param>
    /// <returns></returns>
    protected bool IsSystemVersioned(IEnumerable<string> columns)
    {
        return columns.Contains(value: "ValidFrom", comparer: StringComparer.InvariantCultureIgnoreCase) &&
               columns.Contains(value: "ValidTo", comparer: StringComparer.InvariantCultureIgnoreCase);
    }

    /// <summary>
    /// These are pulled from the BP now so we need populate them here for anything that needs them.
    /// </summary>
    protected void PopulateRefPlacementStatus()
    {
        CreateRow(tableName: "ref.PlacementStatus", values: new
        {
            PlacementStatusId = (int)PlacementStatus.NotStarted,
            PlacementStatusKey = (int)PlacementStatus.NotStarted,
            PlacementStatus = "Not Started",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform
        });
        CreateRow(tableName: "ref.PlacementStatus", values: new
        {
            PlacementStatusId = (int)PlacementStatus.InProgress,
            PlacementStatusKey = (int)PlacementStatus.InProgress,
            PlacementStatus = "In Progress",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform
        });
        CreateRow(tableName: "ref.PlacementStatus", values: new
        {
            PlacementStatusId = (int)PlacementStatus.Validating,
            PlacementStatusKey = (int)PlacementStatus.Validating,
            PlacementStatus = "Validating",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform
        });
        CreateRow(tableName: "ref.PlacementStatus", values: new
        {
            PlacementStatusId = (int)PlacementStatus.Complete,
            PlacementStatusKey = (int)PlacementStatus.Complete,
            PlacementStatus = "Complete",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform
        });
        CreateRow(tableName: "ref.PlacementStatus", values: new
        {
            PlacementStatusId = (int)PlacementStatus.Cancelled,
            PlacementStatusKey = (int)PlacementStatus.Cancelled,
            PlacementStatus = "Cancelled",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform
        });
        CreateRow(tableName: "ref.PlacementStatus", values: new
        {
            PlacementStatusId = (int)PlacementStatus.Merged,
            PlacementStatusKey = (int)PlacementStatus.Merged,
            PlacementStatus = "Merged",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform
        });
    }

    protected void PopulateRefContractStatus()
    {
        CreateRow(tableName: "ref.ContractStatus", values: new
        {
            ContractStatusId = (int)ContractStatus.Draft,
            ContractStatusKey = (int)ContractStatus.Draft,
            ContractStatus = "Draft",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform
        });

        CreateRow(tableName: "ref.ContractStatus", values: new
        {
            ContractStatusId = (int)ContractStatus.ReadyForMarketSubmission,
            ContractStatusKey = (int)ContractStatus.ReadyForMarketSubmission,
            ContractStatus = "Ready for Market Submission",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform
        });

        CreateRow(tableName: "ref.ContractStatus", values: new
        {
            ContractStatusId = (int)ContractStatus.MarketSubmissionInProgress,
            ContractStatusKey = (int)ContractStatus.MarketSubmissionInProgress,
            ContractStatus = "Market Submission in Progress",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform
        });

        CreateRow(tableName: "ref.ContractStatus", values: new
        {
            ContractStatusId = (int)ContractStatus.ContractBound,
            ContractStatusKey = (int)ContractStatus.ContractBound,
            ContractStatus = "Contract Bound",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform
        });

        CreateRow(tableName: "ref.ContractStatus", values: new
        {
            ContractStatusId = (int)ContractStatus.NotTakenUp,
            ContractStatusKey = (int)ContractStatus.NotTakenUp,
            ContractStatus = "Not Taken Up",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform
        });

        CreateRow(tableName: "ref.ContractStatus", values: new
        {
            ContractStatusId = (int)ContractStatus.Cancelled,
            ContractStatusKey = (int)ContractStatus.Cancelled,
            ContractStatus = "Cancelled",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform
        });
    }

    /// <summary>
    /// Returns the primary key value or values if a compound key.
    /// </summary>
    /// <param name="tableName"></param>
    /// <param name="values"></param>
    /// <returns></returns>
    protected object[]? LookupPrimaryKeyValues(string tableName, object values)
    {
        string[] primaryKeyColumns = GetPrimaryKeyColumns(tableName: tableName);
        Assert.NotEmpty(primaryKeyColumns);

        return GetColumnValues(tableName: tableName, columnNames: primaryKeyColumns, values: values);
    }

    /// <summary>
    /// As we get the PlacementStatus values from the broking platform we can't
    /// assume that the placementStatusId is the same so we need to get the 
    /// value where the PlacementStatusKey is the old id and the Data Source Instance in Broking Platform.
    /// You Probably need to call PopulatePlacementStatus to ensure the values are populated.
    /// </summary>
    /// <param name="dataSourceInstance"></param>
    /// <param name="placementStatus"></param>
    /// <returns></returns>
    protected object LookupRefPlacementStatusId(DataSourceInstance dataSourceInstance, PlacementStatus placementStatus)
    {
        return LookupRefPlacementStatusId(dataSourceInstance: dataSourceInstance, placementStatusKey: (int)placementStatus);
    }

    /// <summary>
    /// Looks up a PlacementStatusId given the key
    /// </summary>
    /// <param name="dataSourceInstance"></param>
    /// <param name="placementStatusKey"></param>
    /// <returns></returns>
    protected object LookupRefPlacementStatusId(DataSourceInstance dataSourceInstance, int placementStatusKey)
    {
        var placementStatusId = LookupPrimaryKeyValues(tableName: "ref.PlacementStatus", values: new { DataSourceInstanceId = (int)dataSourceInstance, PlacementStatusKey = placementStatusKey });
        Assert.Single(placementStatusId!);
        Assert.NotNull(placementStatusId[0]);
        return placementStatusId[0];
    }

    protected object LookupRefContractStatusId(DataSourceInstance dataSourceInstance, int contractStatusKey)
    {
        var contractStatusId = LookupPrimaryKeyValues(tableName: "ref.ContractStatus", values: new { DataSourceInstanceId = (int)dataSourceInstance, ContractStatusKey = contractStatusKey });
        Assert.Single(contractStatusId!);
        Assert.NotNull(contractStatusId[0]);
        return contractStatusId[0];
    }

    /// <summary>
    /// There is a feature where a default offset is set. This is set to 5 by the post deployment
    /// scripts but I don't want to have to create lots of records all the time so setting it 
    /// back to zero for the majority of the tests.
    /// The default is actually only used if the value isn't specified in the JSON.
    /// </summary>
    protected void SetDefaultSuccessfulRunOffsetToZero()
    {
        ExecuteStoredProcedureWithoutResult(storedProcedureName: @"PactConfig.SetControlValue_Int", values: new
        {
            @ItemName = "ADF-DefaultSuccessfulRunOffset",
            @Value = 0
        });
    }

    /// <summary>
    /// Code that checks that a valid Lineage comment exists in the view.
    /// Checks that the columns are all part of the view and that the column they refer to exists.
    /// This check doesn't confirm that the lineage is correct only what is provided is formatted correctly and 
    /// refers to valid things.
    /// </summary>
    /// <param name="objectColumns"></param>
    /// <param name="viewName"></param>
    /// <param name="viewSql"></param>
    protected void CheckForValidLineageComment(Dictionary<string, Dictionary<string, bool>> objectColumns, string schemaName, string viewName, string viewSql)
    {
        var lineageLines = ExtractLineageFromObject(objectName: viewName, sql: viewSql);
        Assert.NotEmpty(lineageLines);

        var lineageOptions = ExtractLineageOptionsFromObject(sql: viewSql);

        var sourceViewColumns = GetColumnInformation(tableName: $"{schemaName}.{viewName}").ToDictionary(x => x);

        StringBuilder sb = new StringBuilder();
        foreach(var lineageLine in lineageLines)
        {
            var m = Regex.Match(input: lineageLine, pattern: @"^([\w\d ]+)=([\w]+\.[\w\d]+)\.([\w\d ]+)$");
            if(!m.Success)
            {
                sb.Append($"{viewName} has a badly formatted Lineage comment. '{lineageLine}'. It may be easier to delete it and insert the generated lineage.\r\n");
            }
            else
            {
                var viewColumn = m.Groups[1].Value;
                var sourceTable = m.Groups[2].Value;
                var sourceColumn = m.Groups[3].Value;
                if(!sourceViewColumns.ContainsKey(key: viewColumn))
                {
                    sb.Append($"{viewName} has a column '{viewColumn}' which doesn't appear to be a valid column.\r\n");
                }
                Dictionary<string, bool> sourceColumns;
                if(!objectColumns.TryGetValue(key: sourceTable, value: out sourceColumns))
                {
                    sourceColumns = GetColumnInformation(tableName: sourceTable).ToDictionary(x => x, x => true);
                    if(sourceColumns.Count == 0)
                    {
                        sb.Append($"{viewName} has a column '{viewColumn}' which maps to column '{sourceColumn}' in table/view '{sourceTable}' which wasn't found.\r\n");
                    }
                    objectColumns.Add(key: sourceTable, value: sourceColumns);
                }
                if(!sourceColumns.ContainsKey(key: sourceColumn))
                {
                    sb.Append($"{viewName} has a column '{viewColumn}' which maps to column '{sourceColumn}' which can't be found in '{sourceTable}'.\r\n");
                }
            }
        }

        // Traditionally output is not set so allow this to handle that case.
        output?.WriteLine(sb.ToString());

        // Check no errors were found for this object.
        Assert.True(sb.Length == 0, sb.ToString());
    }


    /// <summary>
    /// Tries to extract the lineage from the object.
    /// If it is the right format a list of all the lines is return.
    /// The first error fails the extract.
    /// If there is no lineage it returns an empty array. The caller should
    /// check if this is important.
    /// </summary>
    /// <param name="objectName"></param>
    /// <param name="sql"></param>
    /// <returns></returns>
    protected IList<string> ExtractLineageFromObject(string objectName, string sql)
    {
        IList<string> lines = new List<string>();

        // This is set to work with Linux line endings - which also works fine with Windows.
        var objectLines = sql.Replace("\r", "").Split("\n");
        if(objectLines[0].TrimEnd() == "/*" && objectLines[1].TrimEnd() == "Lineage")
        {
            int i = 2;
            while(i < objectLines.Length)
            {
                var objectLine = objectLines[i].TrimEnd();
                if(objectLine == "*/")
                {
                    break;
                }
                var m = Regex.Match(input: objectLine, pattern: @"^([\w ]+|([\w]+\.[\w]+)\.([\w ]+))=([\w]+\.[\w]+)\.([\w ]+)$");
                if(!m.Success)
                {
                    Assert.Fail($"{objectName} has a badly formatted Lineage comment. '{objectLine}'.  It may be easier to delete it and insert the generated lineage.");
                }
                else
                {
                    lines.Add(m.Value);
                }
                i++;
            }
            // Check that the Lineage comment ends.
            if((i >= objectLines.Length || objectLines[i].TrimEnd() != "*/"))
            {
                Assert.Fail($"{objectName} is missing the end of the Lineage comment.");
            }
        }
        return lines;
    }

    /// <summary>
    /// The object may contain some lineage options that control whether we
    /// should be processing it at all.
    /// This needs a comment like:
    /// 
    ///     /* LineageOptions=NoScan,NoValue; */
    /// 
    /// All values are comma separated. 
    /// This doesn't check for valid options.
    /// Converted to uppercase to make it easier to check them later.
    /// </summary>
    /// <param name="sql"></param>
    /// <returns></returns>
    /// <exception cref="NotImplementedException"></exception>
    protected List<string> ExtractLineageOptionsFromObject(string sql)
    {
        var results = new List<string>();

        var r = Regex.Match(input: sql, pattern: @"/\*\s+lineageoptions?\s*=(\s*(?<opt>\w+)\s*,?)+\s*;.*?\*/", options: RegexOptions.IgnoreCase | RegexOptions.Singleline);
        if(r.Success)
        {
            foreach(Capture capt in r.Groups["opt"].Captures)
            {
                results.Add(capt.Value.ToUpper());
            }
        }
        return results;
    }


    /// <summary>
    /// Checks all views in the provided schema have extended properties for all columns.
    /// This is only done for views which have a top level property describing the view.
    /// A separate test checks those exist. This means that test can be turned off and this 
    /// will make sure that the ones that have been changed are correct.
    /// If any are found to be missing it will send them to output.
    /// </summary>
    /// <param name="schemaName"></param>
    protected void CheckViewHasColumnExtendedPropertiesForSchema(string schemaName)
    {
        var result = Convert.ToString(ExecuteSQLStatementWithResult(tSQL:
        @$"
                SELECT Names = ISNULL(STRING_AGG(CAST(CONCAT(s.name, '.', o.name, '.', c.name) AS NVARCHAR(MAX)), ', ' + CHAR(10) + CHAR(13)), '')
                FROM
                    sys.all_objects O
                    INNER JOIN sys.columns c
                        ON O.object_id = c.object_id

                    INNER JOIN sys.schemas S
                        ON O.schema_id = S.schema_id

                    LEFT JOIN sys.extended_properties EPP
                        ON EPP.major_id = O.object_id
                           AND EPP.minor_id = 0
                           AND EPP.name = 'Description'

                    LEFT JOIN sys.extended_properties EP
                        ON EP.major_id = O.object_id
                           AND EP.minor_id = c.column_id
                           AND EP.name = 'Description'
                           AND LEN(CAST(EP.value AS NVARCHAR(MAX))) > ''
                WHERE
                    O.type = 'V'
                    AND S.name = '{schemaName}'
                    AND EP.name IS NULL
                ;
        "
        ));
        output?.WriteLine(result);

        Assert.Equal(expected: "", actual: result);
    }

    /// <summary>
    /// Checks all the views in a schema have an extended property.
    /// This is the property that describes the top level view description.
    /// </summary>
    /// <param name="schemaName"></param>
    protected void CheckViewsHaveExtendedProperties(string schemaName)
    {
        var result = Convert.ToString(ExecuteSQLStatementWithResult(tSQL:
        @$"
                SELECT Names = ISNULL(STRING_AGG(CAST(CONCAT(s.name, '.', o.name) AS NVARCHAR(MAX)), ', '), '')
                FROM
                    sys.all_objects O
                    INNER JOIN sys.schemas S
                        ON O.schema_id = S.schema_id

                    LEFT JOIN sys.extended_properties EP
                        ON EP.major_id = O.object_id
                           AND EP.minor_id = 0
                           AND EP.name = 'Description'
                           AND LEN(CAST(EP.value AS NVARCHAR(MAX))) > ''
                WHERE
                    O.type = 'V'
                    AND S.name = '{schemaName}'
                    AND EP.name IS NULL 
                ;
        "
        ));
        output?.WriteLine(result);

        Assert.Equal(expected: "", actual: result);
    }

    /// <summary>
    /// Runs some SQL to check that the extended properties for key columns always have a consistent description.
    /// Not too fussed about ending full stops, although if it is wrong it will include one in the suggested comment.
    /// </summary>
    /// <param name="schemaName"></param>
    protected void CheckCommonExtendedPropertiesConsistent(string? schemaName = null)
    {
        var result = Convert.ToString(ExecuteSQLStatementWithResult(tSQL:
        @$"
            DROP TABLE IF EXISTS #ExtendedPropertyColumns;

            /*
                These are the columns that have only one description.
                Don't include the full-stop. The code handles that.
            */
            SELECT
                t.ColumnName
                , t.Description
            INTO #ExtendedPropertyColumns
            FROM (
                VALUES (
                    'ETLCreatedDate', 'The UTC date when the record was created in the Placement Store'
                )
                        , (
                    'ETLUpdatedDate', 'The UTC date when the record was last updated in the Placement Store'
                )
                        , (
                    'SourceUpdatedDate', 'The date when the record was last updated in the source system'
                )
                        , (
                    'IsDeprecated', 'An indicator to show if the record has been deprecated'
                )
                        , (
                    'IsDeleted', 'An indicator to show if the record has been deleted'
                )
            --     , (
            --    'PlacementId', 'The unique Id for the Placement from Placement Store.'
            --)
            ) t (ColumnName, Description);

            CREATE UNIQUE INDEX IXU_#ExtendedPropertyColumns
            ON #ExtendedPropertyColumns
            (
                ColumnName
            )
            INCLUDE
            (
                Description
            );

            SELECT
                message = STRING_AGG(
                                CAST(CONCAT(S.name, '.', O.name, ' ', c.name, ': ""', EPC.Description, '."".') AS NVARCHAR(MAX))
                            , CONCAT(CHAR(10), CHAR(13))
                            )
            FROM
                sys.extended_properties EP
                INNER JOIN sys.all_objects O
                    ON EP.major_id = O.object_id

                INNER JOIN sys.schemas S
                    ON O.schema_id = S.schema_id

                INNER JOIN sys.columns c
                    ON EP.major_id = c.object_id
                        AND EP.minor_id = c.column_id

                INNER JOIN #ExtendedPropertyColumns EPC
                    ON EPC.ColumnName = c.name
            WHERE
                /* We are slightly flexible. 
                   A trailing full-stop is optional.
                   If there is a string that starts the same but must have full-stop and space that is allowed 
                   with the assumption there is a qualifying additional comment. Like always NULL!
                */
                EPC.Description COLLATE SQL_Latin1_General_CP1_CS_AS <> CONVERT(NVARCHAR(MAX), EP.value) COLLATE SQL_Latin1_General_CP1_CS_AS
                AND CONCAT(EPC.Description, '.') COLLATE SQL_Latin1_General_CP1_CS_AS <> CONVERT(NVARCHAR(MAX), EP.value) COLLATE SQL_Latin1_General_CP1_CS_AS
                AND CONVERT(NVARCHAR(MAX), EP.value) COLLATE SQL_Latin1_General_CP1_CS_AS NOT LIKE CONCAT(EPC.Description, '. %') COLLATE SQL_Latin1_General_CP1_CS_AS
                AND EP.name = 'Description'
                {(schemaName != null ? "AND S.name = '" + schemaName + "'" : "")}
        ;
        "
        ));
        output?.WriteLine(result);

        Assert.Equal(expected: "", actual: result);
    }

    /// <summary>
    /// Checks all the views in the sceham have a valid lineage block.
    /// </summary>
    /// <param name="schemaName"></param>
    protected void CheckViewsInSchemaHaveValidLineageBlock(string schemaName, bool onlyWhenHasLineageComment = false)
    {
        Dictionary<string, Dictionary<string, bool>> objectColumns = new Dictionary<string, Dictionary<string, bool>>();

        var views = GetResultRows(sql:
            @$"
                SELECT
                    ViewName = name
                  , object_id
                  , SqlForView = OBJECT_DEFINITION(object_id)
                FROM
                    sys.all_views
                WHERE
                    schema_id = SCHEMA_ID('{schemaName}') 
                    {(onlyWhenHasLineageComment ? "AND OBJECT_DEFINITION(object_id) LIKE '%/*%Lineage%*/%'" : "")}
                ORDER BY 
                    ViewName ASC;
            "
        );
        foreach(DataRow v in views.Rows)
        {
            string viewName = Convert.ToString(v["ViewName"])!;
            string viewSql = Convert.ToString(v["SqlForView"])!;
            // output?.WriteLine($"{viewName}");

            CheckForValidLineageComment(objectColumns, schemaName, viewName, viewSql);
        }
    }

    /// <summary>
    /// This code checks the lineage matches what the generated version says it should be.
    /// If it finds a difference, or no lineage it will also send the full lineage comment to output so it
    /// can be copied in.
    /// </summary>
    /// <param name="schemaName"></param>
    /// <param name="viewName">Optional stored proedure name if you want to target just a single one.</param>
    /// <param name="onlyWhenHasLineageComment">Can be used to only check when there is some sort of lineage comment</param>
    protected void CheckViewLineageBlockDefinition(string schemaName, string? viewName = null, bool onlyWhenHasLineageComment = false)
    {
        Assert.True(viewName == null || !viewName.Contains('\''));
        var views = GetResultRows(sql:
            @$"
                SELECT
                    ObjectName = name
                  , object_id
                  , SqlForObject = OBJECT_DEFINITION(object_id)
                FROM
                    sys.all_views
                WHERE
                    schema_id = SCHEMA_ID('{schemaName}')
                    {(viewName != null ? "AND name = '" + viewName + "'" : "")}
                    {(onlyWhenHasLineageComment ? "AND OBJECT_DEFINITION(object_id) LIKE '%/*%Lineage%*/%'" : "")}
                ORDER BY  
                    ObjectName ASC;
            "
        );
        CheckLineageForObjects(views);
    }

    /// <summary>
    /// Checks the lineage for stored procedures in the provided schema.
    /// </summary>
    /// <param name="schemaName"></param>
    /// <param name="storedProcedureName">Optional stored proedure name if you want to target just a single one.</param>
    /// <param name="onlyWhenHasLineageComment">Can be used to only check when there is some sort of lineage comment</param>
    protected void CheckStoredProcedureLineageBlockDefinition(string schemaName, string? storedProcedureName = null, bool onlyWhenHasLineageComment = false)
    {
        Assert.True(storedProcedureName == null || !storedProcedureName.Contains('\''));
        var sprocs = GetResultRows(sql:
            @$"
                SELECT
                    ObjectName = name
                  , object_id
                  , SqlForObject = OBJECT_DEFINITION(object_id)
                FROM
                    sys.procedures
                WHERE
                   schema_id = SCHEMA_ID('{schemaName}') 
                    {(storedProcedureName != null ? "AND name = '" + storedProcedureName + "'" : "")}
                    {(onlyWhenHasLineageComment ? "AND OBJECT_DEFINITION(object_id) LIKE '%/*%Lineage%*/%'" : "")}
                ORDER BY 
                    ObjectName ASC;
            "
         );
        CheckLineageForObjects(sprocs);
    }

    /// <summary>
    /// Can be given a DataTable of objects to check.
    /// </summary>
    /// <param name="objects"></param>
    private void CheckLineageForObjects(DataTable objects)
    {
        StringBuilder sb = new StringBuilder();

        foreach(DataRow v in objects.Rows)
        {
            string objectName = Convert.ToString(v["ObjectName"])!;
            string objectSql = Convert.ToString(v["SqlForObject"])!;
            // output.WriteLine($"{objectName}");

            CheckLineageForObject(sb, objectName, objectSql);
        }
        Assert.Equal(expected: "", actual: sb.ToString());
    }

    /// <summary>
    /// Checks the lineage for a single object.
    /// Returns a string containing information about any differences.
    /// Check it is empty to see if it worked.
    /// </summary>
    /// <param name="sb">A StringBuilder to hold the report in. If not empty at the end something is wrong.</param>
    /// <param name="objectName">The name of the object. Just for reporting</param>
    /// <param name="objectSql">The SQL to parse.</param>
    /// <param name="lineageCanBeEmpty">Might be some cases when no lineage is calculated and it is correct.</param>
    protected void CheckLineageForObject(StringBuilder sb, string objectName, string objectSql, bool lineageCanBeEmpty = false)
    {
        bool objectWrong = false;

        Assert.NotNull(sb);

        // Get the options for this object
        var lineageOptions = ExtractLineageOptionsFromObject(sql: objectSql);

        if(lineageOptions.Contains("NOSCAN"))
        {
            // Told not to scan this object
            // It might have lineage but this doesn't check it against what it thinks it should be.
            output?.WriteLine($"NOSCAN Lineage option for '{objectName}'.");
        }
        else
        {
            // This option stops adding lineage differences to the StringBuilder so it wont be seen as wrong.
            // It will just write the differences to output.
            // Errors in the scan can still fail.
            var noFail = lineageOptions.Contains("NOFAIL");

            try
            {
                // Result is a unique list of the lineage the parser thinks are needed.
                var calculatedLineage = LineageForSql.LineageForObject(sql: objectSql);
                if(!lineageCanBeEmpty)
                {
                    // If we don't allow empty lineage flag it up.
                    Assert.NotEmpty(calculatedLineage);
                }

                var actualLineage = ExtractLineageFromObject(objectName: objectName, sql: objectSql);

                if(actualLineage.IsNullOrEmpty())
                {
                    sb.Append($"'{objectName}' is missing lineage.\r\n");
                    objectWrong = true;
                }
                else
                {
                    // Check to see if the provide lineage has all the expected lines.
                    foreach(var calculatedLineageLine in calculatedLineage)
                    {
                        if(!actualLineage.Contains(calculatedLineageLine))
                        {
                            objectWrong = true;
                            output?.WriteLine($"'{objectName}' is missing the lineage '{calculatedLineageLine}'");
                            if(!noFail)
                            {
                                sb.Append($"'{objectName}' is missing the lineage '{calculatedLineageLine}'\r\n");
                            }
                        }
                    }
                    // Check to see if we have any extra lines in the provide lineage
                    foreach(var actualLineageLine in actualLineage)
                    {
                        if(!calculatedLineage.Contains(actualLineageLine))
                        {
                            objectWrong = true;
                            output?.WriteLine($"'{objectName}' has an unexpected lineage '{actualLineageLine}'");
                            if(!noFail)
                            {
                                sb.Append($"'{objectName}' has an unexpected lineage '{actualLineageLine}'\r\n");
                            }
                        }
                    }
                }
#if OutputLineageIfWrongOrEmpty
                // If the object is wrong we write out the expected lineage
                // This can be copied and used to save working it out.
                if(objectWrong && output != null)
                {
                    output.WriteLine(@"---------------------------------------------------------------------------------------");
                    output.WriteLine($"The Lineage comment wasn't as expected for '{objectName}'. This is the generated lineage:");
                    output.WriteLine(@"/*");
                    output.WriteLine(@"Lineage");
                    foreach(var calculatedLineageLine in calculatedLineage)
                    {
                        output.WriteLine(calculatedLineageLine);
                    }
                    output.WriteLine(@"*/");
                    output.WriteLine(@"---------------------------------------------------------------------------------------");
                    output.WriteLine(@"");
                }
#endif // OutputLineageIfWrongOrEmpty
#if FixLineageIfWrongOrEmpty
                /* An advanced feature. Should only be used if you know what you are doing */
                /* This will try and find the file and replace the lineage block in it!    */
                /* This can be conditionally compiled in                                   */
                /* But should not be enabled in code that is pushed!                       */
                if(objectWrong)
                {
                    output?.WriteLine($"The Lineage comment wasn't as expected for '{objectName}'. Attempting to update it!");
                    var m = Regex.Match(input: objectSql, pattern: @"CREATE\s+(VIEW|PROCEDURE)\s+(\w+)\.(\w+)", options: RegexOptions.IgnoreCase | RegexOptions.Singleline);
                    if (m.Success)
                    {
                        
                        var filePath = FindPathToFile(fileType: m.Groups[1].Value, schemaName: m.Groups[2].Value, objectName: m.Groups[3].Value);
                        output?.WriteLine($"File path: {filePath} Exists:{File.Exists(filePath)}");
                        if(File.Exists(filePath))
                        {
                            var oldContent = File.ReadAllText(filePath);
                            var newLineage = new StringBuilder();
                            newLineage.AppendLine("/*");
                            newLineage.AppendLine("Lineage");
                            foreach(var calculatedLineageLine in calculatedLineage)
                            {
                                newLineage.AppendLine(calculatedLineageLine);
                            }
                            newLineage.AppendLine("*/");
                            var newContent = Regex.Replace(input: oldContent, pattern: @"^(/\*\s*\nLineage\s*\n.+?\*/\s*\n\s*)?", replacement: newLineage.ToString(), options: RegexOptions.Singleline);
                            if(oldContent != newContent)
                            {
                                output?.WriteLine($"Updating Lineage in: {filePath}");
                                File.WriteAllText(path: filePath, contents: newContent);
                            }
                        }
                        else
                        {
                            output?.WriteLine($"There isn't a file to update at: {filePath}!");
                        }
                    }
                }
#endif // FixLineageIfWrongOrEmpty
            }
            catch(Exception ex)
            {
                // Expect this to catch errors in the parsing process.
                // That way we can try to process other objects
                output?.WriteLine($"'{objectName}' was not able to be parsed. Error: '{ex.Message}'.");
                output?.WriteLine(ex.StackTrace);
                if(!noFail)
                {
                    sb.Append($"'{objectName}' was not able to be parsed. Error: '{ex.Message}'.\r\n");
                }
            }
        }
    }



    /// <summary>
    /// This test is here to ensure that we don't create Id columns as ID. The standard is Id.
    /// If we are exposing them they should be exposed as they currently are.
    /// Staging tables should match the source. Other local-only tests could check those.
    /// Inconsistency causes problems with SQL Prompt.
    /// Task (PRP) schema is ignored.
    /// </summary>
    protected void CheckCaseOfIdentityColumns()
    {
        var table = GetResultRows(sql:
            @"
                SELECT
                    TableName = CONCAT(SCHEMA_NAME(t.schema_id), '.', t.name)
                  , ActualName = c.name
                  , ExpectedName = SUBSTRING(c.name, 1, LEN(c.name)-2) + 'Id'
                FROM
                    sys.tables t
                    INNER JOIN sys.columns c
                        ON c.object_id = t.object_id
                WHERE
                    c.name LIKE '%id'
                    AND c.name COLLATE SQL_Latin1_General_CP1_CS_AS NOT LIKE 'Id'
                    AND c.name COLLATE SQL_Latin1_General_CP1_CS_AS NOT LIKE '%[0-9a-z_]Id'
                    AND SCHEMA_NAME(t.schema_id) NOT LIKE '%Staging'
                    AND SCHEMA_NAME(t.schema_id) <> 'FMATemp'
                    AND SCHEMA_NAME(t.schema_id) <> 'Task'
                    AND c.name COLLATE SQL_Latin1_General_CP1_CS_AS <> 'SPID'
                    AND c.name COLLATE SQL_Latin1_General_CP1_CS_AS <> 'SID'
                    AND c.name COLLATE SQL_Latin1_General_CP1_CS_AS <> 'ObjectSID'
                    AND c.name COLLATE SQL_Latin1_General_CP1_CS_AS <> 'eGlobalIBO_RegistrationID'
                    AND c.name COLLATE SQL_Latin1_General_CP1_CS_AS <> 'IsInvalid'
                    AND c.name COLLATE SQL_Latin1_General_CP1_CS_AS <> 'IsVisibleInGrid'
                    AND c.name COLLATE SQL_Latin1_General_CP1_CS_AS <> 'IsValid'
                    AND c.name COLLATE SQL_Latin1_General_CP1_CS_AS NOT LIKE '%Guid'
                    AND c.name COLLATE SQL_Latin1_General_CP1_CS_AS NOT LIKE '%GUID'
                ORDER BY TableName;
            "
);
        foreach(DataRow row in table.Rows)
        {
            output?.WriteLine($"'{row["TableName"]}': Expecting '{row["ExpectedName"]}' got '{row["ActualName"]}'.");
        }
        Assert.Empty(table.Rows);
    }

    /// <summary>
    /// Check the foreign keys have consistent names.
    /// Optionally you can update the ones that are wrong if there are more to change than you can cope with.
    /// we are allowed to use PRIMARY KEY on the column in which case the system will define the name.
    /// We only care when it is given a name.
    /// </summary>
    protected void CheckPrimaryKeys()
    {
        var table = GetResultRows(sql:
            @"
                SELECT
                    TableName = T.name
                  , SchemaName = SCHEMA_NAME(T.schema_id)
                  , FullTableName = CONCAT(SCHEMA_NAME(T.schema_id), '.', T.name)
                  , ActualName = IX.name
                  , ExpectedName = SUBSTRING(
                                        CONCAT('PK_', SCHEMA_NAME(T.schema_id), '_', T.name)
                                        , 0
                                        , 128
                                    )
                  , KC.is_system_named
                FROM
                    sys.tables T
                    INNER JOIN sys.indexes IX
                        ON IX.object_id = T.object_id AND IX.is_primary_key = 1
                    INNER JOIN sys.key_constraints KC 
                        ON KC.parent_object_id = IX.object_id 
                            AND KC.type = 'PK'
                WHERE
                    IX.name COLLATE SQL_Latin1_General_CP1_CS_AS <> CONCAT('PK_', SCHEMA_NAME(T.schema_id), '_', T.name) COLLATE SQL_Latin1_General_CP1_CS_AS
                    AND KC.is_system_named = 0
                ORDER BY TableName ASC;
            "
        );
        foreach(DataRow row in table.Rows)
        {
            output?.WriteLine($"'{row["FullTableName"]}': Expecting '{row["ExpectedName"]}' got '{row["ActualName"]}'.");
        }

#if FixKeyAndConstraintNames
        output?.WriteLine("Attempting to update the incorrect primary key names in the solution...");

        foreach(DataRow row in table.Rows)
        {
            var pathToFile = FindPathToFile(fileType: "table", schemaName: Convert.ToString(row["SchemaName"])!, objectName: Convert.ToString(row["TableName"])!);

            var oldPKName = Convert.ToString(row["ActualName"])!;
            var newPKName = Convert.ToString(row["ExpectedName"])!;

            if(ChangeProjectFile(filePath: pathToFile, oldValue: oldPKName, newValue: newPKName) == 0)
            {
                // No change made. Why?
                output?.WriteLine($"WARNING: {pathToFile} was unable to be updated replacing '{oldPKName}' with '{newPKName}'. Is the primary key defined in this file? Maybe it is defined on the column line.");
            }
        }
#endif //FixKeyAndConstraintNames

        Assert.Empty(table.Rows);
    }

    /// <summary>
    /// Check the Foreign keys.
    /// Excludes the dbo schema as that is supposed to be going away.
    /// Expecting FK_{source schema}_{source table}_{target schema}_{target table}
    /// Or if there are duplicate entries to the same foreign table:
    /// FK_{source schema}_{source table}_{source column}_{target schema}_{target table}
    /// </summary>
    protected void CheckForeignKeyNames()
    {
        var table = GetResultRows(sql:
            @"
                SELECT
                    fkeys3.TableName
                  , fkeys3.SchemaName
                  , fkeys3.FullTableName
                  , fkeys3.ActualName
                  , fkeys3.ExpectedName
                FROM (
                    SELECT
                        TableName = OBJECT_NAME(fkeys2.parent_object_id)
                      , SchemaName = OBJECT_SCHEMA_NAME(fkeys2.parent_object_id)
                      , FullTableName = CONCAT(OBJECT_SCHEMA_NAME(fkeys2.parent_object_id), '.', OBJECT_NAME(fkeys2.parent_object_id))
                      , ExpectedName = SUBSTRING(
                                            CONCAT(
                                               'FK_'
                                             , fkeys2.ForeignKeyFrom
                                             , '_'
                                             , CASE WHEN fkeys2.Dupes > 1
                                                        THEN CONCAT(fkeys2.ForeignKeyFromColumnName, '_') END
                                             , fkeys2.ForeignKeyTo
                                           )
                                         , 0
                                         , 128
                                       )

                      , ActualName = fkeys2.ActualName
                    FROM (
                        SELECT
                            fkeys.ForeignKeyFrom
                          , fkeys.ForeignKeyTo
                          , ActualName = fkeys.name
                          , Dupes = COUNT(*) OVER (PARTITION BY fkeys.ForeignKeyFrom, fkeys.ForeignKeyTo)
                          , ForeignKeyFromColumnName = cols.Names
                          , fkeys.parent_object_id
                        FROM (
                            SELECT
                                fk.object_id
                              , fk.parent_object_id
                              , fk.referenced_object_id
                              , fk.name
                              , ForeignKeyFrom = CONCAT(OBJECT_SCHEMA_NAME(fk.parent_object_id), '_', OBJECT_NAME(fk.parent_object_id))
                              , ForeignKeyTo = CONCAT(
                                                   OBJECT_SCHEMA_NAME(fk.referenced_object_id)
                                                 , '_'
                                                 , OBJECT_NAME(fk.referenced_object_id)
                                               )
                            FROM
                                sys.foreign_keys fk
                            -- WHERE
                            --    OBJECT_SCHEMA_NAME(fk.parent_object_id) <> 'dbo' 
                        ) fkeys
                             INNER JOIN (
                                 SELECT
                                     fkc.constraint_object_id
                                   , fkc.parent_object_id
                                   , fkc.referenced_object_id
                                   , Names = STRING_AGG(c.name, '')
                                 FROM
                                     sys.foreign_key_columns fkc
                                     LEFT JOIN sys.columns c
                                         ON c.column_id = fkc.parent_column_id
                                            AND c.object_id = fkc.parent_object_id
                                 GROUP BY
                                     fkc.constraint_object_id
                                   , fkc.parent_object_id
                                   , fkc.referenced_object_id
                             ) cols
                                 ON cols.parent_object_id = fkeys.parent_object_id
                                    AND cols.constraint_object_id = fkeys.object_id
                                    AND cols.referenced_object_id = fkeys.referenced_object_id
                    ) fkeys2
                ) fkeys3
                WHERE
                    fkeys3.ExpectedName COLLATE SQL_Latin1_General_CP1_CS_AS <> fkeys3.ActualName COLLATE SQL_Latin1_General_CP1_CS_AS
                ORDER BY
                    fkeys3.FullTableName;
            "
        );
        foreach(DataRow row in table.Rows)
        {
            output?.WriteLine($"'{row["FullTableName"]}': Expecting '{row["ExpectedName"]}' got '{row["ActualName"]}'.");
        }

#if FixKeyAndConstraintNames
        output?.WriteLine("Attempting to update the incorrect foreign key names in the solution...");

        foreach(DataRow row in table.Rows)
        {
            var pathToFile = FindPathToFile(fileType: "table", schemaName: Convert.ToString(row["SchemaName"])!, objectName: Convert.ToString(row["TableName"])!);

            var oldFKName = Convert.ToString(row["ActualName"])!;
            var newFKName = Convert.ToString(row["ExpectedName"])!;

            if(ChangeProjectFile(filePath: pathToFile, oldValue: oldFKName, newValue: newFKName) == 0)
            {
                // No change made. Why?
                output?.WriteLine($"WARNING: {pathToFile} was unable to be updated replacing '{oldFKName}' with '{newFKName}'. Is the foreign key defined in this file?");
            }
        }
#endif //FixKeyAndConstraintNames

        Assert.Empty(table.Rows);
    }

    /// <summary>
    /// Checks the index names match the standards.
    /// And even if not exactly to the standard at least we are consistent.
    /// </summary>
    protected void CheckIndexNames()
    {
        var table = GetResultRows(sql:
            @"
                SELECT
                    indexes.FullTableName
                  , indexes.ActualName
                  , indexes.ExpectedName
                  , indexes.TableName
                  , indexes.SchemaName
                FROM (
                    SELECT
                        TableName = t.name
                      , SchemaName = SCHEMA_NAME(t.schema_id)
                      , FullTableName = CONCAT(SCHEMA_NAME(t.schema_id), '.', t.name)
                      , ActualName = ix.name
                      , ExpectedName = SUBSTRING(
                                           CONCAT(
                                               'IX'
                                             , CASE WHEN ix.is_unique = 1
                                                        THEN 'U' END
                                             , '_'
                                             , SCHEMA_NAME(t.schema_id)
                                             , '_'
                                             , t.name
                                             , '_'
                                             , STRING_AGG(c.name, '') WITHIN GROUP(ORDER BY
                                                                                       ixc.key_ordinal ASC)
                                           )
                                         , 0
                                         , 128
                                       )
                    FROM
                        sys.tables t
                        INNER JOIN sys.indexes ix
                            ON ix.object_id = t.object_id

                        INNER JOIN sys.index_columns ixc
                            ON ixc.object_id = ix.object_id
                               AND ixc.index_id = ix.index_id
                               AND ixc.is_included_column = 0

                        INNER JOIN sys.columns c
                            ON c.object_id = ix.object_id
                               AND c.column_id = ixc.column_id
                    WHERE
                        ix.is_primary_key = 0
                        AND ix.is_unique_constraint = 0 /* To stop arguing with the constraint check. */
                    GROUP BY
                        t.schema_id
                      , t.name
                      , ix.name
                      , ix.is_unique
                ) indexes
                WHERE
                    indexes.ActualName COLLATE SQL_Latin1_General_CP1_CS_AS <> indexes.ExpectedName COLLATE SQL_Latin1_General_CP1_CS_AS;
            "
        );
        foreach(DataRow row in table.Rows)
        {
            output?.WriteLine($"'{row["FullTableName"]}': Expecting '{row["ExpectedName"]}' got '{row["ActualName"]}'.");
        }

#if FixKeyAndConstraintNames
        output?.WriteLine("Attempting to update the incorrect index names in the solution...");

        foreach(DataRow row in table.Rows)
        {
            var pathToFile = FindPathToFile(fileType: "table", schemaName: Convert.ToString(row["SchemaName"])!, objectName: Convert.ToString(row["TableName"])!);

            var oldIndexName = Convert.ToString(row["ActualName"])!;
            var newIndexName = Convert.ToString(row["ExpectedName"])!;

            if (ChangeProjectFile(filePath: pathToFile, oldValue: oldIndexName, newValue: newIndexName) == 0)
            {
                // No change made. Why?
                output?.WriteLine($"WARNING: {pathToFile} was unable to be updated replacing '{oldIndexName}' with '{newIndexName}'. Is the index defined in this file?");
            }
        }
#endif //FixKeyAndConstraintNames

            Assert.Empty(table.Rows);
    }

    /// <summary>
    /// There are other constraints that could have names.
    /// Any that have not been system named can be checked here so they can 
    /// at least all be consistent.
    /// </summary>
    protected void CheckOtherConstraintNames()
    {
        var table = GetResultRows(sql:
            @"
                SELECT
                    constraints.TableName
                  , constraints.SchemaName
                  , constraints.FullTableName
                  , constraints.ActualName
                  , constraints.ExpectedName
                FROM(
                    SELECT
                        TableName = OBJECT_NAME(kc.parent_object_id)
                      , SchemaName = OBJECT_SCHEMA_NAME(kc.parent_object_id)
                      , FullTableName = CONCAT(OBJECT_SCHEMA_NAME(kc.parent_object_id), '.', OBJECT_NAME(kc.parent_object_id))
                      , ActualName = kc.name
                      , kc.type
                      , kc.parent_object_id
                      , ExpectedName = CONCAT(
                                           kc.type
                                         , '_'
                                         , OBJECT_SCHEMA_NAME(kc.parent_object_id)
                                         , '_'
                                         , OBJECT_NAME(kc.parent_object_id)
                                         , '_'
                                         , columns.Columns
                                       )
                    FROM
                        sys.key_constraints kc
                        INNER JOIN(
                            SELECT
                                ix.object_id
                              , Columns = STRING_AGG(cols.name, '') COLLATE SQL_Latin1_General_CP1_CI_AS
                            FROM
                                sys.indexes ix
                                INNER JOIN sys.index_columns ixc
                                    ON ixc.index_id = ix.index_id
                                       AND ixc.object_id = ix.object_id

                                INNER JOIN sys.columns cols
                                    ON cols.column_id = ixc.column_id
                                       AND cols.object_id = ixc.object_id
                            GROUP BY
                                ix.object_id
                        ) columns
                            ON columns.object_id = kc.parent_object_id
                    WHERE
                        kc.type NOT IN(
                            'PK'
                        )
                        AND kc.is_system_named = 0
                    UNION ALL
                    SELECT
                        TableName = OBJECT_NAME(cc.parent_object_id)
                      , SchemaName = OBJECT_SCHEMA_NAME(cc.parent_object_id)
                      , FullTableName = CONCAT(OBJECT_SCHEMA_NAME(cc.parent_object_id), '.', OBJECT_NAME(cc.parent_object_id))
                      , ActualName = cc.name
                      , cc.type
                      , cc.parent_object_id
                      , ExpectedName = CONCAT(
                                           'CK_'
                                         , OBJECT_SCHEMA_NAME(cc.parent_object_id)
                                         , '_'
                                         , OBJECT_NAME(cc.parent_object_id)
                                         , '_'
                                         , cols.name
                                       )
                    FROM
                        sys.check_constraints cc
                        INNER JOIN sys.columns cols
                            ON cols.object_id = cc.parent_object_id
                               AND cols.column_id = cc.parent_column_id
                    WHERE
                        cc.is_system_named = 0
                    UNION ALL
                    SELECT
                        TableName = OBJECT_NAME(dc.parent_object_id)
                      , SchemaName = OBJECT_SCHEMA_NAME(dc.parent_object_id)
                      , FullTableName = CONCAT(OBJECT_SCHEMA_NAME(dc.parent_object_id), '.', OBJECT_NAME(dc.parent_object_id))
                      , ActualName = dc.name
                      , dc.type
                      , dc.parent_object_id
                      , ExpectedName = CONCAT(
                                           'DF_'
                                         , OBJECT_SCHEMA_NAME(dc.parent_object_id)
                                         , '_'
                                         , OBJECT_NAME(dc.parent_object_id)
                                         , '_'
                                         , cols.name
                                       )
                    FROM
                        sys.default_constraints dc
                        INNER JOIN sys.columns cols
                            ON cols.object_id = dc.parent_object_id
                               AND cols.column_id = dc.parent_column_id
                    WHERE
                        dc.is_system_named = 0
                ) constraints
                WHERE
                    constraints.ActualName COLLATE SQL_Latin1_General_CP1_CS_AS<> constraints.ExpectedName COLLATE SQL_Latin1_General_CP1_CS_AS;
            "
        );
        foreach(DataRow row in table.Rows)
        {
            output?.WriteLine($"'{row["FullTableName"]}': Expecting '{row["ExpectedName"]}' got '{row["ActualName"]}'.");
        }

#if FixKeyAndConstraintNames
        output?.WriteLine("Attempting to update the incorrect constraint names in the solution...");

        foreach(DataRow row in table.Rows)
        {
            var pathToFile = FindPathToFile(fileType: "table", schemaName: Convert.ToString(row["SchemaName"])!, objectName: Convert.ToString(row["TableName"])!);

            var oldConstraintName = Convert.ToString(row["ActualName"])!;
            var newConstraintName = Convert.ToString(row["ExpectedName"])!;

            if(ChangeProjectFile(filePath: pathToFile, oldValue: oldConstraintName, newValue: newConstraintName) == 0)
            {
                // No change made. Why?
                output?.WriteLine($"WARNING: {pathToFile} was unable to be updated replacing '{oldConstraintName}' with '{newConstraintName}'. Is the constraint defined in this file?");
            }
        }
#endif //FixKeyAndConstraintNames

        Assert.Empty(table.Rows);
    }

    /// <summary>
    /// This method tries to establish which tables are being used by a stored procedure.
    /// It includes any foreign keys for the table being updated.
    /// It assumes only one target table. It will fail if this is not true.
    /// To find the dependencies there are assumptions being made on the names of stored procedures.
    /// It doesn't flag up extra dependencies.
    /// It doesn't currently explode views to find all the tables in those this should be dependent on. 
    /// That was not needed here.
    /// </summary>
    /// <param name="storedProcedureName"></param>
    protected void CheckToSeeIfInterdependenciesExistForStoredProcedure(string storedProcedureName, StringBuilder? sb = null, IList<string>? excludedTables = null)
    {
        output?.WriteLine($"Checking interdependencies for stored procedure: '{storedProcedureName}'.");

        var stringBuilderProvided = (sb != null);
        if(sb == null)
        {
            sb = new StringBuilder();
        }

        string targetTableFromConvention = string.Empty;
        string updatedTable = string.Empty;

        var m = Regex.Match(input: storedProcedureName, pattern: @"^\w+\.Load(?:_(\w+)_)?(\w+)$");
        if (m.Success)
        {
            targetTableFromConvention = $"{(m.Groups[1].Value == "" ? "dbo" : m.Groups[1].Value)}.{m.Groups[2].Value}";
        }

        var inputAndOutputTables = GetResultRows(sql: $@"
                SELECT DISTINCT
                       name = CONCAT(referenced_schema_name, '.', referenced_entity_name)
                     , is_updated
                     , is_selected
                FROM
                    sys.dm_sql_referenced_entities('{storedProcedureName}', 'OBJECT')
                WHERE
                    NOT (
                        referenced_schema_name = 'ADF'
                        AND referenced_entity_name IN (
                                'StoredProcEndLog', 'StoredProcErrorLog', 'StoredProcSetSqlLog', 'StoredProcStartLog'
                            )
                    )
                    AND referenced_id IS NOT NULL
        ");
        IEnumerable<string> updatedTables = inputAndOutputTables.Select(filterExpression: "is_updated = 1 AND is_selected = 0").Select((x) => $"{x["name"]}").Distinct();
        IEnumerable<string> readObjects = inputAndOutputTables.Select(filterExpression: "is_updated = 0 AND is_selected = 1").Select((x) => $"{x["name"]}").Distinct().Except(updatedTables);

        if (readObjects.Count() == 0)
        {
            // If no rows were matched then just get them all.
            readObjects = inputAndOutputTables.Select(filterExpression: "is_updated = 0").Select((x) => $"{x["name"]}").Distinct().Except(updatedTables);
        }

        if (updatedTables.Count() == 0)
        {
            if (targetTableFromConvention == string.Empty)
            {
                // Can't use convention to guess the target.
                sb.AppendLine($"WARNING: Could not derive target table, and not available by naming convention for '{storedProcedureName}'!");
                return;
            }
            else
            {
                updatedTable = targetTableFromConvention;
            }
        }
        else if (updatedTables.Count() == 1)
        {
            if(targetTableFromConvention != string.Empty)
            {
                // Check convention actually works.
                if(!targetTableFromConvention.Equals(value: updatedTables.First(), comparisonType: StringComparison.CurrentCultureIgnoreCase))
                {
                    sb.AppendLine($"WARNING: The target table '{updatedTables.First()}' did not match the name expected by convention '{targetTableFromConvention}' for '{storedProcedureName}'!");
                    return;
                }
            }

            updatedTable = updatedTables.First();
        }
        else
        {
            sb.AppendLine($"WARNING: Found more than one table that was being updated for '{storedProcedureName}' - {String.Join(',', updatedTables)}! We don't support that.");
            return;
        }

        // Need to see if in addition to the source tables there are any foreign key dependencies we need to consider.
        var tableInfo = GetTableInfo(tableName: updatedTable);
        Assert.NotNull(tableInfo);

        var dependentOn = new List<string>();
        foreach(var readObject in readObjects)
        {
            if(readObject != updatedTable)
            {
                var explodedView = ExplodeView(possibleViewName: readObject);
                dependentOn.AddRange(explodedView);
            }
        }
        foreach(var fk in tableInfo.ForeignKeys)
        {
            if(!dependentOn.Contains(fk.ReferencedTableName))
            {
                dependentOn.Add(fk.ReferencedTableName);
            }
        }

        // Can't depend on the target table.
        if (dependentOn.Contains(item: updatedTable))
        {
            dependentOn.Remove(item: updatedTable);
        }

        // We might need to selectively exclude some other tables if they are scripted in...
        // Or for some other reason.
        if(excludedTables != null)
        {
            foreach(var excludedTable in excludedTables)
            {
                if(dependentOn.Contains(item: excludedTable))
                {
                    dependentOn.Remove(item: excludedTable);
                }
                else
                {
                    output?.WriteLine($"WARNING: Count not find table '{excludedTable}' in the tables for stored procedure '{storedProcedureName}'");
                }
            }
        }

        // See what dependencies are defined for this.
        var processInterdependencies = GetResultRows(sql: @$"
            SELECT 
                  pstage.Name
                , pstage.ProcessTypeId
                , StoredProcName = JSON_VALUE(pstage.JSONConfig, '$.StoredProcedure')
                , TableName = JSON_VALUE(pstage.JSONConfig, '$.TargetTable')
            FROM
                ADF.ProcessInterdependency pind
                INNER JOIN ADF.Process pstage
                    ON pstage.ProcessId = pind.PrerequisiteProcessId

                INNER JOIN ADF.Process pload
                    ON pload.ProcessId = pind.ProcessId
            WHERE
                pload.Name = '{storedProcedureName}'
                AND pind.IsDeleted = 0
                AND pload.IsDeleted = 0
                AND pstage.IsDeleted = 0;
            ");

        // Hope to get at least one otherwise something must be wrong.
        // Unless it's not a stored procedure. But if not why are you checking it?
        Assert.NotEmpty(processInterdependencies.Rows);
        var dependenciesChecked = 0;
        var dependenciesWrong = 0;
        foreach(var table in dependentOn)
        {
            dependenciesChecked++;
            var found = false;
            var tableSchema = table.Split(".")[0];
            var tableName = table.Split(".")[1];
            // Need to make some assumptions on what we are looking for.
            foreach(DataRow row in processInterdependencies.Rows)
            {
                var piTableName = Convert.ToString(row["TableName"]);
                var piStoredProcedureName = Convert.ToString(row["StoredProcName"]);

                // Most of the naming conventions being used.
                if(piTableName.Equals(value: table, comparisonType: StringComparison.CurrentCultureIgnoreCase))
                {
                    found = true;
                    break;
                }

                if (piStoredProcedureName.EndsWith(value: $".Load_{tableSchema}_{tableName}", comparisonType: StringComparison.CurrentCultureIgnoreCase))
                {
                    found = true;
                    break;
                }

                if((tableSchema == "dbo" || tableSchema == "ref") && piStoredProcedureName.EndsWith(value: $".Load{tableName}", comparisonType: StringComparison.CurrentCultureIgnoreCase))
                {
                    found = true;
                    break;
                }

                if((tableSchema == "dbo" || tableSchema == "ref") && piStoredProcedureName.EndsWith(value: $".Merge{tableName}IntoProgrammeStore", comparisonType: StringComparison.CurrentCultureIgnoreCase))
                {
                    found = true;
                    break;
                }

                if(tableSchema == "Reference" && piStoredProcedureName.EndsWith(value: $".MergeInto{tableName}", comparisonType: StringComparison.CurrentCultureIgnoreCase))
                {
                    found = true;
                    break;
                }
            }

            if(!found)
            {
                dependenciesWrong++;
                sb.AppendLine($"WARNING: Did not find a dependency for table '{table}' in '{storedProcedureName}'.");
                output?.WriteLine($"WARNING: Did not find a dependency for table '{table}' in '{storedProcedureName}'. It has: {String.Join(',', processInterdependencies.Select(filterExpression: "StoredProcName IS NOT NULL").Concat(processInterdependencies.Select(filterExpression: "TableName IS NOT NULL")).Select((x) => x["TableName"] == DBNull.Value ? x["StoredProcName"] : x["TableName"]))}");
            }
        }

        output?.WriteLine($"Dependency check for '{storedProcedureName}' {(dependenciesWrong > 0 ? "failed" : "passed")}. Checked: {dependenciesChecked}. Missing: {dependenciesWrong}. Dependencies defined: {processInterdependencies.Rows.Count}.");

        // and we want the results.
        if(!stringBuilderProvided)
        {
            output?.WriteLine(sb.ToString());
            Assert.Equal(expected: "", actual: sb.ToString());
        }
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="schemaName"></param>
    /// <param name="storedProcedureName"></param>
    protected void CheckStoredProcedureDependencies(string schemaName, string? storedProcedureName = null)
    {
        Assert.True(storedProcedureName == null || !storedProcedureName.Contains('\''));
        var sprocs = GetResultRows(sql:
            @$"
                SELECT
                    ObjectName = CONCAT(SCHEMA_NAME(schema_id), '.', name)
                  , object_id
                FROM
                    sys.procedures
                WHERE
                   schema_id = SCHEMA_ID('{schemaName}') 
                    {(storedProcedureName != null ? "AND name = '" + storedProcedureName + "'" : "")}
                ORDER BY 
                    ObjectName ASC;
            "
         );

        var sb = new StringBuilder();
        
        foreach(DataRow r in sprocs.Rows)
        {
            CheckToSeeIfInterdependenciesExistForStoredProcedure(storedProcedureName: Convert.ToString(r["ObjectName"])!, sb: sb);
        }

        output.WriteLine(sb.ToString());
        Assert.Equal(expected: "", actual: sb.ToString());
    }

    /// <summary>
    /// Compute a the file to the path for the database object.
    /// We should have standard names.
    /// </summary>
    /// <param name="fileType"></param>
    /// <param name="schemaName"></param>
    /// <param name="objectName"></param>
    /// <returns></returns>
    protected string FindPathToFile(string fileType, string schemaName, string objectName)
    {
        string filePath = string.Empty;

        // Use the test folder as a reference.
        if (Directory.GetCurrentDirectory().IndexOf(@"\test\") < 0)
        {
            // If we can't find test in the file path then this is probably the PR build and this won't work so just return the empty string.
            output?.WriteLine("FindPathToFile cannot find the test folder. Assuming this is the PR build and returning empty string.");
            return filePath;
        }

        var path = Directory.GetCurrentDirectory().Substring(startIndex: 0, length: Directory.GetCurrentDirectory().IndexOf(@"\test\"));

        if(fileType.ToLower() == "view")
        {
            // View
            filePath = Path.Combine(path, @"src\PlacementStore", schemaName, "Views", objectName + ".sql");
        }
        else if(fileType.ToLower() == "table")
        {
            // View
            filePath = Path.Combine(path, @"src\PlacementStore", schemaName, "Tables", objectName + ".sql");
        }
        else if(fileType.ToLower() == "procedure")
        {
            // Stored procedure. Our naming isn't consistent.
            filePath = Path.Combine(path, @"src\PlacementStore", schemaName, "Stored Procedures", objectName + ".sql");
            if(!File.Exists(filePath))
            {
                // A variation
                filePath = Path.Combine(path, @"src\PlacementStore", schemaName, "StoredProcedure", objectName + ".sql");
            }
            if(!File.Exists(filePath))
            {
                // A variation
                filePath = Path.Combine(path, @"src\PlacementStore", schemaName, "StoredProcedures", objectName + ".sql");
            }
        }
        else if(fileType.ToLower() == "script")
        {
            filePath = Path.Combine(path, @"src\PlacementStore\Scripts\reference", schemaName, objectName + ".sql");
        }
        else
        {
            throw new ApplicationException($"The file type '{fileType}' is not handled here!");
        }

        return filePath;
    }

    #region private methods

    /// <summary>
    /// Assumes a name is a view.
    /// Tries to expand it. If it can it then checks each one of those to see if it is a view.
    /// If it can't expand it it is just returned as it must be a table.
    /// </summary>
    /// <param name="possibleViewName"></param>
    /// <returns></returns>
    private IList<string> ExplodeView(string possibleViewName)
    {
        var tables = new List<string>();
        var explodedView = GetResultRows(sql: $@"
                        SELECT DISTINCT
                               name = CONCAT(referenced_schema_name, '.', referenced_entity_name)
                        FROM
                            sys.dm_sql_referenced_entities('{possibleViewName}', 'OBJECT')
                ");
        if(explodedView.Rows.Count > 0)
        {
            foreach(DataRow row in explodedView.Rows)
            {
                // Don't add if the view references itself.
                if(!Convert.ToString(row["name"]).Equals(value: possibleViewName, comparisonType: StringComparison.CurrentCultureIgnoreCase))
                {
                    tables.AddRange(ExplodeView(possibleViewName: Convert.ToString(row["name"])!));
                }
            }
        }
        else
        {
            tables.Add(possibleViewName);
        }
        return tables;
    }



    /// <summary>
    /// Changes the file replacing the oldValue with newValue.
    /// Returns 0 if no change made - which is probably wrong.
    /// Returns 1 if a change was made.
    /// Only allowed to make a single change for the usage here.
    /// The file must exist.
    /// The values must not contain any stored procedure escape sequences.
    /// oldValue is checked as a full word.
    /// </summary>
    /// <param name="filePath"></param>
    /// <param name="oldValue"></param>
    /// <param name="newValue"></param>
    /// <returns></returns>
    private int ChangeProjectFile(string filePath, string oldValue, string newValue)
    {
        Assert.True(File.Exists(filePath), $"Couldn't find file: {filePath}");
        Assert.True(oldValue != newValue, $"The oldValue and newValue are the same: '{oldValue}' '{newValue}'!");

        var originalFileContent = File.ReadAllText(path: filePath);
        // Before we replace check that the string only exists zero or once.

        Assert.True(originalFileContent.CountOccurrences(oldValue) <= 1, $"The string '{oldValue}' occurred more than once in file '{filePath}'. That is not expected here.");

        var newFileContent = Regex.Replace(input: originalFileContent, pattern: $@"(\s){oldValue}(\s)", replacement: $"$1{newValue}$2");
        if(originalFileContent != newFileContent)
        {
            // Provided the file has changed write it out.
            File.WriteAllText(path: filePath, contents: newFileContent);
            output?.WriteLine($"{filePath} was updated replacing '{oldValue}' with '{newValue}'.");
            return 1;
        }
        return 0;
    }

    #endregion

    #region Constructor
    public PlacementStoreTestBase(DatabaseFixture fixture, ITestOutputHelper? output = null) : base(fixture, output)
    {
        this.output = output;
    }
    #endregion
}