﻿using Azure;
using Microsoft.SqlServer.Server;
using PsDb.Tests.PlacementStoreHelpers;
using System.Diagnostics.CodeAnalysis;

namespace PsDb.Tests.Rules
{
    [ExcludeFromCodeCoverage]

    public class ClearSegmentationRulesTest : PlacementStoreRulesTestBase
    {
        [Fact]
        public void ClearSegmentationNoPoliciesRules()
        {
            // Call the stored procedure
            ExecuteStoredProcedureWithoutResult(storedProcedureName: "Rules.ClearSegmentationRules", values: new { @ProcessSessionId = 101});

            string dayOfWeek = DateTime.Now.DayOfWeek.ToString();

            // See if a record exists in dbo.PlacementListener
            dynamic row = GetResultRow(tableName: "ADF.StoredProcLog", whereClause: $"StoredProcName LIKE 'Rules.ClearSegmentationRules%'");
            Assert.NotNull(row);
            Assert.Equal(expected: $"Run on {dayOfWeek} or Override: 0" , actual: row.ActionDescription);
            Assert.Equal(expected: "Rules.ClearSegmentationRules(101,0)", actual: row.StoredProcName);
        }

        [Fact]
        public void ClearSegmentationOverrideRules()
        {

            ExecuteStoredProcedureWithoutResult(storedProcedureName: "PactConfig.SetControlValue_Bit", values: new
            {
                @ItemName = @"ClearRules_Override",
                @Value = true,
            });

            dynamic policyRecord = CreateRow(tableName: "dbo.Policy", values: new { 
                RuleId = 10,
                IsDeleted = false
            });

            // Call the stored procedure
            ExecuteStoredProcedureWithoutResult(storedProcedureName: "Rules.ClearSegmentationRules", values: new { @ProcessSessionId = 101 });

            string dayOfWeek = DateTime.Now.DayOfWeek.ToString();

            // See if a record exists in dbo.PlacementListener
            dynamic row = GetResultRow(tableName: "ADF.StoredProcLog", whereClause: $"StoredProcName LIKE 'Rules.ClearSegmentationRules%'");
            Assert.NotNull(row);
            Assert.Equal(expected: $"Run on {dayOfWeek} or Override: 1", actual: row.ActionDescription);
            Assert.Equal(expected: "Rules.ClearSegmentationRules(101,1)", actual: row.StoredProcName);
            Assert.Equal(expected: 1, actual: row.UpdatedCount);

            dynamic policyResult = GetResultRow(tableName: "dbo.Policy");
            Assert.NotNull(policyResult);
            Assert.Equal(expected: DBNull.Value, actual: policyResult.RuleId);
        }

        [Fact]
        public void ClearSegmentationOverridePlacementRules()
        {

            ExecuteStoredProcedureWithoutResult(storedProcedureName: "PactConfig.SetControlValue_Bit", values: new
            {
                @ItemName = @"ClearRules_Override",
                @Value = true,
            });

            dynamic policyRecord = CreateRow(tableName: "dbo.Policy", values: new
            {
                RuleId = 10,
                IsDeleted = false,
                ExpiryDate = DateTime.Now.AddDays(1)
            });

            dynamic placementRecord = CreateRow(tableName: "dbo.Placement", values: new
            {
                DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
                IsDeleted = false
            });

            dynamic policyPlacementRecord = CreateRow(tableName: "dbo.PlacementPolicy", values: new
            {
                PolicyId = policyRecord.PolicyId,
                PlacementPolicyRelationshipTypeId = (int)PlacementPolicyRelationshipType.Current,
                DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
                PlacementId = placementRecord.PlacementId,
                IsDeleted = false
            });

            // Call the stored procedure
            ExecuteStoredProcedureWithoutResult(storedProcedureName: "Rules.ClearSegmentationRules", values: new { @ProcessSessionId = 101 });

            string dayOfWeek = DateTime.Now.DayOfWeek.ToString();

            dynamic row = GetResultRow(tableName: "ADF.StoredProcLog", whereClause: $"StoredProcName LIKE 'Rules.ClearSegmentationRules%'");
            Assert.NotNull(row);
            Assert.Equal(expected: $"Run on {dayOfWeek} or Override: 1", actual: row.ActionDescription);
            Assert.Equal(expected: "Rules.ClearSegmentationRules(101,1)", actual: row.StoredProcName);
            Assert.Equal(expected: 0, actual: row.UpdatedCount);

            dynamic policyResult = GetResultRow(tableName: "dbo.Policy");
            Assert.NotNull(policyResult);
            Assert.Equal(expected: policyRecord.RuleId, actual: policyResult.RuleId);
        }

        /*
         * This test will not update the Policy record as it is attached as a current policy to an existing placement
         * The override flag is set for this test case
         */
        [Fact]
        public void ClearSegmentationDailyRules()
        {
            dynamic eclipseDataSourceInstanceRecord = CreateRow(tableName: "Reference.DataSourceInstance", values: new
            {
                DataSourceInstanceId = (int)DataSourceInstance.Eclipse
            });

            dynamic policyRecord = CreateRow(tableName: "dbo.Policy", values: new
            {
                RuleId = 10,
                IsDeleted = false,
                ExpiryDate = DateTime.Now.AddDays(1),
                DataSourceInstanceId = eclipseDataSourceInstanceRecord.DataSourceInstanceId
            });

            dynamic placementRecord = CreateRow(tableName: "dbo.Placement", values: new
            {
                DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
                PlacementSystemId = 1234,
                IsDeleted = false
            });

            dynamic policyPlacementRecord = CreateRow(tableName: "dbo.PlacementPolicy", values: new
            {
                PolicyId = policyRecord.PolicyId,
                PlacementPolicyRelationshipTypeId = (int)PlacementPolicyRelationshipType.Current,
                DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
                PlacementId = placementRecord.PlacementId,
                IsDeleted = false
            });

            // Call the stored procedure
            ExecuteStoredProcedureWithoutResult(storedProcedureName: "Rules.ClearSegmentationRules", values: new { @ProcessSessionId = 101 });

            string dayOfWeek = DateTime.Now.DayOfWeek.ToString();

            dynamic row = GetResultRow(tableName: "ADF.StoredProcLog", whereClause: $"StoredProcName LIKE 'Rules.ClearSegmentationRules%'");
            Assert.NotNull(row);
            Assert.Equal(expected: $"Run on {dayOfWeek} or Override: 0", actual: row.ActionDescription);
            Assert.Equal(expected: "Rules.ClearSegmentationRules(101,0)", actual: row.StoredProcName);
            Assert.Equal(expected: 1, actual: row.UpdatedCount);

            dynamic policyResult = GetResultRow(tableName: "dbo.Policy");
            Assert.NotNull(policyResult);
            Assert.Equal(expected: DBNull.Value, actual: policyResult.RuleId);
            Assert.Equal(expected: policyRecord.RuleId, actual: policyResult.PreviousRuleId);
        }

        [Fact]
        public void ClearSegmentationDailyPlacementRules()
        {
            dynamic eclipseDataSourceInstanceRecord = CreateRow(tableName: "Reference.DataSourceInstance", values: new
            {
                DataSourceInstanceId = (int)DataSourceInstance.Eclipse
            });

            dynamic policyRecord = CreateRow(tableName: "dbo.Policy", values: new
            {
                RuleId = 10,
                IsDeleted = false,
                ExpiryDate = DateTime.Now.AddDays(1),
                DataSourceInstanceId = eclipseDataSourceInstanceRecord.DataSourceInstanceId
            });

            // Call the stored procedure
            ExecuteStoredProcedureWithoutResult(storedProcedureName: "Rules.ClearSegmentationRules", values: new { @ProcessSessionId = 101 });

            string dayOfWeek = DateTime.Now.DayOfWeek.ToString();

            dynamic row = GetResultRow(tableName: "ADF.StoredProcLog", whereClause: $"StoredProcName LIKE 'Rules.ClearSegmentationRules%'");
            Assert.NotNull(row);
            Assert.Equal(expected: $"Run on {dayOfWeek} or Override: 0", actual: row.ActionDescription);
            Assert.Equal(expected: "Rules.ClearSegmentationRules(101,0)", actual: row.StoredProcName);
            Assert.Equal(expected: 1, actual: row.UpdatedCount);

            dynamic policyResult = GetResultRow(tableName: "dbo.Policy");
            Assert.NotNull(policyResult);
            Assert.Equal(expected: DBNull.Value, actual: policyResult.RuleId);
            Assert.Equal(expected: policyRecord.RuleId, actual: policyResult.PreviousRuleId);
        }

        /// <summary>
        /// Do not change.
        /// </summary>
        /// <param name="fixture"></param>
        public ClearSegmentationRulesTest(DatabaseFixture fixture) : base(fixture)
        {
        }
    }
}
