﻿/* Tests the Segmentation part of the rules.                                                   */
/* As this can be quite slow it allows conditional compilation to control which tests are run  */
/* One routine is responsible for creating the test records making it fairly easy to see which */
/* tables and values need to be set.                                                           */
/* The Theory pattern is used to set-up the data for each rule.                                */
/* For rules that rely on not exists you will need at least two tests. One to check for a      */
/* match and another to check that the not exists does not match.                              */

#define EclipseRenewalRulesCheck
#define EpicRenewalRulesCheck
#define COLRenewalRulesCheck
#define eGlobalRenewalRulesCheck
#define OtherRenewalRulesCheck
// #define CheckOrganisationsAgainRealPlacementStore // Turned off because of Finex tests have manufactured data.

using Microsoft.Extensions.Configuration;
using System.Diagnostics.CodeAnalysis;
using Xunit.Abstractions;

namespace PsDb.Tests.Rules;

[ExcludeFromCodeCoverage]
[Trait(name: "Category", value: "RuleTest")]
public class SegmentationRulesTests : PlacementStoreTestBase
{
    public const string DatabaseConnectionStringKey = @"PSDatabase";

    private readonly bool _canQueryPS;
    private readonly string? _psConnectionString;

#if EclipseRenewalRulesCheck
    /// <summary>
    /// This test is to check Eclipse Segmentation rules.
    /// </summary>
    /// <param name="matches"></param>
    /// <param name="ruleId"></param>
    /// <param name="dataSourceInstance"></param>
    /// <param name="orgLevel1"></param>
    /// <param name="orgLevel2"></param>
    /// <param name="orgLevel3"></param>
    /// <param name="orgLevel4"></param>
    /// <param name="orgLevel5"></param>
    /// <param name="productKey"></param>
    /// <param name="globalPartyRole"></param>
    /// <param name="refPolicyStatus"></param>
    /// <param name="facilityLineSlipRef"></param>
    /// <param name="policySectionStatusKey"></param>
    /// <param name="department"></param>
    /// <param name="workerName"></param>
    /// <param name="productClass"></param>
    /// <param name="partyBusinessKey"></param>
    /// <param name="expiryDateString"></param>
    /// <param name="brokingSubSegmentId"></param>
    /// <param name="policyParentKey"></param>
    [Theory]
    // Rule: 1: (pol.OrgLevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.OrgLevel2 IN ('Aerospace') AND pol.OrgLevel5 IN ('Africa Team', 'Asia Pacific', 'Continental Europe', 'East Europe Team', 'LatAm', 'Middle East', 'NA Team', 'Ipswich','Aerospace Personal Accident'))
    [InlineData(true, 1, DataSourceInstance.Eclipse, "Willis Limited", "Aerospace", null, null, "Africa Team", null, null, null, null, null, null, null, null, null)]
    [InlineData(true, 1, DataSourceInstance.Eclipse, "Willis Towers Watson SA NV", "Aerospace", null, null, "Africa Team", null, null, null, null, null, null, null, null, null)]
    // Rule: 4: (pol.OrgLevel1 IN ('Willis Limited', 'Willis Towers Watson SA NV') AND pol.ORGLevel5 IN ('Terrorism', 'Ownership Terrorism (Singapore)', 'WNA-L Terrorism','Terrorism - DIFC') AND (pa.[Value] IS NOT NULL OR pm.FaciltyLineSlipRef IS NOT NULL))
    [InlineData(true, 4, DataSourceInstance.Eclipse, "Willis Limited", null, null, null, "Terrorism", null, null, null, "xx", null, null, null, null, null)]
    [InlineData(true, 4, DataSourceInstance.Eclipse, "Willis Limited", null, null, null, "Terrorism", null, null, null, null, null, null, null, null, null, null, null, "12345")]
    [InlineData(true, 4, DataSourceInstance.Eclipse, "Willis Limited", null, null, null, "Ownership Terrorism (Singapore)", null, null, null, null, null, null, null, null, null, null, null, "12345")]
    [InlineData(true, 4, DataSourceInstance.Eclipse, "Willis Limited", null, null, null, "WNA-L Terrorism", null, null, null, null, null, null, null, null, null, null, null, "12345")]
    [InlineData(true, 4, DataSourceInstance.Eclipse, "Willis Limited", null, null, null, "Terrorism - DIFC", null, null, null, null, null, null, null, null, null, null, null, "12345")]
    [InlineData(true, 4, DataSourceInstance.Eclipse, "Willis Towers Watson SA NV", null, null, null, "Ownership Terrorism (Singapore)", null, null, null, null, null, null, null, null, null, null, null, "12345")]
    [InlineData(false, 4, DataSourceInstance.Eclipse, "Not a valid org", null, null, null, "Ownership Terrorism (Singapore)", null, null, null, null, null, null, null, null, null, null, null, "12345")]
    // Rule: 5: (pol.orglevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND ((pol.orglevel2 LIKE '%marine%' AND pol.orglevel5 NOT IN ('Yacht RoW','Yacht North America')) OR pol.orglevel2 = 'FAJS'))
    [InlineData(true, 5, DataSourceInstance.Eclipse, "Willis Limited", "marine", null, null, "Anything else", null, null, null, null, null, null, null, null, null)]
    [InlineData(false, 5, DataSourceInstance.Eclipse, "Willis Limited", "marine", null, null, "Yacht RoW", null, null, null, null, null, null, null, null, null)]
    [InlineData(true, 5, DataSourceInstance.Eclipse, "Willis Limited", "FAJS", null, null, null, null, null, null, null, null, null, null, null, null)]
    // Rule: 7: (pol.OrgLevel1 IN ('Willis Limited', 'Willis Towers Watson SA NV') AND pol.OrgLevel2 = 'Financial Solutions' AND pol.ORGLevel5 IN ('Credit'))
    [InlineData(true, 7, DataSourceInstance.Eclipse, "Willis Limited", "Financial Solutions", null, null, "Credit", null, null, null, null, null, null, null, null, null)]
    // Rule: 8: (pol.OrgLevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.OrgLevel2 = 'GMI' AND pol.OrgLevel5 IN ('D P - Europe','D P - Asia Pac','D P - Australasia','D P - MEA','D P - Latam/Carribean'))
    [InlineData(true, 8, DataSourceInstance.Eclipse, "Willis Limited", "GMI", null, null, "D P - Europe", null, null, null, null, null, null, null, null, null)]
    // Rule: 9: (pol.OrgLevel1 IN ('Willis Limited', 'Willis Towers Watson SA NV') AND (pol.orglevel2 LIKE '%Terrorism%' or (pol.orglevel2 ='DIFC' and pol.orglevel5 = 'Terrorism - DIFC')) AND (pa.[Value] IS NULL OR pm.FaciltyLineSlipRef IS NULL))
    [InlineData(true, 9, DataSourceInstance.Eclipse, "Willis Limited", "yyyTerrorismxxx", null, null, null, null, null, null, "xx", null, null, null, null, null)]
    [InlineData(true, 9, DataSourceInstance.Eclipse, "Willis Limited", "yyyTerrorismxxx", null, null, null, null, null, null, null, null, null, null, null, null, null, null, "12345")]
    [InlineData(true, 9, DataSourceInstance.Eclipse, "Willis Limited", "yyyTerrorismxxx", null, null, "Anything", null, null, null, null, null, null, null, null, null, null, null, "12345")]
    [InlineData(true, 9, DataSourceInstance.Eclipse, "Willis Towers Watson SA NV", "yyyTerrorismxxx", null, null, "Anything", null, null, null, null, null, null, null, null, null, null, null, "12345")]
    /* The rule overlaps with rule 4. As it's the same team this is not a problem at this time
        [InlineData(true, 9, DataSourceInstance.Eclipse, "Willis Limited", "DIFC", null, null, "Terrorism - DIFC", null, null, null, null, "xx", null, null, null, null, null)]
        [InlineData(true, 9, DataSourceInstance.Eclipse, "Willis Limited", "DIFC", null, null, "Terrorism - DIFC", null, null, null, null, null, null, null, null, null, null, null, null, "12345")]
    */
    // Rule: 10: (pol.OrgLevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.OrgLevel5 IN ('D C - Asia Pac','D C - Australasia','D C - Europe','D C - Latam/Carribean','D C - MEA'))
    [InlineData(true, 10, DataSourceInstance.Eclipse, "Willis Limited", null, null, null, "D C - Asia Pac", null, null, null, null, null, null, null, null, null)]
    // Rule: 22: (pol.OrgLevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.OrgLevel5 IN ('D C - North America','WNA Casualty'))
    [InlineData(true, 22, DataSourceInstance.Eclipse, "Willis Limited", null, null, null, "D C - North America", null, null, null, null, null, null, null, null, null)]
    // Rule: 23: (pol.OrgLevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.OrgLevel5 IN ('Risk Solutions','D P - North America','D P - Facilities'))
    [InlineData(true, 23, DataSourceInstance.Eclipse, "Willis Limited", null, null, null, "Risk Solutions", null, null, null, null, null, null, null, null, null)]
    // Rule: 24: ((pol.OrgLevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.OrgLevel5 IN ('D C - Product Recall')) OR (pol.orglevel1 IN ('Facultative') AND pol.orglevel2 IN ('Faber WL') AND pol.orglevel5 IN ('Faber Recall')))
    [InlineData(true, 24, DataSourceInstance.Eclipse, "Facultative", "Faber WL", null, null, "Faber Recall", null, null, null, null, null, null, null, null, null)]
    // Rule: 25: (pol.OrgLevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.OrgLevel5 IN ('D C - Environmental'))
    [InlineData(true, 25, DataSourceInstance.Eclipse, "Willis Limited", null, null, null, "D C - Environmental", null, null, null, null, null, null, null, null, null)]
    // Rule: 26: (pol.OrgLevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.OrgLevel4 = 'Ownership Energy' AND pol.OrgLevel5 IN ('Upstream - CEMEA Russia','Upstream - Asia','Upstream - North America','Upstream - Western Europe UK','Upstream - Western Europe Europe','Upstream - LATAM','Upstream - CEMEA Middle East','Upstream - CEMEA Africa','Upstream - CEEMEA','Energy Upstream'))
    [InlineData(true, 26, DataSourceInstance.Eclipse, "Willis Limited", null, null, "Ownership Energy", "Upstream - CEMEA Russia", null, null, null, null, null, null, null, null, null)]
    // Rule: 27: (pol.OrgLevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.OrgLevel3 IN ('Ownership Construction') AND pol.OrgLevel5 IN ('Bouygues'))
    [InlineData(true, 27, DataSourceInstance.Eclipse, "Willis Limited", null, "Ownership Construction", null, "Bouygues", null, null, null, null, null, null, null, null, null)]
    // Rule: 28: (pol.OrgLevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.OrgLevel3 IN ('Ownership Construction') AND pol.OrgLevel5 IN ('Hochtief'))
    [InlineData(true, 28, DataSourceInstance.Eclipse, "Willis Limited", null, "Ownership Construction", null, "Hochtief", null, null, null, null, null, null, null, null, null)]
    // Rule: 29: (pol.OrgLevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.OrgLevel3 IN ('Ownership Construction') AND pol.OrgLevel5 IN ('Vinci'))
    [InlineData(true, 29, DataSourceInstance.Eclipse, "Willis Limited", null, "Ownership Construction", null, "Vinci", null, null, null, null, null, null, null, null, null)]
    // Rule: 30: (pol.OrgLevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.OrgLevel3 IN ('Ownership Construction') AND pol.OrgLevel4 IN ('Europe', 'International') AND pol.OrgLevel5 NOT IN ('Besix', 'Bouygues', 'Hochtief', 'Vinci'))
    [InlineData(true, 30, DataSourceInstance.Eclipse, "Willis Limited", null, "Ownership Construction", "Europe", "NOT IN", null, null, null, null, null, null, null, null, null)]
    [InlineData(false, 30, DataSourceInstance.Eclipse, "Willis Limited", null, "Ownership Construction", "Europe", "Besix", null, null, null, null, null, null, null, null, null)]
    // Rule: 31: (pol.OrgLevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.OrgLevel3 = 'Ownership Construction' AND pol.OrgLevel4 = 'UK')
    [InlineData(true, 31, DataSourceInstance.Eclipse, "Willis Limited", null, "Ownership Construction", "UK", null, null, null, null, null, null, null, null, null, null)]
    // Rule: 32: (pol.OrgLevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.OrgLevel2 = 'GSP')
    [InlineData(true, 32, DataSourceInstance.Eclipse, "Willis Limited", "GSP", null, null, null, null, null, null, null, null, null, null, null, null)]
    // Rule: 33: (pol.OrgLevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.OrgLevel2 = 'ISP')
    [InlineData(true, 33, DataSourceInstance.Eclipse, "Willis Limited", "ISP", null, null, null, null, null, null, null, null, null, null, null, null)]
    // Rule: 34: (pol.OrgLevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.OrgLevel2 = 'WREP' AND PROD.ProductClass <> 'REP Direct' AND PROD.ProductClass <> 'Legal Indemnities')
    [InlineData(true, 34, DataSourceInstance.Eclipse, "Willis Limited", "WREP", null, null, null, null, null, null, null, null, null, null, "XXXXXX", null)]
    [InlineData(false, 34, DataSourceInstance.Eclipse, "Willis Limited", "WREP", null, null, null, null, null, null, null, null, null, null, "REP Direct", null)]
    [InlineData(false, 34, DataSourceInstance.Eclipse, "Willis Limited", "WREP", null, null, null, null, null, null, null, null, null, null, "Legal Indemnities", null)]
    // Rule: 35: (pol.OrgLevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.OrgLevel4 IN ('Ownership Energy','Ownership GMI') AND pol.OrgLevel5 IN ('Downstream - Casualty (NR)','Liability Energy','Downstream - Casualty'))
    [InlineData(true, 35, DataSourceInstance.Eclipse, "Willis Limited", null, null, "Ownership Energy", "Downstream - Casualty (NR)", null, null, null, null, null, null, null, null, null)]
    // Rule: 36: (pol.OrgLevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.OrgLevel4 = 'Ownership GMI' AND pol.OrgLevel5 = 'Downstream - Renewables (NR)')
    [InlineData(true, 36, DataSourceInstance.Eclipse, "Willis Limited", null, null, "Ownership GMI", "Downstream - Renewables (NR)", null, null, null, null, null, null, null, null, null)]
    // Rule: 37: (pol.OrgLevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.OrgLevel4 IN ('Ownership GMI','Ownership Energy','Ownership WNA London') AND pol.OrgLevel5 IN ('Downstream - CIS (NR)','Downstream - Utilities (NR)','Downstream - Europe (ex. GB) (NR)','Downstream - LATAM (NR)','Downstream - INEOS (NR)','Downstream - UK (NR)','Downstream - North America (NR)','Downstream - BHP (NR)','Downstream - Pacific (NR)','Downstream - Asia (NR)','Downstream - MEA (NR)','Downstream - Engineers','Downstream North America','Downstream - INEOS','Downstream - North America','Downstream - Europe (ex. GB)','Downstream - CIS','Downstream - MEA','Downstream - UK','Downstream - LATAM','Downstream - Utilities','Downstream - BHP','Downstream - Asia','Downstream - Pacific'))
    [InlineData(true, 37, DataSourceInstance.Eclipse, "Willis Limited", null, null, "Ownership GMI", "Downstream - CIS (NR)", null, null, null, null, null, null, null, null, null)]
    // Rule: 38: (pol.OrgLevel1 IN ('Willis Limited', 'Willis Towers Watson SA NV') AND pol.OrgLevel2 = 'Financial Solutions' AND pol.ORGLevel5 IN ('Retail Credit'))
    [InlineData(true, 38, DataSourceInstance.Eclipse, "Willis Limited", "Financial Solutions", null, null, "Retail Credit", null, null, null, null, null, null, null, null, null)]
    // Rule: 39: (pol.OrgLevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.OrgLevel3 IN ('Ownership inspace') AND pol.OrgLevel5 IN ('Inspace UK','Inspace US','Inspace of Malaysia','Willis Aerospace Consulting'))
    [InlineData(true, 39, DataSourceInstance.Eclipse, "Willis Limited", null, "Ownership inspace", null, "Inspace UK", null, null, null, null, null, null, null, null, null)]
    // Rule: 40: (pol.OrgLevel1 IN ('Willis Limited', 'Willis Towers Watson SA NV') AND pol.OrgLevel3 = 'Ownership FINEX' AND pol.OrgLevel4 = 'FINEX - FI' AND pol.OrgLevel5 IN ('FI - Premises & Transit','FI - Cash in Transit'))
    [InlineData(true, 40, DataSourceInstance.Eclipse, "Willis Limited", null, "Ownership FINEX", "FINEX - FI", "FI - Premises & Transit", null, null, null, null, null, null, null, null, null)]
    // Rule: 41: (pol.OrgLevel1 IN ('Willis Limited', 'Willis Towers Watson SA NV') AND pol.OrgLevel2 = 'FINEX Global' AND pol.OrgLevel3 = 'Ownership FINEX' AND pol.OrgLevel4 = 'FINEX PI' AND pol.OrgLevel5 = 'PI - Medical Malpractice')
    [InlineData(true, 41, DataSourceInstance.Eclipse, "Willis Limited", "FINEX Global", "Ownership FINEX", "FINEX PI", "PI - Medical Malpractice", null, null, null, null, null, null, null, null, null)]
    // Rule: 42: (pol.OrgLevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.OrgLevel2 LIKE 'GB Industries and Practices' AND pol.ORGLevel5 IN ('Food and Drink') )
    [InlineData(true, 42, DataSourceInstance.Eclipse, "Willis Limited", "GB Industries and Practices", null, null, "Food and Drink", null, null, null, null, null, null, null, null, null)]
    // Rule: 43: (pol.OrgLevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.OrgLevel3 IN ('Ownership Transportation') AND pol.ORGLevel5 IN ('Land Transport') )
    [InlineData(true, 43, DataSourceInstance.Eclipse, "Willis Limited", null, "Ownership Transportation", null, "Land Transport", null, null, null, null, null, null, null, null, null)]
    // Rule: 44: (pol.OrgLevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.OrgLevel2 LIKE 'GB Industries and Practices' AND pol.ORGLevel5 IN ('Leisure  and Hospitality', 'Leisure and Hospitality') )
    [InlineData(true, 44, DataSourceInstance.Eclipse, "Willis Limited", "GB Industries and Practices", null, null, "Leisure  and Hospitality", null, null, null, null, null, null, null, null, null)]
    // Rule: 45: (pol.OrgLevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.OrgLevel2 LIKE 'GB Industries and Practices' AND pol.ORGLevel5 IN ('Manufacturing and Industrial') )
    [InlineData(true, 45, DataSourceInstance.Eclipse, "Willis Limited", "GB Industries and Practices", null, null, "Manufacturing and Industrial", null, null, null, null, null, null, null, null, null)]
    // Rule: 46: (pol.OrgLevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.OrgLevel2 LIKE 'GB Industries and Practices' AND pol.ORGLevel5 IN ('Retail') )
    [InlineData(true, 46, DataSourceInstance.Eclipse, "Willis Limited", "GB Industries and Practices", null, null, "Retail", null, null, null, null, null, null, null, null, null)]
    // Rule: 47: (pol.OrgLevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.OrgLevel2 LIKE 'GB Industries and Practices' AND pol.ORGLevel5 IN ('Services') )
    [InlineData(true, 47, DataSourceInstance.Eclipse, "Willis Limited", "GB Industries and Practices", null, null, "Services", null, null, null, null, null, null, null, null, null)]
    // Rule: 48: (pol.OrgLevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND ((pol.OrgLevel2 LIKE 'GB Industries and Practices' AND pol.ORGLevel5 IN ('Services - GI')) OR (pol.OrgLevel2 LIKE 'Finex Global' AND pol.ORGLevel5 IN ('Nat - Ips - GI'))) )
    [InlineData(true, 48, DataSourceInstance.Eclipse, "Willis Limited", "GB Industries and Practices", null, null, "Services - GI", null, null, null, null, null, null, null, null, null)]
    [InlineData(true, 48, DataSourceInstance.Eclipse, "Willis Limited", "Finex Global", null, null, "Nat - Ips - GI", null, null, null, null, null, null, null, null, null)]
    // Rule: 49: (pol.OrgLevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.OrgLevel2 LIKE 'GB Industries and Practices' AND pol.ORGLevel5 IN ('IBU') )
    [InlineData(true, 49, DataSourceInstance.Eclipse, "Willis Limited", "GB Industries and Practices", null, null, "IBU", null, null, null, null, null, null, null, null, null)]
    // Rule: 50: (pol.OrgLevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.OrgLevel2 LIKE 'GB Industries and Practices' AND pol.ORGLevel5 IN ('JBU') )
    [InlineData(true, 50, DataSourceInstance.Eclipse, "Willis Limited", "GB Industries and Practices", null, null, "JBU", null, null, null, null, null, null, null, null, null)]
    // Rule: 51: (pol.OrgLevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.OrgLevel2 LIKE 'Retail Network' AND pol.ORGLevel5 IN ('Birmingham') )
    [InlineData(true, 51, DataSourceInstance.Eclipse, "Willis Limited", "Retail Network", null, null, "Birmingham", null, null, null, null, null, null, null, null, null)]
    // Rule: 52: (pol.OrgLevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.OrgLevel2 LIKE 'Retail Network' AND pol.ORGLevel5 IN ('Leeds','Manchester') )
    [InlineData(true, 52, DataSourceInstance.Eclipse, "Willis Limited", "Retail Network", null, null, "Leeds", null, null, null, null, null, null, null, null, null)]
    // Rule: 53: (pol.OrgLevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.OrgLevel2 LIKE 'Retail Network' AND pol.ORGLevel5 IN ('Aberdeen','Edinburgh','Glasgow') )
    [InlineData(true, 53, DataSourceInstance.Eclipse, "Willis Limited", "Retail Network", null, null, "Aberdeen", null, null, null, null, null, null, null, null, null)]
    // Rule: 54: (pol.OrgLevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.OrgLevel2 LIKE 'Retail Network' AND pol.ORGLevel5 IN ('Reading') )
    [InlineData(true, 54, DataSourceInstance.Eclipse, "Willis Limited", "Retail Network", null, null, "Reading", null, null, null, null, null, null, null, null, null)]
    // Rule: 55: (pol.OrgLevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.OrgLevel2 LIKE 'Retail Network' AND pol.ORGLevel5 IN ('Bristol','Cardiff') )
    [InlineData(true, 55, DataSourceInstance.Eclipse, "Willis Limited", "Retail Network", null, null, "Bristol", null, null, null, null, null, null, null, null, null)]
    // Rule: 56: (pol.OrgLevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.OrgLevel2 = 'Marine' AND pol.OrgLevel3 IN ('Ownership Marine') AND pol.OrgLevel5 IN ('Yacht RoW','Yacht North America'))
    [InlineData(true, 56, DataSourceInstance.Eclipse, "Willis Limited", "Marine", "Ownership Marine", null, "Yacht RoW", null, null, null, null, null, null, null, null, null)]
    // Rule: 58: (pol.OrgLevel1 IN ('Facultative','Willis Towers Watson SA NV') AND pol.OrgLevel2 IN ('Faber WL') AND pol.OrgLevel5 IN ('F C - Latam/Carribean','F P - Latam/Carribean'))
    [InlineData(true, 58, DataSourceInstance.Eclipse, "Facultative", "Faber WL", null, null, "F C - Latam/Carribean", null, null, null, null, null, null, null, null, null)]
    // Rule: 59: (pol.OrgLevel1 IN ('Facultative','Willis Towers Watson SA NV') AND pol.OrgLevel2 IN ('Faber WL') AND pol.OrgLevel4 IN ('Property','Management') AND pol.OrgLevel5 IN ('London Market and ROW','Retro'))
    [InlineData(true, 59, DataSourceInstance.Eclipse, "Facultative", "Faber WL", null, "Property", "London Market and ROW", null, null, null, null, null, null, null, null, null)]
    // Rule: 60: (pol.OrgLevel1 IN ('Facultative','Willis Towers Watson SA NV') AND pol.OrgLevel2 IN ('Faber WL') AND pol.OrgLevel4 IN ('Property','Casualty') AND pol.OrgLevel5 IN ('F P - North America','F C - North America'))
    [InlineData(true, 60, DataSourceInstance.Eclipse, "Facultative", "Faber WL", null, "Property", "F P - North America", null, null, null, null, null, null, null, null, null)]
    // Rule: 61: (pol.OrgLevel1 IN ('Facultative','Willis Towers Watson SA NV') AND pol.OrgLevel2 IN ('Faber WL') AND pol.OrgLevel5 IN ('F C - Europe','F P - Europe'))
    [InlineData(true, 61, DataSourceInstance.Eclipse, "Facultative", "Faber WL", null, null, "F C - Europe", null, null, null, null, null, null, null, null, null)]
    // Rule: 62: (pol.OrgLevel1 IN ('Facultative','Willis Towers Watson SA NV') AND pol.OrgLevel2 IN ('Faber WL','DIFC') AND pol.ORGLevel5  IN ('F P - Dubai','FC - Dubai','F C - Dubai - DIFC','F P - Dubai - DIFC'))
    [InlineData(true, 62, DataSourceInstance.Eclipse, "Facultative", "Faber WL", null, null, "F P - Dubai", null, null, null, null, null, null, null, null, null)]
    // Rule: 65: (pol.OrgLevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.OrgLevel2 = 'GB Industries and Practices' AND pol.OrgLevel5 = 'Utilities')
    [InlineData(true, 65, DataSourceInstance.Eclipse, "Willis Limited", "GB Industries and Practices", null, null, "Utilities", null, null, null, null, null, null, null, null, null)]
    // Rule: 67: pol.[OrgLevel1] in ('Willis (Singapore) Pte Ltd (Reinsurance)') AND [OrgLevel2] = 'Retail (Singapore)' AND pol.[OrgLevel5] = 'Facultative RI (Singapore)' AND pol1.[BrokingSubSegmentId] not in (9)
    [InlineData(false, 67, DataSourceInstance.Eclipse, "Willis (Singapore) Pte Ltd (Reinsurance)", "Retail (Singapore)", null, null, "Facultative RI (Singapore)", null, null, null, null, null, null, null, null, null, null, 9)]
    [InlineData(true, 67, DataSourceInstance.Eclipse, "Willis (Singapore) Pte Ltd (Reinsurance)", "Retail (Singapore)", null, null, "Facultative RI (Singapore)", null, null, null, null, null, null, null, null, null, null, 8)]
    // Rule: 68: pol.[OrgLevel1] in ('Willis (Singapore) Pte Ltd (Direct)','Willis (Singapore) Pte Ltd (Reinsurance)') AND [OrgLevel2] = 'Marine (Singapore)' AND pol.[OrgLevel5] in ('Hull APEX Apex (Singapore)','Hull Yacht ROW (Singapore)','ITL RoW Apex (Singapore)','OSR Apex (Singapore)','P&I Apex (Singapore)','Marine Cargo')
    [InlineData(true, 68, DataSourceInstance.Eclipse, "Willis (Singapore) Pte Ltd (Direct)", "Marine (Singapore)", null, null, "Hull APEX Apex (Singapore)", null, null, null, null, null, null, null, null, null)]
    [InlineData(true, 68, DataSourceInstance.Eclipse, "Willis (Singapore) Pte Ltd (Direct)", "Marine (Singapore)", null, null, "Hull Yacht ROW (Singapore)", null, null, null, null, null, null, null, null, null)]
    [InlineData(true, 68, DataSourceInstance.Eclipse, "Willis (Singapore) Pte Ltd (Direct)", "Marine (Singapore)", null, null, "ITL RoW Apex (Singapore)", null, null, null, null, null, null, null, null, null)]
    [InlineData(true, 68, DataSourceInstance.Eclipse, "Willis (Singapore) Pte Ltd (Direct)", "Marine (Singapore)", null, null, "OSR Apex (Singapore)", null, null, null, null, null, null, null, null, null)]
    [InlineData(true, 68, DataSourceInstance.Eclipse, "Willis (Singapore) Pte Ltd (Direct)", "Marine (Singapore)", null, null, "P&I Apex (Singapore)", null, null, null, null, null, null, null, null, null)]
    [InlineData(true, 68, DataSourceInstance.Eclipse, "Willis (Singapore) Pte Ltd (Direct)", "Marine (Singapore)", null, null, "Marine Cargo", null, null, null, null, null, null, null, null, null)]
    // Rule: 70: pol.[OrgLevel1] in ('Willis (Singapore) Pte Ltd (Direct)','Willis (Singapore) Pte Ltd (Reinsurance)') AND [OrgLevel2] = 'GMI (Singapore)' AND pol.[OrgLevel5] in ('Asia Pacific (Singapore)','Liability International (Singapore)')
    [InlineData(true, 70, DataSourceInstance.Eclipse, "Willis (Singapore) Pte Ltd (Direct)", "GMI (Singapore)", null, null, "Asia Pacific (Singapore)", null, null, null, null, null, null, null, null, null)]
    // Rule: 81: (pol.OrgLevel1 IN ('Willis Limited', 'Willis Towers Watson SA NV') AND pol.OrgLevel2 IN ('Aerospace') AND pol.OrgLevel5 IN ('Aerospace Products - Ips','Aerospace Products - Lon'))
    [InlineData(true, 81, DataSourceInstance.Eclipse, "Willis Limited", "Aerospace", null, null, "Aerospace Products - Ips", null, null, null, null, null, null, null, null, null)]
    // Rule: 82: (pol.OrgLevel1 IN ('Willis Limited', 'Willis Towers Watson SA NV') AND pol.OrgLevel2 IN ('Aerospace') AND pol.OrgLevel5 IN ('Airlines - Ips','Airlines - Lon'))
    [InlineData(true, 82, DataSourceInstance.Eclipse, "Willis Limited", "Aerospace", null, null, "Airlines - Ips", null, null, null, null, null, null, null, null, null)]
    // Rule: 83: (pol.OrgLevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND ((pol.OrgLevel2 = 'Financial Solutions' AND pol.OrgLevel5 IN ('Aviation', 'NA Aircraft', 'Glencairn')) OR (pol.OrgLevel2 = 'Aerospace' AND pol.OrgLevel5 IN ('Contingent - Lon', 'Contingent - Ips'))) )
    [InlineData(true, 83, DataSourceInstance.Eclipse, "Willis Limited", "Financial Solutions", null, null, "Aviation", null, null, null, null, null, null, null, null, null)]
    [InlineData(true, 83, DataSourceInstance.Eclipse, "Willis Limited", "Aerospace", null, null, "Contingent - Lon", null, null, null, null, null, null, null, null, null)]
    // Rule: 84: (pol.OrgLevel1 IN ('Willis Limited', 'Willis Towers Watson SA NV') AND pol.OrgLevel2 IN ('Aerospace') AND pol.OrgLevel5 IN ('General Aviation - Ips','General Aviation - Lon','General Aviation - Ips'))
    [InlineData(true, 84, DataSourceInstance.Eclipse, "Willis Limited", "Aerospace", null, null, "General Aviation - Ips", null, null, null, null, null, null, null, null, null)]
    // Rule: 85: (pol.OrgLevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.OrgLevel3 IN ('Ownership Construction') AND pol.OrgLevel5 IN ('Besix'))
    [InlineData(true, 85, DataSourceInstance.Eclipse, "Willis Limited", null, "Ownership Construction", null, "Besix", null, null, null, null, null, null, null, null, null)]
    // Rule: 86: (pol.OrgLevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.OrgLevel3 IN ('Ownership Construction') AND pol.OrgLevel4 NOT IN ('Europe', 'International', 'UK') AND pol.OrgLevel5 NOT IN ('Bouygues','Hochtief','Vinci','Besix'))
    [InlineData(true, 86, DataSourceInstance.Eclipse, "Willis Limited", null, "Ownership Construction", "Anything else", "Anything Else", null, null, null, null, null, null, null, null, null)]
    [InlineData(false, 86, DataSourceInstance.Eclipse, "Willis Limited", null, "Ownership Construction", "Europe", "Anything Else", null, null, null, null, null, null, null, null, null)]
    [InlineData(false, 86, DataSourceInstance.Eclipse, "Willis Limited", null, "Ownership Construction", "Anything else", "Bouygues", null, null, null, null, null, null, null, null, null)]
    // Rule: 89: (pol.OrgLevel1 IN ('Willis Limited', 'Willis Towers Watson SA NV') AND pol.OrgLevel2 = 'Financial Solutions' AND pol.OrgLevel4 LIKE ('Bank%2'))
    [InlineData(true, 89, DataSourceInstance.Eclipse, "Willis Limited", "Financial Solutions", null, "Bankxxxxx2", null, null, null, null, null, null, null, null, null, null)]
    [InlineData(false, 89, DataSourceInstance.Eclipse, "Willis Limited", "Financial Solutions", null, "Bank", null, null, null, null, null, null, null, null, null, null)]
    // Rule: 90: (pol.OrgLevel1 IN ('Willis Limited', 'Willis Towers Watson SA NV') AND pol.OrgLevel2 = 'Financial Solutions' AND pol.OrgLevel4 LIKE 'Bank%1')
    [InlineData(true, 90, DataSourceInstance.Eclipse, "Willis Limited", "Financial Solutions", null, "Bank1231", null, null, null, null, null, null, null, null, null, null)]
    // Rule: 91: (pol.OrgLevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.OrgLevel2 IN ('Financial Solutions') AND pol.OrgLevel4 IN ('International') AND pol.OrgLevel5 LIKE 'International%')
    [InlineData(true, 91, DataSourceInstance.Eclipse, "Willis Limited", "Financial Solutions", null, "International", "International123", null, null, null, null, null, null, null, null, null)]
    // Rule: 1013: pol.[OrgLevel1] in ('Willis (Singapore) Pte Ltd (Direct)','Willis (Singapore) Pte Ltd (Reinsurance)') AND [OrgLevel2] = 'Retail (Singapore)' AND pol.[OrgLevel5] = 'Construction (Singapore)'
    [InlineData(true, 1013, DataSourceInstance.Eclipse, "Willis (Singapore) Pte Ltd (Direct)", "Retail (Singapore)", null, null, "Construction (Singapore)", null, null, null, null, null, null, null, null, null)]
    // Rule: 1015: pol.[OrgLevel1] in ('Willis (Singapore) Pte Ltd (Direct)','Willis (Singapore) Pte Ltd (Reinsurance)') AND [OrgLevel2] = 'Retail (Singapore)' AND pol.[OrgLevel5] IN ( 'Corporate (Singapore)', 'Client Service Team 1', 'Client Service Team 2', 'Client Service Team 3', 'Commercial (Singapore)')
    [InlineData(true, 1015, DataSourceInstance.Eclipse, "Willis (Singapore) Pte Ltd (Direct)", "Retail (Singapore)", null, null, "Corporate (Singapore)", null, null, null, null, null, null, null, null, null)]
    [InlineData(true, 1015, DataSourceInstance.Eclipse, "Willis (Singapore) Pte Ltd (Direct)", "Retail (Singapore)", null, null, "Client Service Team 1", null, null, null, null, null, null, null, null, null)]
    [InlineData(true, 1015, DataSourceInstance.Eclipse, "Willis (Singapore) Pte Ltd (Direct)", "Retail (Singapore)", null, null, "Client Service Team 2", null, null, null, null, null, null, null, null, null)]
    [InlineData(true, 1015, DataSourceInstance.Eclipse, "Willis (Singapore) Pte Ltd (Direct)", "Retail (Singapore)", null, null, "Client Service Team 3", null, null, null, null, null, null, null, null, null)]
    [InlineData(true, 1015, DataSourceInstance.Eclipse, "Willis (Singapore) Pte Ltd (Reinsurance)", "Retail (Singapore)", null, null, "Corporate (Singapore)", null, null, null, null, null, null, null, null, null)]
    [InlineData(true, 1015, DataSourceInstance.Eclipse, "Willis (Singapore) Pte Ltd (Reinsurance)", "Retail (Singapore)", null, null, "Client Service Team 1", null, null, null, null, null, null, null, null, null)]
    [InlineData(true, 1015, DataSourceInstance.Eclipse, "Willis (Singapore) Pte Ltd (Reinsurance)", "Retail (Singapore)", null, null, "Client Service Team 2", null, null, null, null, null, null, null, null, null)]
    [InlineData(true, 1015, DataSourceInstance.Eclipse, "Willis (Singapore) Pte Ltd (Reinsurance)", "Retail (Singapore)", null, null, "Client Service Team 3", null, null, null, null, null, null, null, null, null)]
    [InlineData(false, 1015, DataSourceInstance.Eclipse, "Willis (Singapore) Pte Ltd (Reinsurance)", "Retail (Singapore)", null, null, "Retail (Singapore)", null, null, null, null, null, null, null, null, null)]
    [InlineData(true, 1015, DataSourceInstance.Eclipse, "Willis (Singapore) Pte Ltd (Reinsurance)", "Retail (Singapore)", null, null, "Commercial (Singapore)", null, null, null, null, null, null, null, null, null)]
    // Rule: 1017: pol.[OrgLevel1] in ('Willis (Singapore) Pte Ltd (Direct)','Willis (Singapore) Pte Ltd (Reinsurance)') AND pol.[OrgLevel2] = 'Terrorism (Singapore)'AND pol.[OrgLevel5] = 'Ownership Terrorism (Singapore)'
    [InlineData(true, 1017, DataSourceInstance.Eclipse, "Willis (Singapore) Pte Ltd (Direct)", "Terrorism (Singapore)", null, null, "Ownership Terrorism (Singapore)", null, null, null, null, null, null, null, null, null)]
    // Rule: 1019: (pol.OrgLevel1 IN ('Facultative','Willis Towers Watson SA NV') AND pol.OrgLevel2 IN ('Faber WL') AND pol.OrgLevel5 IN ('F P - UK','F C - UK'))
    [InlineData(true, 1019, DataSourceInstance.Eclipse, "Facultative", "Faber WL", null, null, "F P - UK", null, null, null, null, null, null, null, null, null)]
    // Rule: 1021: pol.[OrgLevel1] in ('Willis (Singapore) Pte Ltd (Direct)','Willis (Singapore) Pte Ltd (Reinsurance)') AND [OrgLevel2] = 'Energy (Singapore)' AND pol.[OrgLevel5] in ('Energy Upstream (Singapore)')
    [InlineData(true, 1021, DataSourceInstance.Eclipse, "Willis (Singapore) Pte Ltd (Reinsurance)", "Energy (Singapore)", null, null, "Energy Upstream (Singapore)", null, null, null, null, null, null, null, null, null)]
    [InlineData(true, 1021, DataSourceInstance.Eclipse, "Willis (Singapore) Pte Ltd (Direct)", "Energy (Singapore)", null, null, "Energy Upstream (Singapore)", null, null, null, null, null, null, null, null, null)]
    // Rule: 1023: pol.[OrgLevel1] in ('Willis (Singapore) Pte Ltd (Direct)','Willis (Singapore) Pte Ltd (Reinsurance)') AND [OrgLevel2] = 'Energy (Singapore)' AND pol.[OrgLevel5] in ('Energy Downstream (Singapore)')
    [InlineData(true, 1023, DataSourceInstance.Eclipse, "Willis (Singapore) Pte Ltd (Direct)", "Energy (Singapore)", null, null, "Energy Downstream (Singapore)", null, null, null, null, null, null, null, null, null)]
    // Rule: 1025: pol.[OrgLevel1] in ('Willis (Singapore) Pte Ltd (Direct)','Willis (Singapore) Pte Ltd (Reinsurance)') AND [OrgLevel2] = 'Energy (Singapore)' AND pol.[OrgLevel5] in ('Power (Singapore)')
    [InlineData(true, 1025, DataSourceInstance.Eclipse, "Willis (Singapore) Pte Ltd (Direct)", "Energy (Singapore)", null, null, "Power (Singapore)", null, null, null, null, null, null, null, null, null)]
    // Rule: 1027: pol.[OrgLevel1] in ('Willis (Singapore) Pte Ltd (Direct)','Willis (Singapore) Pte Ltd (Reinsurance)') AND [OrgLevel2] = 'Energy (Singapore)' AND pol.[OrgLevel5] in ('Renewable Energy (Singapore)')
    [InlineData(true, 1027, DataSourceInstance.Eclipse, "Willis (Singapore) Pte Ltd (Direct)", "Energy (Singapore)", null, null, "Renewable Energy (Singapore)", null, null, null, null, null, null, null, null, null)]
    // Rule: 1031: OrgLevel1 = 'Willis Re Inc' AND OrgLevel2 = 'Willis Facultative' AND OrgLevel3 = 'Ownership Willis Fac'          AND OrgLevel4 = 'Willis Fac Client Ownership' AND OrgLevel5  IN ('Atlanta','Chicago','New York','NA FP Atlanta')         AND PROD.ProductClass = 'Prop'
    [InlineData(true, 1031, DataSourceInstance.Eclipse, "Willis Re Inc", "Willis Facultative", "Ownership Willis Fac", "Willis Fac Client Ownership", "Atlanta", null, null, null, null, null, null, null, "Prop", null)]
    // Rule: 1035: OrgLevel1 = 'Willis Hong Kong Limited' AND OrgLevel2 = 'Marine' AND OrgLevel3 = 'Ownership Marine' AND OrgLevel4 = 'Marine Ownership'
    [InlineData(true, 1035, DataSourceInstance.Eclipse, "Willis Hong Kong Limited", "Marine", "Ownership Marine", "Marine Ownership", null, null, null, null, null, null, null, null, null, null)]
    // Rule: 1063: (pol.OrgLevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.OrgLevel2 = 'WREP' AND PROD.productclass <> 'REP Direct' AND PROD.ProductClass = 'Legal Indemnities')
    [InlineData(true, 1063, DataSourceInstance.Eclipse, "Willis Limited", "WREP", null, null, null, null, null, null, null, null, null, null, "Legal Indemnities", null)]
    // Rule: 1081: (pol.OrgLevel1 IN ('Willis Limited', 'Willis Towers Watson SA NV') AND pol.OrgLevel2 = 'SCS')
    [InlineData(true, 1081, DataSourceInstance.Eclipse, "Willis Limited", "SCS", null, null, null, null, null, null, null, null, null, null, null, null)]
    // Rule: 1155: pol.[OrgLevel1] in ('Willis (Singapore) Pte Ltd (Direct)','Willis (Singapore) Pte Ltd (Reinsurance)') AND pol.[OrgLevel2] = 'Financial Solutions (Singapore)' AND  pol.expirydate > '2020-03-31'
    [InlineData(false, 1155, DataSourceInstance.Eclipse, "Willis (Singapore) Pte Ltd (Direct)", "Financial Solutions (Singapore)", null, null, null, null, null, null, null, null, null, null, null, null, "2020-03-31")]
    [InlineData(true, 1155, DataSourceInstance.Eclipse, "Willis (Singapore) Pte Ltd (Direct)", "Financial Solutions (Singapore)", null, null, null, null, null, null, null, null, null, null, null, null, "2020-04-01")]
    // Rule: 1158: (pol.OrgLevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.OrgLevel2 IN ('Special Contingency Risks') AND pol.OrgLevel3 IN ('Ownership SCR') AND pol.OrgLevel5 IN ('Accident and Health'))
    [InlineData(true, 1158, DataSourceInstance.Eclipse, "Willis Limited", "Special Contingency Risks", "Ownership SCR", null, "Accident and Health", null, null, null, null, null, null, null, null, null)]
    // Rule: 1160: (pol.OrgLevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.OrgLevel2 IN ('Special Contingency Risks') AND pol.OrgLevel3 IN ('Ownership SCR') AND pol.OrgLevel5 IN ('Corporate Clients'))
    [InlineData(true, 1160, DataSourceInstance.Eclipse, "Willis Limited", "Special Contingency Risks", "Ownership SCR", null, "Corporate Clients", null, null, null, null, null, null, null, null, null)]
    // Rule: 1162: (pol.OrgLevel1 IN ('Special Contingency Risks Inc.') AND pol.OrgLevel3 IN ('Ownership SCR') AND pol.OrgLevel5 IN ('SCR Inc.'))
    [InlineData(true, 1162, DataSourceInstance.Eclipse, "Special Contingency Risks Inc.", null, "Ownership SCR", null, "SCR Inc.", null, null, null, null, null, null, null, null, null)]
    // Rule: 1180: (pol.orglevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.orglevel4 = 'Ownership Hughes-Gibb' AND pol.orglevel5 IN ('Agri-Business','Aquaculture','Bloodstock','Livestock'))
    [InlineData(true, 1180, DataSourceInstance.Eclipse, "Willis Limited", null, null, "Ownership Hughes-Gibb", "Agri-Business", null, null, null, null, null, null, null, null, null)]
    // Rule: 1182: (pol.orglevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.orglevel4 = 'Ownership FAJS' AND pol.orglevel5 ='FAJS Private Clients MRC')
    [InlineData(true, 1182, DataSourceInstance.Eclipse, "Willis Limited", null, null, "Ownership FAJS", "FAJS Private Clients MRC", null, null, null, null, null, null, null, null, null)]
    // Rule: 1184: (pol.orglevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.orglevel3 = 'Ownership Marine' AND pol.orglevel5 IN ('Hull Africa/Middle East','Hull Apex','Hull Australia/South Africa','Hull Latin America','Hull North America','Hull USA'))
    [InlineData(true, 1184, DataSourceInstance.Eclipse, "Willis Limited", null, "Ownership Marine", null, "Hull Africa/Middle East", null, null, null, null, null, null, null, null, null)]
    // Rule: 1186: (pol.orglevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.orglevel3 = 'Ownership Marine' AND pol.orglevel5 IN ('Hull Eastern Europe','Hull France / Benelux','Hull Ger/Aus/Switz','Hull Iberia','Hull Italy','Hull Mediterranean','Hull Nordic','Hull Turkey','Hull UK'))
    [InlineData(true, 1186, DataSourceInstance.Eclipse, "Willis Limited", null, "Ownership Marine", null, "Hull Eastern Europe", null, null, null, null, null, null, null, null, null)]
    // Rule: 1188: (pol.orglevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.orglevel3 = 'Ownership Marine' AND pol.orglevel5 IN ('OSR Africa/Middle East','OSR Apex','OSR Australia/South Africa','OSR Eastern Europe','OSR France / Benelux','OSR Ger/Aus/Switz','OSR Iberia','OSR Italy','OSR Japan','OSR Latin America','OSR Mediterranean','OSR Nordic','OSR North America','OSR Turkey','OSR UK'))
    [InlineData(true, 1188, DataSourceInstance.Eclipse, "Willis Limited", null, "Ownership Marine", null, "OSR Africa/Middle East", null, null, null, null, null, null, null, null, null)]
    // Rule: 1190: (pol.orglevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.orglevel3 = 'Ownership Marine' AND pol.orglevel5 IN ('ITL Americas Latin America','ITL North America'))
    [InlineData(true, 1190, DataSourceInstance.Eclipse, "Willis Limited", null, "Ownership Marine", null, "ITL Americas Latin America", null, null, null, null, null, null, null, null, null)]
    // Rule: 1192: (pol.orglevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.orglevel3 = 'Ownership Marine' AND pol.orglevel5 IN ('ITL Europe Eastern Europe','ITL Europe France / Benelux','ITL Europe Ger/Aus/Switz','ITL Europe Italy','ITL Europe Mediterranean','ITL Europe Nordic', 'ITL Europe Iberia))
    [InlineData(true, 1192, DataSourceInstance.Eclipse, "Willis Limited", null, "Ownership Marine", null, "ITL Europe Eastern Europe", null, null, null, null, null, null, null, null, null)]
    [InlineData(true, 1192, DataSourceInstance.Eclipse, "Willis Limited", null, "Ownership Marine", null, "ITL Europe France / Benelux", null, null, null, null, null, null, null, null, null)]
    [InlineData(true, 1192, DataSourceInstance.Eclipse, "Willis Limited", null, "Ownership Marine", null, "ITL Europe Ger/Aus/Switz", null, null, null, null, null, null, null, null, null)]
    [InlineData(true, 1192, DataSourceInstance.Eclipse, "Willis Limited", null, "Ownership Marine", null, "ITL Europe Italy", null, null, null, null, null, null, null, null, null)]
    [InlineData(true, 1192, DataSourceInstance.Eclipse, "Willis Limited", null, "Ownership Marine", null, "ITL Europe Mediterranean", null, null, null, null, null, null, null, null, null)]
    [InlineData(true, 1192, DataSourceInstance.Eclipse, "Willis Limited", null, "Ownership Marine", null, "ITL Europe Nordic", null, null, null, null, null, null, null, null, null)]
    [InlineData(true, 1192, DataSourceInstance.Eclipse, "Willis Limited", null, "Ownership Marine", null, "ITL Europe Iberia", null, null, null, null, null, null, null, null, null)]
    // Rule: 1194: (pol.orglevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.orglevel3 = 'Ownership Marine' AND pol.orglevel5 IN ('ITL Freight & Logistics'))
    [InlineData(true, 1194, DataSourceInstance.Eclipse, "Willis Limited", null, "Ownership Marine", null, "ITL Freight & Logistics", null, null, null, null, null, null, null, null, null)]
    // Rule: 1196: (pol.orglevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.orglevel3 = 'Ownership Marine' AND pol.orglevel5 IN ('ITL RoW Africa/Middle East','ITL RoW Apex','ITL RoW Australia/South Africa','ITL Row India','ITL RoW Japan'))
    [InlineData(true, 1196, DataSourceInstance.Eclipse, "Willis Limited", null, "Ownership Marine", null, "ITL RoW Africa/Middle East", null, null, null, null, null, null, null, null, null)]
    // Rule: 1198: (pol.orglevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.orglevel3 = 'Ownership Marine' AND pol.orglevel5 IN ('ITL UK UK'))
    [InlineData(true, 1198, DataSourceInstance.Eclipse, "Willis Limited", null, "Ownership Marine", null, "ITL UK UK", null, null, null, null, null, null, null, null, null)]
    // Rule: 1200: (pol.orglevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.orglevel3 = 'Ownership FAJS' AND pol.orglevel5 IN ('FAJS Cash In Transit','FAJS Cash In Transit (Ipswich)'))
    [InlineData(true, 1200, DataSourceInstance.Eclipse, "Willis Limited", null, "Ownership FAJS", null, "FAJS Cash In Transit", null, null, null, null, null, null, null, null, null)]
    // Rule: 1202: (pol.orglevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.orglevel3 = 'Ownership FAJS' AND pol.orglevel5 IN ('FAJS Fine Art','FAJS Fine Art (Ipswich)'))
    [InlineData(true, 1202, DataSourceInstance.Eclipse, "Willis Limited", null, "Ownership FAJS", null, "FAJS Fine Art", null, null, null, null, null, null, null, null, null)]
    // Rule: 1204: (pol.orglevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.orglevel3 = 'Ownership FAJS' AND pol.orglevel5 IN ('FAJS General Specie','FAJS General Specie (Ipswich)'))
    [InlineData(true, 1204, DataSourceInstance.Eclipse, "Willis Limited", null, "Ownership FAJS", null, "FAJS General Specie", null, null, null, null, null, null, null, null, null)]
    // Rule: 1206: (pol.orglevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.orglevel3 = 'Ownership FAJS' AND pol.orglevel5 IN ('FAJS ROWJB','FAJS ROWJB (Ipswich)','FAJS USJB','FAJS USJB (Ipswich)'))
    [InlineData(true, 1206, DataSourceInstance.Eclipse, "Willis Limited", null, "Ownership FAJS", null, "FAJS ROWJB", null, null, null, null, null, null, null, null, null)]
    // Rule: 1270: (pol.OrgLevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.OrgLevel2 IN ('Financial Solutions') AND pol.OrgLevel4 IN ('International') AND pol.OrgLevel5 LIKE 'European%')
    [InlineData(true, 1270, DataSourceInstance.Eclipse, "Willis Limited", "Financial Solutions", null, "International", "European123", null, null, null, null, null, null, null, null, null)]
    // Rule: 1278: pol.[OrgLevel1] in ('Willis (Singapore) Pte Ltd (Reinsurance)') AND [OrgLevel2] = 'Retail (Singapore)' AND pol.[OrgLevel5] = 'Facultative RI (Singapore)'
    //Rule has been disabled
    //[InlineData(true, 1278, DataSourceInstance.Eclipse, "Willis (Singapore) Pte Ltd (Reinsurance)", "Retail (Singapore)", null, null, "Facultative RI (Singapore)", null, null, null, null, null, null, null, null, null, null, 9, null)]
    //[InlineData(false, 1278, DataSourceInstance.Eclipse, "Willis (Singapore) Pte Ltd (Reinsurance)", "Retail (Singapore)", null, null, "Energy Upstream (Singapore)", null, null, null, null, null, null, null, null, null, null, 24, null)]
    // Rule: 1286: (pol.orglevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.orglevel2 LIKE 'Finex%' AND pol.orglevel5 IN ('PI - Reg - Tech/TMT', 'Cyber - Mid Market', 'Cyber - UK Regions'))
    [InlineData(true, 1286, DataSourceInstance.Eclipse, "Willis Limited", "Finex123", null, null, "PI - Reg - Tech/TMT", null, null, null, null, null, null, null, null, null)]
    [InlineData(true, 1286, DataSourceInstance.Eclipse, "Willis Limited", "Finex123", null, null, "Cyber - Mid Market", null, null, null, null, null, null, null, null, null)]
    [InlineData(true, 1286, DataSourceInstance.Eclipse, "Willis Limited", "Finex123", null, null, "Cyber - UK Regions", null, null, null, null, null, null, null, null, null)]
    // Rule: 1288: (pol.orglevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.orglevel2 LIKE 'Finex%' AND pol.orglevel5 IN ('Cyber - Retail', 'Cyber - UK Large'))
    [InlineData(true, 1288, DataSourceInstance.Eclipse, "Willis Limited", "Finex123", null, null, "Cyber - Retail", null, null, null, null, null, null, null, null, null)]
    [InlineData(true, 1288, DataSourceInstance.Eclipse, "Willis Limited", "Finex123", null, null, "Cyber - UK Large", null, null, null, null, null, null, null, null, null)]
    // Rule: 1290: (pol.orglevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.orglevel2 LIKE 'Finex%' AND pol.orglevel5 IN ('Cyber - Wholesale', 'PI - Tech/TMT', 'Cyber - International', 'Cyber - US', 'Cyber - International Mid-Market'))
    [InlineData(true, 1290, DataSourceInstance.Eclipse, "Willis Limited", "Finex123", null, null, "Cyber - Wholesale", null, null, null, null, null, null, null, null, null)]
    [InlineData(true, 1290, DataSourceInstance.Eclipse, "Willis Limited", "Finex123", null, null, "PI - Tech/TMT", null, null, null, null, null, null, null, null, null)]
    [InlineData(true, 1290, DataSourceInstance.Eclipse, "Willis Limited", "Finex123", null, null, "Cyber - International", null, null, null, null, null, null, null, null, null)]
    [InlineData(true, 1290, DataSourceInstance.Eclipse, "Willis Limited", "Finex123", null, null, "Cyber - US", null, null, null, null, null, null, null, null, null)]
    [InlineData(true, 1290, DataSourceInstance.Eclipse, "Willis Limited", "Finex123", null, null, "Cyber - International Mid-Market", null, null, null, null, null, null, null, null, null)]
    // Rule: 1292: (pol.orglevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.orglevel2 LIKE 'Finex%' AND pol.orglevel5 IN ('D&O - International'))
    [InlineData(true, 1292, DataSourceInstance.Eclipse, "Willis Limited", "Finex123", null, null, "D&O - International", null, null, null, null, null, null, null, null, null)]
    // Rule: 1294: (pol.orglevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.orglevel2 LIKE 'Finex%' AND pol.orglevel5 IN ('D&O - North America'))
    [InlineData(true, 1294, DataSourceInstance.Eclipse, "Willis Limited", "Finex123", null, null, "D&O - North America", null, null, null, null, null, null, null, null, null)]
    // Rule: 1296: (pol.orglevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.orglevel2 LIKE 'Finex%' AND pol.orglevel5 IN ('D&O - UK'))
    [InlineData(true, 1296, DataSourceInstance.Eclipse, "Willis Limited", "Finex123", null, null, "D&O - UK", null, null, null, null, null, null, null, null, null)]
    // Rule: 1298: (pol.orglevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.orglevel2 LIKE 'Finex%' AND pol.orglevel5 IN ('Nat - Ips - D&O'))
    [InlineData(true, 1298, DataSourceInstance.Eclipse, "Willis Limited", "Finex123", null, null, "Nat - Ips - D&O", null, null, null, null, null, null, null, null, null)]
    // Rule: 1300: (pol.orglevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.orglevel2 LIKE 'Finex%' AND pol.orglevel5 IN ('FI - International','FI - Latin & Iberian'))
    [InlineData(true, 1300, DataSourceInstance.Eclipse, "Willis Limited", "Finex123", null, null, "FI - International", null, null, null, null, null, null, null, null, null)]
    // Rule: 1302: (pol.orglevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.orglevel2 LIKE 'Finex%' AND pol.orglevel5 IN ('FI - North America','FI - UK Global','FI - UK Large','FI - UK Mid Market'))
    [InlineData(true, 1302, DataSourceInstance.Eclipse, "Willis Limited", "Finex123", null, null, "FI - North America", null, null, null, null, null, null, null, null, null)]
    // Rule: 1304: (pol.orglevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.orglevel2 LIKE 'Finex%' AND pol.orglevel5 IN ('Nat - Ips - FI'))
    [InlineData(true, 1304, DataSourceInstance.Eclipse, "Willis Limited", "Finex123", null, null, "Nat - Ips - FI", null, null, null, null, null, null, null, null, null)]
    // Rule: 1306: (pol.orglevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.orglevel2 LIKE 'Finex%' AND pol.orglevel5 IN ('M&A (Transactional)'))
    [InlineData(true, 1306, DataSourceInstance.Eclipse, "Willis Limited", "Finex123", null, null, "M&A (Transactional)", null, null, null, null, null, null, null, null, null)]
    // Rule: 1307: (pol.orglevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.orglevel2 LIKE 'Finex%' AND pol.orglevel5 IN ('PI - ATE Legal Services'))
    [InlineData(true, 1307, DataSourceInstance.Eclipse, "Willis Limited", "Finex123", null, null, "PI - ATE Legal Services", null, null, null, null, null, null, null, null, null)]
    // Rule: 1308: (pol.orglevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.orglevel2 LIKE 'Finex%' AND pol.orglevel5 IN ('PI - Construction'))
    [InlineData(true, 1308, DataSourceInstance.Eclipse, "Willis Limited", "Finex123", null, null, "PI - Construction", null, null, null, null, null, null, null, null, null)]
    // Rule: 1310: (pol.orglevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.orglevel2 LIKE 'Finex%' AND pol.orglevel5 IN ('PI - Financial Services'))
    [InlineData(true, 1310, DataSourceInstance.Eclipse, "Willis Limited", "Finex123", null, null, "PI - Financial Services", null, null, null, null, null, null, null, null, null)]
    // Rule: 1312: (pol.orglevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.orglevel2 LIKE 'Finex%' AND pol.orglevel5 IN ('PI - International','PI - Global Professions Aus/NZ','PI - US Wholesale'))
    [InlineData(true, 1312, DataSourceInstance.Eclipse, "Willis Limited", "Finex123", null, null, "PI - International", null, null, null, null, null, null, null, null, null)]
    // Rule: 1314: (pol.orglevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.orglevel2 LIKE 'Finex%' AND pol.orglevel5 IN ('PI - Legal Services'))
    [InlineData(true, 1314, DataSourceInstance.Eclipse, "Willis Limited", "Finex123", null, null, "PI - Legal Services", null, null, null, null, null, null, null, null, null)]
    // Rule: 1316: (pol.orglevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.orglevel2 LIKE 'Finex%' AND pol.orglevel5 IN ('PI - UK Professions'))
    [InlineData(true, 1316, DataSourceInstance.Eclipse, "Willis Limited", "Finex123", null, null, "PI - UK Professions", null, null, null, null, null, null, null, null, null)]
    // Rule: 1318: (pol.orglevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.orglevel2 LIKE 'Finex%' AND pol.orglevel5 IN ('Nat - Ips - PI','Nat - Man - PI'))
    [InlineData(true, 1318, DataSourceInstance.Eclipse, "Willis Limited", "Finex123", null, null, "Nat - Ips - PI", null, null, null, null, null, null, null, null, null)]
    // Rule: 1320: (pol.orglevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.orglevel2 IN ('Financial Solutions','Surety') AND pol.orglevel5 = 'Surety')
    [InlineData(true, 1320, DataSourceInstance.Eclipse, "Willis Limited", "Financial Solutions", null, null, "Surety", null, null, null, null, null, null, null, null, null)]
    // Rule: 1329: (pol.orglevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.orglevel2 IN ('DIFC','FINEX Global') AND pol.orglevel5 IN ('Cyber - DIFC','D&O - DIFC','FI - DIFC ','PI - DIFC'))
    [InlineData(true, 1329, DataSourceInstance.Eclipse, "Willis Limited", "DIFC", null, null, "Cyber - DIFC", null, null, null, null, null, null, null, null, null)]
    // Rule: 1331: (pol.orglevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.orglevel2 IN ( 'GMI', 'DIFC') AND pol.orglevel5 IN ( 'Downstream - DIFC', 'NR Fac - DIFC'))
    [InlineData(true, 1331, DataSourceInstance.Eclipse, "Willis Limited", "GMI", null, null, "Downstream - DIFC", null, null, null, null, null, null, null, null, null)]
    // Rule: 1333: (pol.orglevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.orglevel2 LIKE 'Finex%' AND pol.orglevel5 IN ('FI - Mort Ind'))
    [InlineData(true, 1333, DataSourceInstance.Eclipse, "Willis Limited", "Finex123", null, null, "FI - Mort Ind", null, null, null, null, null, null, null, null, null)]
    // Rule: 1337: (pol.OrgLevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.OrgLevel4 = 'Ownership Energy' AND pol.OrgLevel5 IN ('Upstream - Fac'))
    [InlineData(true, 1337, DataSourceInstance.Eclipse, "Willis Limited", null, null, "Ownership Energy", "Upstream - Fac", null, null, null, null, null, null, null, null, null)]
    // Rule: 1340: (pol.OrgLevel1 IN ('Facultative','Willis Towers Watson SA NV') AND pol.OrgLevel2 IN ('Faber WL') AND pol.OrgLevel5 IN ('F C - Asia Pac','F P - Asia Pac'))
    [InlineData(true, 1340, DataSourceInstance.Eclipse, "Facultative", "Faber WL", null, null, "F C - Asia Pac", null, null, null, null, null, null, null, null, null)]
    // Rule: 1341: (pol.OrgLevel1 IN ('Facultative','Willis Towers Watson SA NV') AND pol.OrgLevel2 IN ('Faber WL') AND pol.OrgLevel5 IN ('F P - Australasia','F C - Australasia'))
    [InlineData(true, 1341, DataSourceInstance.Eclipse, "Facultative", "Faber WL", null, null, "F P - Australasia", null, null, null, null, null, null, null, null, null)]
    // Rule: 1342: (pol.OrgLevel1 IN ('Facultative','Willis Towers Watson SA NV') AND pol.OrgLevel2 IN ('Faber WL') AND pol.OrgLevel5 IN ('F C - MEA','F P - MEA'))
    [InlineData(true, 1342, DataSourceInstance.Eclipse, "Facultative", "Faber WL", null, null, "F C - MEA", null, null, null, null, null, null, null, null, null)]
    // Rule: 1346: (pol.OrgLevel1 IN ('Facultative','Willis Towers Watson SA NV') AND pol.OrgLevel2 IN ('Faber WL') AND pol.OrgLevel5 IN ('Structured Risks'))
    [InlineData(true, 1346, DataSourceInstance.Eclipse, "Facultative", "Faber WL", null, null, "Structured Risks", null, null, null, null, null, null, null, null, null)]
    // Rule: 1348: (pol.OrgLevel1 IN ('Facultative','Willis Towers Watson SA NV') AND pol.OrgLevel2 IN ('Faber WL') AND pol.OrgLevel5 IN ('Management'))
    [InlineData(true, 1348, DataSourceInstance.Eclipse, "Facultative", "Faber WL", null, null, "Management", null, null, null, null, null, null, null, null, null)]
    // Rule: 1350: (pol.OrgLevel1 IN ('Facultative','Willis Towers Watson SA NV') AND pol.OrgLevel2 IN ('Faber WL') AND pol.OrgLevel5 IN ('Other LoB'))
    [InlineData(true, 1350, DataSourceInstance.Eclipse, "Facultative", "Faber WL", null, null, "Other LoB", null, null, null, null, null, null, null, null, null)]
    // Rule: 1356: (pol.OrgLevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.OrgLevel2 = 'GMI' AND pol.OrgLevel5 IN ('Downstream - Fac'))
    [InlineData(true, 1356, DataSourceInstance.Eclipse, "Willis Limited", "GMI", null, null, "Downstream - Fac", null, null, null, null, null, null, null, null, null)]
    // Rule: 1358: (pol.orglevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.orglevel4 = 'Ownership FAJS' AND pol.orglevel5 = 'FAJS Private Clients')
    [InlineData(true, 1358, DataSourceInstance.Eclipse, "Willis Limited", null, null, "Ownership FAJS", "FAJS Private Clients", null, null, null, null, null, null, null, null, null)]
    // Rule: 1390: (pol.orglevel1 IN ('Willis Limited','Willis Towers Watson SA NV') AND pol.OrgLevel2 = 'Special Contingency Risks' AND pol.orglevel3 = 'Ownership SCR' AND pol.orglevel5 = 'Contingency')
    [InlineData(true, 1390, DataSourceInstance.Eclipse, "Willis Limited", "Special Contingency Risks", "Ownership SCR", null, "Contingency", null, null, null, null, null, null, null, null, null)]
    public void SegmentationRuleForEclipseTest(
        bool matches, /* Enables testing for when the rule should not match some values*/
        int ruleId,
        DataSourceInstance dataSourceInstance,
        string? orgLevel1, string? orgLevel2, string? orgLevel3, string? orgLevel4, string? orgLevel5,
        string? productKey,
        GlobalPartyRole? globalPartyRole,
        RefPolicyStatus? refPolicyStatus,
        string? facilityLineSlipRef,
        string? policySectionStatusKey,
        string? department,
        string? workerName,
        string? productClass,
        string? partyBusinessKey,
        string? expiryDateString = null,
        int? brokingSubSegmentId = null,
        string? policyParentKey = null)
    {
        Assert.Equal(expected: DataSourceInstance.Eclipse, actual: dataSourceInstance);

        dynamic policyRecord;

        CreateDataForSegmentationRule(
            out policyRecord,
            "Eclipse",
            dataSourceInstance,
            orgLevel1,
            orgLevel2,
            orgLevel3,
            orgLevel4,
            orgLevel5,
            productKey,
            globalPartyRole,
            refPolicyStatus,
            facilityLineSlipRef,
            policySectionStatusKey,
            null, //policyAttributeObjectType,
            null, //policyAttributeValue,
            department,
            workerName,
            productClass,
            partyBusinessKey,
            expiryDateString,
            brokingSubSegmentId,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            policyParentKey
            );

        RunSegmentationRules();

        CheckRuleResult(
            matches,
            ruleId,
            policyRecord.PolicyId);

        if(matches)
        {
            CheckOrgLevelsExist(
            orgLevel1: orgLevel1,
            orgLevel2: orgLevel2,
            orgLevel3: orgLevel3,
            orgLevel4: orgLevel4,
            orgLevel5: orgLevel5,
            orgLevel6: null);
        }
    }

#endif

#if EpicRenewalRulesCheck
    /// <summary>
    /// Tests for EPIC US and Canada
    /// </summary>
    /// <param name="matches"></param>
    /// <param name="ruleId"></param>
    /// <param name="dataSourceInstance"></param>
    /// <param name="globalPartyRoleID"></param>
    /// <param name="productKey"></param>
    /// <param name="policySectionStatusKey"></param>
    [Theory]
    // Rule: 66 DataSourceInstance.EpicUS: pol.GlobalPartyRoleID = 100 AND pol.PartyID IS NOT NULL AND pol.[ProductKey] NOT IN ('ACFS', 'ACNS', 'ALFC', 'ALPB', 'ALPJ', 'AOTS', 'ARVS', 'BKEX', 'CERT', 'CFEE', 'CLCL', 'CLHC', 'CNTG', 'CTF', 'FACC', 'FADV', 'FCYR', 'FEXT', 'FHCP', 'FILC', 'FPLC', 'FREF', 'FRET', 'FSUR', 'FSWU', 'FTPA', 'FUND', 'FWPT', 'FWRP', 'HFEE', 'MCOM', 'MDIF', 'PASS', 'PRGR', 'RFEE', 'SCR', 'SFEE', 'TPA', 'TXCO', 'TXFL', 'TXHC', 'TXHL', 'UFEE') AND pol.PolicySectionStatusKey IN ('105', '107', '111', '112', '113', '114', '116', '65538') AND ORA.RelatedOrganisationCode NOT IN ('SBT', 'NHE', 'MIP', 'CTP', 'LPI', 'FRE', 'WPO', 'VRM', 'WNA', 'WAA', 'BER', 'CAY', 'LTD', 'BEM', 'WRE', 'VET', 'WFS', 'WHR', 'WSE', 'VRT', 'HAW', 'WBV', 'PER', 'TST')                 AND ORD.RelatedOrganisationCode IN ('AER', 'ALT', 'CON', 'FCR', 'FCY', 'FFI', 'FIN', 'FSL', 'FWF', 'FWI', 'MCA', 'MFA', 'MFJ', 'MSO', 'MSY', 'NAR', 'P&C', 'TRA', 'MAR', 'M&A', 'TRC', 'RCC', 'RHL', 'T&L', 'PSE', 'R&D', 'HLS', 'T&T')
    [InlineData(true, 66, DataSourceInstance.EpicUS, GlobalPartyRole.Client, "XXXX", "105", "XXX", "AER")]
    [InlineData(true, 66, DataSourceInstance.EpicUS, GlobalPartyRole.Client, "XXXX", "65552", "XXX", "AER")]
    [InlineData(false, 66, DataSourceInstance.EpicUS, GlobalPartyRole.Client, "ACFS", "105", "XXX", "AER")]
    [InlineData(false, 66, DataSourceInstance.EpicUS, GlobalPartyRole.Client, "XXXX", "105", "SBT", "AER")]
    // Rule: 1164 DataSourceInstance.EpicCanada: PSS.PolicySectionStatusKey IN ('65537','107','65538','113','120','65553','65554') AND ORD.RelatedOrganisationCode IN ('AER','CON','COS','FCR','FCY','FFI','FIN','FSL','FWF','FWI','HLS','M&A','MAR','MCA','MFA','MFJ','MSO','MSY','NAR','P&C','PSE','R&D','RCC','RHL','SUR','T&L','T&T','TRC')
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65537", null, "AER")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "107", null, "AER")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65538", null, "AER")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "113", null, "AER")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "120", null, "AER")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65553", null, "AER")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65554", null, "AER")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65555", null, "AER")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65556", null, "AER")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65537", null, "CON")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "107", null, "CON")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65538", null, "CON")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "113", null, "CON")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "120", null, "CON")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65553", null, "CON")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65554", null, "CON")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65555", null, "CON")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65556", null, "CON")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65537", null, "COS")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "107", null, "COS")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65538", null, "COS")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "113", null, "COS")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "120", null, "COS")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65553", null, "COS")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65554", null, "COS")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65555", null, "COS")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65556", null, "COS")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65537", null, "FCR")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "107", null, "FCR")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65538", null, "FCR")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "113", null, "FCR")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "120", null, "FCR")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65553", null, "FCR")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65554", null, "FCR")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65555", null, "FCR")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65556", null, "FCR")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65537", null, "FCY")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "107", null, "FCY")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65538", null, "FCY")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "113", null, "FCY")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "120", null, "FCY")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65553", null, "FCY")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65554", null, "FCY")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65555", null, "FCY")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65556", null, "FCY")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65537", null, "FFI")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "107", null, "FFI")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65538", null, "FFI")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "113", null, "FFI")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "120", null, "FFI")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65553", null, "FFI")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65554", null, "FFI")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65555", null, "FFI")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65556", null, "FFI")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65537", null, "FIN")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "107", null, "FIN")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65538", null, "FIN")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "113", null, "FIN")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "120", null, "FIN")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65553", null, "FIN")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65554", null, "FIN")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65555", null, "FIN")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65556", null, "FIN")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65537", null, "FSL")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "107", null, "FSL")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65538", null, "FSL")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "113", null, "FSL")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "120", null, "FSL")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65553", null, "FSL")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65554", null, "FSL")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65555", null, "FSL")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65556", null, "FSL")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65537", null, "FWF")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "107", null, "FWF")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65538", null, "FWF")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "113", null, "FWF")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "120", null, "FWF")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65553", null, "FWF")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65554", null, "FWF")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65555", null, "FWF")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65556", null, "FWF")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65537", null, "FWI")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "107", null, "FWI")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65538", null, "FWI")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "113", null, "FWI")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "120", null, "FWI")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65553", null, "FWI")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65554", null, "FWI")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65555", null, "FWI")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65556", null, "FWI")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65537", null, "HLS")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "107", null, "HLS")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65538", null, "HLS")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "113", null, "HLS")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "120", null, "HLS")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65553", null, "HLS")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65554", null, "HLS")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65555", null, "HLS")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65556", null, "HLS")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65537", null, "M&A")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "107", null, "M&A")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65538", null, "M&A")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "113", null, "M&A")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "120", null, "M&A")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65553", null, "M&A")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65554", null, "M&A")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65555", null, "M&A")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65556", null, "M&A")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65537", null, "MAR")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "107", null, "MAR")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65538", null, "MAR")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "113", null, "MAR")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "120", null, "MAR")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65553", null, "MAR")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65554", null, "MAR")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65555", null, "MAR")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65556", null, "MAR")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65537", null, "MCA")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "107", null, "MCA")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65538", null, "MCA")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "113", null, "MCA")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "120", null, "MCA")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65553", null, "MCA")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65554", null, "MCA")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65555", null, "MCA")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65556", null, "MCA")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65537", null, "MFA")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "107", null, "MFA")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65538", null, "MFA")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "113", null, "MFA")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "120", null, "MFA")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65553", null, "MFA")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65554", null, "MFA")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65555", null, "MFA")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65556", null, "MFA")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65537", null, "MFJ")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "107", null, "MFJ")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65538", null, "MFJ")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "113", null, "MFJ")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "120", null, "MFJ")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65553", null, "MFJ")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65554", null, "MFJ")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65555", null, "MFJ")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65556", null, "MFJ")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65537", null, "MSO")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "107", null, "MSO")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65538", null, "MSO")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "113", null, "MSO")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "120", null, "MSO")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65553", null, "MSO")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65554", null, "MSO")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65555", null, "MSO")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65556", null, "MSO")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65537", null, "MSY")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "107", null, "MSY")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65538", null, "MSY")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "113", null, "MSY")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "120", null, "MSY")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65553", null, "MSY")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65554", null, "MSY")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65555", null, "MSY")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65556", null, "MSY")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65537", null, "NAR")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "107", null, "NAR")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65538", null, "NAR")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "113", null, "NAR")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "120", null, "NAR")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65553", null, "NAR")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65554", null, "NAR")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65555", null, "NAR")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65556", null, "NAR")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65537", null, "P&C")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "107", null, "P&C")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65538", null, "P&C")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "113", null, "P&C")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "120", null, "P&C")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65553", null, "P&C")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65554", null, "P&C")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65555", null, "P&C")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65556", null, "P&C")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65537", null, "PSE")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "107", null, "PSE")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65538", null, "PSE")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "113", null, "PSE")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "120", null, "PSE")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65553", null, "PSE")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65554", null, "PSE")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65555", null, "PSE")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65556", null, "PSE")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65537", null, "R&D")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "107", null, "R&D")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65538", null, "R&D")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "113", null, "R&D")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "120", null, "R&D")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65553", null, "R&D")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65554", null, "R&D")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65555", null, "R&D")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65556", null, "R&D")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65537", null, "RCC")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "107", null, "RCC")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65538", null, "RCC")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "113", null, "RCC")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "120", null, "RCC")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65553", null, "RCC")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65554", null, "RCC")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65555", null, "RCC")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65556", null, "RCC")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65537", null, "RHL")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "107", null, "RHL")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65538", null, "RHL")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "113", null, "RHL")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "120", null, "RHL")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65553", null, "RHL")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65554", null, "RHL")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65555", null, "RHL")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65537", null, "SUR")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "107", null, "SUR")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65538", null, "SUR")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "113", null, "SUR")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "120", null, "SUR")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65553", null, "SUR")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65554", null, "SUR")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65555", null, "SUR")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65556", null, "SUR")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65537", null, "T&T")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "107", null, "T&T")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65538", null, "T&T")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "113", null, "T&T")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "120", null, "T&T")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65553", null, "T&T")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65554", null, "T&T")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65555", null, "T&T")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65556", null, "T&T")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65537", null, "TRC")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "107", null, "TRC")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65538", null, "TRC")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "113", null, "TRC")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "120", null, "TRC")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65553", null, "TRC")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65554", null, "TRC")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65555", null, "TRC")]
    [InlineData(true, 1392, DataSourceInstance.EpicCanada, null, null, "65556", null, "TRC")]
    [InlineData(false, 1392, DataSourceInstance.EpicCanada, null, null, "99999", null, "XXX")]
    public void SegmentationRuleForEpicTest(
        bool matches, /* Enables testing for when the rule should not match some values*/
        int ruleId,
        DataSourceInstance dataSourceInstance,
        GlobalPartyRole? globalPartyRole,
        string? productKey,
        string policySectionStatusKey,
        string? agencyRelatedOrganisationCode = null,
        string? departmentRelatedOrganisationCode = null
        )
    {
        Assert.True(dataSourceInstance == DataSourceInstance.EpicUS || dataSourceInstance == DataSourceInstance.EpicCanada);

        dynamic policyRecord;

        CreateDataForSegmentationRule(
            out policyRecord,
            "Epic",
            dataSourceInstance,
            null,
            null,
            null,
            null,
            null,
            productKey,
           globalPartyRole,
            null,
            null,
            policySectionStatusKey,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            agencyRelatedOrganisationCode,
            departmentRelatedOrganisationCode,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null
            );

        RunSegmentationRules();

        CheckRuleResult(
            matches: matches,
            ruleId: ruleId,
            policyId: policyRecord.PolicyId);
    }



#endif


#if COLRenewalRulesCheck
    /// <summary>
    /// Tests for COL.
    /// Note that there are new versions of the rules that come into effect after a particular expiry date.
    /// Hence why there are two numbers
    /// </summary>
    /// <param name="matches"></param>
    /// <param name="ruleId"></param>
    /// <param name="dataSourceInstance"></param>
    /// <param name="personType"></param>
    /// <param name="isMainPolicy"></param>
    /// <param name="wtwIsLeadBroker"></param>
    /// <param name="organisationAttributeValue"></param>
    /// <param name="productAttributeValue"></param>
    /// <param name="tipoDocument"></param>
    /// <param name="subTipo"></param>
    /// <param name="productAttributeObjectType"></param>

    [Theory]
    // Rule: 11 DataSourceInstance.BrazilCOL: PT.PersonType NOT IN ( 'F', 'O' ) AND p.IsMainPolicy = '1' AND P.WTWIsLeadBroker = '1'  AND orgattr.[Value] = '67' AND PA.[Value] IN ('15.28.1','15.28.2','15.35.1','15.35.2','15.35.3','15.35.4','15.37.1','15.37.2','15.37.3','15.74.1','15.74.2','15.74.3','15.74.4','15.97.1','15.97.2') and D.Tipo_Documento =1  and D.sub_tipo in(1,24,25,28) AND p.ExpiryDate <= '2024-05-31'
    [InlineData(true, 11, DataSourceInstance.BrazilCOL, "X", 1, 1, "67", "15.28.1", 1, 1, "ColProductCode", "2024-05-31")]
    [InlineData(false, 11, DataSourceInstance.BrazilCOL, "O", 1, 1, "67", "15.28.1", 1, 1, "ColProductCode", "2024-05-31")]
    // Rule: 12 DataSourceInstance.BrazilCOL: PT.PersonType NOT IN ( 'F', 'O' ) AND p.IsMainPolicy = '1' AND P.WTWIsLeadBroker = '1'  AND orgattr.[Value] = '67' AND PA.[Value] IN ('5.20.1','5.24.1','5.25.1','5.31.3','5.42.1','5.53.1','5.53.3','5.53.6','5.53.7','5.53.8') and D.Tipo_Documento =1  and D.sub_tipo in(1,24,25,28) AND p.ExpiryDate <= '2024-05-31'
    [InlineData(true, 12, DataSourceInstance.BrazilCOL, "X", 1, 1, "67", "5.20.1", 1, 1, "ColProductCode", "2024-05-31")]
    [InlineData(false, 12, DataSourceInstance.BrazilCOL, "F", 1, 1, "67", "5.20.1", 1, 1, "ColProductCode", "2024-05-31")]
    // Rule: 14 DataSourceInstance.BrazilCOL: PT.PersonType NOT IN ( 'F', 'O' ) AND p.IsMainPolicy = '1' AND P.WTWIsLeadBroker = '1'  AND orgattr.[Value] = '67' AND PA.[Value] IN ('14.28.1','14.33.1','14.33.2','14.33.3','14.33.4','14.50.1','14.50.2','14.57.1','14.57.2','3.51.30','3.51.31') and D.Tipo_Documento =1  and D.sub_tipo in(1,24,25,28) AND p.ExpiryDate <= '2024-05-31'
    [InlineData(true, 14, DataSourceInstance.BrazilCOL, "X", 1, 1, "67", "14.28.1", 1, 1, "ColProductCode", "2024-05-31")]
    [InlineData(false, 14, DataSourceInstance.BrazilCOL, "F", 1, 1, "67", "14.28.1", 1, 1, "ColProductCode", "2024-05-31")]
    // Rule: 15 DataSourceInstance.BrazilCOL: PT.PersonType NOT IN ( 'F', 'O' ) AND p.IsMainPolicy = '1' AND P.WTWIsLeadBroker = '1'  AND orgattr.[Value] = '67' AND PA.[Value] IN ('1.14.1','1.14.2','1.15.1','1.16.1','1.18.1','1.41.1','1.67.2','1.67.3','1.67.7','1.71.1','1.71.2','1.71.3','1.71.4','1.71.5','1.71.6','1.71.7','1.71.9','1.71.10','1.71.11','1.71.12','1.71.13','1.71.14','1.71.15','1.71.16','1.71.17','1.71.18','1.71.19','1.71.20','1.71.21','1.71.22','1.71.23','1.71.24','1.71.25','1.71.26','1.71.27','1.71.29','1.71.30','1.71.32','1.95.1','1.96.1','1.96.2','14.17.1','15.39.1','15.40.1','3.51.34') and D.Tipo_Documento =1  and D.sub_tipo in(1,24,25,28) AND p.ExpiryDate <= '2024-05-31'
    [InlineData(true, 15, DataSourceInstance.BrazilCOL, "X", 1, 1, "67", "1.14.1", 1, 1, "ColProductCode", "2024-05-31")]
    [InlineData(false, 15, DataSourceInstance.BrazilCOL, "O", 1, 1, "67", "1.14.1", 1, 1, "ColProductCode", "2024-05-31")]
    // Rule: 16 DataSourceInstance.BrazilCOL: PT.PersonType NOT IN ( 'F', 'O' ) AND p.IsMainPolicy = '1' AND P.WTWIsLeadBroker = '1'  AND orgattr.[Value] = '67' AND PA.[Value] IN ('3.51.1','3.51.2','3.51.4','3.51.5','3.51.6','3.51.7','3.51.8','3.51.9','3.51.10','3.51.11','3.51.13','3.51.14','3.51.15','3.51.16','3.51.17','3.51.18','3.51.19','3.51.20','3.51.21','3.51.22','3.51.23','3.51.24','3.51.25','3.51.26','3.51.28','3.51.29','3.51.32','3.51.33') and D.Tipo_Documento =1  and D.sub_tipo in(1,24,25,28) AND p.ExpiryDate <= '2024-05-31'
    [InlineData(true, 16, DataSourceInstance.BrazilCOL, "X", 1, 1, "67", "3.51.1", 1, 1, "ColProductCode", "2024-05-31")]
    [InlineData(false, 16, DataSourceInstance.BrazilCOL, "F", 1, 1, "67", "3.51.1", 1, 1, "ColProductCode", "2024-05-31")]
    // Rule: 17 DataSourceInstance.BrazilCOL: PT.PersonType NOT IN ( 'F', 'O' ) AND p.IsMainPolicy = '1' AND P.WTWIsLeadBroker = '1'  AND orgattr.[Value] = '67' AND PA.[Value] IN ('1.96.3','1.96.4','17.34.1','17.34.2','17.34.3','17.34.4','17.34.5','18.72.1','2.34.1','2.72.1') and D.Tipo_Documento =1  and D.sub_tipo in(1,24,25,28) AND p.ExpiryDate <= '2024-05-31'
    [InlineData(true, 17, DataSourceInstance.BrazilCOL, "X", 1, 1, "67", "1.96.3", 1, 1, "ColProductCode", "2024-05-31")]
    [InlineData(false, 17, DataSourceInstance.BrazilCOL, "O", 1, 1, "67", "1.96.3", 1, 1, "ColProductCode", "2024-05-31")]
    // Rule: 18 DataSourceInstance.BrazilCOL: PT.PersonType NOT IN ( 'F', 'O' ) AND p.IsMainPolicy = '1' AND P.WTWIsLeadBroker = '1'  AND orgattr.[Value] = '67' AND PA.[Value] IN ('6.21.1','6.22.1','6.22.2','6.22.3','6.23.1','6.28.1','6.32.1','6.38.1','6.44.1','6.52.1','6.54.1','6.55.1','6.56.1','6.58.1') and D.Tipo_Documento =1  and D.sub_tipo in(1,24,25,28) AND p.ExpiryDate <= '2024-05-31'
    [InlineData(true, 18, DataSourceInstance.BrazilCOL, "X", 1, 1, "67", "6.21.1", 1, 1, "ColProductCode", "2024-05-31")]
    [InlineData(false, 18, DataSourceInstance.BrazilCOL, "F", 1, 1, "67", "6.21.1", 1, 1, "ColProductCode", "2024-05-31")]
    // Rule: 19 DataSourceInstance.BrazilCOL: PT.PersonType NOT IN ( 'F', 'O' ) AND p.IsMainPolicy = '1' AND P.WTWIsLeadBroker = '1'  AND orgattr.[Value] = '67' AND PA.[Value] IN  ('1.71.8','1.71.28','1.71.31','1.73.1','12.70.1','12.89.1','3.10.1','3.10.2','3.10.3','3.10.4','3.13.1','3.13.2','3.27.1','3.51.27','3.78.1','3.78.2','3.78.3','3.78.4','3.78.5','3.78.6','3.78.7','3.78.8','3.78.9','3.78.10','7.11.1','7.11.2','7.11.3','7.11.4','7.43.1') and D.Tipo_Documento =1  and D.sub_tipo in(1,24,25,28) AND p.ExpiryDate <= '2024-05-31'
    [InlineData(true, 19, DataSourceInstance.BrazilCOL, "X", 1, 1, "67", "1.71.8", 1, 1, "ColProductCode", "2024-05-31")]
    [InlineData(false, 19, DataSourceInstance.BrazilCOL, "O", 1, 1, "67", "1.71.8", 1, 1, "ColProductCode", "2024-05-31")]
    // Rule: 20 DataSourceInstance.BrazilCOL: PT.PersonType NOT IN ( 'F', 'O' ) AND p.IsMainPolicy = '1' AND P.WTWIsLeadBroker = '1'  AND orgattr.[Value] = '67' AND PA.[Value] IN ('7.46.1','7.48.1','7.49.1') AND D.Tipo_Documento = 1 AND D.sub_tipo IN(1,24,25,28) AND p.ExpiryDate <= '2024-05-31'
    [InlineData(true, 20, DataSourceInstance.BrazilCOL, "X", 1, 1, "67", "7.46.1", 1, 1, "ColProductCode", "2024-05-31")]
    [InlineData(false, 20, DataSourceInstance.BrazilCOL, "F", 1, 1, "67", "7.46.1", 1, 1, "ColProductCode", "2024-05-31")]
    // Rule: 21 DataSourceInstance.BrazilCOL: PT.PersonType NOT IN ( 'F', 'O' ) AND p.IsMainPolicy = '1' AND P.WTWIsLeadBroker = '1'  AND orgattr.[Value] = '67' AND PA.[Value] IN ('11.05.1','11.06.1','11.07.1','11.08.1','11.09.1','11.30.1','11.62.1','11.64.1','11.98.1') AND D.Tipo_Documento = 1 AND D.sub_tipo IN(1,24,25,28) AND p.ExpiryDate <= '2024-05-31'
    [InlineData(true, 21, DataSourceInstance.BrazilCOL, "X", 1, 1, "67", "11.05.1", 1, 1, "ColProductCode", "2024-05-31")]
    [InlineData(false, 21, DataSourceInstance.BrazilCOL, "O", 1, 1, "67", "11.05.1", 1, 1, "ColProductCode", "2024-05-31")]
    // Rule: 1277 DataSourceInstance.BrazilCOL: PT.PersonType NOT IN ( 'F', 'O' ) AND p.IsMainPolicy = '1' AND P.WTWIsLeadBroker = '1'  AND orgattr.[Value] = '67' AND PA.[Value] IN ('11.01.1','11.02.1','11.03.1','11.04.1') and D.Tipo_Documento =1  and D.sub_tipo in(1,24,25,28) AND p.ExpiryDate <= '2024-05-31'
    [InlineData(true, 1277, DataSourceInstance.BrazilCOL, "X", 1, 1, "67", "11.01.1", 1, 1, "ColProductCode", "2024-05-31")]
    [InlineData(false, 1277, DataSourceInstance.BrazilCOL, "F", 1, 1, "67", "11.01.1", 1, 1, "ColProductCode", "2024-05-31")]
    [InlineData(false, 1277, DataSourceInstance.BrazilCOL, "X", 1, 1, "67", "11.01.1", 1, 1, "ColProductCode", "2024-06-01")]
    // Rule: 1360 DataSourceInstance.BrazilCOL: PT.PersonType NOT IN ( 'F', 'O' ) AND p.IsMainPolicy = '1' AND P.WTWIsLeadBroker = '1'  AND orgattr.[Value] = '67' AND PA.[Value] IN ('15.28.1','15.28.2','15.35.1','15.35.2','15.35.3','15.35.4','15.37.1','15.37.2','15.37.3','15.74.1','15.74.2','15.74.3','15.74.4','15.97.1','15.97.2') and D.Tipo_Documento =1  and D.sub_tipo in(1,24,25,28) AND p.ExpiryDate >= '2024-06-01''
    [InlineData(true, 1360, DataSourceInstance.BrazilCOL, "X", 1, 1, "67", "15.28.1", 1, 1, "ColProductCode", "2024-06-01")]
    [InlineData(false, 1360, DataSourceInstance.BrazilCOL, "F", 1, 1, "67", "15.28.1", 1, 1, "ColProductCode", "2024-06-01")]
    // Rule: 1362 DataSourceInstance.BrazilCOL: PT.PersonType NOT IN ( 'F', 'O' ) AND p.IsMainPolicy = '1' AND P.WTWIsLeadBroker = '1'  AND orgattr.[Value] = '67' AND PA.[Value] IN ('5.20.1','5.25.1','5.31.3','5.53.1','5.53.3','5.53.7') and D.Tipo_Documento =1  and D.sub_tipo in(1,24,25,28) AND p.ExpiryDate >= '2024-06-01''
    [InlineData(true, 1362, DataSourceInstance.BrazilCOL, "X", 1, 1, "67", "5.20.1", 1, 1, "ColProductCode", "2024-06-01")]
    [InlineData(false, 1362, DataSourceInstance.BrazilCOL, "F", 1, 1, "67", "5.20.1", 1, 1, "ColProductCode", "2024-06-01")]
    // Rule: 1364 DataSourceInstance.BrazilCOL: PT.PersonType NOT IN ( 'F', 'O' ) AND p.IsMainPolicy = '1' AND P.WTWIsLeadBroker = '1'  AND orgattr.[Value] = '67' AND PA.[Value] IN ('1.67.1','1.67.10','1.67.4','1.67.5','1.67.6','1.67.8','1.67.9','3.51.3') and D.Tipo_Documento =1  and D.sub_tipo in(1,24,25,28) AND p.ExpiryDate >= '2024-06-01''
    [InlineData(true, 1364, DataSourceInstance.BrazilCOL, "X", 1, 1, "67", "1.67.1", 1, 1, "ColProductCode", "2024-06-01")]
    [InlineData(false, 1364, DataSourceInstance.BrazilCOL, "F", 1, 1, "67", "1.67.1", 1, 1, "ColProductCode", "2024-06-01")]
    // Rule: 1366 DataSourceInstance.BrazilCOL: PT.PersonType NOT IN ( 'F', 'O' ) AND p.IsMainPolicy = '1' AND P.WTWIsLeadBroker = '1'  AND orgattr.[Value] = '67' AND PA.[Value] IN ('14.57.2','3.51.30') and D.Tipo_Documento =1  and D.sub_tipo in(1,24,25,28) AND p.ExpiryDate >= '2024-06-01'
    [InlineData(true, 1366, DataSourceInstance.BrazilCOL, "X", 1, 1, "67", "14.57.2", 1, 1, "ColProductCode", "2024-06-01")]
    [InlineData(false, 1366, DataSourceInstance.BrazilCOL, "F", 1, 1, "67", "14.57.2", 1, 1, "ColProductCode", "2024-06-01")]
    // Rule: 1368 DataSourceInstance.BrazilCOL: PT.PersonType NOT IN ( 'F', 'O' ) AND p.IsMainPolicy = '1' AND P.WTWIsLeadBroker = '1'  AND orgattr.[Value] = '67' AND PA.[Value] IN ('1.14.1','1.14.2','1.15.1','1.16.1','1.18.1','1.41.1','1.71.1','1.71.10','1.71.11','1.71.12','1.71.14','1.71.15','1.71.16','1.71.19','1.71.21','1.71.25','1.71.26','1.71.27','1.71.29','1.71.30','1.71.31','1.71.32','1.71.4','1.71.9','1.95.1','1.96.1','1.96.2','14.17.1','3.13.1','3.51.34') and D.Tipo_Documento =1  and D.sub_tipo in(1,24,25,28) AND p.ExpiryDate >= '2024-06-01''
    [InlineData(true, 1368, DataSourceInstance.BrazilCOL, "X", 1, 1, "67", "1.14.1", 1, 1, "ColProductCode", "2024-06-01")]
    [InlineData(false, 1368, DataSourceInstance.BrazilCOL, "F", 1, 1, "67", "1.14.1", 1, 1, "ColProductCode", "2024-06-01")]
    // Rule: 1370 DataSourceInstance.BrazilCOL: PT.PersonType NOT IN ( 'F', 'O' ) AND p.IsMainPolicy = '1' AND P.WTWIsLeadBroker = '1'  AND orgattr.[Value] = '67' AND PA.[Value] IN ('3.51.1','3.51.11','3.51.13','3.51.14','3.51.15','3.51.17','3.51.2','3.51.21','3.51.22','3.51.23','3.51.25','3.51.28','3.51.29','3.51.32','3.51.33','3.51.4','3.51.5','3.51.6','3.51.8','3.51.9') and D.Tipo_Documento =1  and D.sub_tipo in(1,24,25,28) AND p.ExpiryDate >= '2024-06-01''
    [InlineData(true, 1370, DataSourceInstance.BrazilCOL, "X", 1, 1, "67", "3.51.1", 1, 1, "ColProductCode", "2024-06-01")]
    [InlineData(false, 1370, DataSourceInstance.BrazilCOL, "F", 1, 1, "67", "3.51.1", 1, 1, "ColProductCode", "2024-06-01")]
    // Rule: 1372 DataSourceInstance.BrazilCOL: PT.PersonType NOT IN ( 'F', 'O' ) AND p.IsMainPolicy = '1' AND P.WTWIsLeadBroker = '1'  AND orgattr.[Value] = '67' AND PA.[Value] IN ('1.96.3','1.96.4','17.34.1','17.34.2','17.34.3','17.34.4','17.34.5','18.72.1') and D.Tipo_Documento =1  and D.sub_tipo in(1,24,25,28) AND p.ExpiryDate >= '2024-06-01''
    [InlineData(true, 1372, DataSourceInstance.BrazilCOL, "X", 1, 1, "67", "1.96.3", 1, 1, "ColProductCode", "2024-06-01")]
    [InlineData(false, 1372, DataSourceInstance.BrazilCOL, "F", 1, 1, "67", "1.96.3", 1, 1, "ColProductCode", "2024-06-01")]
    // Rule: 1374 DataSourceInstance.BrazilCOL: PT.PersonType NOT IN ( 'F', 'O' ) AND p.IsMainPolicy = '1' AND P.WTWIsLeadBroker = '1'  AND orgattr.[Value] = '67' AND PA.[Value] IN ('6.21.1','6.22.1','6.22.2','6.22.3','6.23.1','6.28.1','6.32.1','6.38.1','6.44.1','6.52.1','6.54.1','6.55.1','6.56.1','6.58.1') and D.Tipo_Documento =1  and D.sub_tipo in(1,24,25,28) AND p.ExpiryDate >= '2024-06-01''
    [InlineData(true, 1374, DataSourceInstance.BrazilCOL, "X", 1, 1, "67", "6.21.1", 1, 1, "ColProductCode", "2024-06-01")]
    [InlineData(false, 1374, DataSourceInstance.BrazilCOL, "F", 1, 1, "67", "6.21.1", 1, 1, "ColProductCode", "2024-06-01")]
    // Rule: 1376 DataSourceInstance.BrazilCOL: PT.PersonType NOT IN ( 'F', 'O' ) AND p.IsMainPolicy = '1' AND P.WTWIsLeadBroker = '1'  AND orgattr.[Value] = '67' AND PA.[Value] IN  ('1.71.28','1.73.1','12.89.1','3.10.1','3.10.2','3.10.3','3.10.4','3.13.2','3.27.1','3.51.27','3.78.1','3.78.10','3.78.2','3.78.3','3.78.4','3.78.5','3.78.6','3.78.7','3.78.8','3.78.9','7.11.2','7.11.3','7.11.4','7.43.1') and D.Tipo_Documento =1  and D.sub_tipo in(1,24,25,28) AND p.ExpiryDate >= '2024-06-01''
    [InlineData(true, 1376, DataSourceInstance.BrazilCOL, "X", 1, 1, "67", "1.71.28", 1, 1, "ColProductCode", "2024-06-01")]
    [InlineData(false, 1376, DataSourceInstance.BrazilCOL, "F", 1, 1, "67", "1.71.28", 1, 1, "ColProductCode", "2024-06-01")]
    // Rule: 1378 DataSourceInstance.BrazilCOL: PT.PersonType NOT IN ( 'F', 'O' ) AND p.IsMainPolicy = '1' AND P.WTWIsLeadBroker = '1'  AND orgattr.[Value] = '67' AND PA.[Value] IN ('7.48.1','7.49.1') AND D.Tipo_Documento = 1 AND D.sub_tipo IN(1,24,25,28) AND p.ExpiryDate >= '2024-06-01''
    [InlineData(true, 1378, DataSourceInstance.BrazilCOL, "X", 1, 1, "67", "7.48.1", 1, 1, "ColProductCode", "2024-06-01")]
    [InlineData(false, 1378, DataSourceInstance.BrazilCOL, "F", 1, 1, "67", "7.48.1", 1, 1, "ColProductCode", "2024-06-01")]
    // Rule: 1380 DataSourceInstance.BrazilCOL: PT.PersonType NOT IN ( 'F', 'O' ) AND p.IsMainPolicy = '1' AND P.WTWIsLeadBroker = '1'  AND orgattr.[Value] = '67' AND PA.[Value] IN ('11.01.1','11.02.1','11.03.1','11.04.1','11.05.1','11.06.1','11.07.1','11.08.1','11.09.1','11.30.1','11.62.1','11.64.1') AND D.Tipo_Documento = 1 AND D.sub_tipo IN(1,24,25,28) AND p.ExpiryDate >= '2024-06-01''
    [InlineData(true, 1380, DataSourceInstance.BrazilCOL, "X", 1, 1, "67", "11.05.1", 1, 1, "ColProductCode", "2024-06-01")]
    [InlineData(false, 1380, DataSourceInstance.BrazilCOL, "F", 1, 1, "67", "11.05.1", 1, 1, "ColProductCode", "2024-06-01")]
    public void SegmentationRuleForColTest(
        bool matches, /* Enables testing for when the rule should not match some values*/
        int ruleId,
        DataSourceInstance dataSourceInstance,
        string? personType,
        int? isMainPolicy,
        int? wtwIsLeadBroker,
        string? organisationAttributeValue,
        string? productAttribute,
        int? tipoDocument,
        int? subTipo,
        string? productAttributeObjectType,
        string? expiryDateString = null
        )
    {
        Assert.Equal(expected: DataSourceInstance.BrazilCOL, actual: dataSourceInstance);

        dynamic policyRecord;

        CreateDataForSegmentationRule(
            out policyRecord,
            "COL",
            dataSourceInstance,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            expiryDateString,
            null,
            null,
            null,
            null,
            null,
            null,
            personType,
            isMainPolicy,
            wtwIsLeadBroker,
            organisationAttributeValue,
            null,
            tipoDocument,
            subTipo,
            productAttribute,
            null,
            null,
            productAttributeObjectType,
            null
            );

        dynamic r = GetResultRow(tableName: "Rules.PoliciesAndClients");
        RunSegmentationRules();

        CheckRuleResult(
            matches,
            ruleId,
            policyRecord.PolicyId);
    }

#endif

#if eGlobalRenewalRulesCheck

    /// <summary>
    /// Tests for eGlobals
    /// </summary>
    /// <param name="matches"></param>
    /// <param name="ruleId"></param>
    /// <param name="dataSourceInstance"></param>
    /// <param name="orgLevel1"></param>
    /// <param name="orgLevel2"></param>
    /// <param name="orgLevel3"></param>
    /// <param name="orgLevel4"></param>
    /// <param name="orgLevel5"></param>
    /// <param name="productKey"></param>
    /// <param name="globalPartyRole"></param>
    /// <param name="refPolicyStatus"></param>
    /// <param name="renewableFlag"></param>
    /// <param name="facilityLineSlipRef"></param>
    /// <param name="policySectionStatusKey"></param>
    /// <param name="policyAttributeObjectType"></param>
    /// <param name="policyAttributeValue"></param>
    /// <param name="department"></param>
    /// <param name="workerName"></param>
    /// <param name="productClass"></param>
    /// <param name="partyBusinessKey"></param>
    /// <param name="expiryDateString"></param>
    /// <param name="brokingSubSegmentId"></param>    ///
    /// <param name="productAttributeObjectType"></param>
    [Theory]
    // Rule: 1085 DataSourceInstance.eGlobalChina: P.Department = 'AER'
    [InlineData(true, 1085, DataSourceInstance.eGlobalChina, null, null, null, null, null, null, null, null, null, null, null, null, "AER", null, null, null, null, null, null, null, null, null)]
    // Rule: 1087 DataSourceInstance.eGlobalChina: P.Department = 'AFF' AND W.WorkerName in ('Robert Shi','Frank Yan','Kelvin Zhong','Mo Li','Rong Xie','Doris Zhou')
    [InlineData(true, 1087, DataSourceInstance.eGlobalChina, null, null, null, null, null, null, null, null, null, null, null, null, "AFF", "Robert Shi", null, null, null, null, null, null, null, null)]
    // Rule: 1089 DataSourceInstance.eGlobalChina: P.Department = 'AFF' AND W.WorkerName in ('Tommy Xie')
    [InlineData(true, 1089, DataSourceInstance.eGlobalChina, null, null, null, null, null, null, null, null, null, null, null, null, "AFF", "Tommy Xie", null, null, null, null, null, null, null, null)]
    // Rule: 1091 DataSourceInstance.eGlobalChina: P.Department IN ('CON','CPV')
    [InlineData(true, 1091, DataSourceInstance.eGlobalChina, null, null, null, null, null, null, null, null, null, null, null, null, "CON", null, null, null, null, null, null, null, null, null)]
    // Rule: 1093 DataSourceInstance.eGlobalChina: P.Department = 'REI'
    [InlineData(true, 1093, DataSourceInstance.eGlobalChina, null, null, null, null, null, null, null, null, null, null, null, null, "REI", null, null, null, null, null, null, null, null, null)]
    // Rule: 1095 DataSourceInstance.eGlobalChina: P.Department = 'RE2' AND W.WorkerName in ('Billy Bo','Lois Liu','Richard W Nie','Yingyi Tan')
    [InlineData(true, 1095, DataSourceInstance.eGlobalChina, null, null, null, null, null, null, null, null, null, null, null, null, "RE2", "Billy Bo", null, null, null, null, null, null, null, null)]
    // Rule: 1097 DataSourceInstance.eGlobalChina: P.Department = 'RE2' AND W.WorkerName in ('Jessica Zhang','Ivy Xu')
    [InlineData(true, 1097, DataSourceInstance.eGlobalChina, null, null, null, null, null, null, null, null, null, null, null, null, "RE2", "Jessica Zhang", null, null, null, null, null, null, null, null)]
    // Rule: 1099 DataSourceInstance.eGlobalChina: P.Department = 'FIN'
    [InlineData(true, 1099, DataSourceInstance.eGlobalChina, null, null, null, null, null, null, null, null, null, null, null, null, "FIN", null, null, null, null, null, null, null, null, null)]
    // Rule: 1101 DataSourceInstance.eGlobalChina: P.Department = 'SUR'
    [InlineData(true, 1101, DataSourceInstance.eGlobalChina, null, null, null, null, null, null, null, null, null, null, null, null, "SUR", null, null, null, null, null, null, null, null, null)]
    // Rule: 1103 DataSourceInstance.eGlobalChina: P.Department = 'TCR'
    [InlineData(true, 1103, DataSourceInstance.eGlobalChina, null, null, null, null, null, null, null, null, null, null, null, null, "TCR", null, null, null, null, null, null, null, null, null)]
    // Rule: 1105 DataSourceInstance.eGlobalChina: P.Department = 'SPO'
    [InlineData(true, 1105, DataSourceInstance.eGlobalChina, null, null, null, null, null, null, null, null, null, null, null, null, "SPO", null, null, null, null, null, null, null, null, null)]
    // Rule: 1107 DataSourceInstance.eGlobalChina: P.Department = 'OID'
    [InlineData(true, 1107, DataSourceInstance.eGlobalChina, null, null, null, null, null, null, null, null, null, null, null, null, "OID", null, null, null, null, null, null, null, null, null)]
    // Rule: 1109 DataSourceInstance.eGlobalChina: P.Department = 'MIN'
    [InlineData(true, 1109, DataSourceInstance.eGlobalChina, null, null, null, null, null, null, null, null, null, null, null, null, "MIN", null, null, null, null, null, null, null, null, null)]
    // Rule: 1111 DataSourceInstance.eGlobalChina: P.Department in ('OIU','TIJ')
    [InlineData(true, 1111, DataSourceInstance.eGlobalChina, null, null, null, null, null, null, null, null, null, null, null, null, "OIU", null, null, null, null, null, null, null, null, null)]
    // Rule: 1115 DataSourceInstance.eGlobalChina: P.Department in ('POW','ENS')
    [InlineData(true, 1115, DataSourceInstance.eGlobalChina, null, null, null, null, null, null, null, null, null, null, null, null, "POW", null, null, null, null, null, null, null, null, null)]
    // Rule: 1117 DataSourceInstance.eGlobalChina: P.Department = 'NJ2'
    [InlineData(true, 1117, DataSourceInstance.eGlobalChina, null, null, null, null, null, null, null, null, null, null, null, null, "NJ2", null, null, null, null, null, null, null, null, null)]
    // Rule: 1119 DataSourceInstance.eGlobalChina: P.Department = 'NAN'
    [InlineData(true, 1119, DataSourceInstance.eGlobalChina, null, null, null, null, null, null, null, null, null, null, null, null, "NAN", null, null, null, null, null, null, null, null, null)]
    // Rule: 1121 DataSourceInstance.eGlobalChina: P.Department = 'AGE'
    [InlineData(true, 1121, DataSourceInstance.eGlobalChina, null, null, null, null, null, null, null, null, null, null, null, null, "AGE", null, null, null, null, null, null, null, null, null)]
    // Rule: 1123 DataSourceInstance.eGlobalChina: P.Department = 'CMM'
    [InlineData(true, 1123, DataSourceInstance.eGlobalChina, null, null, null, null, null, null, null, null, null, null, null, null, "CMM", null, null, null, null, null, null, null, null, null)]
    // Rule: 1125 DataSourceInstance.eGlobalChina: P.Department = 'FB'
    [InlineData(true, 1125, DataSourceInstance.eGlobalChina, null, null, null, null, null, null, null, null, null, null, null, null, "FB", null, null, null, null, null, null, null, null, null)]
    // Rule: 1127 DataSourceInstance.eGlobalChina: P.Department = 'GTS'
    [InlineData(true, 1127, DataSourceInstance.eGlobalChina, null, null, null, null, null, null, null, null, null, null, null, null, "GTS", null, null, null, null, null, null, null, null, null)]
    // Rule: 1131 DataSourceInstance.eGlobalChina: P.Department in ('KUM','TJ2')
    [InlineData(true, 1131, DataSourceInstance.eGlobalChina, null, null, null, null, null, null, null, null, null, null, null, null, "KUM", null, null, null, null, null, null, null, null, null)]
    // Rule: 1133 DataSourceInstance.eGlobalChina: P.Department = 'CIB'
    [InlineData(true, 1133, DataSourceInstance.eGlobalChina, null, null, null, null, null, null, null, null, null, null, null, null, "CIB", null, null, null, null, null, null, null, null, null)]
    // Rule: 1135 DataSourceInstance.eGlobalChina: P.Department in ('GUZ','NWK') OR (P.Department = 'CR2' AND P.ExpiryDate >= '2023-04-01')
    [InlineData(true, 1135, DataSourceInstance.eGlobalChina, null, null, null, null, null, null, null, null, null, null, null, null, "CR2", null, null, null, "2023-04-01", null, null, null, null, null)]
    [InlineData(false, 1135, DataSourceInstance.eGlobalChina, null, null, null, null, null, null, null, null, null, null, null, null, "CR2", null, null, null, "2023-03-01", null, null, null, null, null)]
    [InlineData(true, 1135, DataSourceInstance.eGlobalChina, null, null, null, null, null, null, null, null, null, null, null, null, "GUZ", null, null, null, "2023-03-01", null, null, null, null, null)]
    // Rule: 1137 DataSourceInstance.eGlobalChina: P.Department = 'LIA'
    [InlineData(true, 1137, DataSourceInstance.eGlobalChina, null, null, null, null, null, null, null, null, null, null, null, null, "LIA", null, null, null, null, null, null, null, null, null)]
    // Rule: 1139 DataSourceInstance.eGlobalChina: P.Department = 'HLR'
    [InlineData(true, 1139, DataSourceInstance.eGlobalChina, null, null, null, null, null, null, null, null, null, null, null, null, "HLR", null, null, null, null, null, null, null, null, null)]
    // Rule: 1141 DataSourceInstance.eGlobalChina: P.Department = 'EC'
    [InlineData(true, 1141, DataSourceInstance.eGlobalChina, null, null, null, null, null, null, null, null, null, null, null, null, "EC", null, null, null, null, null, null, null, null, null)]
    // Rule: 1145 DataSourceInstance.eGlobalChina: P.Department = 'SM2'
    [InlineData(true, 1145, DataSourceInstance.eGlobalChina, null, null, null, null, null, null, null, null, null, null, null, null, "SM2", null, null, null, null, null, null, null, null, null)]
    // Rule: 1147 DataSourceInstance.eGlobalChina: P.Department in ('SM1','NAJ')
    [InlineData(true, 1147, DataSourceInstance.eGlobalChina, null, null, null, null, null, null, null, null, null, null, null, null, "SM1", null, null, null, null, null, null, null, null, null)]
    // Rule: 1149 DataSourceInstance.eGlobalChina: P.Department = 'TMT'
    [InlineData(true, 1149, DataSourceInstance.eGlobalChina, null, null, null, null, null, null, null, null, null, null, null, null, "TMT", null, null, null, null, null, null, null, null, null)]
    // Rule: 1151 DataSourceInstance.eGlobalChina: P.Department = 'CIJ'
    [InlineData(true, 1151, DataSourceInstance.eGlobalChina, null, null, null, null, null, null, null, null, null, null, null, null, "CIJ", null, null, null, null, null, null, null, null, null)]
    // Rule: 1153 DataSourceInstance.eGlobalChina: P.Department = 'PLM'
    [InlineData(true, 1153, DataSourceInstance.eGlobalChina, null, null, null, null, null, null, null, null, null, null, null, null, "PLM", null, null, null, null, null, null, null, null, null)]
    // Rule: 1335 DataSourceInstance.eGlobalHK: P.Department = 'CST'
    [InlineData(true, 1335, DataSourceInstance.eGlobalHK, null, null, null, null, null, null, null, null, null, null, null, null, "CST", null, null, null, null, null, null, null, null, null)]
    // Rule: 1033 DataSourceInstance.eGlobalHK: P.Department = 'CON'
    [InlineData(true, 1033, DataSourceInstance.eGlobalHK, null, null, null, null, null, null, null, null, null, null, null, null, "CON", null, null, null, null, null, null, null, null, null)]
    // Rule: 1037 DataSourceInstance.eGlobalHK: P.Department in ('GRM','COR')
    [InlineData(true, 1037, DataSourceInstance.eGlobalHK, null, null, null, null, null, null, null, null, null, null, null, null, "GRM", null, null, null, null, null, null, null, null, null)]
    // Rule: 1041 DataSourceInstance.eGlobalHK: P.Department = 'NWK'
    [InlineData(true, 1041, DataSourceInstance.eGlobalHK, null, null, null, null, null, null, null, null, null, null, null, null, "NWK", null, null, null, null, null, null, null, null, null)]
    // Rule: 1043 DataSourceInstance.eGlobalHK: P.Department = 'NBD'
    [InlineData(true, 1043, DataSourceInstance.eGlobalHK, null, null, null, null, null, null, null, null, null, null, null, null, "NBD", null, null, null, null, null, null, null, null, null)]
    // Rule: 1045 DataSourceInstance.eGlobalHK: P.Department = 'MAC'
    [InlineData(true, 1045, DataSourceInstance.eGlobalHK, null, null, null, null, null, null, null, null, null, null, null, null, "MAC", null, null, null, null, null, null, null, null, null)]
    // Rule: 1047 DataSourceInstance.eGlobalHK: P.Department = 'RCA'
    [InlineData(true, 1047, DataSourceInstance.eGlobalHK, null, null, null, null, null, null, null, null, null, null, null, null, "RCA", null, null, null, null, null, null, null, null, null)]
    // Rule: 1352 DataSourceInstance.eGlobalNetherlands: P.IsDeleted = 0
    [InlineData(true, 1352, DataSourceInstance.eGlobalNetherlands, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null)]
    // Rule: 1326 DataSourceInstance.eGlobalNewZealand: P.IsDeleted = 0
    [InlineData(true, 1326, DataSourceInstance.eGlobalNewZealand, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null)]
    // Rule: 1029 DataSourceInstance.eGlobalSouthAfrica: ((substring(pol1.ply_branch, CHARINDEX('|', pol1.ply_branch) + 1, LEN(pol1.ply_branch) ) <> 'GRM') OR (pa.value not like '%FEE%') OR (pa.value not like '%SASRIA%'))
    [InlineData(true, 1029, DataSourceInstance.eGlobalSouthAfrica, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "SOMETHING", null, null, "Product")]
    [InlineData(false, 1029, DataSourceInstance.eGlobalSouthAfrica, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "yyyFEExxx", null, null, "Product")]
    [InlineData(false, 1029, DataSourceInstance.eGlobalSouthAfrica, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "yyySASRIAxxx", null, null, "Product")]
    [InlineData(false, 1029, DataSourceInstance.eGlobalSouthAfrica, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "yyyFEExxx", null, "|GRM", "Product")]
    [InlineData(false, 1029, DataSourceInstance.eGlobalSouthAfrica, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "|GRM", "Product")]
    [InlineData(true, 1029, DataSourceInstance.eGlobalSouthAfrica, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "|XXX", "Product")]
    // Rule: 1327 DataSourceInstance.eGlobalSweden: P.IsDeleted = 0
    [InlineData(false, 1327, DataSourceInstance.eGlobalSweden, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null)]
    // Rule: 87 DataSourceInstance.eGlobalColumbia: Substring(P.department,1,2) = '03' AND ((pa.value = 'DIRECTORES Y ADMIN' And P.MarketSegment = 'LARGE ACCOUNT') OR (pa.value = 'GLOBAL BANCARIA' And P.MarketSegment = 'COMMERCIAL')
    // OR(pa.value = 'GLOBAL BANCARIA' And P.MarketSegment = 'LARGE ACCOUNT') OR(pa.value = 'GLOBAL BANCARIA' And P.MarketSegment = 'MIDDLE MARKET') OR(pa.value = 'INFID Y RIESG FINANC' And P.MarketSegment = 'COMMERCIAL')
    // OR(pa.value = 'INFID Y RIESG FINANC' And P.MarketSegment = 'LARGE ACCOUNT') OR(pa.value = 'INFID Y RIESG FINANC' And P.MarketSegment = 'MIDDLE MARKET') OR(pa.value = 'RC MEDICA' And P.MarketSegment = 'COMMERCIAL')
    // OR(pa.value = 'RC MEDICA' And P.MarketSegment = 'LARGE ACCOUNT') OR(pa.value = 'RC MEDICA' And P.MarketSegment = 'MIDDLE MARKET') OR(pa.value = 'RC MEDIOS' And P.MarketSegment = 'COMMERCIAL')
    // OR(pa.value = 'RC MEDIOS' And P.MarketSegment = 'LARGE ACCOUNT') OR(pa.value = 'RC MEDIOS' And P.MarketSegment = 'MIDDLE MARKET') OR(pa.value = 'RC PROFESIONAL (E&O)' And P.MarketSegment = 'COMMERCIAL')
    // OR(pa.value = 'RC PROFESIONAL (E&O)' And P.MarketSegment = 'LARGE ACCOUNT') OR(pa.value = 'RC PROFESIONAL (E&O)' And P.MarketSegment = 'MIDDLE MARKET') OR(pa.value = 'RC SERVIDORES PUBL' And P.MarketSegment = 'COMMERCIAL')
    // OR(pa.value = 'RC SERVIDORES PUBL' And P.MarketSegment = 'LARGE ACCOUNT') OR(pa.value = 'RC SERVIDORES PUBL' And P.MarketSegment = 'MIDDLE MARKET') OR(pa.value = 'RC TARJETAS' And P.MarketSegment = 'COMMERCIAL')
    // OR(pa.value = 'RC TARJETAS' And P.MarketSegment = 'LARGE ACCOUNT') OR(pa.value = 'RC TARJETAS' And P.MarketSegment = 'MIDDLE MARKET') OR(pa.value = 'RIESGO CIBERNETICO' And P.MarketSegment = 'COMMERCIAL')
    // OR(pa.value = 'RIESGO CIBERNETICO' And P.MarketSegment = 'LARGE ACCOUNT') OR(pa.value = 'RIESGO CIBERNETICO' And P.MarketSegment = 'MIDDLE MARKET'))
    [InlineData(true, 87, DataSourceInstance.eGlobalColumbia, null, null, null, null, null, null, null, null, null, null, null, null, "03999", null, null, null, null, null, "DIRECTORES Y ADMIN", "LARGE ACCOUNT", null, "Product")]
    // Rule: 88 DataSourceInstance.eGlobalColumbia: Substring(P.department,1,2) = '03' And ((pa.value = 'CONTROL POZOS' And P.MarketSegment = 'COMMERCIAL') OR (pa.value = 'CONTROL POZOS' And P.MarketSegment = 'LARGE ACCOUNT') OR (pa.value = 'CONTROL POZOS' And P.MarketSegment = 'MIDDLE MARKET')
    // OR(pa.value = 'COPROPIEDADES' And P.MarketSegment = 'LARGE ACCOUNT') OR(pa.value = 'EQUIPO ELECTRONICO' And P.MarketSegment = 'COMMERCIAL') OR(pa.value = 'EQUIPO ELECTRONICO' And P.MarketSegment = 'LARGE ACCOUNT')
    // OR(pa.value = 'EQUIPO ELECTRONICO' And P.MarketSegment = 'MIDDLE MARKET') OR(pa.value = 'EQUIPO Y MAQUINARIA' And P.MarketSegment = 'COMMERCIAL') OR(pa.value = 'EQUIPO Y MAQUINARIA' And P.MarketSegment = 'LARGE ACCOUNT')
    // OR(pa.value = 'EQUIPO Y MAQUINARIA' And P.MarketSegment = 'MIDDLE MARKET') OR(pa.value = 'INCENDIO' And P.MarketSegment = 'COMMERCIAL') OR(pa.value = 'INCENDIO' And P.MarketSegment = 'LARGE ACCOUNT')
    // OR(pa.value = 'INCENDIO' And P.MarketSegment = 'MIDDLE MARKET') OR(pa.value = 'INCENDIO DEUDORES' And P.MarketSegment = 'COMMERCIAL') OR(pa.value = 'INCENDIO DEUDORES' And P.MarketSegment = 'LARGE ACCOUNT')
    // OR(pa.value = 'INCENDIO DEUDORES' And P.MarketSegment = 'MIDDLE MARKET') OR(pa.value = 'MANEJO' And P.MarketSegment = 'LARGE ACCOUNT') OR(pa.value = 'MODULAR' And P.MarketSegment = 'COMMERCIAL')
    // OR(pa.value = 'MODULAR' And P.MarketSegment = 'LARGE ACCOUNT') OR(pa.value = 'MODULAR' And P.MarketSegment = 'MIDDLE MARKET') OR(pa.value = 'MONTAJE' And P.MarketSegment = 'COMMERCIAL')
    // OR(pa.value = 'MONTAJE' And P.MarketSegment = 'LARGE ACCOUNT') OR(pa.value = 'MONTAJE' And P.MarketSegment = 'MIDDLE MARKET') OR(pa.value = 'NAVEGACION CASCO' And P.MarketSegment = 'COMMERCIAL')
    // OR(pa.value = 'NAVEGACION CASCO' And P.MarketSegment = 'LARGE ACCOUNT') OR(pa.value = 'NAVEGACION CASCO' And P.MarketSegment = 'MIDDLE MARKET') OR(pa.value = 'RC' And P.MarketSegment = 'LARGE ACCOUNT') OR(pa.value = 'RC CLINICAS Y HOSPIT' And P.MarketSegment = 'COMMERCIAL') OR(pa.value = 'RC CLINICAS Y HOSPIT' And P.MarketSegment = 'LARGE ACCOUNT')
    // OR(pa.value = 'RC CLINICAS Y HOSPIT' And P.MarketSegment = 'MIDDLE MARKET') OR(pa.value = 'RC EXTRACON HIDROCAR' And P.MarketSegment = 'COMMERCIAL') OR(pa.value = 'RC EXTRACON HIDROCAR' And P.MarketSegment = 'LARGE ACCOUNT')
    // OR(pa.value = 'RC EXTRACON HIDROCAR' And P.MarketSegment = 'MIDDLE MARKET') OR(pa.value = 'TERRORISMO' And P.MarketSegment = 'COMMERCIAL') OR(pa.value = 'TERRORISMO' And P.MarketSegment = 'LARGE ACCOUNT')
    // OR(pa.value = 'TERRORISMO' And P.MarketSegment = 'MIDDLE MARKET') OR(pa.value = 'RC TERRORISMO' And P.MarketSegment = 'COMMERCIAL') OR(pa.value = 'RC TERRORISMO' And P.MarketSegment = 'LARGE ACCOUNT')
    // OR(pa.value = 'RC TERRORISMO' And P.MarketSegment = 'MIDDLE MARKET') OR(pa.value = 'RC TRANSPORTES' And P.MarketSegment = 'COMMERCIAL') OR(pa.value = 'RC TRANSPORTES' And P.MarketSegment = 'LARGE ACCOUNT')
    // OR(pa.value = 'RC TRANSPORTES' And P.MarketSegment = 'MIDDLE MARKET') OR(pa.value = 'ROT MAQUINARIA' And P.MarketSegment = 'COMMERCIAL') OR(pa.value = 'ROT MAQUINARIA' And P.MarketSegment = 'LARGE ACCOUNT')
    // OR(pa.value = 'ROT MAQUINARIA' And P.MarketSegment = 'MIDDLE MARKET') OR(pa.value = 'SEGURO AGRICOLA' And P.MarketSegment = 'COMMERCIAL') OR(pa.value = 'SEGURO AGRICOLA' And P.MarketSegment = 'LARGE ACCOUNT')
    // OR(pa.value = 'SEGURO AGRICOLA' And P.MarketSegment = 'MIDDLE MARKET') OR(pa.value = 'SEMOVIENTES' And P.MarketSegment = 'COMMERCIAL') OR(pa.value = 'SEMOVIENTES' And P.MarketSegment = 'LARGE ACCOUNT')
    // OR(pa.value = 'SEMOVIENTES' And P.MarketSegment = 'MIDDLE MARKET') OR(pa.value = 'SUSTRACCION' And P.MarketSegment = 'COMMERCIAL') OR(pa.value = 'SUSTRACCION' And P.MarketSegment = 'LARGE ACCOUNT')
    // OR(pa.value = 'SUSTRACCION' And P.MarketSegment = 'MIDDLE MARKET') OR(pa.value = 'TERREMOTO' And P.MarketSegment = 'COMMERCIAL') OR(pa.value = 'TERREMOTO' And P.MarketSegment = 'LARGE ACCOUNT')
    // OR(pa.value = 'TERREMOTO' And P.MarketSegment = 'MIDDLE MARKET') OR(pa.value = 'TERRORISMO' And P.MarketSegment = 'COMMERCIAL') OR(pa.value = 'TERRORISMO' And P.MarketSegment = 'LARGE ACCOUNT')
    // OR(pa.value = 'TERRORISMO' And P.MarketSegment = 'MIDDLE MARKET') OR(pa.value = 'TODO RIESGO' And P.MarketSegment = 'LARGE ACCOUNT') OR(pa.value = 'TODO RIESGO CONSTR' And P.MarketSegment = 'COMMERCIAL')
    // OR(pa.value = 'TODO RIESGO CONSTR' And P.MarketSegment = 'LARGE ACCOUNT') OR(pa.value = 'TODO RIESGO CONSTR' And P.MarketSegment = 'MIDDLE MARKET') OR(pa.value = 'TODO RIESGO DANO MAT' And P.MarketSegment = 'LARGE ACCOUNT')
    // OR(pa.value = 'TODO RIESGO MONT' And P.MarketSegment = 'COMMERCIAL') OR(pa.value = 'TODO RIESGO MONT' And P.MarketSegment = 'LARGE ACCOUNT') OR(pa.value = 'TODO RIESGO MONT' And P.MarketSegment = 'MIDDLE MARKET')
    // OR(pa.value = 'TRANSP MERCANCIAS' And P.MarketSegment = 'LARGE ACCOUNT') OR(pa.value = 'TRANSP VALORES' And P.MarketSegment = 'LARGE ACCOUNT') OR(pa.value = 'TRANSPORTE' And P.MarketSegment = 'LARGE ACCOUNT')
    // OR(pa.value = 'VIDRIOS PLANOS' And P.MarketSegment = 'COMMERCIAL') OR(pa.value = 'VIDRIOS PLANOS' And P.MarketSegment = 'LARGE ACCOUNT') OR(pa.value = 'VIDRIOS PLANOS' And P.MarketSegment = 'MIDDLE MARKET')
    // OR(pa.value = 'DRONES' And P.MarketSegment = 'COMMERCIAL') OR(pa.value = 'DRONES' And P.MarketSegment = 'LARGE ACCOUNT') OR(pa.value = 'DRONES' And P.MarketSegment = 'MIDDLE MARKET')
    // OR(pa.value = 'RC EN EXCESO' And P.MarketSegment = 'COMMERCIAL') OR(pa.value = 'RC EN EXCESO' And P.MarketSegment = 'LARGE ACCOUNT') OR(pa.value = 'RC EN EXCESO' And P.MarketSegment = 'MIDDLE MARKET'))
    [InlineData(true, 88, DataSourceInstance.eGlobalColumbia, null, null, null, null, null, null, null, null, null, null, null, null, "03999", null, null, null, null, null, "CONTROL POZOS", "COMMERCIAL", null, "Product")]
    // Rule: 1355 DataSourceInstance.eGlobalAustralia: P.IsDeleted = 0
    [InlineData(true, 1355, DataSourceInstance.eGlobalAustralia,
        null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null)]
    public void SegmentationRuleForeGlobalTest(
        bool matches, /* Enables testing for when the rule should not match some values*/
        int ruleId,
        DataSourceInstance dataSourceInstance,
        string? orgLevel1,
        string? orgLevel2,
        string? orgLevel3,
        string? orgLevel4,
        string? orgLevel5,
        string? productKey,
        GlobalPartyRole? globalPartyRole,
        RefPolicyStatus? refPolicyStatus,
        string? facilityLineSlipRef,
        string? policySectionStatusKey,
        string? policyAttributeObjectType,
        string? policyAttributeValue,
        string? department,
        string? workerName,
        string? productClass,
        string? partyBusinessKey,
        string? expiryDateString,
        int? brokingSubSegmentId,
        string? productAttribute,
        string? marketSegment,
        string? ply_branch,
        string? productAttributeObjectType)
    {
        dynamic policyRecord;

        CreateDataForSegmentationRule(
            out policyRecord,
            "eGlobal",
            dataSourceInstance,
            orgLevel1,
            orgLevel2,
            orgLevel3,
            orgLevel4,
            orgLevel5,
            productKey,
            globalPartyRole,
            refPolicyStatus,
            facilityLineSlipRef,
            policySectionStatusKey,
            policyAttributeObjectType,
            policyAttributeValue,
            department,
            workerName,
            productClass,
            partyBusinessKey,
            expiryDateString,
            brokingSubSegmentId,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            productAttribute,
            marketSegment,
            ply_branch,
            productAttributeObjectType,
            null
            );

        RunSegmentationRules();

        CheckRuleResult(
            matches,
            ruleId,
            policyRecord.PolicyId);

        if(matches)
        {
            CheckOrgLevelsExist(
            orgLevel1: orgLevel1,
            orgLevel2: orgLevel2,
            orgLevel3: orgLevel3,
            orgLevel4: orgLevel4,
            orgLevel5: orgLevel5,
            orgLevel6: null);
        }
    }
#endif

#if OtherRenewalRulesCheck

    /// <summary>
    /// Tests for systems that don't fall into a previous category
    /// </summary>
    /// <param name="matches"></param>
    /// <param name="ruleId"></param>
    /// <param name="dataSourceInstance"></param>
    /// <param name="orgLevel1"></param>
    /// <param name="orgLevel2"></param>
    /// <param name="orgLevel3"></param>
    /// <param name="orgLevel4"></param>
    /// <param name="orgLevel5"></param>
    /// <param name="productKey"></param>
    /// <param name="globalPartyRole"></param>
    /// <param name="refPolicyStatus"></param>
    /// <param name="renewableFlag"></param>
    /// <param name="facilityLineSlipRef"></param>
    /// <param name="policySectionStatusKey"></param>
    /// <param name="policyAttributeObjectType"></param>
    /// <param name="policyAttributeValue"></param>
    /// <param name="department"></param>
    /// <param name="workerName"></param>
    /// <param name="productClass"></param>
    /// <param name="partyBusinessKey"></param>
    /// <param name="organisationKey"></param>
    /// <param name="sourceProductName"></param>
    /// <param name="Value"></param>/
    [Theory]
    // Rule: 1322 DataSourceInstance.ASYSGermanyAndAustria: Pty.BusinessKey like '%|A'
    [InlineData(true, 1322, DataSourceInstance.ASYSGermanyAndAustria, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "123|A")]
    // Rule: 1353 DataSourceInstance.ASYSGermanyAndAustria: Pty.BusinessKey not like '%|A'
    [InlineData(true, 1353, DataSourceInstance.ASYSGermanyAndAustria, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "SOMETHING")]
    [InlineData(false, 1353, DataSourceInstance.ASYSGermanyAndAustria, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "123|A")]
    // Rule: 1325 DataSourceInstance.IBISMexico: P.IsDeleted = 0
    [InlineData(true, 1325, DataSourceInstance.IBISMexico, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null)]
    // Rule: 1051 DataSourceInstance.InbrokerArgentina: prod.SourceProductName not in ('012|Accidentes Personales','012|Accidentes Personales(Affinidad)','012|Accidentes Personales(Corporativo)','016|Acc.  Riesgo Trabajo',              '016|Accidentes de Trabajo','016|Accidentes del Trabajo','016|Responsabilidad Civil Patronal','021|Caucion','025|Personal Domestico','025|Seguro Colectivo de Saldo Deudor',            '025|Seguro Incapacidad Colectivo','025|Seguro Incapacidad Individual','025|Seguro Retiro Individual','025|Sepelio','025|Vida Con Constitucion Reservas Matemat',               '025|Vida Sin Constitucion Reservas Matemat','025|Vida Colectivo Capital Uniforme','025|Vida Colectivio Convenio Mercantil','025|Vida Colectivio Convenio San Juan',            '025|Vida Collectivo Escala de Capitales','025|Vida Colectivo Ley Contrato de Trabajo','025|Vida Colectivo Multiplo de Sueldo','025|Vida Obligatorio (decreto 1567/74)',                '025|Vida Obligatorio (Trabajadores Rurales)','114|Accidentes A Pasajeros','114|Seguro de Salud')
    [InlineData(true, 1051, DataSourceInstance.InbrokerArgentina, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "SOMETHING")]
    [InlineData(false, 1051, DataSourceInstance.InbrokerArgentina, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "012|Accidentes Personales")]
    // Rule: 1271 DataSourceInstance.segEleviaPortugal: (T.Organisation NOT LIKE '%H&B%')
    [InlineData(true, 1271, DataSourceInstance.segEleviaPortugal, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "W Corretores de Seguros SA | CRB - FINEX (155700) | Oporto | FINEX POR")]
    [InlineData(false, 1271, DataSourceInstance.segEleviaPortugal, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "W Corretores de Seguros SA | CRB - AFFINITY | Lisboa | H&B Sur")]
    // Rule: 1354 DataSourceInstance.VisualSegSpain: P.IsDeleted = 0
    [InlineData(true, 1354, DataSourceInstance.VisualSegSpain, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null)]
    // Rule: 1324 DataSourceInstance.WIBS: P.IsDeleted = 0
    [InlineData(true, 1324, DataSourceInstance.WIBS, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null)]
    // Rule: 1061 DataSourceInstance.BrokingNetIreland: ORG.OrganisationKey not in ('TW','CR','BP','T5', '12','CL')
    [InlineData(true, 1061, DataSourceInstance.BrokingNetIreland, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "XX")]
    [InlineData(false, 1061, DataSourceInstance.BrokingNetIreland, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "TW")]
    // Rule: 1339 DataSourceInstance.GrasSavoyeEGS: P.IsDeleted = 0
    [InlineData(true, 1339, DataSourceInstance.GrasSavoyeEGS, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null)]
    // Rule: 1328 DataSourceInstance.FirstBrokerSwitzerland: P.IsDeleted = 0
    [InlineData(true, 1328, DataSourceInstance.FirstBrokerSwitzerland, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null)]
    // Rule: 1323 DataSourceInstance.IBISChile: P.IsDeleted = 0
    [InlineData(true, 1323, DataSourceInstance.IBISChile, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null)]
    // Rule: 1386 DataSourceInstance.BizCore: PA.[Value] = 'CRB Norway'
    [InlineData(true, 1386, DataSourceInstance.BizCore, null, null, null, null, null, null, null, null, null, null, "BizCoreBusinessTypeName", "CRB Norway", null, null, null, null)]
    // Rule: 1387 DataSourceInstance.BizCore: PA.[Value] = 'CRB Sweden'
    [InlineData(true, 1387, DataSourceInstance.BizCore, null, null, null, null, null, null, null, null, null, null, "BizCoreBusinessTypeName", "CRB Sweden", null, null, null, null)]
    // Rule: 1388 DataSourceInstance.BizCore: PA.[Value] = 'Finland'
    [InlineData(true, 1388, DataSourceInstance.BizCore, null, null, null, null, null, null, null, null, null, null, "BizCoreBusinessTypeName", "Finland", null, null, null, null)]
    public void SegmentationRuleEverythingElseTest(
        bool matches, /* Enables testing for when the rule should not match some values*/
        int ruleId,
        DataSourceInstance dataSourceInstance,
        string? orgLevel1, string? orgLevel2, string? orgLevel3, string? orgLevel4, string? orgLevel5,
        string? productKey,
        GlobalPartyRole? globalPartyRole,
        RefPolicyStatus? refPolicyStatus,
        string? facilityLineSlipRef,
        string? policySectionStatusKey,
        string? policyAttributeObjectType,
        string? policyAttributeValue,
        string? department,
        string? workerName,
        string? productClass,
        string? partyBusinessKey,
        string? organisationKey = null,
        string? sourceProductName = null,
        string? organisationName = null
        )
    {
        dynamic policyRecord;

        CreateDataForSegmentationRule(
            out policyRecord,
            "Other",
            dataSourceInstance,
            orgLevel1,
            orgLevel2,
            orgLevel3,
            orgLevel4,
            orgLevel5,
            productKey,
            globalPartyRole,
            refPolicyStatus,
            facilityLineSlipRef,
            policySectionStatusKey,
            policyAttributeObjectType,
            policyAttributeValue,
            department,
            workerName,
            productClass,
            partyBusinessKey,
            null,
            null,
            organisationKey,
            sourceProductName,
            organisationName,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null
            );

        RunSegmentationRules();

        CheckRuleResult(
            matches,
            ruleId,
            policyRecord.PolicyId);

        if(matches)
        {
            CheckOrgLevelsExist(
            orgLevel1: orgLevel1,
            orgLevel2: orgLevel2,
            orgLevel3: orgLevel3,
            orgLevel4: orgLevel4,
            orgLevel5: orgLevel5,
            orgLevel6: null);
        }
    }

#endif

    #region private

    /// <summary>
    /// Helper class that does the hard work of creating the data so we can split the tests into logical units.
    /// SHows all the records that need to be created.
    /// </summary>
    /// <param name="policyRecord"></param>
    /// <param name="dataSource"></param>
    /// <param name="dataSourceInstance"></param>
    /// <param name="orgLevel1"></param>
    /// <param name="orgLevel2"></param>
    /// <param name="orgLevel3"></param>
    /// <param name="orgLevel4"></param>
    /// <param name="orgLevel5"></param>
    /// <param name="productKey"></param>
    /// <param name="globalPartyRole"></param>
    /// <param name="refPolicyStatus"></param>
    /// <param name="facilityLineSlipRef"></param>
    /// <param name="policySectionStatusKey"></param>
    /// <param name="policyAttributeObjectType"></param>
    /// <param name="policyAttributeValue"></param>
    /// <param name="department"></param>
    /// <param name="workerName"></param>
    /// <param name="productClass"></param>
    /// <param name="partyBusinessKey"></param>
    /// <param name="expiryDateString"></param>
    /// <param name="brokingSubSegmentId"></param>
    /// <param name="organisationKey"></param>
    /// <param name="sourceProductName"></param>
    /// <param name="organisationName"></param>
    /// <param name="agencyRelatedOrganisationCode"></param>
    /// <param name="departmentRelatedOrganisationCode"></param>
    /// <param name="personType"></param>
    /// <param name="isMainPolicy"></param>
    /// <param name="wtwIsLeadBroker"></param>
    /// <param name="organisationAttributeValue"></param>
    /// <param name="productAttributeValue"></param>
    /// <param name="tipoDocument"></param>
    /// <param name="subTipo"></param>
    /// <param name="productAttribute"></param>
    /// <param name="marketSegment"></param>
    /// <param name="ply_branch"></param>
    private void CreateDataForSegmentationRule(
        out dynamic policyRecord,
        string dataSource,
        DataSourceInstance dataSourceInstance,
        string? orgLevel1,
        string? orgLevel2,
        string? orgLevel3,
        string? orgLevel4,
        string? orgLevel5,
        string? productKey,
        GlobalPartyRole? globalPartyRole,
        RefPolicyStatus? refPolicyStatus,
        string? facilityLineSlipRef,
        string? policySectionStatusKey,
        string? policyAttributeObjectType,
        string? policyAttributeValue,
        string? department,
        string? workerName,
        string? productClass,
        string? partyBusinessKey,
        string? expiryDateString,
        int? brokingSubSegmentId,
        string? organisationKey,
        string? sourceProductName,
        string? organisationName,
        string? agencyRelatedOrganisationCode,
        string? departmentRelatedOrganisationCode,
        string? personType,
        int? isMainPolicy,
        int? wtwIsLeadBroker,
        string? organisationAttributeValue,
        string? productAttributeValue,
        int? tipoDocument,
        int? subTipo,
        string? productAttribute,
        string? marketSegment,
        string? ply_branch,
        string? productAttributeObjectType,
        string? policyParentKey)
    {
        dynamic dataSourceRecord = CreateRowIfNotExists(tableName: "Reference.DataSource",
            values: new
            {
                DataSourceName = dataSource
            });

        dynamic dataSourceInstanceRecord = CreateRowIfNotExists(tableName: "Reference.DataSourceInstance",
            values: new
            {
                DataSourceInstanceId = (int)dataSourceInstance,
                DataSourceId = dataSourceRecord.DataSourceId
            });

        policyRecord = CreateRow(tableName: "dbo.Policy",
            values: new
            {
                DataSourceInstanceId = dataSourceInstanceRecord.DataSourceInstanceId,
                RefPolicyStatusId = refPolicyStatus.HasValue ? (int)refPolicyStatus : (int)RefPolicyStatus.Live,
                InceptionDate = DateTime.UtcNow.AddDays(-200).Date,
                ExpiryDate = (expiryDateString == null ? DateTime.UtcNow.AddDays(90).Date : DateTime.Parse(expiryDateString)),
                WTWIsLeadBroker = (wtwIsLeadBroker.HasValue ? wtwIsLeadBroker.Value : 1),
                IsMainPolicy = (isMainPolicy.HasValue ? isMainPolicy.Value : 1),
                PolicyKey = "123456",
                Department = department,
                BrokingSubSegmentId = brokingSubSegmentId,
                MarketSegment = marketSegment,
                ply_branch = ply_branch,
                ParentKey = policyParentKey
            });

        dynamic organisationRecord = CreateRow(
            tableName: "PAS.Organisation",
            values: new
            {
                OrganisationKey = organisationKey,
                Organisation = organisationName,
                DataSourceInstanceId = dataSourceInstanceRecord.DataSourceInstanceId
            });

        dynamic psOrganisationRecord = CreateRow(
            tableName: "PS.Organisation",
            values: new
            {
                OrganisationKey = organisationKey,
                DataSourceInstanceId = dataSourceInstanceRecord.DataSourceInstanceId,
            });

        dynamic organisationHierarchyRecord = CreateRow(
            tableName: "APIv1.OrganisationHierarchyTable",
            values: new
            {
                OrganisationId = psOrganisationRecord.OrganisationSK,
                OrgLevel1 = orgLevel1,
                OrgLevel2 = orgLevel2,
                OrgLevel3 = orgLevel3,
                OrgLevel4 = orgLevel4,
                OrgLevel5 = orgLevel5,
            });

        dynamic policyOrganisationRecord = CreateRow(tableName: "dbo.PolicyOrganisation",
            values: new
            {
                PolicyId = policyRecord.PolicyId,
                OrganisationId = psOrganisationRecord.OrganisationSK,
                DataSourceInstanceId = dataSourceInstanceRecord.DataSourceInstanceId,
                IsDeleted = 0
            });

        if(organisationAttributeValue != null)
        {
            dynamic organisationAttributeRecord = CreateRow(tableName: "PAS.OrganisationAttribute",
                values: new
                {
                    OrganisationKey = organisationRecord.OrganisationKey,
                    DataSourceInstanceId = organisationRecord.DataSourceInstanceId,
                    IsDeleted = 0,
                    COLNivel7Divisao = organisationAttributeValue
                });
        }

        if(tipoDocument.HasValue || subTipo.HasValue)
        {
            // More COL
            dynamic tabelaDocumentosRecord = CreateRow(tableName: "COLStaging.Tabela_Documentos",
                values: new
                {
                    Documento = policyRecord.PolicyKey,
                    Tipo_Documento = tipoDocument.Value,
                    sub_tipo = subTipo.Value
                });
        }

        // Need these as although a LEFT JOIN the where clause make it like an INNER JOIN
        // Due to special case for Epic US wanting GlobalPartyRoleId = 100.
        if(globalPartyRole.HasValue || partyBusinessKey != null || personType != null)
        {
            dynamic partyRoleRecord = CreateRow(tableName: "ref.PartyRole",
                values: new
                {
                    PartyRole = "A Party Role",
                    GlobalPartyRoleId = globalPartyRole.HasValue ? (object)(int)globalPartyRole.Value : null
                });

            dynamic partyRecord = CreateRow(tableName: "dbo.Party",
                values: new
                {
                    BusinessKey = partyBusinessKey,
                    PersonType = personType,
                });

            dynamic policyPartyRelationshipRecord = CreateRow(tableName: "dbo.PolicyPartyRelationship",
                values: new
                {
                    PartyId = partyRecord.PartyId,
                    PolicyId = policyRecord.PolicyId,
                    PartyRoleId = partyRoleRecord.PartyRoleId
                });
        }

        if(policySectionStatusKey != null || productKey != null || productClass != null || sourceProductName != null || productAttributeValue != null || productAttribute != null)
        {
            dynamic policySectionStatusRecord = null;

            if(policySectionStatusKey != null)
            {
                policySectionStatusRecord = CreateRow(tableName: "PAS.PolicySectionStatus",
                    values: new
                    {
                        PolicySectionStatusKey = policySectionStatusKey,
                        DataSourceInstanceId = dataSourceInstanceRecord.DataSourceInstanceId,
                    });
            }

            dynamic policySectionRecord = CreateRow(tableName: "dbo.PolicySection",
                values: new
                {
                    DataSourceInstanceId = dataSourceInstanceRecord.DataSourceInstanceId,
                    policySectionStatusKey = policySectionStatusRecord != null ? policySectionStatusRecord.PolicySectionStatusKey : null,
                    PolicyId = policyRecord.PolicyId
                });

            if(productKey != null || productClass != null || sourceProductName != null || productAttributeValue != null || productAttribute != null)
            {
                dynamic productRecord = CreateRow(tableName: "dbo.Product",
                    values: new
                    {
                        DataSourceInstanceId = policyRecord.DataSourceInstanceId,
                        ProductKey = productKey,
                        ProductClass = productClass,
                        sourceProductName = sourceProductName,
                        SourceProductId = 1
                    });

                dynamic policySectionProductRecord = CreateRow(tableName: "dbo.PolicySectionProduct",
                    values: new
                    {
                        ProductId = productRecord.ProductId,
                        PolicySectionId = policySectionRecord.PolicySectionId
                    });

                // For COL
                if(productRecord.DataSourceInstanceId == (int)DataSourceInstance.BrazilCOL)
                {
                    dynamic colProductAttributeRecord = CreateRow(tableName: "PS.ProductAttribute",
                        values: new
                        {
                            ProductId = productRecord.ProductId,
                            DataSourceInstanceId = productRecord.DataSourceInstanceId,
                            COLProductCode = productAttribute,
                            IsDeleted = 0
                        });
                }

                if(dataSource == "eGlobal")
                {
                    dynamic pasProductRecord = CreateRow(tableName: "PAS.Product",
                       values: new
                       {
                           ProductKey = productRecord.ProductKey,
                           DataSourceInstanceId = productRecord.DataSourceInstanceId,
                           PASProductId = productRecord.SourceProductId,
                           IsDeleted = 0,
                       });

                    dynamic pasProductAttributeRecord = CreateRow(tableName: "PAS.ProductAttribute",
                       values: new
                       {
                           ProductKey = pasProductRecord.ProductKey,
                           DataSourceInstanceId = pasProductRecord.DataSourceInstanceId,
                           eGlobalRI_ABBRNAME = productAttribute,
                           IsDeleted = 0,
                       });
                }
            }
        }

        if(facilityLineSlipRef != null)
        {
            dynamic policyMarketRecord = CreateRow(tableName: "PolicyMarket",
                values: new
                {
                    PolicyId = policyRecord.PolicyId,
                    FaciltyLineSlipRef = facilityLineSlipRef // Yes. Typo
                });
        }

        if(policyAttributeObjectType != null)
        {
            dynamic policyAttributeRecord = CreateRow(tableName: "dbo.PolicyAttribute",
                values: new
                {
                    ObjectType = policyAttributeObjectType,
                    Value = policyAttributeValue,
                    PolicyId = policyRecord.PolicyId,
                    RowIndex = 1
                });
        }

        /* Worker Name */
        if(workerName != null)
        {
            dynamic workerRecord = CreateRow(tableName: "PAS.Worker",
                values: new
                {
                    DataSourceInstanceId = dataSourceInstanceRecord.DataSourceInstanceId,
                    WorkerKey = "WorkerKey",
                    WorkerName = workerName
                });
            dynamic psWorkerRecord = CreateRow(tableName: "PS.Worker",
                values: new
                {
                    DataSourceInstanceId = dataSourceInstanceRecord.DataSourceInstanceId,
                    WorkerKey = "WorkerKey",
                    WorkerSK = 1
                });
            dynamic policyWorkerRecord = CreateRow(tableName: "dbo.PolicyWorker",
                values: new
                {
                    PolicyId = policyRecord.PolicyId,
                    WorkerId = psWorkerRecord.WorkerSK,
                });
        }

        if(agencyRelatedOrganisationCode != null)
        {
            CreateRow(tableName: "PS.OrganisationRelationship",
                values: new
                {
                    DataSourceInstanceId = dataSourceInstanceRecord.DataSourceInstanceId,
                    OrganisationKey = organisationRecord.OrganisationKey,
                    OrganisationRelationshipType = "Agency",
                    RelatedOrganisationKey = agencyRelatedOrganisationCode,
                    RelatedOrganisationCode = agencyRelatedOrganisationCode
                });
        }

        if(departmentRelatedOrganisationCode != null)
        {
            CreateRow(tableName: "PS.OrganisationRelationship",
                values: new
                {
                    DataSourceInstanceId = dataSourceInstanceRecord.DataSourceInstanceId,
                    OrganisationKey = organisationRecord.OrganisationKey,
                    OrganisationRelationshipType = "Department",
                    RelatedOrganisationKey = departmentRelatedOrganisationCode,
                    RelatedOrganisationCode = departmentRelatedOrganisationCode
                });
        }
    }

    private void CheckRuleResult(bool matches, int ruleId, int? policyId)
    {
        dynamic row = GetResultRow(tableName: "dbo.Policy", whereClause: $"PolicyId = {policyId}");
        Assert.NotNull(row);
        if(matches)
        {
            // To check that rule did match
            Assert.Equal(expected: ruleId, actual: row.RuleId);
            Assert.True(row.ETLUpdatedDate > DateTime.UtcNow.AddMinutes(-1));
        }
        else
        {
            // To check rule doesn't match
            if(row.RuleId is not DBNull)
            {
                Assert.NotEqual(expected: ruleId, actual: row.RuleId);
            }
            //            Assert.true(row.RuleId is DBNull);
        }
    }

    /// <summary>
    /// Creates everything needed and runs the segmentation rules.
    /// </summary>
    private void RunSegmentationRules()
    {
        dynamic instanceLogRecord = CreateRow(
            tableName: @"ADF.InstanceLog",
            values: new
            {

            });
        dynamic processSessionRecord = CreateRow(
            tableName: @"ADF.ProcessSession",
            values: new
            {
                InstanceLogId = instanceLogRecord.InstanceLogId
            });
        dynamic etlRunRecord = CreateRow(
            tableName: @"PactConfig.EtlRun",
            values: new
            {
                InstanceLogId = instanceLogRecord.InstanceLogId
            });

        ExecuteStoredProcedureWithoutResult(
            storedProcedureName: "Rules.Run_SegmentationRules",
            values: new
            {
                @ProcessSessionId = processSessionRecord.ProcessSessionId,
                @Success = 1
            });
    }

    /// <summary>
    /// In a local only check we can see if these organisations really exist.
    /// This relies on the orgs in the rules being added into a test.
    /// But at the same time that does mean that we can remove a test if an org isn't found.
    /// We were caught out when we were given some incorrect names and had a typo when the PBI was created.
    /// Lots of Finex test fail because we have a LIKE check and we have added a random string to match it rather than use real data
    /// </summary>
    /// <param name="orgLevel1"></param>
    /// <param name="orgLevel2"></param>
    /// <param name="orgLevel3"></param>
    /// <param name="orgLevel4"></param>
    /// <param name="orgLevel5"></param>
    /// <param name="orgLevel6"></param>
    /// <exception cref="NotImplementedException"></exception>
    private void CheckOrgLevelsExist(string? orgLevel1, string? orgLevel2, string? orgLevel3, string? orgLevel4, string? orgLevel5, string? orgLevel6)
    {
#if  CheckOrganisationsAgainRealPlacementStore
        if((orgLevel1 != null || orgLevel2 != null || orgLevel3 != null || orgLevel4 != null || orgLevel5 != null || orgLevel6 != null) && _canQueryPS)
        {
            // But only if there are any orgs to look-up.
            var whereBits = new List<string>();
            if(orgLevel1 != null)
            {
                whereBits.Add($"OrgLevel1 = '{orgLevel1}'");
            }
            if(orgLevel2 != null)
            {
                whereBits.Add($"OrgLevel2 = '{orgLevel2}'");
            }
            if(orgLevel3 != null)
            {
                whereBits.Add($"OrgLevel3 = '{orgLevel3}'");
            }
            if(orgLevel4 != null)
            {
                whereBits.Add($"OrgLevel4 = '{orgLevel4}'");
            }
            if(orgLevel5 != null)
            {
                whereBits.Add($"OrgLevel5 = '{orgLevel5}'");
            }
            if(orgLevel6 != null)
            {
                whereBits.Add($"OrgLevel6 = '{orgLevel6}'");
            }
            using(var psConnection = new SqlConnection(connectionString: _psConnectionString))
            {
                psConnection.Open();

                using(var queryCommand = psConnection.CreateCommand())
                {
                    queryCommand.CommandText = $"SELECT TOP(1) 1 FROM APIv1.OrganisationHierarchyTable WHERE {string.Join(" AND ", whereBits)}";
                    queryCommand.CommandType = System.Data.CommandType.Text;
                    queryCommand.CommandTimeout = 30;

                    var result = queryCommand.ExecuteScalar();
                    Assert.True( result != null, $"Unable to find an APIv1.OrganisationHierarchyTable record for OrgLevel1 = '{orgLevel1}', OrgLevel2 = '{orgLevel2}', OrgLevel3 = '{orgLevel3}', OrgLevel4 = '{orgLevel4}', OrgLevel5 = '{orgLevel5}', OrgLevel6 = '{orgLevel6}'! ({queryCommand.CommandText})");
                }
            }
        }
#endif // CheckOrganisationsAgainRealPlacementStore
    }

    #endregion

    /// <summary>
    /// Constructor - do not change.
    /// </summary>
    /// <param name="fixture"></param>
    public SegmentationRulesTests(DatabaseFixture fixture, ITestOutputHelper output) : base(fixture, output)
    {
        var connectionString = fixture.Configuration.GetConnectionString(name: DatabaseConnectionStringKey);
        _psConnectionString = connectionString;
        _canQueryPS = (connectionString != null);
        if(!_canQueryPS)
        {
            output.WriteLine("No connection string for PS - unable to run checks against PS! A local operation only.");
        }
    }

}
