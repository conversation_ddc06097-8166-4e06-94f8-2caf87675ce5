﻿using System.Diagnostics;
using System.Diagnostics.CodeAnalysis;
using System.Text.Json.Nodes;

namespace PsDb.Tests.Support.StoredProcedures;
[ExcludeFromCodeCoverage]

public class OverrideProcessLastUpdatedDate : PlacementStoreTestBase
{
    [Fact]
    public void UpdateBatchControlWithInvalidProcessNameTest()
    {
        ExecuteStoredProcedureWithoutResult(storedProcedureName: "Support.OverrideProcessLastUpdatedDate", values: new
        {
            @ProcessName = "XXXXXXXXXX"
        });
        dynamic result = GetResultRow(tableName: "ADF.StoredProcLog", whereClause: $"StoredProcName = 'Support.OverrideProcessLastUpdatedDate'");
        Assert.Equal(expected: "Support.OverrideProcessLastUpdatedDate", actual: result.StoredProcName);
        Assert.Equal(expected: "OverrideProcessLastUpdatedDate - No Process found like the name provided.", actual: result.ErrorMessage);
    }

    [Fact]
    public void UpdateBatchControlWithMissingParametersTest()
    {
        dynamic processRecord = CreateTestProcess();

        ExecuteStoredProcedureWithoutResult(storedProcedureName: "Support.OverrideProcessLastUpdatedDate", values: new
        {
            @ProcessName = processRecord.Name
        });

        dynamic result = GetResultRow(tableName: "ADF.StoredProcLog", whereClause: $"StoredProcName = 'Support.OverrideProcessLastUpdatedDate'");
        Assert.Equal(expected: "Support.OverrideProcessLastUpdatedDate", actual: result.StoredProcName);
        Assert.Equal(expected: "OverrideProcessLastUpdatedDate - You must provide exactly one of @ExtractDateToUse or @DaysBack.", actual: result.ErrorMessage);
    }



    [Fact]
    public void UpdateBatchControlWithDateToUseTest()
    {
        dynamic processRecord = CreateTestProcess();

        ExecuteStoredProcedureWithoutResult(storedProcedureName: "Support.OverrideProcessLastUpdatedDate", values: new
        {
            @ProcessName = processRecord.Name,
            @DateToUse = "2021-08-01"
        });

        dynamic result = GetResultRow(tableName: "ADF.StoredProcLog", whereClause: $"StoredProcName = 'Support.OverrideProcessLastUpdatedDate'");
        Assert.Equal(expected: "Support.OverrideProcessLastUpdatedDate", actual: result.StoredProcName);
        Assert.Equal(expected: DBNull.Value, actual: result.ErrorMessage);

        dynamic processOverrideRow = GetResultRow(tableName: @"ADF.ProcessOverride");
        Assert.Equal(expected: DateTime.Parse("2021-08-01"), actual: processOverrideRow.MaxLastUpdatedUTCDateToUse);
        Assert.Equal(expected: true, actual: processOverrideRow.IsActive);
    }

    [Fact]
    public void UpdateBatchControlWithDateToUseAndProcessTypeTest()
    {
        dynamic processRecord = CreateTestProcess();

        ExecuteStoredProcedureWithoutResult(storedProcedureName: "Support.OverrideProcessLastUpdatedDate", values: new
        {
            @ProcessName = processRecord.Name,
            @ProcessTypeId = 1,
            @DateToUse = "2021-08-01"
        });

        dynamic result = GetResultRow(tableName: "ADF.StoredProcLog", whereClause: $"StoredProcName = 'Support.OverrideProcessLastUpdatedDate'");
        Assert.Equal(expected: "Support.OverrideProcessLastUpdatedDate", actual: result.StoredProcName);
        Assert.Equal(expected: DBNull.Value, actual: result.ErrorMessage);

        dynamic processOverrideRow = GetResultRow(tableName: @"ADF.ProcessOverride");
        Assert.Equal(expected: DateTime.Parse("2021-08-01"), actual: processOverrideRow.MaxLastUpdatedUTCDateToUse);
        Assert.Equal(expected: true, actual: processOverrideRow.IsActive);
    }


    [Fact]
    public void UpdateBatchControlWithDaysBackTest()
    {
        dynamic processRecord = CreateTestProcess();

        ExecuteStoredProcedureWithoutResult(storedProcedureName: "Support.OverrideProcessLastUpdatedDate", values: new
        {
            @ProcessName = processRecord.Name,
            @DaysBack = 5
        });

        dynamic result = GetResultRow(tableName: "ADF.StoredProcLog", whereClause: $"StoredProcName = 'Support.OverrideProcessLastUpdatedDate'");
        Assert.Equal(expected: "Support.OverrideProcessLastUpdatedDate", actual: result.StoredProcName);
        Assert.Equal(expected: DBNull.Value, actual: result.ErrorMessage);

        dynamic processOverrideRow = GetResultRow(tableName: @"ADF.ProcessOverride");
        Assert.Equal(expected: DateTime.Parse("1900-01-01"), actual: processOverrideRow.MaxLastUpdatedUTCDateToUse);
        Assert.Equal(expected: true, actual: processOverrideRow.IsActive);
    }

    [Fact]
    public void UpdateBatchControlWithDaysBackAndPreviousRunTest()
    {
        dynamic processRecord = CreateTestProcess();
        dynamic instanceLogRecord = CreateRow(tableName: "ADF.InstanceLog", values: new
        {
            InstanceStartDate = DateTime.UtcNow
        });
        dynamic processSessionRecord = CreateRow(tableName: "ADF.ProcessSession", values: new
        {
            ProcessId = processRecord.ProcessId,
            InstanceLogId = instanceLogRecord.InstanceLogId,
            ProcessSessionStatusId = (int)PSSStatus.PSS_Succeeded,
            MaxLastUpdatedUTCDate = "2020-01-01"
        });

        ExecuteStoredProcedureWithoutResult(storedProcedureName: "Support.OverrideProcessLastUpdatedDate", values: new
        {
            @ProcessName = processRecord.Name,
            @DaysBack = 5
        });

        dynamic result = GetResultRow(tableName: "ADF.StoredProcLog", whereClause: $"StoredProcName = 'Support.OverrideProcessLastUpdatedDate'");
        Assert.Equal(expected: "Support.OverrideProcessLastUpdatedDate", actual: result.StoredProcName);
        Assert.Equal(expected: DBNull.Value, actual: result.ErrorMessage);

        dynamic processOverrideRow = GetResultRow(tableName: @"ADF.ProcessOverride");
        Assert.Equal(expected: DateTime.Parse("2020-01-01").AddDays(-5), actual: processOverrideRow.MaxLastUpdatedUTCDateToUse);
        Assert.Equal(expected: true, actual: processOverrideRow.IsActive);
    }

    [Fact]
    public void UpdateBatchControlToTurnOffExistingOverrideNoOtherParametersTest()
    {
        dynamic processRecord = CreateTestProcess();
        dynamic processOverrideRecord = CreateRow(tableName: @"ADF.ProcessOverride", values: new
        {
            ProcessId = processRecord.ProcessId,
            IsActive = true,
            MaxLastUpdatedUTCDateToUse = DateTime.Now
        });
        ExecuteStoredProcedureWithoutResult(storedProcedureName: "Support.OverrideProcessLastUpdatedDate", values: new
        {
            @ProcessName = processRecord.Name,
            @IsActive = false,
            @DaysBack = 5
        });

        dynamic result = GetResultRow(tableName: "ADF.StoredProcLog", whereClause: $"StoredProcName = 'Support.OverrideProcessLastUpdatedDate'");
        Assert.Equal(expected: "Support.OverrideProcessLastUpdatedDate", actual: result.StoredProcName);
        Assert.Equal(expected: "OverrideProcessLastUpdatedDate - @IsActive is 0 to turn off the override you must not provide @ExtractDateToUse or @DaysBack.", actual: result.ErrorMessage);
    }

    [Fact]
    public void UpdateBatchControlToTurnOffExistingOverrideTest()
    {
        dynamic processRecord = CreateTestProcess();
        dynamic processOverrideRecord = CreateRow(tableName: @"ADF.ProcessOverride", values: new
        {
            ProcessId = processRecord.ProcessId,
            OverrideColumn = "MaxLastUpdatedUTCDate",
            IsActive = true,
            MaxLastUpdatedUTCDateToUse = DateTime.Now
        });
        ExecuteStoredProcedureWithoutResult(storedProcedureName: "Support.OverrideProcessLastUpdatedDate", values: new
        {
            @ProcessName = processRecord.Name,
            @IsActive = false
        });

        dynamic result = GetResultRow(tableName: "ADF.StoredProcLog", whereClause: $"StoredProcName = 'Support.OverrideProcessLastUpdatedDate'");
        Assert.Equal(expected: "Support.OverrideProcessLastUpdatedDate", actual: result.StoredProcName);
        Assert.Equal(expected: DBNull.Value, actual: result.ErrorMessage);

        dynamic processOverrideRow = GetResultRow(tableName: @"ADF.ProcessOverride");
        Assert.Equal(expected: false, actual: processOverrideRow.IsActive);
    }

    private dynamic CreateTestProcess()
    {
        var json = new JsonObject();
        json.Add(propertyName: "SourceSQL", value: JsonValue.Parse("\"\""));
        json.Add(propertyName: "TargetTable", value: JsonValue.Parse("\"ReferenceStaging.rpt_vwCountry\""));
        json.Add(propertyName: "SourceTable", value: JsonValue.Parse("\"rpt.vwCountry\""));
        json.Add(propertyName: "AlwaysFullLoad", value: JsonValue.Parse("false"));
        json.Add(propertyName: "SystemVersioned", value: JsonValue.Parse("false"));
        json.Add(propertyName: "IncrementalUpdatedDateColumn", value: JsonValue.Parse("\"ETLUpdatedDate\""));

        dynamic processRecord = CreateRow(tableName: "ADF.Process", values: new
        {
            Name = "Unit Test",
            JSONConfig = json.ToString(),
            processTypeId = 1
        });
        return processRecord;
    }

    /// <summary>
    /// Do not change this
    /// </summary>
    /// <param name="fixture"></param>
    public OverrideProcessLastUpdatedDate(DatabaseFixture fixture) : base(fixture)
    {
    }
}
