﻿using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Text;
using System.Text.Json.Nodes;
using System.Threading.Tasks;

namespace PsDb.Tests.Support.StoredProcedures;

[ExcludeFromCodeCoverage]
public class UpdatePlacementListener : PlacementStoreTestBase
{
    [Fact]
    public void UpdatePlacementListenerWithNoPlacementNameTest()
    {
        ExecuteStoredProcedureWithoutResult(storedProcedureName: "Support.UpdatePlacementListener", values: new
        {
            @PlacementId = 1,
            @RenewalProcessStartDate = "2024-03-15",
            @Comments = "Test"
        });
        dynamic result = GetResultRow(tableName: "ADF.StoredProcLog", whereClause: $"StoredProcName = 'Support.UpdatePlacementListener'");
        Assert.Equal(expected: "Support.UpdatePlacementListener", actual: result.StoredProcName);

        dynamic result2 = GetResultRow(tableName: "Support.SupportSpLog");
        Assert.Equal(expected: "Support.UpdatePlacementListener", actual: result2.SprocName);
        Assert.Equal(expected: "Test : No Placement Provided", actual: result2.Comments);
    }

    [Fact]
    public void UpdatePlacementListenerWithPlacementNameTest()
    {
        dynamic placementRecord = CreateRow(tableName: "dbo.Placement", values: new { 
            DataSourceInstanceId = (int) DataSourceInstance.BrokingPlatform,
            IsDeleted = 0
        });
        dynamic placementListenerRecord = CreateRow(tableName: "dbo.PlacementListener", values: new {
            PlacementId = placementRecord.PlacementId
        });

        ExecuteStoredProcedureWithoutResult(storedProcedureName: "Support.UpdatePlacementListener", values: new
        {
            @PlacementId = placementRecord.placementId,
            @RenewalProcessStartDate = "2024-03-15",
            @RuleId = 21,
            @Comments = "Test"
        });
        dynamic result = GetResultRow(tableName: "ADF.StoredProcLog", whereClause: $"StoredProcName = 'Support.UpdatePlacementListener'");
        Assert.Equal(expected: "Support.UpdatePlacementListener", actual: result.StoredProcName);

        dynamic result2 = GetResultRow(tableName: "Support.SupportSpLog");
        Assert.Equal(expected: "Support.UpdatePlacementListener", actual: result2.SprocName);
        Assert.Equal(expected: "Test : " + placementRecord.placementId, actual: result2.Comments);

        dynamic row = GetResultRow(tableName: "dbo.PlacementListener");
        Assert.Equal(expected: placementRecord.placementId, actual: row.placementId);
        Assert.Equal(expected: true, actual: row.IsReadyToSend);
        Assert.Equal(expected: 21, actual: row.ListenerRuleId);
    }

    [Fact]
    public void UpdatePlacementListenerDontChangeRuleTest()
    {
        dynamic placementRecord = CreateRow(tableName: "dbo.Placement", values: new
        {
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            IsDeleted = 0
        });
        dynamic placementListenerRecord = CreateRow(tableName: "dbo.PlacementListener", values: new
        {
            PlacementId = placementRecord.PlacementId,
            ListenerRuleId = 1
        });

        ExecuteStoredProcedureWithoutResult(storedProcedureName: "Support.UpdatePlacementListener", values: new
        {
            @PlacementId = placementRecord.placementId,
            @RenewalProcessStartDate = "2024-03-15",
            
            @Comments = "Test"
        });
        dynamic result = GetResultRow(tableName: "ADF.StoredProcLog", whereClause: $"StoredProcName = 'Support.UpdatePlacementListener'");
        Assert.Equal(expected: "Support.UpdatePlacementListener", actual: result.StoredProcName);

        dynamic result2 = GetResultRow(tableName: "Support.SupportSpLog");
        Assert.Equal(expected: "Support.UpdatePlacementListener", actual: result2.SprocName);
        Assert.Equal(expected: "Test : " + placementRecord.placementId, actual: result2.Comments);

        dynamic row = GetResultRow(tableName: "dbo.PlacementListener");
        Assert.Equal(expected: placementRecord.placementId, actual: row.placementId);
        Assert.Equal(expected: true, actual: row.IsReadyToSend);
        Assert.Equal(expected: placementListenerRecord.ListenerRuleId, actual: row.ListenerRuleId);
    }

    /// <summary>
    /// Do not change this
    /// </summary>
    /// <param name="fixture"></param>
    public UpdatePlacementListener(DatabaseFixture fixture) : base(fixture)
    {
    }
}
