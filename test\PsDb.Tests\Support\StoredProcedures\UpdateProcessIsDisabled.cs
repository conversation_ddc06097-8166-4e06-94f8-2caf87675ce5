﻿using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Text;
using System.Text.Json.Nodes;
using System.Threading.Tasks;

namespace PsDb.Tests.Support.StoredProcedures;

[ExcludeFromCodeCoverage]
public class UpdateProcessIsDisabled : PlacementStoreTestBase
{
    [Fact]
    public void UpdateProcessIsDisabledWithInvalidProcessNameTest()
    {
        ExecuteStoredProcedureWithoutResult(storedProcedureName: "Support.UpdateProcessIsDisabled", values: new
        {
            @ProcessName = "XXXXXXXXXX",
            @IsDisabled = true
        });
        dynamic result = GetResultRow(tableName: "ADF.StoredProcLog", whereClause: $"StoredProcName = 'Support.UpdateProcessIsDisabled'");
        Assert.Equal(expected: "Support.UpdateProcessIsDisabled", actual: result.StoredProcName);
        Assert.Equal(expected: "UpdateProcessIsDisabled - No Process found like the name provided.", actual: result.ErrorMessage);
    }

    [Fact]
    public void UpdateProcessIsDisabledWithMissingIsDisabledTest()
    {
        dynamic processRecord = CreateTestProcess();

        ExecuteStoredProcedureWithoutResult(storedProcedureName: "Support.UpdateProcessIsDisabled", values: new
        {
            @ProcessName = "Unit Test",
            @IsDisabled = DBNull.Value
        });
        dynamic result = GetResultRow(tableName: "ADF.StoredProcLog", whereClause: $"StoredProcName = 'Support.UpdateProcessIsDisabled'");
        Assert.Equal(expected: "Support.UpdateProcessIsDisabled", actual: result.StoredProcName);
        Assert.Equal(expected: "UpdateProcessIsDisabled - You must provide a value for @IsDisabled.", actual: result.ErrorMessage);
    }

    [Fact]
    public void UpdateProcessIsDisabledSetsDisabledTest()
    {
        dynamic processRecord = CreateTestProcess();

        ExecuteStoredProcedureWithoutResult(storedProcedureName: "Support.UpdateProcessIsDisabled", values: new
        {
            @ProcessName = "Unit Test",
            @IsDisabled = true
        });

        dynamic result = GetResultRow(tableName: "ADF.StoredProcLog", whereClause: $"StoredProcName = 'Support.UpdateProcessIsDisabled'");
        Assert.Equal(expected: 1, actual: result.UpdatedCount);

        result = GetResultRow(tableName: "ADF.Process", whereClause: $"ProcessId = {processRecord.ProcessId}");
        Assert.Equal(expected: true, actual: result.IsDisabled);
    }

    [Fact]
    public void UpdateProcessIsDisabledNoChangeToValueTest()
    {
        dynamic processRecord = CreateTestProcess();

        ExecuteStoredProcedureWithoutResult(storedProcedureName: "Support.UpdateProcessIsDisabled", values: new
        {
            @ProcessName = "Unit Test",
            @IsDisabled = false
        });

        dynamic result = GetResultRow(tableName: "ADF.StoredProcLog", whereClause: $"StoredProcName = 'Support.UpdateProcessIsDisabled'");
        Assert.Equal(expected: 0, actual: result.UpdatedCount);

        result = GetResultRow(tableName: "ADF.Process", whereClause: $"ProcessId = {processRecord.ProcessId}");
        Assert.Equal(expected: false, actual: result.IsDisabled);
    }


    private dynamic CreateTestProcess()
    {
        var json = new JsonObject();
        json.Add(propertyName: "SourceSQL", value: JsonValue.Parse("\"\""));
        json.Add(propertyName: "TargetTable", value: JsonValue.Parse("\"ReferenceStaging.rpt_vwCountry\""));
        json.Add(propertyName: "SourceTable", value: JsonValue.Parse("\"rpt.vwCountry\""));
        json.Add(propertyName: "AlwaysFullLoad", value: JsonValue.Parse("false"));
        json.Add(propertyName: "SystemVersioned", value: JsonValue.Parse("false"));
        json.Add(propertyName: "IncrementalUpdatedDateColumn", value: JsonValue.Parse("\"ETLUpdatedDate\""));

        dynamic processRecord = CreateRow(tableName: "ADF.Process", values: new
        {
            Name = "Unit Test",
            JSONConfig = json.ToString(),
            processTypeId = 1
        });
        return processRecord;
    }

    /// <summary>
    /// Do not change this
    /// </summary>
    /// <param name="fixture"></param>
    public UpdateProcessIsDisabled(DatabaseFixture fixture) : base(fixture)
    {
    }
}
