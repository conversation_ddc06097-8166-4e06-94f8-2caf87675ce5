﻿using System.Diagnostics.CodeAnalysis;

namespace PsDb.Tests.Task.Scripts;

[ExcludeFromCodeCoverage]
public class ValidateConfigurationTests : PlacementStoreTestBase
{
    /// <summary>
    /// Just want to prove that it is possible, and valid not to match.
    /// I can see this test needing to be altered if the servicing platform 
    /// picked has a proper rule added.
    /// </summary>
    [Theory]
    [InlineData("SG Retail - Client Service Team 1", 50000, 7)]
    [InlineData("SG Retail - Client Service Team 2", 50000, 6)]
    [InlineData("SG Retail - Construction", 50000, 4)]
    [InlineData("SG Retail - Finex", 50000, 7)]
    [InlineData("SG Retail - Network", 50000, 4)]
    public void TestPolicyConfig(string Collection, int DataSourceInstanceId, int policyConfigRowCount)
    {
        dynamic policyConfigRecord = GetResultRow(sql: @$"SELECT * FROM Task.PolicyConfig WHERE Collection = '{Collection}'");
        Assert.NotNull(policyConfigRecord);
        Assert.Equal(expected: DataSourceInstanceId, actual: policyConfigRecord.DataSourceInstanceId);
        Assert.Equal(expected: Collection, actual: policyConfigRecord.Collection);

        dynamic policyConfigRoleRecords = GetResultRows(sql: @$"SELECT * FROM Task.PolicyConfigRole WHERE PolicyConfigId = '{policyConfigRecord.Id}'");
        Assert.NotNull(policyConfigRoleRecords);
        Assert.Equal(expected: policyConfigRowCount, actual: policyConfigRoleRecords.Rows.Count);

        dynamic policyConfigEventRecord = GetResultRow(sql: @$"SELECT * FROM Task.PolicyConfigEvent WHERE PolicyConfigId = '{policyConfigRecord.Id}'");
        Assert.NotNull(policyConfigEventRecord);
        Assert.NotNull(policyConfigEventRecord.TaskConfigId);

        dynamic taskConfigRecord = GetResultRow(sql: @$"SELECT * FROM Task.TaskConfig WHERE Id = '{policyConfigEventRecord.TaskConfigId}'");
        Assert.NotNull(taskConfigRecord);
        Assert.NotNull(taskConfigRecord.Id);
    }

    /// <summary>
    /// Provide a way to test Metadata configuration.
    /// Based on how the PRP code processes these tables.
    /// For some reason the seeding scripts like everything as strings. We are fussier here about the correct types.
    /// If you want to generate the InlineData from existing data:
    /// SELECT
    ///    CONCAT(
    ///        '[InlineData("'
    ///      , mg.Name
    ///      , '", "'
    ///      , mi.Name
    ///      , '", "'
    ///      , mi.DisplayName
    ///      , '", "'
    ///      , mi.DataType
    ///      , '", '
    ///      , CASE WHEN mi.IsVisibleInGrid = 1
    ///                 THEN 'true'
    ///             ELSE 'false' END
    ///      , ', '
    ///      , CASE WHEN mi.IsVisibleInDetailsScreen = 1
    ///                 THEN 'true'
    ///             ELSE 'false' END
    ///      , ', '
    ///      , CASE WHEN mi.DefaultValue IS NULL
    ///                 THEN 'null'
    ///             ELSE CONCAT('"', mi.DefaultValue, '"') END
    ///      , ', '
    ///      , CASE WHEN mi.DataSetNumber IS NULL
    ///                 THEN 'null'
    ///             ELSE CONCAT('"', mi.DataSetNumber, '"') END
    ///      , ', '
    ///      , CASE WHEN mi.ScriptId IS NULL
    ///                 THEN 'null'
    ///             ELSE CONCAT('"', mi.ScriptId, '"') END, ')]'
    ///    )
    ///FROM
    ///    Task.MetadataGroup mg
    ///    LEFT JOIN Task.MetadataGroupItem mgi
    ///        ON mgi.MetadataGroupId = mg.Id
    ///
    ///    LEFT JOIN Task.MetadataItem mi
    ///        ON mi.Id = mgi.MetadataItemId
    ///WHERE
    ///    mg.Name = 'PIG - PRP Metadata';
    /// </summary>
    [Theory]
    [InlineData("PIG - PRP Metadata", "PolicyRef", "Policy Ref", "string", true, true, null, 0, null)]
    [InlineData("PIG - PRP Metadata", "ExpiryDate", "Expiry Date", "calendar", true, true, null, 0, null)]
    [InlineData("PIG - PRP Metadata", "Slipheader", "Slipheader", "string", true, true, null, null, null)]
    [InlineData("PIG - PRP Metadata", "Insureds", "Insureds", "string", true, true, null, 3, null)]
    [InlineData("PIG - PRP Metadata", "Product", "Product", "string", true, true, null, 4, null)]
    [InlineData("PIG - PRP Metadata", "CleansedWorkType", "Cleansed Work Type", "string", true, true, null, null, null)]
    [InlineData("PIG - PRP Metadata", "TatMissComments", "Tat Miss Comments", "string", true, true, null, null, null)]
    [InlineData("PIG - PRP Metadata", "PolicyId", "PolicyId", "string", true, true, null, 0, null)]
    [InlineData("PIG - PRP Metadata", "AllocatedDate", "Allocated Date", "calendar", true, true, null, null, null)]
    [InlineData("PIG - PRP Metadata", "InsuredCountry", "Insured Country", "string", true, true, null, null, null)]
    [InlineData("PIG - PRP Metadata", "PolicyStatus", "Policy Status", "string", true, true, null, 0, null)]
    [InlineData("PIG - PRP Metadata", "TIVValueforSchedule", "TIV Value for Schedule", "numeric", true, true, null, null, null)]
    [InlineData("PIG - PRP Metadata", "BU", "BU", "string", true, true, null, 1, null)]
    [InlineData("PIG - PRP Metadata", "NoofLinesinSchedule", "No of Lines in Schedule", "numeric", true, true, null, null, null)]
    [InlineData("PIG - PRP Metadata", "PolicyType", "Policy Type", "string", true, true, "First", null, null)]
    [InlineData("PIG - PRP Metadata", "UniqueMarketRef", "UniqueMarketRef", "string", true, true, null, null, null)]
    [InlineData("PIG - PRP Metadata", "Productclass", "Product Class", "string", true, true, null, 4, null)]
    [InlineData("PIG - PRP Metadata", "WorkType", "Work Type", "string", true, true, null, null, null)]
    [InlineData("PIG - PRP Metadata", "ParentRef", "Parent Ref", "string", true, true, null, 0, null)]
    public void TestTaskConfig(string metadataGroupName, string metadataItemName, string displayName, string dataType, bool isVisibleInGrid, bool isVisibleInDetailsScreen, string? defaultValue, int? dataSetNumber, Guid? scriptId)
    {
        dynamic result = GetResultRow(
            sql: $@"
                SELECT
                    MetadataGroupId = mg.Id
                  , MetadataGroupName = mg.Name
                  , MetadataItemId = mi.Id
                  , MetadataItemName = mi.Name
                  , mi.DisplayName
                  , mi.DataType
                  , mi.IsVisibleInGrid
                  , mi.IsVisibleInDetailsScreen
                  --, mi.FieldValueList
                  , mi.DataSetNumber
                  , mi.ScriptId
                  --, mi.ColumnName
                  --, mi.FilterColumnName
                  --, mi.FilterValue
                  , mi.DefaultValue
                FROM
                    Task.MetadataGroup mg
                    LEFT JOIN Task.MetadataGroupItem mgi
                        ON mgi.MetadataGroupId = mg.Id

                    LEFT JOIN Task.MetadataItem mi
                        ON mi.Id = mgi.MetadataItemId
                WHERE
                    mg.Name = '{metadataGroupName}'
                    AND mi.Name = '{metadataItemName}';
            "
        );
        Assert.NotNull(result);
        Assert.Equal(expected: displayName, actual: result.DisplayName);
        Assert.Equal(expected: dataType, actual: result.DataType);
        Assert.Equal(expected: isVisibleInGrid, actual: result.IsVisibleInGrid);
        Assert.Equal(expected: isVisibleInDetailsScreen, actual: result.IsVisibleInDetailsScreen);

        /* The next values can be null so need special handling */
        if(defaultValue == null)
        {
            Assert.Equal(expected: DBNull.Value, actual: result.DefaultValue);
        }
        else
        {
            Assert.Equal(expected: defaultValue, actual: result.DefaultValue);
        }
        if(dataSetNumber == null)
        {
            Assert.Equal(expected: DBNull.Value, actual: result.DataSetNumber);
        }
        else
        {
            Assert.Equal(expected: dataSetNumber, actual: result.DataSetNumber);
        }
        if(scriptId == null)
        {
            Assert.Equal(expected: DBNull.Value, actual: result.ScriptId);
        }
        else
        {
            Assert.Equal(expected: scriptId, actual: result.ScriptId);
        }
    }

    [Theory]
    [InlineData("P&B Aero - Aerospace - Ipswich", false, "NewPolicy", "Claim Stats/Loss Run/Claim Experience", 150, 95)]
    [InlineData("P&B Aero - Aerospace - Ipswich", false, "NewPolicy", "Draft Renewal Invite", 150, 90)]
    [InlineData("P&B Aero - Aerospace - Ipswich", false, "NewPolicy", "MRC", 150, 90)]
    [InlineData("P&B Aero - Aerospace - Ipswich", false, "NewPolicy", "Premium Stats", 150, 90)]
    [InlineData("P&B Aero - Aerospace - Ipswich", false, "NewPolicy", "TMF", 150, 90)]
    [InlineData("P&B Aero - Aerospace - Ipswich", false, "Renewal", "Policy Renewal", 150, 120)]
    [InlineData("P&B Aero - Aerospace - Ipswich", true, "NewPolicy", "Claim Stats/Loss Run/Claim Experience", 150, 95)]
    [InlineData("P&B Aero - Aerospace - Ipswich", true, "NewPolicy", "Draft Renewal Invite", 150, 90)]
    [InlineData("P&B Aero - Aerospace - Ipswich", true, "NewPolicy", "MRC", 150, 90)]
    [InlineData("P&B Aero - Aerospace - Ipswich", true, "NewPolicy", "Premium Stats", 150, 90)]
    [InlineData("P&B Aero - Aerospace - Ipswich", true, "NewPolicy", "TMF", 150, 90)]
    [InlineData("P&B Aero - Aerospace - Ipswich", true, "Renewal", "Policy Renewal", 150, 120)]
    [InlineData("P&B Aero - Aerospace - London", false, "NewPolicy", "Claim Stats/Loss Run/Claim Experience", 150, 95)]
    [InlineData("P&B Aero - Aerospace - London", false, "NewPolicy", "Draft Renewal Invite", 150, null)] /* These are being deleted */
    [InlineData("P&B Aero - Aerospace - London", false, "NewPolicy", "MRC", 150, 90)]
    [InlineData("P&B Aero - Aerospace - London", false, "NewPolicy", "Premium Stats", 150, 90)]
    [InlineData("P&B Aero - Aerospace - London", false, "NewPolicy", "TMF", 150, 90)]
    [InlineData("P&B Aero - Aerospace - London", false, "Renewal", "Policy Renewal", 150, 120)]
    [InlineData("P&B Aero - Aerospace - London", true, "NewPolicy", "Claim Stats/Loss Run/Claim Experience", 150, 95)]
    [InlineData("P&B Aero - Aerospace - London", true, "NewPolicy", "Draft Renewal Invite", 150, null)] /* These are being deleted */
    [InlineData("P&B Aero - Aerospace - London", true, "NewPolicy", "MRC", 150, 90)]
    [InlineData("P&B Aero - Aerospace - London", true, "NewPolicy", "Premium Stats", 150, 90)]
    [InlineData("P&B Aero - Aerospace - London", true, "NewPolicy", "TMF", 150, 90)]
    [InlineData("P&B Aero - Aerospace - London", true, "Renewal", "Policy Renewal", 150, 120)]
    [InlineData("P&B Aero - Airlines - Ipswich", false, "NewPolicy", "Claim Stats/Loss Run/Claim Experience", 150, 95)]
    [InlineData("P&B Aero - Airlines - Ipswich", false, "NewPolicy", "MRC", 150, 90)]
    [InlineData("P&B Aero - Airlines - Ipswich", false, "NewPolicy", "Premium Stats", 150, 90)]
    [InlineData("P&B Aero - Airlines - Ipswich", false, "NewPolicy", "TMF", 150, 90)]
    [InlineData("P&B Aero - Airlines - Ipswich", false, "Renewal", "Policy Renewal", 150, 120)]
    [InlineData("P&B Aero - Airlines - Ipswich", true, "NewPolicy", "Claim Stats/Loss Run/Claim Experience", 150, 95)]
    [InlineData("P&B Aero - Airlines - Ipswich", true, "NewPolicy", "MRC", 150, 90)]
    [InlineData("P&B Aero - Airlines - Ipswich", true, "NewPolicy", "Premium Stats", 150, 90)]
    [InlineData("P&B Aero - Airlines - Ipswich", true, "NewPolicy", "TMF", 150, 90)]
    [InlineData("P&B Aero - Airlines - Ipswich", true, "Renewal", "Policy Renewal", 150, 120)]
    [InlineData("P&B Aero - Airlines - London", false, "NewPolicy", "Claim Stats/Loss Run/Claim Experience", 150, 95)]
    [InlineData("P&B Aero - Airlines - London", false, "NewPolicy", "MRC", 150, 90)]
    [InlineData("P&B Aero - Airlines - London", false, "NewPolicy", "Premium Stats", 150, 90)]
    [InlineData("P&B Aero - Airlines - London", false, "NewPolicy", "TMF", 150, 90)]
    [InlineData("P&B Aero - Airlines - London", false, "Renewal", "Policy Renewal", 150, 120)]
    [InlineData("P&B Aero - Airlines - London", true, "NewPolicy", "Claim Stats/Loss Run/Claim Experience", 150, 95)]
    [InlineData("P&B Aero - Airlines - London", true, "NewPolicy", "MRC", 150, 90)]
    [InlineData("P&B Aero - Airlines - London", true, "NewPolicy", "Premium Stats", 150, 90)]
    [InlineData("P&B Aero - Airlines - London", true, "NewPolicy", "TMF", 150, 90)]
    [InlineData("P&B Aero - Airlines - London", true, "Renewal", "Policy Renewal", 150, 120)]
    [InlineData("P&B Aero - Contingent - Ipswich", false, "NewPolicy", "Claim Stats/Loss Run/Claim Experience", 150, 95)]
    [InlineData("P&B Aero - Contingent - Ipswich", false, "NewPolicy", "MRC", 150, 90)]
    [InlineData("P&B Aero - Contingent - Ipswich", false, "NewPolicy", "Premium Stats", 150, 90)]
    [InlineData("P&B Aero - Contingent - Ipswich", false, "NewPolicy", "TMF", 150, 90)]
    [InlineData("P&B Aero - Contingent - Ipswich", false, "Renewal", "Policy Renewal", 150, 120)]
    [InlineData("P&B Aero - Contingent - London", false, "NewPolicy", "Claim Stats/Loss Run/Claim Experience", 150, 95)]
    [InlineData("P&B Aero - Contingent - London", false, "NewPolicy", "MRC", 150, 90)]
    [InlineData("P&B Aero - Contingent - London", false, "NewPolicy", "Premium Stats", 150, 90)]
    [InlineData("P&B Aero - Contingent - London", false, "NewPolicy", "TMF", 150, 90)]
    [InlineData("P&B Aero - Contingent - London", false, "Renewal", "Policy Renewal", 150, 120)]
    [InlineData("P&B Aero - General Aviation - Ipswich", false, "NewPolicy", "Claim Stats/Loss Run/Claim Experience", 150, 95)]
    [InlineData("P&B Aero - General Aviation - Ipswich", false, "NewPolicy", "Draft Renewal Invite", 150, 90)]
    [InlineData("P&B Aero - General Aviation - Ipswich", false, "NewPolicy", "MRC", 150, 90)]
    [InlineData("P&B Aero - General Aviation - Ipswich", false, "NewPolicy", "Premium Stats", 150, 90)]
    [InlineData("P&B Aero - General Aviation - Ipswich", false, "NewPolicy", "TMF", 150, 90)]
    [InlineData("P&B Aero - General Aviation - Ipswich", false, "Renewal", "Policy Renewal", 150, 120)]
    [InlineData("P&B Aero - General Aviation - Ipswich", true, "NewPolicy", "Claim Stats/Loss Run/Claim Experience", 150, 95)]
    [InlineData("P&B Aero - General Aviation - Ipswich", true, "NewPolicy", "Draft Renewal Invite", 150, 90)]
    [InlineData("P&B Aero - General Aviation - Ipswich", true, "NewPolicy", "MRC", 150, 90)]
    [InlineData("P&B Aero - General Aviation - Ipswich", true, "NewPolicy", "Premium Stats", 150, 90)]
    [InlineData("P&B Aero - General Aviation - Ipswich", true, "NewPolicy", "TMF", 150, 90)]
    [InlineData("P&B Aero - General Aviation - Ipswich", true, "Renewal", "Policy Renewal", 150, 120)]
    [InlineData("P&B Aero - General Aviation - London", false, "NewPolicy", "Claim Stats/Loss Run/Claim Experience", 150, 95)]
    [InlineData("P&B Aero - General Aviation - London", false, "NewPolicy", "Draft Renewal Invite", 150, 90)]
    [InlineData("P&B Aero - General Aviation - London", false, "NewPolicy", "MRC", 150, 90)]
    [InlineData("P&B Aero - General Aviation - London", false, "NewPolicy", "Premium Stats", 150, 90)]
    [InlineData("P&B Aero - General Aviation - London", false, "NewPolicy", "TMF", 150, 90)]
    [InlineData("P&B Aero - General Aviation - London", false, "Renewal", "Policy Renewal", 150, 120)]
    [InlineData("P&B Aero - General Aviation - London", true, "NewPolicy", "Claim Stats/Loss Run/Claim Experience", 150, 95)]
    [InlineData("P&B Aero - General Aviation - London", true, "NewPolicy", "Draft Renewal Invite", 150, 90)]
    [InlineData("P&B Aero - General Aviation - London", true, "NewPolicy", "MRC", 150, 90)]
    [InlineData("P&B Aero - General Aviation - London", true, "NewPolicy", "Premium Stats", 150, 90)]
    [InlineData("P&B Aero - General Aviation - London", true, "NewPolicy", "TMF", 150, 90)]
    [InlineData("P&B Aero - General Aviation - London", true, "Renewal", "Policy Renewal", 150, 120)]
    public void DaysBeforeExpiryAndSLADaysBeforeExpiryCheckTest(string collectionName, bool hasParent, string eventName, string taskName, int daysBeforeExpiry, int? slaDaysBeforeExpiry)
    {
        // There should always be a PolicyConfig
        dynamic daysBeforeExpiryResult = GetResultRow(sql: $@"
            SELECT
                PolicyConfigId = pc.Id
              , pc.Collection
              , pc.DaysBeforeExpiry
            FROM
                Task.PolicyConfig pc
            WHERE
                pc.Collection = '{collectionName}'
                AND pc.IsDeleted = 0
                AND pc.ParentId {(hasParent ? "IS NOT NULL" : "IS NULL")};
            ");
        Assert.NotNull(daysBeforeExpiryResult);
        Assert.True(daysBeforeExpiryResult.DaysBeforeExpiry == daysBeforeExpiry, $"Expected DaysBeforeExpiry to be {daysBeforeExpiry}. It was {daysBeforeExpiryResult.DaysBeforeExpiry}. PolicyConfigId: {daysBeforeExpiryResult.PolicyConfigId}.");

        // But there may only be a PolicyConfigEvent if we are expecting it.
        dynamic slaDaysBeforeExpiryResult = GetResultRow(sql: $@"
            SELECT
                PolicyConfigId = pc.Id
              , pc.Collection
              , pc.DaysBeforeExpiry
              , PolicyConfigEventId = pce.Id
              , pce.SLADaysBeforeExpiry
              , evt.Name
              , tc.TaskName
            FROM
                Task.PolicyConfig pc
                INNER JOIN Task.PolicyConfigEvent pce
                    ON pce.PolicyConfigId = pc.Id
                       AND pce.IsDeleted = 0

                INNER JOIN Task.Event evt
                    ON evt.Id = pce.EventId

                INNER JOIN Task.TaskConfig tc
                    ON tc.Id = pce.TaskConfigId
            WHERE
                pc.Collection = '{collectionName}'
                AND evt.Name = '{eventName}'
                AND tc.TaskName = '{taskName}'
                AND pc.IsDeleted = 0
                AND pc.ParentId {(hasParent ? "IS NOT NULL" : "IS NULL")};
            ");
        if (slaDaysBeforeExpiry.HasValue)
        {
            Assert.NotNull(slaDaysBeforeExpiryResult);
            Assert.True(slaDaysBeforeExpiryResult.SLADaysBeforeExpiry == slaDaysBeforeExpiry, $"Expected SLADaysBeforeExpiry to be {slaDaysBeforeExpiry}. It was {slaDaysBeforeExpiryResult.SLADaysBeforeExpiry}. PolicyConfigEventId is {slaDaysBeforeExpiryResult.PolicyConfigEventId}.");
        }
        else if (slaDaysBeforeExpiryResult != null)
        {
            Assert.Fail($"Didn't expect to find a not deleted PolicyConfigEvent. PolicyConfigEventId is {slaDaysBeforeExpiryResult.PolicyConfigEventId}.");
        }
    }

    /// <summary>
    /// Constructor - do not change
    /// </summary>
    /// <param name="fixture"></param>
    public ValidateConfigurationTests(DatabaseFixture fixture) : base(fixture)
    {
    }
}
