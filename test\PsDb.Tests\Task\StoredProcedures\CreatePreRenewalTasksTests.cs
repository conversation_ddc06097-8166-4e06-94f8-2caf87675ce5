﻿// #define DiagnosticOutputForPRP // This can be uncommented to provide some extra diagnosis logging during tests.
using System.Data;
using System.Diagnostics.CodeAnalysis;
using Xunit.Abstractions;

namespace PsDb.Tests.Task.StoredProcedures;

[ExcludeFromCodeCoverage]
public class CreatePreRenewalTasksTests : PlacementStoreTestBase
{
    private readonly ITestOutputHelper output;

    public enum EventType
    {
        NewPolicy,
        Renewal
    }

    /// <summary>
    /// Just want to prove that it is possible, and valid not to match.
    /// I can see this test needing to be altered if the servicing platform
    /// picked has a proper rule added.
    /// </summary>
    [Fact]
    public void PreRenewalsTest()
    {
        Guid runId = Guid.NewGuid();
        Guid policyConfigId = Guid.NewGuid();
        Guid eventId = Guid.NewGuid();
        Guid taskConfigId = Guid.NewGuid();
        int dataSourceInstanceId = 50000;
        int policyId = 1;
        int placementId = 1000;
        Guid newPolicyEventId = new Guid("337D25CF-842B-E911-8936-645D867D6294");
        Guid postBindEventId = new Guid("D04F42C8-1C5D-E911-9130-005056ADC758");
        Guid renewalEventId = new Guid("327D25CF-842B-E911-8936-645D867D6294");

        dynamic runRecord = CreateRow(
        tableName: "Task.Run",
        values: new
        {
            Id = runId,
            UTCDateFrom = DateTime.UtcNow.AddMinutes(240),
            UTCDateTo = DateTime.Now
        });

        dynamic policyRecord = CreateRow(
           tableName: "dbo.Policy",
           values: new
           {
               PolicyId = policyId,
               PolicyReference = "45075156",
               DataSourceInstanceId = 50000,
               PolicyKey = "15222RE18",
               PolicyStatusId = 2,
               InceptionDate = DateTime.UtcNow.AddYears(-1),
               ExpiryDate = DateTime.UtcNow.AddDays(45),
               PlacementId = placementId,
               RefPolicyStatusId = 102
           });

        dynamic placementPolicyRecord = CreateRow(
         tableName: "dbo.PlacementPolicy",
         values: new
         {
             PlacementPolicyId = 1,
             PlacementId = placementId,
             PolicyId = policyId,
             DataSourceInstanceId = dataSourceInstanceId,
             IsDeleted = 0
         });

        dynamic policyOrganisationHierarchyRecord = CreateRow(tableName: "PAS.Organisation", values: new
        {
            Organisation = "Org",
            OrganisationKey = "Org Key 1",
            DataSourceInstanceId = (int)DataSourceInstance.Eclipse,
            PASOrganisationId = 123,
            IsDeleted = false
        });

        dynamic psPolicyOrganisationHierarchyRecord = CreateRow(tableName: "PS.Organisation", values: new
        {
            OrganisationKey = "Org Key 1",
            DataSourceInstanceId = (int)DataSourceInstance.Eclipse,
            IsDeleted = false
        });

        dynamic placementListenerRecord = CreateRow(
         tableName: "dbo.PlacementListener",
         values: new
         {
             PlacementListenerID = 1,
             PlacementID = 2,
             IsDeleted = 0,
             PlacementDataSourceInstanceID = dataSourceInstanceId,
         });


        dynamic policySectionRecord = CreateRow(
         tableName: "dbo.PolicySection",
         values: new
         {
             PolicySectionID = 1,
             PACTPolicySectionID = 1294297,
             PolicyID = policyId,
             PACTPolicyID = 1,
             PolicyKey = "PolicyKey",
             PolicySectionReference = "PolicySectionReference",
             PolicySectionStatusKey = "1",
             IsDeleted = 0,
             DataSourceInstanceID = dataSourceInstanceId,
             PolicySectionKey = "PolicySectionKey"
         });

        dynamic policySectionProductRecord = CreateRow(
         tableName: "dbo.PolicySectionProduct",
         values: new
         {
             PolicySectionProductID = 1,
             DataSourceInstanceID = dataSourceInstanceId,
             PolicySectionID = 1,
             PolicySectionKey = "PolicySectionKey",
             ProductID = 1
         });


        dynamic referenceProductClassRecord = CreateRow(
         tableName: "Reference.ProductClass",
         values: new
         {
             ProductClassID = 1,
             ProductClassName = "ProductClassName",
             ProductClassDescription = "ProductClassDescription",
             IsDeleted = 0
         });

        var insertCount = 0;
        ExecuteStoredProcedureWithoutResult(storedProcedureName: "Task.CreatePreRenewalTasks", values: new { runId, insertCount });


        dynamic policyRenewalTaskRow = GetResultRow(tableName: "Task.PolicyRenewalTask", whereClause: $"RunId='{runId}'");
        Assert.Null(policyRenewalTaskRow);

        dynamic row = GetResultRow(tableName: "Task.run", whereClause: "Id = '" + runId + "'");

        Assert.Equal(row.Id, runId);
        Assert.Equal(DateTime.UtcNow.Date, ((DateTime)row.UTCDateCompleted).Date);

    }

    /// <summary>
    /// Generic test for PRP task.  This will currently only work for Eclipse policies as the datasource instance
    /// and org configuration are hardcoded to Eclipse
    ///  We have been asked to stop the MRC task being generated for "Team" which is a "Collection".
    ///  The way to make this work is to delete the Task.PolicyConfigEvent record that applies this "rule"!
    ///  SG Specs - P&C
    /// </summary>
    [Theory]
    // Ensure no MRC tasks for SG Specs - P&C
    [InlineData("018B621D-752D-EA11-913C-005056ADC758", "SG Specs - P&C", "Willis (Singapore) Pte Ltd (Direct)", "GMI (Singapore)", "Asia Pacific (Singapore)", EventType.NewPolicy, RefPolicyStatus.Lapsed, 0)]
    [InlineData("018B621D-752D-EA11-913C-005056ADC758", "SG Specs - P&C", "Willis (Singapore) Pte Ltd (Direct)", "GMI (Singapore)", "Liability Energy (Singapore)", EventType.NewPolicy, RefPolicyStatus.Lapsed, 0)]
    [InlineData("018B621D-752D-EA11-913C-005056ADC758", "SG Specs - P&C", "Willis (Singapore) Pte Ltd (Direct)", "GMI (Singapore)", "Liability International (Singapore)", EventType.NewPolicy, RefPolicyStatus.Lapsed, 0)]
    [InlineData("018B621D-752D-EA11-913C-005056ADC758", "SG Specs - P&C", "Willis (Singapore) Pte Ltd (Direct)", "GMI (Singapore)", "Liability Product Recall (Singapore)", EventType.NewPolicy, RefPolicyStatus.Lapsed, 0)]
    [InlineData("018B621D-752D-EA11-913C-005056ADC758", "SG Specs - P&C", "Willis (Singapore) Pte Ltd (Direct)", "GMI (Singapore)", "Terrorism (Singapore)", EventType.NewPolicy, RefPolicyStatus.Lapsed, 0)]
    [InlineData("018B621D-752D-EA11-913C-005056ADC758", "SG Specs - P&C", "Willis (Singapore) Pte Ltd (Reinsurance)", "GMI (Singapore)", "Asia Pacific (Singapore)", EventType.NewPolicy, RefPolicyStatus.Lapsed, 0)]
    [InlineData("018B621D-752D-EA11-913C-005056ADC758", "SG Specs - P&C", "Willis (Singapore) Pte Ltd (Reinsurance)", "GMI (Singapore)", "Liability Energy (Singapore)", EventType.NewPolicy, RefPolicyStatus.Lapsed, 0)]
    [InlineData("018B621D-752D-EA11-913C-005056ADC758", "SG Specs - P&C", "Willis (Singapore) Pte Ltd (Reinsurance)", "GMI (Singapore)", "Liability International (Singapore)", EventType.NewPolicy, RefPolicyStatus.Lapsed, 0)]
    [InlineData("018B621D-752D-EA11-913C-005056ADC758", "SG Specs - P&C", "Willis (Singapore) Pte Ltd (Reinsurance)", "GMI (Singapore)", "Liability Product Recall (Singapore)", EventType.NewPolicy, RefPolicyStatus.Lapsed, 0)]
    [InlineData("018B621D-752D-EA11-913C-005056ADC758", "SG Specs - P&C", "Willis (Singapore) Pte Ltd (Reinsurance)", "GMI (Singapore)", "Terrorism (Singapore)", EventType.NewPolicy, RefPolicyStatus.Lapsed, 0)]

    [InlineData("1D760CF4-D569-4C69-9BED-C7DC6E2B01BE", "SG Retail - Client Service Team 1", "Willis (Singapore) Pte Ltd (Direct)", "Retail (Singapore)", "Asia Placement", EventType.Renewal, RefPolicyStatus.Live, 1, "Policy Renewal")]
    [InlineData("1D760CF4-D569-4C69-9BED-C7DC6E2B01BE", "SG Retail - Client Service Team 1", "Willis (Singapore) Pte Ltd (Direct)", "Retail (Singapore)", "Client Service Team 1", EventType.Renewal, RefPolicyStatus.Live, 1, "Policy Renewal")]
    [InlineData("1D760CF4-D569-4C69-9BED-C7DC6E2B01BE", "SG Retail - Client Service Team 1", "Willis (Singapore) Pte Ltd (Direct)", "Retail (Singapore)", "Client Service Team 3", EventType.Renewal, RefPolicyStatus.Live, 1, "Policy Renewal")]
    [InlineData("1D760CF4-D569-4C69-9BED-C7DC6E2B01BE", "SG Retail - Client Service Team 1", "Willis (Singapore) Pte Ltd (Direct)", "Retail (Singapore)", "Corporate (Singapore)", EventType.Renewal, RefPolicyStatus.Live, 1, "Policy Renewal")]
    [InlineData("1D760CF4-D569-4C69-9BED-C7DC6E2B02BE", "SG Retail - Client Service Team 2", "Willis (Singapore) Pte Ltd (Direct)", "Retail (Singapore)", "Affinity (Singapore)", EventType.Renewal, RefPolicyStatus.Live, 1, "Policy Renewal")]
    [InlineData("1D760CF4-D569-4C69-9BED-C7DC6E2B02BE", "SG Retail - Client Service Team 2", "Willis (Singapore) Pte Ltd (Direct)", "Retail (Singapore)", "Client Service Team 2", EventType.Renewal, RefPolicyStatus.Live, 1, "Policy Renewal")]
    [InlineData("1D760CF4-D569-4C69-9BED-C7DC6E2B02BE", "SG Retail - Client Service Team 2", "Willis (Singapore) Pte Ltd (Direct)", "Retail (Singapore)", "Commercial (Singapore)", EventType.Renewal, RefPolicyStatus.Live, 1, "Policy Renewal")]
    [InlineData("68EF1EA9-8E3E-E911-912F-005056ADC758", "P&B Finex - D&O", "", "Finex Global", "D&O - International", EventType.NewPolicy, RefPolicyStatus.Lapsed, 3, "DMS Folder Creation|Claim Stats/Loss Run/Claim Experience|MRC")]
    [InlineData("68EF1EA9-8E3E-E911-912F-005056ADC758", "P&B Finex - D&O", "", "Finex Global", "D&O - North America", EventType.NewPolicy, RefPolicyStatus.Lapsed, 3, "DMS Folder Creation|Claim Stats/Loss Run/Claim Experience|MRC")]
    [InlineData("718AB34B-B041-E911-912F-005056ADC758", "P&B Finex - PI", "", "Finex Global", "FINEX National PI (Surveyors)", EventType.NewPolicy, RefPolicyStatus.Lapsed, 2, "DMS Folder Creation|MRC")]
    [InlineData("7DF19C4A-C12C-4EBE-8D0C-637C4902D4ED", "SG Retail - Finex", "Willis (Singapore) Pte Ltd (Direct)", "Retail (Singapore)", "Asia Finex M&A", EventType.Renewal, RefPolicyStatus.Live, 1, "Policy Renewal")]
    [InlineData("7DF19C4A-C12C-4EBE-8D0C-637C4902D4ED", "SG Retail - Finex", "Willis (Singapore) Pte Ltd (Direct)", "Retail (Singapore)", "Asia Finex", EventType.Renewal, RefPolicyStatus.Live, 1, "Policy Renewal")]
    [InlineData("7DF19C4A-C12C-4EBE-8D0C-637C4902D4ED", "SG Retail - Finex", "Willis (Singapore) Pte Ltd (Direct)", "Retail (Singapore)", "Facultative FINEX", EventType.Renewal, RefPolicyStatus.Live, 1, "Policy Renewal")]
    [InlineData("7DF19C4A-C12C-4EBE-8D0C-637C4902D4ED", "SG Retail - Finex", "Willis (Singapore) Pte Ltd (Direct)", "Retail (Singapore)", "FINEX (Singapore)", EventType.Renewal, RefPolicyStatus.Live, 1, "Policy Renewal")]
    [InlineData("9C05CA66-E249-4FA1-9AD3-7B9F722CE886", "SG Retail - Construction", "Willis (Singapore) Pte Ltd (Direct)", "Retail (Singapore)", "Construction (Singapore)", EventType.Renewal, RefPolicyStatus.Live, 1, "Policy Renewal")]
    [InlineData("CEC966F7-0144-4D5A-9DF1-7C79CF00F742", "SG Retail - Network", "Willis (Singapore) Pte Ltd (Direct)", "Retail (Singapore)", "Network (Singapore)", EventType.Renewal, RefPolicyStatus.Live, 1, "Policy Renewal")]
    [InlineData("EDA1FA3E-B041-E911-912F-005056ADC758", "P&B Terrorism", "", "DIFC", "Terrorism - DIFC", EventType.NewPolicy, RefPolicyStatus.Lapsed, 2, "MRC|DMS Folder Creation")]
    [InlineData("EDA1FA3E-B041-E911-912F-005056ADC758", "P&B Terrorism", "", "DIFC", "Terrorism", EventType.NewPolicy, RefPolicyStatus.Lapsed, 2, "MRC|DMS Folder Creation")]
    [InlineData("EDA1FA3E-B041-E911-912F-005056ADC758", "P&B Terrorism", "", "Financial Solutions US", "Terrorism", EventType.NewPolicy, RefPolicyStatus.Lapsed, 2, "MRC|DMS Folder Creation")]
    [InlineData("EDA1FA3E-B041-E911-912F-005056ADC758", "P&B Terrorism", "", "Financial Solutions US", "WNA-L Terrorism", EventType.NewPolicy, RefPolicyStatus.Lapsed, 2, "MRC|DMS Folder Creation")]
    [InlineData("EDA1FA3E-B041-E911-912F-005056ADC758", "P&B Terrorism", "", "GMI", "Terrorism (Singapore)", EventType.NewPolicy, RefPolicyStatus.Lapsed, 2, "MRC|DMS Folder Creation")]
    [InlineData("EDA1FA3E-B041-E911-912F-005056ADC758", "P&B Terrorism", "", "GMI", "WNA-L Terrorism", EventType.NewPolicy, RefPolicyStatus.Lapsed, 2, "MRC|DMS Folder Creation")]
    [InlineData("EDA1FA3E-B041-E911-912F-005056ADC758", "P&B Terrorism", "", "Terrorism", "Terrorism (Singapore)", EventType.NewPolicy, RefPolicyStatus.Lapsed, 2, "MRC|DMS Folder Creation")]
    [InlineData("EDA1FA3E-B041-E911-912F-005056ADC758", "P&B Terrorism", "", "Terrorism", "Terrorism - DIFC", EventType.NewPolicy, RefPolicyStatus.Lapsed, 2, "MRC|DMS Folder Creation")]
    [InlineData("EDA1FA3E-B041-E911-912F-005056ADC758", "P&B Terrorism", "", "Willis Ltd Terrorism", "Terrorism - DIFC", EventType.NewPolicy, RefPolicyStatus.Lapsed, 2, "MRC|DMS Folder Creation")]
    [InlineData("EDA1FA3E-B041-E911-912F-005056ADC758", "P&B Terrorism", "", "Willis Ltd Terrorism", "Terrorism (Singapore)", EventType.NewPolicy, RefPolicyStatus.Lapsed, 2, "MRC|DMS Folder Creation")]
    [InlineData("EDA1FA3E-B041-E911-912F-005056ADC758", "P&B Terrorism", "", "WNA London", "Terrorism (Singapore)", EventType.NewPolicy, RefPolicyStatus.Lapsed, 2, "MRC|DMS Folder Creation")]
    [InlineData("EDA1FA3E-B041-E911-912F-005056ADC758", "P&B Terrorism", "", "WNA London", "WNA-L Terrorism", EventType.NewPolicy, RefPolicyStatus.Lapsed, 2, "MRC|DMS Folder Creation")]
    [InlineData("66A6E769-B041-E911-912F-005056ADC758", "P&B Finex - Cyber", "", "FINEX Global", "Cyber - UK Large", EventType.Renewal, RefPolicyStatus.Live, 1, "Policy Renewal")]
    [InlineData("66A6E769-B041-E911-912F-005056ADC758", "P&B Finex - Cyber", "", "FINEX Global", "Cyber - UK Large", EventType.NewPolicy, RefPolicyStatus.Lapsed, 3, "MRC|DMS Folder Creation|Claim Stats/Loss Run/Claim Experience")]
    [InlineData("66A6E769-B041-E911-912F-005056ADC758", "P&B Finex - Cyber", "", "FINEX Global", "Cyber - UK Regions", EventType.Renewal, RefPolicyStatus.Live, 1, "Policy Renewal")]
    [InlineData("66A6E769-B041-E911-912F-005056ADC758", "P&B Finex - Cyber", "", "FINEX Global", "Cyber - UK Regions", EventType.NewPolicy, RefPolicyStatus.Lapsed, 3, "MRC|DMS Folder Creation|Claim Stats/Loss Run/Claim Experience")]
    [InlineData("66A6E769-B041-E911-912F-005056ADC758", "P&B Finex - Cyber", "", "FINEX Global", "Cyber - US", EventType.Renewal, RefPolicyStatus.Live, 1, "Policy Renewal")]
    [InlineData("66A6E769-B041-E911-912F-005056ADC758", "P&B Finex - Cyber", "", "FINEX Global", "Cyber - US", EventType.NewPolicy, RefPolicyStatus.Lapsed, 3, "MRC|DMS Folder Creation|Claim Stats/Loss Run/Claim Experience")]
    [InlineData("66A6E769-B041-E911-912F-005056ADC758", "P&B Finex - Cyber", "", "FINEX Global", "Cyber - International", EventType.Renewal, RefPolicyStatus.Live, 1, "Policy Renewal")]
    [InlineData("66A6E769-B041-E911-912F-005056ADC758", "P&B Finex - Cyber", "", "FINEX Global", "Cyber - International", EventType.NewPolicy, RefPolicyStatus.Lapsed, 3, "MRC|DMS Folder Creation|Claim Stats/Loss Run/Claim Experience")]
    [InlineData("66A6E769-B041-E911-912F-005056ADC758", "P&B Finex - Cyber", "", "FINEX Global", "Cyber - International Mid-Market", EventType.Renewal, RefPolicyStatus.Live, 1, "Policy Renewal")]
    [InlineData("66A6E769-B041-E911-912F-005056ADC758", "P&B Finex - Cyber", "", "FINEX Global", "Cyber - International Mid-Market", EventType.NewPolicy, RefPolicyStatus.Lapsed, 3, "MRC|DMS Folder Creation|Claim Stats/Loss Run/Claim Experience")]
    [InlineData("43C14C7E-BCAE-E911-9136-005056ADC758", "P&B Natural Resources - Upstream", "", "Energy", "Upstream - Fac", EventType.Renewal, RefPolicyStatus.Live, 1, "Policy Renewal")]
    [InlineData("43C14C7E-BCAE-E911-9136-005056ADC758", "P&B Natural Resources - Upstream", "", "Energy", "Upstream - Fac", EventType.NewPolicy, RefPolicyStatus.Lapsed, 2, "MRC|DMS Folder Creation")]
    [InlineData("062C7DAA-D535-4A36-8CF2-AF56BC9BC6E0", "P&B Natural Resources - Downstream - Fac", "", "GMI", "Downstream - Fac", EventType.Renewal, RefPolicyStatus.Live, 1, "Policy Renewal")]
    [InlineData("062C7DAA-D535-4A36-8CF2-AF56BC9BC6E0", "P&B Natural Resources - Downstream - Fac", "", "GMI", "Downstream - Fac", EventType.NewPolicy, RefPolicyStatus.Lapsed, 2, "MRC|DMS Folder Creation")]
    [InlineData("72845D96-A943-4170-B4BC-2674C680D121", "P&B Natural Resources - DIFC", "", "DIFC", "Downstream - DIFC", EventType.Renewal, RefPolicyStatus.Live, 1, "Policy Renewal")]
    [InlineData("72845D96-A943-4170-B4BC-2674C680D121", "P&B Natural Resources - DIFC", "", "DIFC", "Downstream - DIFC", EventType.NewPolicy, RefPolicyStatus.Lapsed, 2, "MRC|DMS Folder Creation")]

    // These ones need products as well as teams.
    // This is to test where we changed the trigger from 120 days before to 150 days before for Aerospace teams.
    // We are testing a new policy where the number of days before isn't used.
    // Then a number of days where it is just too early and one when it is just in to prove those work once the change has been made.
    [InlineData("18C24C7E-BCAE-E911-9136-005056ADC758", "P&B Aero - Airlines - London", "", "Aerospace", "Africa Team", EventType.Renewal, RefPolicyStatus.Live, 0, null, 151, null, "Airlines", "Aviation")]
    [InlineData("18C24C7E-BCAE-E911-9136-005056ADC758", "P&B Aero - Airlines - London", "", "Aerospace", "Africa Team", EventType.Renewal, RefPolicyStatus.Live, 1, "Policy Renewal", 149, null, "Airlines", "Aviation")]
    [InlineData("22C24C7E-BCAE-E911-9136-005056ADC758", "P&B Aero - Airlines - Ipswich", "", "Aerospace", "Ipswich", EventType.NewPolicy, RefPolicyStatus.Lapsed, 4, "Claim Stats/Loss Run/Claim Experience|MRC|Premium Stats|TMF", null, null, "Airlines", "Aviation")]
    [InlineData("22C24C7E-BCAE-E911-9136-005056ADC758", "P&B Aero - Airlines - Ipswich", "", "Aerospace", "Ipswich", EventType.Renewal, RefPolicyStatus.Live, 0, null, 151, null, "Airlines", "Aviation")]
    [InlineData("22C24C7E-BCAE-E911-9136-005056ADC758", "P&B Aero - Airlines - Ipswich", "", "Aerospace", "Ipswich", EventType.Renewal, RefPolicyStatus.Live, 1, "Policy Renewal", 149, null, "Airlines", "Aviation")]
    [InlineData("2DC24C7E-BCAE-E911-9136-005056ADC758", "P&B Aero - Aerospace - London", "", "Aerospace", "Asia Pacific", EventType.NewPolicy, RefPolicyStatus.Lapsed, 4, "Premium Stats|TMF|Claim Stats/Loss Run/Claim Experience|MRC", null, null, "Aerospace", "Aviation")]
    [InlineData("2DC24C7E-BCAE-E911-9136-005056ADC758", "P&B Aero - Aerospace - London", "", "Aerospace", "Asia Pacific", EventType.Renewal, RefPolicyStatus.Live, 0, null, 151, null, "Aerospace", "Aviation")]
    [InlineData("2DC24C7E-BCAE-E911-9136-005056ADC758", "P&B Aero - Aerospace - London", "", "Aerospace", "Asia Pacific", EventType.Renewal, RefPolicyStatus.Live, 1, "Policy Renewal", 149, null, "Aerospace", "Aviation")]
    [InlineData("38C24C7E-BCAE-E911-9136-005056ADC758", "P&B Aero - Aerospace - Ipswich", "", "Aerospace", "Ipswich", EventType.NewPolicy, RefPolicyStatus.Lapsed, 5, "Claim Stats/Loss Run/Claim Experience|Draft Renewal Invite|MRC|Premium Stats|TMF", null, null, "Aerospace", "Aviation")]
    [InlineData("38C24C7E-BCAE-E911-9136-005056ADC758", "P&B Aero - Aerospace - Ipswich", "", "Aerospace", "Ipswich", EventType.Renewal, RefPolicyStatus.Live, 0, null, 151, null, "Aerospace", "Aviation")]
    [InlineData("38C24C7E-BCAE-E911-9136-005056ADC758", "P&B Aero - Aerospace - Ipswich", "", "Aerospace", "Ipswich", EventType.Renewal, RefPolicyStatus.Live, 1, "Policy Renewal", 149, null, "Aerospace", "Aviation")]
    [InlineData("43C24C7E-BCAE-E911-9136-005056ADC758", "P&B Aero - General Aviation - London", "", "Aerospace", "Continental Europe", EventType.NewPolicy, RefPolicyStatus.Lapsed, 5, "Claim Stats/Loss Run/Claim Experience|Draft Renewal Invite|MRC|Premium Stats|TMF", null, null, "General Aviation", "Aviation")]
    [InlineData("43C24C7E-BCAE-E911-9136-005056ADC758", "P&B Aero - General Aviation - London", "", "Aerospace", "Continental Europe", EventType.Renewal, RefPolicyStatus.Live, 0, null, 151, null, "General Aviation", "Aviation")]
    [InlineData("43C24C7E-BCAE-E911-9136-005056ADC758", "P&B Aero - General Aviation - London", "", "Aerospace", "Continental Europe", EventType.Renewal, RefPolicyStatus.Live, 1, "Policy Renewal", 149, null, "General Aviation", "Aviation")]
    [InlineData("4DC24C7E-BCAE-E911-9136-005056ADC758", "P&B Aero - General Aviation - Ipswich", "", "Aerospace", "Ipswich", EventType.NewPolicy, RefPolicyStatus.Lapsed, 5, "Claim Stats/Loss Run/Claim Experience|Draft Renewal Invite|MRC|Premium Stats|TMF", null, null, "General Aviation", "Aviation")]
    [InlineData("4DC24C7E-BCAE-E911-9136-005056ADC758", "P&B Aero - General Aviation - Ipswich", "", "Aerospace", "Ipswich", EventType.Renewal, RefPolicyStatus.Live, 0, null, 151, null, "General Aviation", "Aviation")]
    [InlineData("4DC24C7E-BCAE-E911-9136-005056ADC758", "P&B Aero - General Aviation - Ipswich", "", "Aerospace", "Ipswich", EventType.Renewal, RefPolicyStatus.Live, 1, "Policy Renewal", 149, null, "General Aviation", "Aviation")]
    [InlineData("61094926-FBD9-E911-9138-005056ADC758", "P&B Aero - Contingent - London", "", "Aerospace", "Contingent - Lon", EventType.NewPolicy, RefPolicyStatus.Lapsed, 4, "Claim Stats/Loss Run/Claim Experience|MRC|Premium Stats|TMF", null, null, "Aerospace", "Aviation")]
    [InlineData("61094926-FBD9-E911-9138-005056ADC758", "P&B Aero - Contingent - London", "", "Aerospace", "Contingent - Lon", EventType.Renewal, RefPolicyStatus.Live, 0, null, 151, null, "Aerospace", "Aviation")]
    [InlineData("61094926-FBD9-E911-9138-005056ADC758", "P&B Aero - Contingent - London", "", "Aerospace", "Contingent - Lon", EventType.Renewal, RefPolicyStatus.Live, 1, "Policy Renewal", 149, null, "Aerospace", "Aviation")]
    [InlineData("62094926-FBD9-E911-9138-005056ADC758", "P&B Aero - Contingent - Ipswich", "", "Aerospace", "Contingent - Ips", EventType.NewPolicy, RefPolicyStatus.Lapsed, 4, "Claim Stats/Loss Run/Claim Experience|MRC|Premium Stats|TMF", null, null, "Aerospace", "Aviation")]
    [InlineData("62094926-FBD9-E911-9138-005056ADC758", "P&B Aero - Contingent - Ipswich", "", "Aerospace", "Contingent - Ips", EventType.Renewal, RefPolicyStatus.Live, 0, null, 151, null, "Aerospace", "Aviation")]
    [InlineData("62094926-FBD9-E911-9138-005056ADC758", "P&B Aero - Contingent - Ipswich", "", "Aerospace", "Contingent - Ips", EventType.Renewal, RefPolicyStatus.Live, 1, "Policy Renewal", 149, null, "Aerospace", "Aviation")]
    [InlineData("A20FE2DB-F7D9-E911-9138-005056ADC758", "P&B Aero - Airlines - London", "", "Aerospace", "Airlines - Lon", EventType.NewPolicy, RefPolicyStatus.Lapsed, 4, "Claim Stats/Loss Run/Claim Experience|MRC|Premium Stats|TMF", null, null, "Aerospace", "Aviation")]
    [InlineData("A20FE2DB-F7D9-E911-9138-005056ADC758", "P&B Aero - Airlines - London", "", "Aerospace", "Airlines - Lon", EventType.Renewal, RefPolicyStatus.Live, 0, null, 151, null, "Aerospace", "Aviation")]
    [InlineData("A20FE2DB-F7D9-E911-9138-005056ADC758", "P&B Aero - Airlines - London", "", "Aerospace", "Airlines - Lon", EventType.Renewal, RefPolicyStatus.Live, 1, "Policy Renewal", 149, null, "Aerospace", "Aviation")]
    [InlineData("A30FE2DB-F7D9-E911-9138-005056ADC758", "P&B Aero - Airlines - Ipswich", "", "Aerospace", "Airlines - Ips", EventType.NewPolicy, RefPolicyStatus.Lapsed, 4, "Premium Stats|TMF|Claim Stats/Loss Run/Claim Experience|MRC", null, null, "Aerospace", "Aviation")]
    [InlineData("A30FE2DB-F7D9-E911-9138-005056ADC758", "P&B Aero - Airlines - Ipswich", "", "Aerospace", "Airlines - Ips", EventType.Renewal, RefPolicyStatus.Live, 0, null, 151, null, "Aerospace", "Aviation")]
    [InlineData("A30FE2DB-F7D9-E911-9138-005056ADC758", "P&B Aero - Airlines - Ipswich", "", "Aerospace", "Airlines - Ips", EventType.Renewal, RefPolicyStatus.Live, 1, "Policy Renewal", 149, null, "Aerospace", "Aviation")]
    [InlineData("A40FE2DB-F7D9-E911-9138-005056ADC758", "P&B Aero - Aerospace - London", "", "Aerospace", "Aerospace Products - Lon", EventType.NewPolicy, RefPolicyStatus.Lapsed, 4, "Premium Stats|TMF|Claim Stats/Loss Run/Claim Experience|MRC", null)]
    [InlineData("A40FE2DB-F7D9-E911-9138-005056ADC758", "P&B Aero - Aerospace - London", "", "Aerospace", "Aerospace Products - Lon", EventType.Renewal, RefPolicyStatus.Live, 0, null, 151)]
    [InlineData("A40FE2DB-F7D9-E911-9138-005056ADC758", "P&B Aero - Aerospace - London", "", "Aerospace", "Aerospace Products - Lon", EventType.Renewal, RefPolicyStatus.Live, 1, "Policy Renewal", 149)]
    [InlineData("A50FE2DB-F7D9-E911-9138-005056ADC758", "P&B Aero - Aerospace - Ipswich", "", "Aerospace", "Aerospace Products - Ips", EventType.NewPolicy, RefPolicyStatus.Lapsed, 5, "Claim Stats/Loss Run/Claim Experience|Draft Renewal Invite|MRC|Premium Stats|TMF", null)]
    [InlineData("A50FE2DB-F7D9-E911-9138-005056ADC758", "P&B Aero - Aerospace - Ipswich", "", "Aerospace", "Aerospace Products - Ips", EventType.Renewal, RefPolicyStatus.Live, 0, null, 151)]
    [InlineData("A50FE2DB-F7D9-E911-9138-005056ADC758", "P&B Aero - Aerospace - Ipswich", "", "Aerospace", "Aerospace Products - Ips", EventType.Renewal, RefPolicyStatus.Live, 1, "Policy Renewal", 149)]
    [InlineData("A60FE2DB-F7D9-E911-9138-005056ADC758", "P&B Aero - General Aviation - London", "", "Aerospace", "General Aviation - Lon", EventType.NewPolicy, RefPolicyStatus.Lapsed, 5, "Claim Stats/Loss Run/Claim Experience|Draft Renewal Invite|MRC|Premium Stats|TMF", null)]
    [InlineData("A60FE2DB-F7D9-E911-9138-005056ADC758", "P&B Aero - General Aviation - London", "", "Aerospace", "General Aviation - Lon", EventType.Renewal, RefPolicyStatus.Live, 0, null, 151)]
    [InlineData("A60FE2DB-F7D9-E911-9138-005056ADC758", "P&B Aero - General Aviation - London", "", "Aerospace", "General Aviation - Lon", EventType.Renewal, RefPolicyStatus.Live, 1, "Policy Renewal", 149)]
    [InlineData("A70FE2DB-F7D9-E911-9138-005056ADC758", "P&B Aero - General Aviation - Ipswich", "", "Aerospace", "General Aviation - Ips", EventType.NewPolicy, RefPolicyStatus.Lapsed, 5, "Claim Stats/Loss Run/Claim Experience|Draft Renewal Invite|MRC|Premium Stats|TMF", null)]
    [InlineData("A70FE2DB-F7D9-E911-9138-005056ADC758", "P&B Aero - General Aviation - Ipswich", "", "Aerospace", "General Aviation - Ips", EventType.Renewal, RefPolicyStatus.Live, 0, null, 151)]
    [InlineData("A70FE2DB-F7D9-E911-9138-005056ADC758", "P&B Aero - General Aviation - Ipswich", "", "Aerospace", "General Aviation - Ips", EventType.Renewal, RefPolicyStatus.Live, 1, "Policy Renewal", 149)]
    public void GenericTaskConfiguration(string policyConfigId, string collection, string legalEntity, string businessUnit, string team, EventType eventType, RefPolicyStatus policyStatus, int taskCount, string? taskTypes = null, int? expiryDateDaysAfterNow = null, string? classOfBusiness = null, string? productName = null, string? className = null)
    {
        // Make sure Policy Configuration we are testing against exists
        dynamic policyConfigRow = GetResultRow(tableName: "Task.PolicyConfig", whereClause: @$"Id ='{policyConfigId}'");
        Assert.NotNull(policyConfigRow);
        Assert.Equal(expected: collection, actual: policyConfigRow.Collection);

        var taskTypeArray = taskTypes?.Split("|");

#if DiagnosticOutputForPRP
        // Some sanity checking going on.
        var rows = GetResultRows(sql: $@"
        SELECT CollectionName = pc.Collection,
               tc.BusinessContext,
               tc.Category,
               tc.TaskName,
               pc.DataSourceInstanceId,
               LegalEntity = pcrle.Name,
               BusinessUnit = pcrbu.Name,
               TeamName = pcrt.Name,
               AgencyName = pcra.Name,
               BranchName = pcrb.Name,
               DepartmentName = pcrd.Name,
               FunctionName = pcrf.Name,
               UnitName = pcru.Name,
               EventName = e.Name,
               pc.DaysBeforeExpiry,
               pc.StartDate,
               pc.RequiresPlacement,
               pc.SendOnAuthorisedStatus,
               pc.PolicyReferenceRestriction,
               ProductStuff = STRING_AGG(
                                            CONCAT(
                                                      'II:',
                                                      cbl.IsInclusive,
                                                      ' COB:',
                                                      cbi.ClassOfBusiness,
                                                      ' C:',
                                                      cbi.Class,
                                                      ' P:',
                                                      cbi.Product
                                                  ),
                                            '|'
                                        ),
               pc.Id,
               pce.TaskConfigId,
               pce.EventId

        FROM Task.PolicyConfig pc
            LEFT JOIN Task.PolicyConfigRole pcrle
                ON pcrle.PolicyConfigId = pc.Id
                   AND pcrle.Role = 'Legal Entity'
            LEFT JOIN Task.PolicyConfigRole pcrbu
                ON pcrbu.PolicyConfigId = pc.Id
                   AND pcrbu.Role = 'Business Unit'
            LEFT JOIN Task.PolicyConfigRole pcrt
                ON pcrt.PolicyConfigId = pc.Id
                   AND pcrt.Role = 'Team'
            LEFT JOIN Task.PolicyConfigRole pcra
                ON pcra.PolicyConfigId = pc.Id
                   AND pcra.Role = 'Agency'
            LEFT JOIN Task.PolicyConfigRole pcrb
                ON pcrb.PolicyConfigId = pc.Id
                   AND pcrb.Role = 'Branch'
            LEFT JOIN Task.PolicyConfigRole pcrd
                ON pcrd.PolicyConfigId = pc.Id
                   AND pcrd.Role = 'Department'
            LEFT JOIN Task.PolicyConfigRole pcrf
                ON pcrf.PolicyConfigId = pc.Id
                   AND pcrf.Role = 'Function'
            LEFT JOIN Task.PolicyConfigRole pcru
                ON pcru.PolicyConfigId = pc.Id
                   AND pcru.Role = 'Unit'
            LEFT JOIN Task.PolicyConfigEvent pce
                ON pce.PolicyConfigId = pc.Id
            LEFT JOIN Task.TaskConfig tc
                ON tc.Id = pce.TaskConfigId
            LEFT JOIN Task.Event e
                ON e.Id = pce.EventId
            LEFT JOIN Task.PolicyConfigClassOfBusinessRestrictionList cbl
                ON cbl.PolicyConfigId = pc.Id
            LEFT JOIN Task.ClassOfBusinessItem cbi
                ON cbi.ClassOfBusinessListId = cbl.ClassOfBusinessListId
        WHERE
            pcrle.Name {(String.IsNullOrEmpty(legalEntity) ? "IS NULL" : " = '" + legalEntity + "'")}
            AND pcrbu.Name = '{businessUnit}'
            AND pcrt.Name = '{team}'
            AND e.Name = '{eventType.ToString()}'
            AND pc.IsDeleted = 0
        GROUP BY pc.Collection,
                 tc.BusinessContext,
                 tc.Category,
                 tc.TaskName,
                 pc.DataSourceInstanceId,
                 pcrle.Name,
                 pcrbu.Name,
                 pcrt.Name,
                 pcra.Name,
                 pcrb.Name,
                 pcrd.Name,
                 pcrf.Name,
                 pcru.Name,
                 e.Name,
                 pc.DaysBeforeExpiry,
                 pc.StartDate,
                 pc.RequiresPlacement,
                 pc.SendOnAuthorisedStatus,
                 pc.PolicyReferenceRestriction,
                 pc.Id,
                 pce.TaskConfigId,
                 pce.EventId
        ORDER BY LegalEntity,
                 BusinessUnit,
                 TeamName,
                 EventName;
        ");
        if (rows.Rows.Count != 1)
        {
            // It appears that it is possible to have more than one match.
            // Right? Not sure. Maybe multiple teams are involved and need notifying.
            // Write out what we find here.
            // Some tests work even if there are multiple rows here.
            output.WriteLine($"Expected to match 1 row. Matched {rows.Rows.Count} rows for LE:{legalEntity}, BU:{businessUnit}, T:{team} ET:{eventType}");
            output.WriteLine("-----");
            foreach(DataRow r in rows.Rows)
            {
                output.WriteLine(String.Join("~", r.ItemArray));
            }
            output.WriteLine("-----");
        }
#endif // DiagnosticOutputForPRP

        // Create the "Run"
        var runId = Guid.NewGuid();
        dynamic runRecord = CreateRow(tableName: "Task.Run", values: new { Id = runId, UTCDateFrom = DateTime.UtcNow.AddMinutes(240), UTCDateTo = DateTime.UtcNow });

        dynamic legalEntityRecord, legalEntityOrgIdRecord, businessUnitRecord, businessUnitOrgIdRecord, teamRecord, teamOrgIdRecord;
        CreateEclipseOrgRecord(out legalEntityRecord, out legalEntityOrgIdRecord, legalEntity, "Legal Entity", 123, legalEntity);
        CreateEclipseOrgRecord(out businessUnitRecord, out businessUnitOrgIdRecord, businessUnit, "Business Unit", 124, legalEntity);
        CreateEclipseOrgRecord(out teamRecord, out teamOrgIdRecord, team, "Team", 125, businessUnit);

        var inceptionDate = DateTime.UtcNow.AddDays(eventType == EventType.NewPolicy ? 30 : -120);
        var expiryDate = DateTime.UtcNow.AddDays(expiryDateDaysAfterNow.HasValue ? expiryDateDaysAfterNow.Value : ( eventType == EventType.NewPolicy ? 400 : 90));

        dynamic policyRecord = SetUpEclipsePolicy(teamOrgIdRecord, "15222RE18", inceptionDate, expiryDate, policyStatus, "11111", classOfBusiness: classOfBusiness, productClass: className, productLine: productName); // This Policy should be picked up by the task

        // Execute the stored procedure
        var insertCount = 0;
        ExecuteStoredProcedureWithoutResult(storedProcedureName: "Task.CreatePreRenewalTasks", values: new { runId, insertCount });

        // There are always many more rows in the PolicyAudit table than you would expect.
        dynamic row = GetResultRow(tableName: "Task.PolicyAudit", whereClause: $"PolicyConfigId = '{policyConfigId}' AND PolicyId = '{policyRecord.PolicyId}'");
        if(taskCount > 0)
        {
            Assert.NotNull(row);
            Assert.Equal(expected: policyRecord.PolicyId, actual: row.PolicyId);
            Assert.True(row.IsValid);
        }
        else
        {
            // If we don't expect tasks we don't expect an audit record.
            Assert.Null(row);
        }

        dynamic policyRenewalTaskRows = GetResultRows(tableName: "Task.PolicyRenewalTask");
        //Assert.Equal(expected: taskCount, actual: policyRenewalTaskRows.Rows.Count);
        var tasksForMyCollection = 0;
        var actualTasks = new List<string>();
        ;
        foreach(DataRow r in policyRenewalTaskRows.Rows)
        {

            // This collection name may not be the same as the collection passed in.
            // It seems that we can match more than 1 collection at a time.
            // Perhaps I should be more specific or is there a flaw in the configuration.
            var collectionName = (string)ExecuteSQLStatementWithResult(tSQL: $@"
                        SELECT pc.Collection
                        FROM Task.PolicyConfigEvent pce
                        INNER JOIN Task.PolicyConfig pc ON pc.Id = pce.PolicyConfigId
                        WHERE pce.Id = '{r["PolicyConfigEventId"]}'");
            output.WriteLine($"Collection Name: {collectionName}");
            // We may get matches for other collections
            // only count the ones that match ours and ignore the others
            if(collectionName == collection)
            {
                tasksForMyCollection++;

                var taskNames = GetResultRows(sql: $@"
                        SELECT tc.TaskName
                        FROM Task.PolicyConfigEvent pce
                        INNER JOIN Task.TaskConfig tc ON tc.Id = pce.TaskConfigId
                        WHERE pce.Id = '{r["PolicyConfigEventId"]}'");
                output.WriteLine($"Task count {taskNames.Rows.Count}");
                foreach(DataRow tnr in taskNames.Rows)
                {
                    var taskName = Convert.ToString(tnr["TaskName"]);
                    output.WriteLine($"Task Name: {taskName}");

                   // Assert.True(taskTypeArray.Contains(taskName), $"Did not expect the Task name: {taskName}");

                    if(!actualTasks.Contains(taskName))
                    {
                        actualTasks.Add(taskName);
                    }
                }
            }
        }
        var actualOrdered = string.Join('|', actualTasks.Order());
        var expectedOrdered = taskTypeArray != null ? string.Join('|', taskTypeArray!.Order()) : "";
        if (actualOrdered != expectedOrdered)
        {
            Assert.Fail($"Expected: '{expectedOrdered}'. Actual: '{actualOrdered}'.");
        }
        Assert.True((taskTypeArray == null ? 0 : taskTypeArray.Length) == actualTasks.Count, $"There are more task names than we are expecting. Expecting:'{(taskTypeArray == null ? "" : string.Join("|", taskTypeArray))}'. Got: '{string.Join("|", actualTasks.ToArray())}'.");
        Assert.Equal(expected: taskCount, actual: tasksForMyCollection);
    }


    /// <summary>
    /// Covers the scenario where a New Policy is Authorised and the PostBind event is triggered.
    /// Unless SendOnAuthorisedStatus is set to 1, policy Inception date must be in range >= (today - 60) days AND >= (PolicyStartDate + DaysBeforeExpiry) days
    /// </summary>
    [Fact]
    public void WhenNewPolicyPostBindEvent_ItCreatesTaskWhenAuthorisedAndInceptionDateIsInRange()
    {
        // Arrange

        // Make sure Policy Configuration 3F2EACA9-A035-4ECA-A5B7-244B87B434EC we are testing against exists
        dynamic policyConfigRow = GetResultRow(tableName: "Task.PolicyConfig", whereClause: "Id ='3F2EACA9-A035-4ECA-A5B7-244B87B434EC'");
        Assert.NotNull(policyConfigRow);
        Assert.Equal("Indonesia", policyConfigRow.Collection);

        // D04F42C8-1C5D-E911-9130-005056ADC758
        dynamic postBindEventRow = GetResultRow(tableName: "Task.Event", whereClause: "Id ='D04F42C8-1C5D-E911-9130-005056ADC758'");
        Assert.NotNull(postBindEventRow);
        Assert.Equal("PostBind", postBindEventRow.Name);

        // There are currently no NON-PIG PostBind PolicyConfigEvents, so lets add one
        dynamic taskConfigEventRecord = CreateRow(tableName: "Task.TaskConfig", values: new
        {
            Id = Guid.NewGuid(),
            BusinessContext = "NON PIG",
            Category = "Pre Renewal Process",
            TaskName = "Indonesia",
            PriorityTypeId = 1,
            IsAssignedToRole = 0,
            TaskSourceTypeId = 10,
            IsClientToBeAddedToTask = 1,
            IsCarrierToBeAddedToTask = 0
        });

        dynamic policyConfigEventRecord = CreateRow(tableName: "Task.PolicyConfigEvent", values: new
        {
            Id = Guid.NewGuid(),
            PolicyConfigId = policyConfigRow.Id,
            EventId = postBindEventRow.Id,
            TaskConfigId = taskConfigEventRecord.Id,
            DaysOffset = 0,
            IsDeleted = 0,
            IncludeNewPolicies = 1,
            IncludeRenewedPolicies = 1
        });

        var runId = Guid.NewGuid();
        dynamic runRecord = CreateRow(tableName: "Task.Run", values: new { Id = runId, UTCDateFrom = DateTime.UtcNow.AddMinutes(240), UTCDateTo = DateTime.Now });

        dynamic legalEntityRecord, legalEntityOrgIdRecord, businessUnitRecord, businessUnitOrgIdRecord, teamRecord, teamOrgIdRecord;
        CreateEclipseOrgRecord(out legalEntityRecord, out legalEntityOrgIdRecord, "PT.Willis Reinsurance Brokers Indonesia", "Legal Entity", 123, "PT.Willis Reinsurance Brokers Indonesia");
        CreateEclipseOrgRecord(out businessUnitRecord, out businessUnitOrgIdRecord, "Fac Management", "Business Unit", 124, "PT.Willis Reinsurance Brokers Indonesia");
        CreateEclipseOrgRecord(out teamRecord, out teamOrgIdRecord, "Corporate FAC Re Indonesia", "Team", 125, "Fac Management");

        var inceptionDate = DateTime.UtcNow.AddDays(120);
        var expiryDate = DateTime.UtcNow.AddDays(145); // Setting this to 145 days to make sure it is not picked up by the Renewal Event task
        var policyStatus = RefPolicyStatus.Live; // Policy Status is AUTHORISED

        dynamic policyRecord = SetUpEclipsePolicy(teamOrgIdRecord, "15222RE18", inceptionDate, expiryDate, policyStatus, "15222RE18A"); // This Policy should be picked up by the task

        inceptionDate = DateTime.UtcNow.AddDays(-70); // For Post Bind events Policy must have Inception date >= (today - 60) days
        dynamic inceptionOutOfRangePolicyRecord = SetUpEclipsePolicy(teamOrgIdRecord, "15223RE18", inceptionDate, expiryDate, policyStatus, "15223RE18A", 10001);

        dynamic nonAuthorisedPolicy = SetUpEclipsePolicy(teamOrgIdRecord, "15224RE18", inceptionDate, expiryDate, RefPolicyStatus.Cancelled, "15224RE18A", 10003); // Policy Status is CANCELLED

        inceptionDate = DateTime.Parse("03-09-2021"); // For Post Bind events Policy must have Inception date >= (PolicyStartDate + DaysBeforeExpiry) days (i.e. 2021-09-03 + 120 days)
        dynamic inceptionOutOfRangePolicy1Record = SetUpEclipsePolicy(teamOrgIdRecord, "15225RE18", inceptionDate, expiryDate, policyStatus, "15225RE18A", 10002);

        dynamic policyThatWasRunBeforeRecord = SetUpEclipsePolicy(teamOrgIdRecord, "15226RE18", inceptionDate, expiryDate, policyStatus, "15226RE18A");
        dynamic existingPolicyAuditRecord = CreateRow(tableName: "Task.PolicyAudit", values: new { policyThatWasRunBeforeRecord.PolicyId, EventId = postBindEventRow.Id, PolicyConfigId = policyConfigRow.Id });

        // Execute
        var insertCount = 0;
        ExecuteStoredProcedureWithoutResult(storedProcedureName: "Task.CreatePreRenewalTasks", values: new { runId, insertCount });

        // Assert
        dynamic policyRenewalRows = GetResultRows(tableName: "Task.PolicyRenewal", whereClause: $"PolicyConfigId='{policyConfigRow.Id}'");
        Assert.Equal(expected: 1, actual: policyRenewalRows.Rows.Count);

        dynamic row = GetResultRow(tableName: "Task.PolicyAudit", whereClause: $"PolicyConfigId='{policyConfigRow.Id}' AND PolicyId ='{policyRecord.PolicyId}'"); // NON PIG is processed
        Assert.NotNull(row);
        Assert.Equal(expected: policyRecord.PolicyId, actual: row.PolicyId);
        Assert.Equal(expected: postBindEventRow.Id, actual: row.EventId);
        Assert.True(row.IsValid);

        // Make sure we are not getting anything but PostBind events for this Policy
        dynamic rows = GetResultRows(tableName: "Task.PolicyRenewal");
        Assert.NotNull(rows);
        foreach(var r in rows.Rows)
        {
            Assert.Equal(expected: postBindEventRow.Id, actual: r["EventId"]);
        }

        dynamic policyRenewalTaskRow = GetResultRows(tableName: "Task.PolicyRenewalTask");
        Assert.Equal(expected: 1, actual: policyRenewalTaskRow.Rows.Count);
    }

    /// <summary>
    ///  Covers the scenario where a New Policy is Authorised PIG policy and the PostBind event is triggered.
    ///  For PIG policies SendOnAuthorisedStatus is set to 1. Policy Inception date filtering by Task.PolicyConfig.StartDate was removed as per business request.
    /// </summary>
    [Fact]
    public void WhenNewPolicyPostBindEvent_AndSendOnAuthorisedStatusIsSet_ItCreatesTask()
    {
        // Arrange

        // Make sure Policy Configuration 43CB74C4-0F71-4DA9-928E-54EA48B176F2 we are testing against exists
        dynamic policyConfigRow = GetResultRow(tableName: "Task.PolicyConfig", whereClause: "Id ='43CB74C4-0F71-4DA9-928E-54EA48B176F2'");
        Assert.NotNull(policyConfigRow);
        Assert.Equal("FAJS", policyConfigRow.Collection);

        // D04F42C8-1C5D-E911-9130-005056ADC758
        dynamic postBindEventRow = GetResultRow(tableName: "Task.Event", whereClause: "Id ='D04F42C8-1C5D-E911-9130-005056ADC758'");
        Assert.NotNull(postBindEventRow);
        Assert.Equal("PostBind", postBindEventRow.Name);

        var runId = Guid.NewGuid();

        dynamic runRecord = CreateRow(tableName: "Task.Run", values: new { Id = runId, UTCDateFrom = DateTime.UtcNow.AddMinutes(240), UTCDateTo = DateTime.Now });

        dynamic legalEntityRecord, legalEntityOrgIdRecord, businessUnitRecord, businessUnitOrgIdRecord, functionRecord, functionOrgIdRecord, departmentRecord, departmentOrgIdRecord, teamRecord, teamOrgIdRecord;
        CreateEclipseOrgRecord(out legalEntityRecord, out legalEntityOrgIdRecord, "Willis Limited", "Legal Entity", 125, "Willis Limited");
        CreateEclipseOrgRecord(out businessUnitRecord, out businessUnitOrgIdRecord, "FAJS", "Business Unit", 124, "Willis Limited");
        CreateEclipseOrgRecord(out functionRecord, out functionOrgIdRecord, "Ownership FAJS", "Function", 127, "FAJS");
        CreateEclipseOrgRecord(out departmentRecord, out departmentOrgIdRecord, "Fine Arts", "Department", 126, "Ownership FAJS");
        CreateEclipseOrgRecord(out teamRecord, out teamOrgIdRecord, "FAJS General Specie", "Team", 123, "Fine Arts");

        var inceptionDate = policyConfigRow.StartDate; // PIG Policy must have Inception date >= Task.PolicyConfig.StartDate
        var expiryDate = DateTime.UtcNow.AddDays(145); // Setting this to 145 days to make sure it is not picked up by the Renewal Event task
        var policyStatus = RefPolicyStatus.Live; // Policy Status is AUTHORISED
        dynamic pigPolicyRecord = SetUpEclipsePolicy(teamOrgIdRecord, "15227RE18", inceptionDate, expiryDate, policyStatus, "15227RE18A");

        inceptionDate = inceptionDate.AddDays(-1);
        dynamic pigPolicyWasOutOfRangeInceptionButNotAnymoreRecord = SetUpEclipsePolicy(teamOrgIdRecord, "15228RE18", inceptionDate, expiryDate, policyStatus, "15228RE18A", 10001);

        inceptionDate = policyConfigRow.StartDate;
        dynamic policyThatWasRunBeforeRecord = SetUpEclipsePolicy(teamOrgIdRecord, "15229RE18", inceptionDate, expiryDate, policyStatus, "15229RE18A");
        dynamic existingPolicyAuditRecord = CreateRow(tableName: "Task.PolicyAudit", values: new { policyThatWasRunBeforeRecord.PolicyId, EventId = postBindEventRow.Id, PolicyConfigId = policyConfigRow.Id });

        policyStatus = RefPolicyStatus.Cancelled; // Policy Status CANCELLED
        dynamic policyIsNotAuthorizedStatusRecord = SetUpEclipsePolicy(teamOrgIdRecord, "15230RE18", inceptionDate, expiryDate, policyStatus, "15230RE18A", 10003);

        // Execute
        var insertCount = 0;
        ExecuteStoredProcedureWithoutResult(storedProcedureName: "Task.CreatePreRenewalTasks", values: new { runId, insertCount });

        // Assert
        dynamic policyRenewalRows = GetResultRows(tableName: "Task.PolicyRenewal", whereClause: $"PolicyConfigId='{policyConfigRow.Id}'");
        Assert.Equal(expected: 2, actual: policyRenewalRows.Rows.Count);

        dynamic row = GetResultRow(tableName: "Task.PolicyAudit", whereClause: $"PolicyConfigId='{policyConfigRow.Id}' AND PolicyId ='{pigPolicyRecord.PolicyId}'"); // PIG is processed
        Assert.NotNull(row);
        Assert.Equal(expected: pigPolicyRecord.PolicyId, actual: row.PolicyId);
        Assert.Equal(expected: postBindEventRow.Id, actual: row.EventId);
        Assert.True(row.IsValid);

        row = GetResultRow(tableName: "Task.PolicyAudit", whereClause: $"PolicyConfigId='{policyConfigRow.Id}' AND PolicyId ='{pigPolicyWasOutOfRangeInceptionButNotAnymoreRecord.PolicyId}'"); // PIG is also processed, as the Policy Inception filter is removed whihc makes it in range 
        Assert.NotNull(row);
        Assert.Equal(expected: pigPolicyWasOutOfRangeInceptionButNotAnymoreRecord.PolicyId, actual: row.PolicyId);
        Assert.Equal(expected: postBindEventRow.Id, actual: row.EventId);
        Assert.True(row.IsValid);

        // Make sure we are not getting anything but PostBind events for this Policy
        dynamic rows = GetResultRows(tableName: "Task.PolicyRenewal");
        Assert.NotNull(rows);
        foreach(var r in rows.Rows)
        {
            Assert.Equal(expected: postBindEventRow.Id, actual: r["EventId"]);
        }

        dynamic policyRenewalTaskRow = GetResultRows(tableName: "Task.PolicyRenewalTask");
        Assert.Equal(expected: 2, actual: policyRenewalTaskRow.Rows.Count);
    }

    private dynamic SetUpEclipsePolicy(dynamic teamOrgIdRecord, dynamic policyKey, DateTime inceptionDate, DateTime expiryDate, RefPolicyStatus policyStatus, dynamic policySectionKey, int placementId = 1000, string? productClass = null, string? productLine = null, string? classOfBusiness = null)
    {
        dynamic policyRecord = CreateRow(tableName: "dbo.Policy", values: new
        {
            //PolicyId = 1,
            PolicyReference = "45075156",
            DataSourceInstanceId = (int)DataSourceInstance.Eclipse,
            PolicyKey = policyKey,
            PolicyStatusId = 2,
            InceptionDate = inceptionDate,
            //ExpiryDate = DateTime.UtcNow.AddDays(45),
            ExpiryDate = expiryDate,
            PlacementId = placementId,
            RefPolicyStatusId = (int)policyStatus,
            RenewedFromPolicyId = 123456
        });

        dynamic policyOrganisationHierarchy2Record = CreateRow(tableName: "dbo.PolicyOrganisation", values: new
        {
            DataSourceInstanceId = (int)DataSourceInstance.Eclipse,
            PolicyId = policyRecord.PolicyId,
            OrganisationId = teamOrgIdRecord.OrganisationSK,
            IsDeleted = false
        });

        dynamic policySectionRecord = CreateRow(tableName: "dbo.PolicySection", values: new
        {
            DataSourceInstanceId = (int)DataSourceInstance.Eclipse,
            PolicyId = policyRecord.PolicyId,
            PolicySectionKey = policySectionKey,
            IsDeleted = false
        });

        dynamic productRecord = CreateRow(tableName: "dbo.Product", values: new
        {
            DataSourceInstanceId = (int)DataSourceInstance.Eclipse,
            ProductClass = productClass,
            ProductLine = productLine,
            SourceProductName = classOfBusiness,
            IsDeleted = false
        });

        dynamic policySectionProductRecord = CreateRow(tableName: "dbo.PolicySectionProduct", values: new
        {
            DataSourceInstanceId = (int)DataSourceInstance.Eclipse,
            PolicySectionId = policySectionRecord.PolicySectionId,
            ProductId = productRecord.ProductId,
            PACTPolicySectionProductId = policyRecord.PolicyId,
            PolicySectionKey = policySectionKey,
            IsDeleted = false
        });
        return policyRecord;
    }

    private void CreateEclipseOrgRecord(out dynamic orgHierRecord, out dynamic psOrgHierRecord, string Organisation, string Role, int OrganisationId, string Parent)
    {
        orgHierRecord = CreateRow(tableName: "PAS.Organisation", values: new
        {
            Organisation = Organisation,
            OrganisationKey = "KEY|" + Organisation,
            DataSourceInstanceId = (int)DataSourceInstance.Eclipse,
            PASOrganisationId = OrganisationId,
            IsDeleted = false,
            ParentKey = "KEY|" + Parent
        });
        psOrgHierRecord = CreateRow(tableName: "PS.Organisation", values: new
        {
            OrganisationKey = "KEY|" + Organisation,
            DataSourceInstanceId = (int)DataSourceInstance.Eclipse,
            IsDeleted = false,
        });
        dynamic organisationAttributeRecord = CreateRow(tableName: "PAS.OrganisationAttribute", values: new
        {
            DataSourceInstanceId = (int)DataSourceInstance.Eclipse,
            OrganisationKey = orgHierRecord.OrganisationKey,
            EclipseRole = Role,
            IsDeleted = false
        });
    }

    /// <summary>
    /// Constructor - do not change
    /// </summary>
    /// <param name="fixture"></param>
    public CreatePreRenewalTasksTests(DatabaseFixture fixture, ITestOutputHelper output) : base(fixture, output)
    {
        this.output = output;
    }
}
