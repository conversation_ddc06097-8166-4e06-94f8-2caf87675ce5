﻿using System.Reflection;

namespace PsDb.Tests.rpt.StoredProcedures;
public class Load_rpt_PlacementPrimaryRoleTests : PlacementStoreTestBase
{

    [Fact]
    public void Load_rpt_PlacementPrimaryRoleNoDataTest()
    {
        NoDataStoredProcedureTest(storedProcedureTestMethod: MethodBase.GetCurrentMethod());
    }

    [Theory]
    [InlineData("Client Executive", "AccountExecutive")]
    [InlineData("Account Executive", "AccountExecutive")]
    [InlineData("Umbrella & Excess Broker", "UmbrellaAndExcessBroker")]
    [InlineData("Account Manager", "AccountManager")]    
    [InlineData("Account Technician", "AccountTechnician")]
    [InlineData("Analyst", "Analyst")]
    [InlineData("Assistant Client Service Specialist", "AssistantClientServiceSpecialist")]
    [InlineData("Client Advocate", "ClientAdvocate")]
    [InlineData("Producer", "Producer")]
    [InlineData("Product Group Leader", "ProductGroupLeader")]
    [InlineData("Senior Client Manager", "SeniorClientManager")]
    [InlineData("Strategic Broking Advisor", "StrategicBrokingAdvisor")]
    [InlineData("Support Broker", "SupportBroker")]
    [InlineData("BLU", "BLU")]

    public void PlacementPrimaryRoleLoadTest(string bpRole, string column)
    {
        dynamic PLRecord = CreateRow(tableName: "dbo.Placement", values: new
        {
            PlacementSystemId = 1234,
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            IsDeleted = 0
        });
        dynamic PRecord = CreateRow(tableName: "dbo.Party", values: new
        {
            PartyName = "Bob"
        });
        dynamic PPRRecord = CreateRow(tableName: "dbo.PlacementPartyRole", values: new
        {
            PlacementId = PLRecord.PlacementId,
            PartyId = PRecord.PartyId,
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            PartyName = PRecord.PartyName,
            IsPrimaryParty = true,
            OnPlacement = 1,
            IsDeleted = 0
        });
        dynamic PSURecord = CreateRow(tableName: "dbo.PlacementSystemUser", values: new
        {
            UserID = 1,
            GivenName = "Kevin",
            Surname = "Carter",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform
        });
        dynamic SRRecord = CreateRow(tableName: "ref.ServicingRole", values: new
        {
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            ServicingRole = bpRole
        });
        dynamic PTMRecord = CreateRow(tableName: "dbo.PlacementTeamMember", values: new
        {
            PlacementTeamMemberID = 1,
            PlacementID = PLRecord.PlacementId,
            DataSourceInstanceID = (int)DataSourceInstance.BrokingPlatform,
            UserID = PSURecord.UserID,
            RoleID = SRRecord.ServicingRoleId,
            ETLCreatedDate = DateTime.Parse("2024-06-20"),
            ETLUpdatedDate = DateTime.Parse("2024-06-20"),
            SourceUpdatedDate = DateTime.Parse("2024-06-20"),
            IsDeleted = 0
        });

        ExecuteStoredProcedureWithoutResult(storedProcedureName: "rpt.Load_rpt_PlacementPrimaryRole");
        CheckSprocExecutionLog(sprocName: "rpt.Load_rpt_PlacementPrimaryRole", insertedCount: 1);

        dynamic row = GetResultRow(tableName: "rpt.PlacementPrimaryRole");
        Assert.Equal(expected: PLRecord.PlacementId, actual: row.PlacementId);

        dynamic result = GetResultRow(sql: $"SELECT [{column}] AS r from rpt.PlacementPrimaryRole");
        Assert.Equal(expected: PSURecord.GivenName + " " + PSURecord.Surname, actual: result.r);

    }

    /// <summary>
    /// Constructor - Do not change.
    /// </summary>
    /// <param name="fixture"></param>
    public Load_rpt_PlacementPrimaryRoleTests(DatabaseFixture fixture) : base(fixture)
    {

    }
}