﻿using System;
using System.Reflection;
using System.Xml.Linq;
using Xunit.Abstractions;

namespace PsDb.Tests.rpt.StoredProcedures;
public class Load_rpt_MarketInteractionResponseTests : PlacementStoreTestBase
{
    [Fact]
    public void Load_rpt_MarketInteractionResponseNoDataTest()
    {
        NoDataStoredProcedureTest(storedProcedureTestMethod: MethodBase.GetCurrentMethod());
    }

    [Fact]
    public void Load_rpt_MarketInteractionResponseOldModelTest()
    {
        var carrierId = 23;

        dynamic responseTypeRecord,
            placementSystemTableRecordCR,
            placementSystemTableRecordSCM,
            placementSystemTableRecordPC,
            placementSystemTableRecordMR,
            outcomeStatusRecordAcc,
            outcomeStatusRecordNotAcc,
            outcomeReasonRecord,
            layerTypeRecord,
            currencyRecordEur,
            facilityRecord,
            facilitySectionRecord,
            facilitySectionCarrierRecord,
            productHierarchyRecord,
            productHierarchyRecord2,
            placementStatusRecord;
        CreateReferenceRecords(carrierId, out responseTypeRecord, out placementSystemTableRecordCR, out placementSystemTableRecordSCM, out placementSystemTableRecordPC, out placementSystemTableRecordMR, out outcomeStatusRecordAcc, out outcomeStatusRecordNotAcc, out outcomeReasonRecord, out layerTypeRecord, out currencyRecordEur, out facilityRecord, out facilitySectionRecord, out facilitySectionCarrierRecord, out productHierarchyRecord, out productHierarchyRecord2, out placementStatusRecord);

        dynamic placementRecord = CreateRow(tableName: "dbo.Placement", values: new
        {
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            PlacementName = "Test Placement",
            PlacementStatusId = placementStatusRecord.PlacementStatusId,
            PlacementSystemId = 1022232,
            IsDeleted = false
        });

        dynamic marketSelectionRecord = CreateRow(tableName: "dbo.MarketSelection", values: new
        {
            PlacementId = placementRecord.PlacementId,
            CarrierId = 31,
            PlacementSystemMarketSelectionId = 41,
            SourceUpdatedDate = "2023-11-21",

        });

        //RiskProfile for the specification
        dynamic riskProfileRecord = CreateRow(tableName: "PS.RiskProfile", values: new
        {
            PlacementId = placementRecord.PlacementId,
            ProductId = productHierarchyRecord.ProductId,
            ODSProductId = 24074,
            ClassId = 811,
            RiskProfileKey = "ELEM|12345",
            LineOfBusiness = "RiskProfileLOB",
            ClassOfBusiness = "RiskProfileCOB",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform
        });

        dynamic productRecord = CreateRow(tableName: "dbo.Product", values: new
        {
            ProductId = riskProfileRecord.ODSProductId,
            ProductKey = productHierarchyRecord.ProductKey,
            DataSourceInstanceId = productHierarchyRecord.DataSourceInstanceId,
            IsDeleted = false
        });

        dynamic marketInteractionRecord = CreateRow(tableName: "dbo.MarketInteraction", values: new
        {
            MarketSelectionId = marketSelectionRecord.MarketSelectionId,
            OutcomeReasonId = outcomeReasonRecord.OutcomeReasonId,
            ResponseTypeId = responseTypeRecord.ResponseTypeId,
            OutcomeStatusId = outcomeStatusRecordNotAcc.OutcomeStatusId,
            LayerTypeId = layerTypeRecord.LayerTypeId,
            PremiumCurrencyID = 2,
            AttachmentCurrencyID = 2,
            LimitCurrencyID = 2,
            ResponseDate = "2023-11-21",
            IsDeleted = false,
            OfferedLineRate = 100,
            Premium = 10,
            PremiumRate = 10000,
            OfferedLine = 1000,
            Limit = 1000,
            ProductId = productHierarchyRecord.ProductId,
            CommissionRate = 15,
            CarrierResponseId = 152,
            Subjectivity = " Subjectivity ",
            Comments = new string('t', 3000),
            AttachmentPoint = 100000,
            QuotedToLead = 1,
            UnderwriterName = "Joe Bloggs",
            SubmittedDate = "2023-11-11",
            InteractionName = "Test1"
        });

        dynamic results = ExecuteStoredProcedureWithResultRow(storedProcedureName: "rpt.Load_rpt_MarketInteractionResponse");
        Assert.Equal(expected: 1, actual: results.InsertedCount);
        Assert.Equal(expected: 0, actual: results.UpdatedCount);
        Assert.Equal(expected: 0, actual: results.DeletedCount);
        Assert.Equal(expected: 0, actual: results.RejectedCount);

        //This should create only one row
        CheckSprocExecutionLog(sprocName: "rpt.Load_rpt_MarketInteractionResponse", insertedCount: 1);

        dynamic row = GetResultRow(tableName: "rpt.MarketInteractionResponse");
        Assert.Equal(expected: "MarketSelection", actual: row.MarketCategory);
        Assert.Equal(expected: marketInteractionRecord.CarrierResponseId, actual: row.CarrierResponseId);
        Assert.Equal(expected: $"CARRES|{marketInteractionRecord.CarrierResponseId}", actual: row.MarketResponseKey);
        Assert.Equal(expected: marketInteractionRecord.MarketSelectionId, actual: row.MarketSelectionId);
        Assert.Equal(expected: $"MKTSEL|{marketSelectionRecord.PlacementSystemMarketSelectionId}", actual: row.NegotiationMarketKey);
        Assert.Equal(expected: $"{placementSystemTableRecordPC.TableId}|{marketInteractionRecord.MarketSelectionId}", actual: row.MarketSelectionKey);
        Assert.Equal(expected: marketInteractionRecord.InteractionName, actual: row.InteractionName);
        Assert.Equal(expected: DateTime.Parse(marketInteractionRecord.SubmittedDate), actual: row.SubmittedDate);
        Assert.Equal(expected: DateTime.Parse(marketInteractionRecord.ResponseDate), actual: row.ResponseDate);
        Assert.Equal(expected: 10, actual: row.ResponseTimeDays);
        Assert.Equal(expected: outcomeReasonRecord.OutcomeReason, actual: row.OutcomeReason);
        Assert.Equal(expected: "Suitability of insurer", actual: row.OutcomeReasonGroupName);
        Assert.Equal(expected: outcomeStatusRecordNotAcc.OutcomeStatus, actual: row.OutcomeStatus);
        Assert.Equal(expected: productHierarchyRecord.ProductKey, actual: row.ProductKey);
        Assert.Contains("classOfBusiness_", row.ProductKey); //for changes in 348804, 
        Assert.Contains("|", row.ProductKey);                //ensuring the ProductKey follows classOfBusiness_xxx|lineOfBusiness_xxx format
        Assert.Contains("lineOfBusiness_", row.ProductKey);  //when that is the format of the ProductKey in rpt.ProductHierarchy
        Assert.Equal(expected: riskProfileRecord.ProductId, actual: row.ProductId);
        Assert.Equal(expected: riskProfileRecord.ClassId, actual: row.ClassId);
        Assert.Equal(expected: layerTypeRecord.LayerType, actual: row.LayerType);
        Assert.Equal(expected: true, actual: row.QuotedToLead);
        Assert.Equal(expected: currencyRecordEur.CurrencyAlphaCode, actual: row.Premium_Currency);
        Assert.Equal(expected: marketInteractionRecord.Premium, actual: row.Premium);
        Assert.Equal(expected: 10, actual: row.Premium_USD);
        Assert.Equal(expected: marketInteractionRecord.OfferedLine, actual: row.OfferedLine);
        Assert.Equal(expected: 1000, actual: row.OfferedLine_USD);
        Assert.Equal(expected: marketInteractionRecord.OfferedLineRate, actual: row.OfferedLineRate);
        Assert.Equal(expected: marketInteractionRecord.PremiumRate, actual: row.PremiumRate);
        Assert.Equal(expected: marketInteractionRecord.CommissionRate, actual: row.CommissionRate);
        Assert.Equal(expected: currencyRecordEur.CurrencyAlphaCode, actual: row.Limit_Currency);
        Assert.Equal(expected: marketInteractionRecord.Limit, actual: row.Limit);
        Assert.Equal(expected: 1000, actual: row.Limit_USD);
        Assert.Equal(expected: currencyRecordEur.CurrencyAlphaCode, actual: row.AttachmentPoint_Currency);
        Assert.Equal(expected: 20231121, actual: row.ResponseDateKey);
        Assert.Equal(expected: $"{placementSystemTableRecordCR.TableId}|{marketInteractionRecord.CarrierResponseId}", actual: row.ResponseKey);
        Assert.Equal(expected: 10, actual: row.WrittenPremium);
        Assert.Equal(expected: 10, actual: row.WrittenPremium_USD);
        Assert.Equal(expected: false, actual: row.MultiRisk);
        Assert.Equal(expected: marketInteractionRecord.OfferedLineRate, actual: row.Split);
        Assert.Equal(expected: "Lead", actual: row.FollowType);
        Assert.Equal(expected: 2999, actual: row.Comments.Length);
        Assert.Equal(expected: "Subjectivity", actual: row.Subjectivity);
        Assert.Equal(expected: false, actual: row.IsInvalid);

    }

    [Fact]
    public void Load_rpt_MarketInteractionResponseMarketSecuritySpecTest()
    {
        var submissionContainerId = 20;
        var submissionContainerMarketId = 10;
        var marketResponseId = 40;
        var carrierId = 23;

        dynamic responseTypeRecord,
           placementSystemTableRecordCR,
           placementSystemTableRecordSCM,
           placementSystemTableRecordPC,
           placementSystemTableRecordMR,
           outcomeStatusRecordAcc,
           outcomeStatusRecordNotAcc,
           outcomeReasonRecord,
           layerTypeRecord,
           currencyRecordEur,
           facilityRecord,
           facilitySectionRecord,
           facilitySectionCarrierRecord,
           productHierarchyRecord,
           productHierarchyRecord2,
           placementStatusRecord;
        CreateReferenceRecords(carrierId, out responseTypeRecord, out placementSystemTableRecordCR, out placementSystemTableRecordSCM, out placementSystemTableRecordPC, out placementSystemTableRecordMR, out outcomeStatusRecordAcc, out outcomeStatusRecordNotAcc, out outcomeReasonRecord, out layerTypeRecord, out currencyRecordEur, out facilityRecord, out facilitySectionRecord, out facilitySectionCarrierRecord, out productHierarchyRecord, out productHierarchyRecord2, out placementStatusRecord);

        dynamic placementRecord = CreateRow(tableName: "dbo.Placement", values: new
        {
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            PlacementName = "Test Placement",
            PlacementStatusId = placementStatusRecord.PlacementStatusId,
            PlacementSystemId = 1022232,
            IsDeleted = false
        });

        //RiskProfile for the specification
        dynamic riskProfileRecord = CreateRow(tableName: "PS.RiskProfile", values: new
        {
            RiskProfileId = 7,
            PlacementId = placementRecord.PlacementId,
            ProductId = productHierarchyRecord.ProductId,
            ClassId = 811,
            RiskProfileKey = "ELEM|12345",
            LineOfBusiness = "RiskProfileLOB",
            ClassOfBusiness = "RiskProfileCOB",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform
        });

        //RiskProfile for the specification
        dynamic riskProfileRecord2 = CreateRow(tableName: "PS.RiskProfile", values: new
        {
            RiskProfileId = 8,
            PlacementId = placementRecord.PlacementId,
            ProductId = productHierarchyRecord2.ProductId,
            ODSProductId = 24074,
            ClassId = 812,
            RiskProfileKey = "SPEC|1",
            LineOfBusiness = "RiskProfileLOB",
            ClassOfBusiness = "RiskProfileCOB",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform
        });

        dynamic productRecord = CreateRow(tableName: "dbo.Product", values: new
        {
            ProductId = riskProfileRecord2.ODSProductId,
            ProductKey = productHierarchyRecord2.ProductKey,
            DataSourceInstanceId = productHierarchyRecord2.DataSourceInstanceId,
            IsDeleted = false
        });

        dynamic negotiationRecord = CreateRow(tableName: "PS.Negotiation", values: new
        {
            NegotiationKey = $"SUBC|{submissionContainerId}",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            SubmissionName = "Submission"
        });

        dynamic negotiationMarketRecord = CreateRow(tableName: "PS.NegotiationMarket", values: new
        {
            NegotiationId = negotiationRecord.NegotiationId,
            NegotiationMarketKey = $"SUBCONMKT|{submissionContainerMarketId}",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
        });

        dynamic SubmissionRecord = CreateRow(tableName: "BP.Submission", values: new
        {
            SubmissionContainerId = submissionContainerId,
            Sent = "2023-11-11"
        });

        dynamic marketResponseRecord = CreateRow(tableName: "PS.MarketResponse", values: new
        {
            NegotiationMarketId = negotiationMarketRecord.NegotiationMarketId,
            MarketResponseKey = $"MKTRES|{marketResponseId}",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            ResponseTypeId = responseTypeRecord.ResponseTypeId,
            LayerTypeId = layerTypeRecord.LayerTypeId,
            LimitCurrencyId = currencyRecordEur.CurrencyId,
            AttachmentPointCurrencyId = currencyRecordEur.CurrencyId,
            ResponseDate = "2023-11-21",
            ResponseCreatedDate = DateTime.Parse("2023-11-21"),
            Limit = 1000,
            Comments = "Comments",
            AttachmentPoint = 100000,
            UnderwriterName = "Joe Bloggs",
            IsInvalid = true
        });

        dynamic followTypeRecord = CreateRow(tableName: "ref.FollowType", values: new {
            FollowType = "Lead"
        });

        dynamic marketQuoteResponseRecord = CreateRow(tableName: "PS.MarketQuoteResponse", values: new
        {
            MarketResponseId = marketResponseRecord.MarketResponseId,
            OutcomeReasonId = outcomeReasonRecord.OutcomeReasonId,
            OutcomeStatusId = 1,
            PremiumCurrencyId = currencyRecordEur.CurrencyId,
            IsDeleted = false,
            QuotedToLead = 1,
            Premium = 10,
            PremiumRate = 10000,
            OfferedLine = 1000,
            OfferedLineRate = 100,
            CommissionRate = 15,
            Subjectivity = "Subjectivity",
            Deductible = 100,
            FirstResponseAcceptedDate = DateTime.Parse("2023-12-05"),
            LastResponseAcceptedDate = DateTime.Parse("2024-01-22"),
            FollowTypeId = followTypeRecord.FollowTypeId
        });

        dynamic marketResponseBasisRecord = CreateRow(tableName: "PS.MarketResponseBasis", values: new
        {
            MarketResponseId = marketResponseRecord.MarketResponseId,
            MarketResponseBasisKey = "MRB|2",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            SpecificationId = 1,
            //ContractId = 2,
            //RiskProfileId = 7
            IsDeleted = false
        });

        dynamic marketSecurityRecord = CreateRow(tableName: "dbo.MarketResponseSecurity", values: new
        {
            MarketResponseId = marketResponseId,
            CarrierId = carrierId,
            Split = 10,
            IsFacilityLead = 1
        });

        dynamic results = ExecuteStoredProcedureWithResultRow(storedProcedureName: "rpt.Load_rpt_MarketInteractionResponse");
        Assert.Equal(expected: 2, actual: results.InsertedCount);
        Assert.Equal(expected: 0, actual: results.UpdatedCount);
        Assert.Equal(expected: 0, actual: results.DeletedCount);
        Assert.Equal(expected: 0, actual: results.RejectedCount);

        //This should create two rows, one for SubmissionMarket and one for MarketSecurity
        CheckSprocExecutionLog(sprocName: "rpt.Load_rpt_MarketInteractionResponse", insertedCount: 2);

        dynamic row = GetResultRow(tableName: "rpt.MarketInteractionResponse", whereClause: $"MarketSecurityCarrierId = {carrierId}");
        Assert.Equal(expected: "MarketSecurity", actual: row.MarketCategory);
        Assert.Equal(expected: marketResponseId, actual: row.CarrierResponseId);
        Assert.Equal(expected: $"MKTRES|{marketResponseId}", actual: row.MarketResponseKey);
        Assert.Equal(expected: submissionContainerMarketId, actual: row.MarketSelectionId);
        Assert.Equal(expected: $"MKTSEC|{submissionContainerMarketId}", actual: row.NegotiationMarketKey);
        Assert.Equal(expected: $"{placementSystemTableRecordSCM.TableId}|{submissionContainerMarketId}", actual: row.MarketSelectionKey);
        Assert.Equal(expected: negotiationRecord.SubmissionName, actual: row.InteractionName);
        Assert.Equal(expected: DateTime.Parse(SubmissionRecord.Sent), actual: row.SubmittedDate);
        Assert.Equal(expected: DateTime.Parse(marketResponseRecord.ResponseDate), actual: row.ResponseDate);
        Assert.Equal(expected: 10, actual: row.ResponseTimeDays);
        Assert.Equal(expected: outcomeReasonRecord.OutcomeReason, actual: row.OutcomeReason);
        Assert.Equal(expected: "Suitability of insurer", actual: row.OutcomeReasonGroupName);
        Assert.Equal(expected: outcomeStatusRecordAcc.OutcomeStatus, actual: row.OutcomeStatus);
        Assert.Equal(expected: productHierarchyRecord2.ProductKey, actual: row.ProductKey);
        Assert.Equal(expected: riskProfileRecord2.ProductId, actual: row.ProductId);
        Assert.Equal(expected: riskProfileRecord2.ClassId, actual: row.ClassId);
        Assert.Equal(expected: layerTypeRecord.LayerType, actual: row.LayerType);
        Assert.Equal(expected: true, actual: row.QuotedToLead);
        Assert.Equal(expected: currencyRecordEur.CurrencyAlphaCode, actual: row.Premium_Currency);
        Assert.Equal(expected: marketQuoteResponseRecord.Premium, actual: row.Premium);
        Assert.Equal(expected: 10, actual: row.Premium_USD);
        Assert.Equal(expected: 100, actual: row.OfferedLine); //Sec.Split / 100 * ms.OfferedLine
        Assert.Equal(expected: 100, actual: row.OfferedLine_USD);
        Assert.Equal(expected: 10, actual: row.OfferedLineRate); //Sec.Split / 100 * ms.OfferedLineRate
        Assert.Equal(expected: marketQuoteResponseRecord.PremiumRate, actual: row.PremiumRate);
        Assert.Equal(expected: marketQuoteResponseRecord.CommissionRate, actual: row.CommissionRate);
        Assert.Equal(expected: currencyRecordEur.CurrencyAlphaCode, actual: row.Limit_Currency);
        Assert.Equal(expected: marketResponseRecord.Limit, actual: row.Limit);
        Assert.Equal(expected: 1000, actual: row.Limit_USD);
        Assert.Equal(expected: currencyRecordEur.CurrencyAlphaCode, actual: row.AttachmentPoint_Currency);
        Assert.Equal(expected: marketResponseRecord.Comments, actual: row.Comments);
        Assert.Equal(expected: marketQuoteResponseRecord.Subjectivity, actual: row.Subjectivity);
        Assert.Equal(expected: 20231121, actual: row.ResponseDateKey);
        Assert.Equal(expected: $"{placementSystemTableRecordMR.TableId}|{marketResponseId}", actual: row.ResponseKey);
        Assert.Equal(expected: 1, actual: row.WrittenPremium);
        Assert.Equal(expected: 1, actual: row.WrittenPremium_USD);
        Assert.Equal(expected: false, actual: row.MultiRisk);
        Assert.Equal(expected: marketSecurityRecord.Split, actual: row.Split);
        Assert.Equal(expected: $"Carrier-{carrierId}", actual: row.MarketSecurityCarrierKey);
        Assert.Equal(expected: carrierId, actual: row.MarketSecurityCarrierId);
        Assert.Equal(expected: marketQuoteResponseRecord.Deductible, actual: row.Deductible);
        Assert.Equal(expected: marketResponseRecord.ResponseCreatedDate, actual: row.ResponseCreatedDate);
        Assert.Equal(expected: marketQuoteResponseRecord.FirstResponseAcceptedDate, actual: row.FirstResponseAcceptedDate);
        Assert.Equal(expected: marketQuoteResponseRecord.LastResponseAcceptedDate, actual: row.LastResponseAcceptedDate);
        Assert.Equal(expected: followTypeRecord.FollowType, actual: row.FollowType);
        Assert.Equal(expected: true, actual: row.IsInvalid);

        dynamic row2 = GetResultRow(tableName: "rpt.MarketInteractionResponse", whereClause: "MarketSecurityCarrierId IS NULL");
        Assert.Equal(expected: "SubmissionMarket", actual: row2.MarketCategory);
        Assert.Equal(expected: marketResponseId, actual: row2.CarrierResponseId);
        Assert.Equal(expected: $"MKTRES|{marketResponseId}", actual: row2.MarketResponseKey);
        Assert.Equal(expected: submissionContainerMarketId, actual: row2.MarketSelectionId);
        Assert.Equal(expected: $"SUBCONMKT|{submissionContainerMarketId}", actual: row2.NegotiationMarketKey);
        Assert.Equal(expected: $"{placementSystemTableRecordSCM.TableId}|{submissionContainerMarketId}", actual: row2.MarketSelectionKey);
        Assert.Equal(expected: negotiationRecord.SubmissionName, actual: row2.InteractionName);
        Assert.Equal(expected: DateTime.Parse(SubmissionRecord.Sent), actual: row2.SubmittedDate);
        Assert.Equal(expected: DateTime.Parse(marketResponseRecord.ResponseDate), actual: row2.ResponseDate);
        Assert.Equal(expected: 10, actual: row2.ResponseTimeDays);
        Assert.Equal(expected: outcomeReasonRecord.OutcomeReason, actual: row2.OutcomeReason);
        Assert.Equal(expected: "Suitability of insurer", actual: row2.OutcomeReasonGroupName);
        Assert.Equal(expected: outcomeStatusRecordAcc.OutcomeStatus, actual: row2.OutcomeStatus);
        Assert.Equal(expected: productHierarchyRecord2.ProductKey, actual: row2.ProductKey);
        Assert.Equal(expected: riskProfileRecord2.ProductId, actual: row2.ProductId);
        Assert.Equal(expected: riskProfileRecord2.ClassId, actual: row2.ClassId);
        Assert.Equal(expected: layerTypeRecord.LayerType, actual: row2.LayerType);
        Assert.Equal(expected: true, actual: row2.QuotedToLead);
        Assert.Equal(expected: currencyRecordEur.CurrencyAlphaCode, actual: row2.Premium_Currency);
        Assert.Equal(expected: marketQuoteResponseRecord.Premium, actual: row2.Premium);
        Assert.Equal(expected: 10, actual: row2.Premium_USD);
        Assert.Equal(expected: marketQuoteResponseRecord.OfferedLine, actual: row2.OfferedLine);
        Assert.Equal(expected: 1000, actual: row2.OfferedLine_USD);
        Assert.Equal(expected: marketQuoteResponseRecord.OfferedLineRate, actual: row2.OfferedLineRate);
        Assert.Equal(expected: marketQuoteResponseRecord.PremiumRate, actual: row2.PremiumRate);
        Assert.Equal(expected: marketQuoteResponseRecord.CommissionRate, actual: row2.CommissionRate);
        Assert.Equal(expected: currencyRecordEur.CurrencyAlphaCode, actual: row2.Limit_Currency);
        Assert.Equal(expected: marketResponseRecord.Limit, actual: row2.Limit);
        Assert.Equal(expected: 1000, actual: row2.Limit_USD);
        Assert.Equal(expected: currencyRecordEur.CurrencyAlphaCode, actual: row2.AttachmentPoint_Currency);
        Assert.Equal(expected: marketResponseRecord.Comments, actual: row2.Comments);
        Assert.Equal(expected: marketQuoteResponseRecord.Subjectivity, actual: row2.Subjectivity);
        Assert.Equal(expected: 20231121, actual: row2.ResponseDateKey);
        Assert.Equal(expected: $"{placementSystemTableRecordMR.TableId}|{marketResponseId}", actual: row2.ResponseKey);
        Assert.Equal(expected: 10, actual: row2.WrittenPremium);
        Assert.Equal(expected: 10, actual: row2.WrittenPremium_USD);
        Assert.Equal(expected: false, actual: row2.MultiRisk);
        Assert.Equal(expected: marketQuoteResponseRecord.OfferedLineRate, actual: row2.Split);
        Assert.Equal(expected: marketQuoteResponseRecord.Deductible, actual: row2.Deductible);
        Assert.Equal(expected: marketResponseRecord.ResponseCreatedDate, actual: row2.ResponseCreatedDate);
        Assert.Equal(expected: marketQuoteResponseRecord.FirstResponseAcceptedDate, actual: row2.FirstResponseAcceptedDate);
        Assert.Equal(expected: marketQuoteResponseRecord.LastResponseAcceptedDate, actual: row2.LastResponseAcceptedDate);
        Assert.Equal(expected: followTypeRecord.FollowType, actual: row2.FollowType);
        Assert.Equal(expected: true, actual: row2.IsInvalid);
    }

    [Theory]
    [InlineData("Co-Lead")]
    [InlineData("Lead")]
    public void LoadMarketInteractionResponseMarketSecuritySpecFollowTypeTest(string followType)
    {
        var submissionContainerId = 20;
        var submissionContainerMarketId = 10;
        var marketResponseId = 40;
        var carrierId = 23;

        dynamic responseTypeRecord,
           placementSystemTableRecordCR,
           placementSystemTableRecordSCM,
           placementSystemTableRecordPC,
           placementSystemTableRecordMR,
           outcomeStatusRecordAcc,
           outcomeStatusRecordNotAcc,
           outcomeReasonRecord,
           layerTypeRecord,
           currencyRecordEur,
           facilityRecord,
           facilitySectionRecord,
           facilitySectionCarrierRecord,
           productHierarchyRecord,
           productHierarchyRecord2,
           placementStatusRecord;
        CreateReferenceRecords(carrierId, out responseTypeRecord, out placementSystemTableRecordCR, out placementSystemTableRecordSCM, out placementSystemTableRecordPC, out placementSystemTableRecordMR, out outcomeStatusRecordAcc, out outcomeStatusRecordNotAcc, out outcomeReasonRecord, out layerTypeRecord, out currencyRecordEur, out facilityRecord, out facilitySectionRecord, out facilitySectionCarrierRecord, out productHierarchyRecord, out productHierarchyRecord2, out placementStatusRecord);

        dynamic placementRecord = CreateRow(tableName: "dbo.Placement", values: new
        {
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            PlacementName = "Test Placement",
            PlacementStatusId = placementStatusRecord.PlacementStatusId,
            PlacementSystemId = 1022232,
            IsDeleted = false
        });

        //RiskProfile for the specification
        dynamic riskProfileRecord = CreateRow(tableName: "PS.RiskProfile", values: new
        {
            RiskProfileId = 7,
            PlacementId = placementRecord.PlacementId,
            ProductId = productHierarchyRecord.ProductId,
            ClassId = 811,
            RiskProfileKey = "ELEM|12345",
            LineOfBusiness = "RiskProfileLOB",
            ClassOfBusiness = "RiskProfileCOB",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform
        });

        //RiskProfile for the specification
        dynamic riskProfileRecord2 = CreateRow(tableName: "PS.RiskProfile", values: new
        {
            RiskProfileId = 8,
            PlacementId = placementRecord.PlacementId,
            ProductId = productHierarchyRecord2.ProductId,
            ClassId = 812,
            RiskProfileKey = "SPEC|1",
            LineOfBusiness = "RiskProfileLOB",
            ClassOfBusiness = "RiskProfileCOB",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform
        });

        dynamic negotiationRecord = CreateRow(tableName: "PS.Negotiation", values: new
        {
            NegotiationKey = $"SUBC|{submissionContainerId}",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            SubmissionName = "Submission"
        });

        dynamic negotiationMarketRecord = CreateRow(tableName: "PS.NegotiationMarket", values: new
        {
            NegotiationId = negotiationRecord.NegotiationId,
            NegotiationMarketKey = $"SUBCONMKT|{submissionContainerMarketId}",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
        });

        dynamic SubmissionRecord = CreateRow(tableName: "BP.Submission", values: new
        {
            SubmissionContainerId = submissionContainerId,
            Sent = "2023-11-11"
        });

        dynamic marketResponseRecord = CreateRow(tableName: "PS.MarketResponse", values: new
        {
            NegotiationMarketId = negotiationMarketRecord.NegotiationMarketId,
            MarketResponseKey = $"MKTRES|{marketResponseId}",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            ResponseTypeId = responseTypeRecord.ResponseTypeId,
            LayerTypeId = layerTypeRecord.LayerTypeId,
            LimitCurrencyId = currencyRecordEur.CurrencyId,
            AttachmentPointCurrencyId = currencyRecordEur.CurrencyId,
            ResponseDate = "2023-11-21",
            Limit = 1000,
            Comments = "Comments",
            AttachmentPoint = 100000,
            UnderwriterName = "Joe Bloggs",
        });

        dynamic followTypeRecord = CreateRow(tableName: "ref.FollowType", values: new
        {
            FollowType = followType
        });

        dynamic marketQuoteResponseRecord = CreateRow(tableName: "PS.MarketQuoteResponse", values: new
        {
            MarketResponseId = marketResponseRecord.MarketResponseId,
            OutcomeReasonId = outcomeReasonRecord.OutcomeReasonId,
            OutcomeStatusId = 1,
            PremiumCurrencyId = currencyRecordEur.CurrencyId,
            IsDeleted = false,
            QuotedToLead = true,
            Premium = 10,
            PremiumRate = 10000,
            OfferedLine = 1000,
            OfferedLineRate = 100,
            CommissionRate = 15,
            Subjectivity = "Subjectivity",
            Deductible = 100,
            FollowTypeId = followTypeRecord.FollowTypeId
        });

        dynamic marketResponseBasisRecord = CreateRow(tableName: "PS.MarketResponseBasis", values: new
        {
            MarketResponseId = marketResponseRecord.MarketResponseId,
            MarketResponseBasisKey = "MRB|2",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            SpecificationId = 1,
            //ContractId = 2,
            //RiskProfileId = 7
            IsDeleted = false
        });

        dynamic marketSecurityRecord = CreateRow(tableName: "dbo.MarketResponseSecurity", values: new
        {
            MarketResponseId = marketResponseId,
            CarrierId = carrierId,
            Split = 10,
            IsFacilityLead = false
        });

        dynamic results = ExecuteStoredProcedureWithResultRow(storedProcedureName: "rpt.Load_rpt_MarketInteractionResponse");
        Assert.Equal(expected: 2, actual: results.InsertedCount);
        Assert.Equal(expected: 0, actual: results.UpdatedCount);
        Assert.Equal(expected: 0, actual: results.DeletedCount);
        Assert.Equal(expected: 0, actual: results.RejectedCount);

        //This should create two rows, one for SubmissionMarket and one for MarketSecurity
        CheckSprocExecutionLog(sprocName: "rpt.Load_rpt_MarketInteractionResponse", insertedCount: 2);

        dynamic row = GetResultRow(tableName: "rpt.MarketInteractionResponse", whereClause: $"MarketSecurityCarrierId = {carrierId}");
        Assert.Equal(expected: "MarketSecurity", actual: row.MarketCategory);
        Assert.Equal(expected: followTypeRecord.FollowType, actual: row.FollowType);
        Assert.Equal(expected: marketSecurityRecord.IsFacilityLead, actual: row.QuotedToLead);
        Assert.Equal(expected: false, actual: row.IsInvalid);

        dynamic row2 = GetResultRow(tableName: "rpt.MarketInteractionResponse", whereClause: "MarketSecurityCarrierId IS NULL");
        Assert.Equal(expected: "SubmissionMarket", actual: row2.MarketCategory);
        Assert.Equal(expected: followTypeRecord.FollowType, actual: row2.FollowType);
        Assert.Equal(expected: marketQuoteResponseRecord.QuotedToLead, actual: row2.QuotedToLead);
        Assert.Equal(expected: false, actual: row2.IsInvalid);

    }
    [Fact]
    public void Load_rpt_MarketInteractionResponseMarketSecurityContractTest()
    {
        var submissionContainerId = 20;
        var submissionContainerMarketId = 10;
        var marketResponseId = 40;
        var carrierId = 23;

        dynamic responseTypeRecord,
            placementSystemTableRecordCR,
            placementSystemTableRecordSCM,
            placementSystemTableRecordPC,
            placementSystemTableRecordMR,
            outcomeStatusRecordAcc,
            outcomeStatusRecordNotAcc,
            outcomeReasonRecord,
            layerTypeRecord,
            currencyRecordEur,
            facilityRecord,
            facilitySectionRecord,
            facilitySectionCarrierRecord,
            productHierarchyRecord,
            productHierarchyRecord2,
            placementStatusRecord;
        CreateReferenceRecords(carrierId, out responseTypeRecord, out placementSystemTableRecordCR, out placementSystemTableRecordSCM, out placementSystemTableRecordPC, out placementSystemTableRecordMR, out outcomeStatusRecordAcc, out outcomeStatusRecordNotAcc, out outcomeReasonRecord, out layerTypeRecord, out currencyRecordEur, out facilityRecord, out facilitySectionRecord, out facilitySectionCarrierRecord, out productHierarchyRecord, out productHierarchyRecord2, out placementStatusRecord);

        dynamic placementRecord = CreateRow(tableName: "dbo.Placement", values: new
        {
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            PlacementName = "Test Placement",
            PlacementStatusId = placementStatusRecord.PlacementStatusId,
            PlacementSystemId = 1022232,
            IsDeleted = false
        });

        //RiskProfile for the specification
        dynamic riskProfileRecord = CreateRow(tableName: "PS.RiskProfile", values: new
        {
            RiskProfileId = 7,
            PlacementId = placementRecord.PlacementId,
            ProductId = productHierarchyRecord2.ProductId,
            ODSProductId = 24074,
            ClassId = 812,
            RiskProfileKey = "ELEM|12345",
            LineOfBusiness = "RiskProfileLOB",
            ClassOfBusiness = "RiskProfileCOB",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform
        });

        //RiskProfile for the specification
        dynamic riskProfileRecord2 = CreateRow(tableName: "PS.RiskProfile", values: new
        {
            RiskProfileId = 8,
            PlacementId = placementRecord.PlacementId,
            ProductId = productHierarchyRecord.ProductId,
            ClassId = riskProfileRecord.ClassId,
            RiskProfileKey = "SPEC|1",
            LineOfBusiness = "RiskProfileLOB",
            ClassOfBusiness = "RiskProfileCOB",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform
        });

        dynamic productRecord = CreateRow(tableName: "dbo.Product", values: new
        {
            ProductId = riskProfileRecord.ODSProductId,
            ProductKey = productHierarchyRecord2.ProductKey,
            DataSourceInstanceId = productHierarchyRecord2.DataSourceInstanceId,
            IsDeleted = false
        });

        dynamic negotiationRecord = CreateRow(tableName: "PS.Negotiation", values: new
        {
            NegotiationKey = $"SUBC|{submissionContainerId}",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            SubmissionName = "Submission"
        });

        dynamic negotiationMarketRecord = CreateRow(tableName: "PS.NegotiationMarket", values: new
        {
            NegotiationId = negotiationRecord.NegotiationId,
            NegotiationMarketKey = $"SUBCONMKT|{submissionContainerMarketId}",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
        });

        dynamic SubmissionRecord = CreateRow(tableName: "BP.Submission", values: new
        {
            SubmissionContainerId = submissionContainerId,
            Sent = "2023-11-11"
        });

        dynamic marketResponseRecord = CreateRow(tableName: "PS.MarketResponse", values: new
        {
            NegotiationMarketId = negotiationMarketRecord.NegotiationMarketId,
            MarketResponseKey = $"MKTRES|{marketResponseId}",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            ResponseTypeId = responseTypeRecord.ResponseTypeId,
            LayerTypeId = layerTypeRecord.LayerTypeId,
            LimitCurrencyId = currencyRecordEur.CurrencyId,
            AttachmentPointCurrencyId = currencyRecordEur.CurrencyId,
            ResponseDate = "2023-11-21",
            Limit = 1000,
            Comments = "Comments",
            AttachmentPoint = 100000,
            UnderwriterName = "Joe Bloggs",
        });

        dynamic marketQuoteResponseRecord = CreateRow(tableName: "PS.MarketQuoteResponse", values: new
        {
            MarketResponseId = marketResponseRecord.MarketResponseId,
            OutcomeReasonId = outcomeReasonRecord.OutcomeReasonId,
            OutcomeStatusId = 1,
            PremiumCurrencyId = currencyRecordEur.CurrencyId,
            IsDeleted = false,
            QuotedToLead = 1,
            Premium = 10,
            PremiumRate = 10000,
            OfferedLine = 1000,
            OfferedLineRate = 100,
            CommissionRate = 15,
            Subjectivity = "Subjectivity",
        });

        dynamic marketResponseBasisRecord = CreateRow(tableName: "PS.MarketResponseBasis", values: new
        {
            MarketResponseId = marketResponseRecord.MarketResponseId,
            MarketResponseBasisKey = "MRB|2",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            //SpecificationId = 1,
            ContractId = 2,
            //RiskProfileId = 7
            IsDeleted = false
        });

        dynamic marketResponseBasisRecord2 = CreateRow(tableName: "PS.MarketResponseBasis", values: new
        {
            MarketResponseId = marketResponseRecord.MarketResponseId,
            MarketResponseBasisKey = "MRB|1",
            //SpecificationId = 1,
            ContractId = 3,
            //RiskProfileId = 7
            IsDeleted = false
        });

        dynamic contractRiskRecord = CreateRow(tableName: "PS.ContractRiskProfile", values: new
        {
            ContractRiskId = 1,
            ContractId = 2,
            //RiskDefinitionElementId = 12345,
            RiskProfileId = 7,
            IsDeleted = false
        });

        dynamic contractRiskRecord2 = CreateRow(tableName: "PS.ContractRiskProfile", values: new
        {
            ContractRiskId = 2,
            ContractId = 3,
            //RiskDefinitionElementId = 12345,
            RiskProfileId = 7,
            IsDeleted = false
        });

        // Negotiation Contract
        dynamic specificationRecord2 = CreateRow(tableName: "dbo.Specification", values: new
        {
            SpecificationId = 1,
            PlacementId = placementRecord.PlacementId,
            Label = "Old Specification",
            IsDeleted = false
        });

        dynamic marketSecurityRecord = CreateRow(tableName: "dbo.MarketResponseSecurity", values: new
        {
            MarketResponseId = marketResponseId,
            CarrierId = carrierId,
            Split = 10,
            IsFacilityLead = 1
        });

        dynamic results = ExecuteStoredProcedureWithResultRow(storedProcedureName: "rpt.Load_rpt_MarketInteractionResponse");
        Assert.Equal(expected: 2, actual: results.InsertedCount);
        Assert.Equal(expected: 0, actual: results.UpdatedCount);
        Assert.Equal(expected: 0, actual: results.DeletedCount);
        Assert.Equal(expected: 0, actual: results.RejectedCount);

        //This should create two rows, one for SubmissionMarket and one for MarketSecurity
        CheckSprocExecutionLog(sprocName: "rpt.Load_rpt_MarketInteractionResponse", insertedCount: 2);

        dynamic row = GetResultRow(tableName: "rpt.MarketInteractionResponse", whereClause: $"MarketSecurityCarrierId = {carrierId}");
        Assert.Equal(expected: "MarketSecurity", actual: row.MarketCategory);
        Assert.Equal(expected: marketResponseId, actual: row.CarrierResponseId);
        Assert.Equal(expected: $"MKTRES|{marketResponseId}", actual: row.MarketResponseKey);
        Assert.Equal(expected: submissionContainerMarketId, actual: row.MarketSelectionId);
        Assert.Equal(expected: $"MKTSEC|{submissionContainerMarketId}", actual: row.NegotiationMarketKey);
        Assert.Equal(expected: $"{placementSystemTableRecordSCM.TableId}|{submissionContainerMarketId}", actual: row.MarketSelectionKey);
        Assert.Equal(expected: negotiationRecord.SubmissionName, actual: row.InteractionName);
        Assert.Equal(expected: DateTime.Parse(SubmissionRecord.Sent), actual: row.SubmittedDate);
        Assert.Equal(expected: DateTime.Parse(marketResponseRecord.ResponseDate), actual: row.ResponseDate);
        Assert.Equal(expected: 10, actual: row.ResponseTimeDays);
        Assert.Equal(expected: outcomeReasonRecord.OutcomeReason, actual: row.OutcomeReason);
        Assert.Equal(expected: "Suitability of insurer", actual: row.OutcomeReasonGroupName);
        Assert.Equal(expected: outcomeStatusRecordAcc.OutcomeStatus, actual: row.OutcomeStatus);
        Assert.Equal(expected: productHierarchyRecord2.ProductKey, actual: row.ProductKey);
        Assert.Equal(expected: riskProfileRecord.ProductId, actual: row.ProductId);
        Assert.Equal(expected: riskProfileRecord.ClassId, actual: row.ClassId);
        Assert.Equal(expected: layerTypeRecord.LayerType, actual: row.LayerType);
        Assert.True(row.QuotedToLead);
        Assert.Equal(expected: currencyRecordEur.CurrencyAlphaCode, actual: row.Premium_Currency);
        Assert.Equal(expected: marketQuoteResponseRecord.Premium, actual: row.Premium);
        Assert.Equal(expected: 10, actual: row.Premium_USD);
        Assert.Equal(expected: 100, actual: row.OfferedLine); //Sec.Split / 100 * ms.OfferedLine
        Assert.Equal(expected: 100, actual: row.OfferedLine_USD);
        Assert.Equal(expected: 10, actual: row.OfferedLineRate); //Sec.Split / 100 * ms.OfferedLineRate
        Assert.Equal(expected: marketQuoteResponseRecord.PremiumRate, actual: row.PremiumRate);
        Assert.Equal(expected: marketQuoteResponseRecord.CommissionRate, actual: row.CommissionRate);
        Assert.Equal(expected: currencyRecordEur.CurrencyAlphaCode, actual: row.Limit_Currency);
        Assert.Equal(expected: marketResponseRecord.Limit, actual: row.Limit);
        Assert.Equal(expected: 1000, actual: row.Limit_USD);
        Assert.Equal(expected: currencyRecordEur.CurrencyAlphaCode, actual: row.AttachmentPoint_Currency);
        Assert.Equal(expected: marketResponseRecord.Comments, actual: row.Comments);
        Assert.Equal(expected: marketQuoteResponseRecord.Subjectivity, actual: row.Subjectivity);
        Assert.Equal(expected: 20231121, actual: row.ResponseDateKey);
        Assert.Equal(expected: $"{placementSystemTableRecordMR.TableId}|{marketResponseId}", actual: row.ResponseKey);
        Assert.Equal(expected: 1, actual: row.WrittenPremium);
        Assert.Equal(expected: 1, actual: row.WrittenPremium_USD);
        Assert.True(row.MultiRisk);
        Assert.Equal(expected: marketSecurityRecord.Split, actual: row.Split);
        Assert.Equal(expected: $"Carrier-{carrierId}", actual: row.MarketSecurityCarrierKey);
        Assert.Equal(expected: carrierId, actual: row.MarketSecurityCarrierId);

        dynamic row2 = GetResultRow(tableName: "rpt.MarketInteractionResponse", whereClause: "MarketSecurityCarrierId IS NULL");
        Assert.Equal(expected: "SubmissionMarket", actual: row2.MarketCategory);
        Assert.Equal(expected: marketResponseId, actual: row2.CarrierResponseId);
        Assert.Equal(expected: $"MKTRES|{marketResponseId}", actual: row2.MarketResponseKey);
        Assert.Equal(expected: submissionContainerMarketId, actual: row2.MarketSelectionId);
        Assert.Equal(expected: $"SUBCONMKT|{submissionContainerMarketId}", actual: row2.NegotiationMarketKey);
        Assert.Equal(expected: $"{placementSystemTableRecordSCM.TableId}|{submissionContainerMarketId}", actual: row2.MarketSelectionKey);
        Assert.Equal(expected: negotiationRecord.SubmissionName, actual: row2.InteractionName);
        Assert.Equal(expected: DateTime.Parse(SubmissionRecord.Sent), actual: row2.SubmittedDate);
        Assert.Equal(expected: DateTime.Parse(marketResponseRecord.ResponseDate), actual: row2.ResponseDate);
        Assert.Equal(expected: 10, actual: row2.ResponseTimeDays);
        Assert.Equal(expected: outcomeReasonRecord.OutcomeReason, actual: row2.OutcomeReason);
        Assert.Equal(expected: "Suitability of insurer", actual: row2.OutcomeReasonGroupName);
        Assert.Equal(expected: outcomeStatusRecordAcc.OutcomeStatus, actual: row2.OutcomeStatus);
        Assert.Equal(expected: productHierarchyRecord2.ProductKey, actual: row2.ProductKey);
        Assert.Equal(expected: riskProfileRecord.ProductId, actual: row2.ProductId);
        Assert.Equal(expected: riskProfileRecord.ClassId, actual: row2.ClassId);
        Assert.Equal(expected: layerTypeRecord.LayerType, actual: row2.LayerType);
        Assert.True(row2.QuotedToLead);
        Assert.Equal(expected: currencyRecordEur.CurrencyAlphaCode, actual: row2.Premium_Currency);
        Assert.Equal(expected: marketQuoteResponseRecord.Premium, actual: row2.Premium);
        Assert.Equal(expected: 10, actual: row2.Premium_USD);
        Assert.Equal(expected: marketQuoteResponseRecord.OfferedLine, actual: row2.OfferedLine);
        Assert.Equal(expected: 1000, actual: row2.OfferedLine_USD);
        Assert.Equal(expected: marketQuoteResponseRecord.OfferedLineRate, actual: row2.OfferedLineRate);
        Assert.Equal(expected: marketQuoteResponseRecord.PremiumRate, actual: row2.PremiumRate);
        Assert.Equal(expected: marketQuoteResponseRecord.CommissionRate, actual: row2.CommissionRate);
        Assert.Equal(expected: currencyRecordEur.CurrencyAlphaCode, actual: row2.Limit_Currency);
        Assert.Equal(expected: marketResponseRecord.Limit, actual: row2.Limit);
        Assert.Equal(expected: 1000, actual: row2.Limit_USD);
        Assert.Equal(expected: currencyRecordEur.CurrencyAlphaCode, actual: row2.AttachmentPoint_Currency);
        Assert.Equal(expected: marketResponseRecord.Comments, actual: row2.Comments);
        Assert.Equal(expected: marketQuoteResponseRecord.Subjectivity, actual: row2.Subjectivity);
        Assert.Equal(expected: 20231121, actual: row2.ResponseDateKey);
        Assert.Equal(expected: $"{placementSystemTableRecordMR.TableId}|{marketResponseId}", actual: row2.ResponseKey);
        Assert.Equal(expected: 10, actual: row2.WrittenPremium);
        Assert.Equal(expected: 10, actual: row2.WrittenPremium_USD);
        Assert.True(row2.MultiRisk);
        Assert.Equal(expected: marketQuoteResponseRecord.OfferedLineRate, actual: row2.Split);
    }

    [Fact]
    public void Load_rpt_MarketInteractionResponseMarketSecurityContractTwoRisks()
    {
        var submissionContainerId = 20;
        var submissionContainerMarketId = 10;
        var marketResponseId = 40;
        var carrierId = 23;

        dynamic responseTypeRecord,
            placementSystemTableRecordCR,
            placementSystemTableRecordSCM,
            placementSystemTableRecordPC,
            placementSystemTableRecordMR,
            outcomeStatusRecordAcc,
            outcomeStatusRecordNotAcc,
            outcomeReasonRecord,
            layerTypeRecord,
            currencyRecordEur,
            facilityRecord,
            facilitySectionRecord,
            facilitySectionCarrierRecord,
            productHierarchyRecord,
            productHierarchyRecord2,
            placementStatusRecord;
        CreateReferenceRecords(carrierId, out responseTypeRecord, out placementSystemTableRecordCR, out placementSystemTableRecordSCM, out placementSystemTableRecordPC, out placementSystemTableRecordMR, out outcomeStatusRecordAcc, out outcomeStatusRecordNotAcc, out outcomeReasonRecord, out layerTypeRecord, out currencyRecordEur, out facilityRecord, out facilitySectionRecord, out facilitySectionCarrierRecord, out productHierarchyRecord, out productHierarchyRecord2, out placementStatusRecord);

        dynamic placementRecord = CreateRow(tableName: "dbo.Placement", values: new
        {
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            PlacementName = "Test Placement",
            PlacementStatusId = placementStatusRecord.PlacementStatusId,
            PlacementSystemId = 1022232,
            IsDeleted = false
        });

        //RiskProfile for the specification
        dynamic riskProfileRecord = CreateRow(tableName: "PS.RiskProfile", values: new
        {
            RiskProfileId = 7,
            PlacementId = placementRecord.PlacementId,
            ProductId = productHierarchyRecord2.ProductId,
            ClassId = 812,
            RiskProfileKey = "ELEM|12345",
            LineOfBusiness = "RiskProfileLOB",
            ClassOfBusiness = "RiskProfileCOB",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform
        });

        //RiskProfile for the specification
        dynamic riskProfileRecord2 = CreateRow(tableName: "PS.RiskProfile", values: new
        {
            RiskProfileId = 8,
            PlacementId = placementRecord.PlacementId,
            ProductId = productHierarchyRecord.ProductId,
            ODSProductId = 24074,
            ClassId = riskProfileRecord.ClassId,
            RiskProfileKey = "SPEC|1",
            LineOfBusiness = "RiskProfileLOB",
            ClassOfBusiness = "RiskProfileCOB",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform
        });

        dynamic productRecord = CreateRow(tableName: "dbo.Product", values: new
        {
            ProductId = riskProfileRecord2.ODSProductId,
            ProductKey = productHierarchyRecord.ProductKey,
            DataSourceInstanceId = productHierarchyRecord.DataSourceInstanceId,
            IsDeleted = false
        });

        dynamic negotiationRecord = CreateRow(tableName: "PS.Negotiation", values: new
        {
            NegotiationKey = $"SUBC|{submissionContainerId}",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            SubmissionName = "Submission"
        });

        dynamic negotiationMarketRecord = CreateRow(tableName: "PS.NegotiationMarket", values: new
        {
            NegotiationId = negotiationRecord.NegotiationId,
            NegotiationMarketKey = $"SUBCONMKT|{submissionContainerMarketId}",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
        });

        dynamic SubmissionRecord = CreateRow(tableName: "BP.Submission", values: new
        {
            SubmissionContainerId = submissionContainerId,
            Sent = "2023-11-11"
        });

        dynamic marketResponseRecord = CreateRow(tableName: "PS.MarketResponse", values: new
        {
            NegotiationMarketId = negotiationMarketRecord.NegotiationMarketId,
            MarketResponseKey = $"MKTRES|{marketResponseId}",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            ResponseTypeId = responseTypeRecord.ResponseTypeId,
            LayerTypeId = layerTypeRecord.LayerTypeId,
            LimitCurrencyId = currencyRecordEur.CurrencyId,
            AttachmentPointCurrencyId = currencyRecordEur.CurrencyId,
            ResponseDate = "2023-11-21",
            Limit = 1000,
            Comments = "Comments",
            AttachmentPoint = 100000,
            UnderwriterName = "Joe Bloggs",
        });

        dynamic marketQuoteResponseRecord = CreateRow(tableName: "PS.MarketQuoteResponse", values: new
        {
            MarketResponseId = marketResponseRecord.MarketResponseId,
            OutcomeReasonId = outcomeReasonRecord.OutcomeReasonId,
            OutcomeStatusId = 1,
            PremiumCurrencyId = currencyRecordEur.CurrencyId,
            IsDeleted = false,
            QuotedToLead = 1,
            Premium = 10,
            PremiumRate = 10000,
            OfferedLine = 1000,
            OfferedLineRate = 100,
            CommissionRate = 15,
            Subjectivity = "Subjectivity",
        });

        dynamic marketResponseBasisRecord = CreateRow(tableName: "PS.MarketResponseBasis", values: new
        {
            MarketResponseId = marketResponseRecord.MarketResponseId,
            MarketResponseBasisKey = "MRB|2",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            //SpecificationId = 1,
            ContractId = 2,
            //RiskProfileId = 7
            IsDeleted = false
        });

        dynamic contractRiskRecord = CreateRow(tableName: "PS.ContractRiskProfile", values: new
        {
            ContractRiskId = 1,
            ContractId = 2,
            //RiskDefinitionElementId = 12345,
            RiskProfileId = 7,
            IsDeleted = false
        });

        dynamic contractRiskRecord2 = CreateRow(tableName: "PS.ContractRiskProfile", values: new
        {
            ContractRiskId = 2,
            ContractId = 2,
            //RiskDefinitionElementId = 12345,
            RiskProfileId = 8,
            IsDeleted = false
        });

        // Negotiation Contract
        dynamic specificationRecord2 = CreateRow(tableName: "dbo.Specification", values: new
        {
            SpecificationId = 1,
            PlacementId = placementRecord.PlacementId,
            Label = "Old Specification",
            IsDeleted = false
        });

        dynamic marketSecurityRecord = CreateRow(tableName: "dbo.MarketResponseSecurity", values: new
        {
            MarketResponseId = marketResponseId,
            CarrierId = carrierId,
            Split = 10,
            IsFacilityLead = 1
        });

        dynamic results = ExecuteStoredProcedureWithResultRow(storedProcedureName: "rpt.Load_rpt_MarketInteractionResponse");
        Assert.Equal(expected: 2, actual: results.InsertedCount);
        Assert.Equal(expected: 0, actual: results.UpdatedCount);
        Assert.Equal(expected: 0, actual: results.DeletedCount);
        Assert.Equal(expected: 0, actual: results.RejectedCount);

        CheckSprocExecutionLog(sprocName: "rpt.Load_rpt_MarketInteractionResponse", insertedCount: 2);

        dynamic row = GetResultRow(tableName: "rpt.MarketInteractionResponse", whereClause: $"MarketSecurityCarrierId = {carrierId}");
        Assert.Equal(expected: "MarketSecurity", actual: row.MarketCategory);
        Assert.Equal(expected: marketResponseId, actual: row.CarrierResponseId);
        Assert.Equal(expected: $"MKTRES|{marketResponseId}", actual: row.MarketResponseKey);
        Assert.Equal(expected: submissionContainerMarketId, actual: row.MarketSelectionId);
        Assert.Equal(expected: $"MKTSEC|{submissionContainerMarketId}", actual: row.NegotiationMarketKey);
        Assert.Equal(expected: $"{placementSystemTableRecordSCM.TableId}|{submissionContainerMarketId}", actual: row.MarketSelectionKey);
        Assert.Equal(expected: negotiationRecord.SubmissionName, actual: row.InteractionName);
        Assert.Equal(expected: DateTime.Parse(SubmissionRecord.Sent), actual: row.SubmittedDate);
        Assert.Equal(expected: DateTime.Parse(marketResponseRecord.ResponseDate), actual: row.ResponseDate);
        Assert.Equal(expected: 10, actual: row.ResponseTimeDays);
        Assert.Equal(expected: outcomeReasonRecord.OutcomeReason, actual: row.OutcomeReason);
        Assert.Equal(expected: "Suitability of insurer", actual: row.OutcomeReasonGroupName);
        Assert.Equal(expected: outcomeStatusRecordAcc.OutcomeStatus, actual: row.OutcomeStatus);
        Assert.Equal(expected: productHierarchyRecord.ProductKey, actual: row.ProductKey);
        Assert.Equal(expected: riskProfileRecord2.ProductId, actual: row.ProductId);
        Assert.Equal(expected: riskProfileRecord2.ClassId, actual: row.ClassId);
        Assert.Equal(expected: layerTypeRecord.LayerType, actual: row.LayerType);
        Assert.True(row.QuotedToLead);
        Assert.Equal(expected: currencyRecordEur.CurrencyAlphaCode, actual: row.Premium_Currency);
        Assert.Equal(expected: marketQuoteResponseRecord.Premium, actual: row.Premium);
        Assert.Equal(expected: 10, actual: row.Premium_USD);
        Assert.Equal(expected: 100, actual: row.OfferedLine); //Sec.Split / 100 * ms.OfferedLine
        Assert.Equal(expected: 100, actual: row.OfferedLine_USD);
        Assert.Equal(expected: 10, actual: row.OfferedLineRate); //Sec.Split / 100 * ms.OfferedLineRate
        Assert.Equal(expected: marketQuoteResponseRecord.PremiumRate, actual: row.PremiumRate);
        Assert.Equal(expected: marketQuoteResponseRecord.CommissionRate, actual: row.CommissionRate);
        Assert.Equal(expected: currencyRecordEur.CurrencyAlphaCode, actual: row.Limit_Currency);
        Assert.Equal(expected: marketResponseRecord.Limit, actual: row.Limit);
        Assert.Equal(expected: 1000, actual: row.Limit_USD);
        Assert.Equal(expected: currencyRecordEur.CurrencyAlphaCode, actual: row.AttachmentPoint_Currency);
        Assert.Equal(expected: marketResponseRecord.Comments, actual: row.Comments);
        Assert.Equal(expected: marketQuoteResponseRecord.Subjectivity, actual: row.Subjectivity);
        Assert.Equal(expected: 20231121, actual: row.ResponseDateKey);
        Assert.Equal(expected: $"{placementSystemTableRecordMR.TableId}|{marketResponseId}", actual: row.ResponseKey);
        Assert.Equal(expected: 1, actual: row.WrittenPremium);
        Assert.Equal(expected: 1, actual: row.WrittenPremium_USD);
        Assert.True(row.MultiRisk);
        Assert.Equal(expected: marketSecurityRecord.Split, actual: row.Split);
        Assert.Equal(expected: $"Carrier-{carrierId}", actual: row.MarketSecurityCarrierKey);
        Assert.Equal(expected: carrierId, actual: row.MarketSecurityCarrierId);
        Assert.Equal(expected: marketResponseBasisRecord.ContractId, actual: row.ContractId);

        dynamic row2 = GetResultRow(tableName: "rpt.MarketInteractionResponse", whereClause: "MarketSecurityCarrierId IS NULL");
        Assert.Equal(expected: "SubmissionMarket", actual: row2.MarketCategory);
        Assert.Equal(expected: marketResponseId, actual: row2.CarrierResponseId);
        Assert.Equal(expected: $"MKTRES|{marketResponseId}", actual: row2.MarketResponseKey);
        Assert.Equal(expected: submissionContainerMarketId, actual: row2.MarketSelectionId);
        Assert.Equal(expected: $"SUBCONMKT|{submissionContainerMarketId}", actual: row2.NegotiationMarketKey);
        Assert.Equal(expected: $"{placementSystemTableRecordSCM.TableId}|{submissionContainerMarketId}", actual: row2.MarketSelectionKey);
        Assert.Equal(expected: negotiationRecord.SubmissionName, actual: row2.InteractionName);
        Assert.Equal(expected: DateTime.Parse(SubmissionRecord.Sent), actual: row2.SubmittedDate);
        Assert.Equal(expected: DateTime.Parse(marketResponseRecord.ResponseDate), actual: row2.ResponseDate);
        Assert.Equal(expected: 10, actual: row2.ResponseTimeDays);
        Assert.Equal(expected: outcomeReasonRecord.OutcomeReason, actual: row2.OutcomeReason);
        Assert.Equal(expected: "Suitability of insurer", actual: row2.OutcomeReasonGroupName);
        Assert.Equal(expected: outcomeStatusRecordAcc.OutcomeStatus, actual: row2.OutcomeStatus);
        Assert.Equal(expected: productHierarchyRecord.ProductKey, actual: row2.ProductKey);
        Assert.Equal(expected: riskProfileRecord2.ProductId, actual: row2.ProductId);
        Assert.Equal(expected: riskProfileRecord2.ClassId, actual: row2.ClassId);
        Assert.Equal(expected: layerTypeRecord.LayerType, actual: row2.LayerType);
        Assert.True(row2.QuotedToLead);
        Assert.Equal(expected: currencyRecordEur.CurrencyAlphaCode, actual: row2.Premium_Currency);
        Assert.Equal(expected: marketQuoteResponseRecord.Premium, actual: row2.Premium);
        Assert.Equal(expected: 10, actual: row2.Premium_USD);
        Assert.Equal(expected: marketQuoteResponseRecord.OfferedLine, actual: row2.OfferedLine);
        Assert.Equal(expected: 1000, actual: row2.OfferedLine_USD);
        Assert.Equal(expected: marketQuoteResponseRecord.OfferedLineRate, actual: row2.OfferedLineRate);
        Assert.Equal(expected: marketQuoteResponseRecord.PremiumRate, actual: row2.PremiumRate);
        Assert.Equal(expected: marketQuoteResponseRecord.CommissionRate, actual: row2.CommissionRate);
        Assert.Equal(expected: currencyRecordEur.CurrencyAlphaCode, actual: row2.Limit_Currency);
        Assert.Equal(expected: marketResponseRecord.Limit, actual: row2.Limit);
        Assert.Equal(expected: 1000, actual: row2.Limit_USD);
        Assert.Equal(expected: currencyRecordEur.CurrencyAlphaCode, actual: row2.AttachmentPoint_Currency);
        Assert.Equal(expected: marketResponseRecord.Comments, actual: row2.Comments);
        Assert.Equal(expected: marketQuoteResponseRecord.Subjectivity, actual: row2.Subjectivity);
        Assert.Equal(expected: 20231121, actual: row2.ResponseDateKey);
        Assert.Equal(expected: $"{placementSystemTableRecordMR.TableId}|{marketResponseId}", actual: row2.ResponseKey);
        Assert.Equal(expected: 10, actual: row2.WrittenPremium);
        Assert.Equal(expected: 10, actual: row2.WrittenPremium_USD);
        Assert.True(row2.MultiRisk);
        Assert.Equal(expected: marketQuoteResponseRecord.OfferedLineRate, actual: row2.Split);
        Assert.Equal(expected: marketResponseBasisRecord.ContractId, actual: row.ContractId);
    }


    [Fact]
    public void Load_rpt_MarketInteractionResponseMarketSecurityRiskProfileTest()
    {
        int submissionContainerId = 20;
        int submissionContainerMarketId = 10;
        int marketResponseId = 40;
        int carrierId = 23;

        dynamic responseTypeRecord,
            placementSystemTableRecordCR,
            placementSystemTableRecordSCM,
            placementSystemTableRecordPC,
            placementSystemTableRecordMR,
            outcomeStatusRecordAcc,
            outcomeStatusRecordNotAcc,
            outcomeReasonRecord,
            layerTypeRecord,
            currencyRecordEur,
            facilityRecord,
            facilitySectionRecord,
            facilitySectionCarrierRecord,
            productHierarchyRecord,
            productHierarchyRecord2,
            placementStatusRecord;
        CreateReferenceRecords(carrierId, out responseTypeRecord, out placementSystemTableRecordCR, out placementSystemTableRecordSCM, out placementSystemTableRecordPC, out placementSystemTableRecordMR, out outcomeStatusRecordAcc, out outcomeStatusRecordNotAcc, out outcomeReasonRecord, out layerTypeRecord, out currencyRecordEur, out facilityRecord, out facilitySectionRecord, out facilitySectionCarrierRecord, out productHierarchyRecord, out productHierarchyRecord2, out placementStatusRecord);

        dynamic placementRecord = CreateRow(tableName: "dbo.Placement", values: new
        {
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            PlacementName = "Test Placement",
            PlacementStatusId = placementStatusRecord.PlacementStatusId,
            PlacementSystemId = 1022232,
            IsDeleted = false
        });

        //RiskProfile for the specification
        dynamic riskProfileRecord = CreateRow(tableName: "PS.RiskProfile", values: new
        {
            RiskProfileId = 7,
            PlacementId = placementRecord.PlacementId,
            ProductId = productHierarchyRecord2.ProductId,
            ClassId = 812,
            RiskProfileKey = "ELEM|12345",
            LineOfBusiness = "RiskProfileLOB",
            ClassOfBusiness = "RiskProfileCOB",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform
        });

        //RiskProfile for the specification
        dynamic riskProfileRecord2 = CreateRow(tableName: "PS.RiskProfile", values: new
        {
            RiskProfileId = 8,
            PlacementId = placementRecord.PlacementId,
            ProductId = productHierarchyRecord.ProductId,
            ODSProductId = 24074,
            ClassId = riskProfileRecord.ClassId,
            RiskProfileKey = "SPEC|1",
            LineOfBusiness = "RiskProfileLOB",
            ClassOfBusiness = "RiskProfileCOB",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform
        });

        dynamic productRecord = CreateRow(tableName: "dbo.Product", values: new
        {
            ProductId = riskProfileRecord2.ODSProductId,
            ProductKey = productHierarchyRecord.ProductKey,
            DataSourceInstanceId = productHierarchyRecord.DataSourceInstanceId,
            IsDeleted = false
        });

        dynamic negotiationRecord = CreateRow(tableName: "PS.Negotiation", values: new
        {
            NegotiationKey = $"SUBC|{submissionContainerId}",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            SubmissionName = "Submission"
        });

        dynamic negotiationMarketRecord = CreateRow(tableName: "PS.NegotiationMarket", values: new
        {
            NegotiationId = negotiationRecord.NegotiationId,
            NegotiationMarketKey = $"SUBCONMKT|{submissionContainerMarketId}",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
        });

        dynamic SubmissionRecord = CreateRow(tableName: "BP.Submission", values: new
        {
            SubmissionContainerId = submissionContainerId,
            Sent = "2023-11-11"

        });

        dynamic marketResponseRecord = CreateRow(tableName: "PS.MarketResponse", values: new
        {
            NegotiationMarketId = negotiationMarketRecord.NegotiationMarketId,
            MarketResponseKey = $"MKTRES|{marketResponseId}",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            ResponseTypeId = responseTypeRecord.ResponseTypeId,
            LayerTypeId = layerTypeRecord.LayerTypeId,
            LimitCurrencyId = currencyRecordEur.CurrencyId,
            AttachmentPointCurrencyId = currencyRecordEur.CurrencyId,
            ResponseDate = "2023-11-21",
            Limit = 1000,
            Comments = "Comments",
            AttachmentPoint = 100000,
            UnderwriterName = "Joe Bloggs",
        });

        dynamic marketQuoteResponseRecord = CreateRow(tableName: "PS.MarketQuoteResponse", values: new
        {
            MarketResponseId = marketResponseRecord.MarketResponseId,
            OutcomeReasonId = outcomeReasonRecord.OutcomeReasonId,
            OutcomeStatusId = 1,
            PremiumCurrencyId = currencyRecordEur.CurrencyId,
            IsDeleted = false,
            QuotedToLead = 1,
            Premium = 10,
            PremiumRate = 10000,
            OfferedLine = 1000,
            OfferedLineRate = 100,
            CommissionRate = 15,
            Subjectivity = "Subjectivity",
        });

        dynamic marketResponseBasisRecord = CreateRow(tableName: "PS.MarketResponseBasis", values: new
        {
            MarketResponseId = marketResponseRecord.MarketResponseId,
            MarketResponseBasisKey = "MRB|2",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            //SpecificationId = 1,
            //ContractId = 2,
            RiskProfileId = 7,
            IsDeleted = false
        });

        dynamic marketResponseBasisRecord2 = CreateRow(tableName: "PS.MarketResponseBasis", values: new
        {
            MarketResponseId = marketResponseRecord.MarketResponseId,
            MarketResponseBasisKey = "MRB|1",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            //SpecificationId = 1,
            //ContractId = 3,
            RiskProfileId = 8,
            IsDeleted = false
        });

        dynamic contractRiskRecord = CreateRow(tableName: "PS.ContractRiskProfile", values: new
        {
            ContractRiskId = 1,
            ContractId = 2,
            //RiskDefinitionElementId = 12345,
            RiskProfileId = 7,
            IsDeleted = false
        });

        dynamic contractRiskRecord2 = CreateRow(tableName: "PS.ContractRiskProfile", values: new
        {
            ContractRiskId = 2,
            ContractId = 3,
            //RiskDefinitionElementId = 12345,
            RiskProfileId = 7,
            IsDeleted = false
        });

        // Negotiation Contract
        dynamic specificationRecord2 = CreateRow(tableName: "dbo.Specification", values: new
        {
            SpecificationId = 1,
            PlacementId = placementRecord.PlacementId,
            Label = "Old Specification",
            IsDeleted = false
        });

        dynamic marketSecurityRecord = CreateRow(tableName: "dbo.MarketResponseSecurity", values: new
        {
            MarketResponseId = marketResponseId,
            CarrierId = carrierId,
            Split = 10,
            IsFacilityLead = 1
        });

        dynamic results = ExecuteStoredProcedureWithResultRow(storedProcedureName: "rpt.Load_rpt_MarketInteractionResponse");
        Assert.Equal(expected: 2, actual: results.InsertedCount);
        Assert.Equal(expected: 0, actual: results.UpdatedCount);
        Assert.Equal(expected: 0, actual: results.DeletedCount);
        Assert.Equal(expected: 0, actual: results.RejectedCount);

        //This should create two rows, one for SubmissionMarket and one for MarketSecurity
        CheckSprocExecutionLog(sprocName: "rpt.Load_rpt_MarketInteractionResponse", insertedCount: 2);

        dynamic row = GetResultRow(tableName: "rpt.MarketInteractionResponse", whereClause: $"MarketSecurityCarrierId = {carrierId}");
        Assert.Equal(expected: "MarketSecurity", actual: row.MarketCategory);
        Assert.Equal(expected: marketResponseId, actual: row.CarrierResponseId);
        Assert.Equal(expected: $"MKTRES|{marketResponseId}", actual: row.MarketResponseKey);
        Assert.Equal(expected: submissionContainerMarketId, actual: row.MarketSelectionId);
        Assert.Equal(expected: $"MKTSEC|{submissionContainerMarketId}", actual: row.NegotiationMarketKey);
        Assert.Equal(expected: $"{placementSystemTableRecordSCM.TableId}|{submissionContainerMarketId}", actual: row.MarketSelectionKey);
        Assert.Equal(expected: negotiationRecord.SubmissionName, actual: row.InteractionName);
        Assert.Equal(expected: DateTime.Parse(SubmissionRecord.Sent), actual: row.SubmittedDate);
        Assert.Equal(expected: DateTime.Parse(marketResponseRecord.ResponseDate), actual: row.ResponseDate);
        Assert.Equal(expected: 10, actual: row.ResponseTimeDays);
        Assert.Equal(expected: outcomeReasonRecord.OutcomeReason, actual: row.OutcomeReason);
        Assert.Equal(expected: "Suitability of insurer", actual: row.OutcomeReasonGroupName);
        Assert.Equal(expected: outcomeStatusRecordAcc.OutcomeStatus, actual: row.OutcomeStatus);
        Assert.Equal(expected: productHierarchyRecord.ProductKey, actual: row.ProductKey);
        Assert.Equal(expected: riskProfileRecord2.ProductId, actual: row.ProductId);
        Assert.Equal(expected: riskProfileRecord2.ClassId, actual: row.ClassId);
        Assert.Equal(expected: layerTypeRecord.LayerType, actual: row.LayerType);
        Assert.Equal(expected: true, actual: row.QuotedToLead);
        Assert.Equal(expected: currencyRecordEur.CurrencyAlphaCode, actual: row.Premium_Currency);
        Assert.Equal(expected: marketQuoteResponseRecord.Premium, actual: row.Premium);
        Assert.Equal(expected: 10, actual: row.Premium_USD);
        Assert.Equal(expected: 100, actual: row.OfferedLine); //Sec.Split / 100 * ms.OfferedLine
        Assert.Equal(expected: 100, actual: row.OfferedLine_USD);
        Assert.Equal(expected: 10, actual: row.OfferedLineRate); //Sec.Split / 100 * ms.OfferedLineRate
        Assert.Equal(expected: marketQuoteResponseRecord.PremiumRate, actual: row.PremiumRate);
        Assert.Equal(expected: marketQuoteResponseRecord.CommissionRate, actual: row.CommissionRate);
        Assert.Equal(expected: currencyRecordEur.CurrencyAlphaCode, actual: row.Limit_Currency);
        Assert.Equal(expected: marketResponseRecord.Limit, actual: row.Limit);
        Assert.Equal(expected: 1000, actual: row.Limit_USD);
        Assert.Equal(expected: currencyRecordEur.CurrencyAlphaCode, actual: row.AttachmentPoint_Currency);
        Assert.Equal(expected: marketResponseRecord.Comments, actual: row.Comments);
        Assert.Equal(expected: marketQuoteResponseRecord.Subjectivity, actual: row.Subjectivity);
        Assert.Equal(expected: 20231121, actual: row.ResponseDateKey);
        Assert.Equal(expected: $"{placementSystemTableRecordMR.TableId}|{marketResponseId}", actual: row.ResponseKey);
        Assert.Equal(expected: 1, actual: row.WrittenPremium);
        Assert.Equal(expected: 1, actual: row.WrittenPremium_USD);
        Assert.Equal(expected: true, actual: row.MultiRisk);
        Assert.Equal(expected: marketSecurityRecord.Split, actual: row.Split);
        Assert.Equal(expected: $"Carrier-{carrierId}", actual: row.MarketSecurityCarrierKey);
        Assert.Equal(expected: carrierId, actual: row.MarketSecurityCarrierId);

        dynamic row2 = GetResultRow(tableName: "rpt.MarketInteractionResponse", whereClause: "MarketSecurityCarrierId IS NULL");
        Assert.Equal(expected: "SubmissionMarket", actual: row2.MarketCategory);
        Assert.Equal(expected: marketResponseId, actual: row2.CarrierResponseId);
        Assert.Equal(expected: $"MKTRES|{marketResponseId}", actual: row2.MarketResponseKey);
        Assert.Equal(expected: submissionContainerMarketId, actual: row2.MarketSelectionId);
        Assert.Equal(expected: $"SUBCONMKT|{submissionContainerMarketId}", actual: row2.NegotiationMarketKey);
        Assert.Equal(expected: $"{placementSystemTableRecordSCM.TableId}|{submissionContainerMarketId}", actual: row2.MarketSelectionKey);
        Assert.Equal(expected: negotiationRecord.SubmissionName, actual: row2.InteractionName);
        Assert.Equal(expected: DateTime.Parse(SubmissionRecord.Sent), actual: row2.SubmittedDate);
        Assert.Equal(expected: DateTime.Parse(marketResponseRecord.ResponseDate), actual: row2.ResponseDate);
        Assert.Equal(expected: 10, actual: row2.ResponseTimeDays);
        Assert.Equal(expected: outcomeReasonRecord.OutcomeReason, actual: row2.OutcomeReason);
        Assert.Equal(expected: "Suitability of insurer", actual: row2.OutcomeReasonGroupName);
        Assert.Equal(expected: outcomeStatusRecordAcc.OutcomeStatus, actual: row2.OutcomeStatus);
        Assert.Equal(expected: productHierarchyRecord.ProductKey, actual: row2.ProductKey);
        Assert.Equal(expected: riskProfileRecord2.ProductId, actual: row2.ProductId);
        Assert.Equal(expected: riskProfileRecord2.ClassId, actual: row2.ClassId);
        Assert.Equal(expected: layerTypeRecord.LayerType, actual: row2.LayerType);
        Assert.Equal(expected: true, actual: row2.QuotedToLead);
        Assert.Equal(expected: currencyRecordEur.CurrencyAlphaCode, actual: row2.Premium_Currency);
        Assert.Equal(expected: marketQuoteResponseRecord.Premium, actual: row2.Premium);
        Assert.Equal(expected: 10, actual: row2.Premium_USD);
        Assert.Equal(expected: marketQuoteResponseRecord.OfferedLine, actual: row2.OfferedLine);
        Assert.Equal(expected: 1000, actual: row2.OfferedLine_USD);
        Assert.Equal(expected: marketQuoteResponseRecord.OfferedLineRate, actual: row2.OfferedLineRate);
        Assert.Equal(expected: marketQuoteResponseRecord.PremiumRate, actual: row2.PremiumRate);
        Assert.Equal(expected: marketQuoteResponseRecord.CommissionRate, actual: row2.CommissionRate);
        Assert.Equal(expected: currencyRecordEur.CurrencyAlphaCode, actual: row2.Limit_Currency);
        Assert.Equal(expected: marketResponseRecord.Limit, actual: row2.Limit);
        Assert.Equal(expected: 1000, actual: row2.Limit_USD);
        Assert.Equal(expected: currencyRecordEur.CurrencyAlphaCode, actual: row2.AttachmentPoint_Currency);
        Assert.Equal(expected: marketResponseRecord.Comments, actual: row2.Comments);
        Assert.Equal(expected: marketQuoteResponseRecord.Subjectivity, actual: row2.Subjectivity);
        Assert.Equal(expected: 20231121, actual: row2.ResponseDateKey);
        Assert.Equal(expected: $"{placementSystemTableRecordMR.TableId}|{marketResponseId}", actual: row2.ResponseKey);
        Assert.Equal(expected: 10, actual: row2.WrittenPremium);
        Assert.Equal(expected: 10, actual: row2.WrittenPremium_USD);
        Assert.Equal(expected: true, actual: row2.MultiRisk);
        Assert.Equal(expected: marketQuoteResponseRecord.OfferedLineRate, actual: row2.Split);
    }

    [Fact]
    public void LoadMarketInteractionSignedPremiumUSDTest()
    {
        var submissionContainerId = 20;
        var submissionContainerMarketId = 10;
        var marketResponseId = 40;

        // Currency
        dynamic currencyRecordEur = CreateRow(tableName: "Reference.Currency", values: new
        {
            CurrencyAlphaCode = "EUR",
            CurrencyId = 2,
            CurrencyName = "Euro"
        });

        // Currency
        dynamic currencyRecord2 = CreateRow(tableName: "Reference.Currency", values: new
        {
            CurrencyAlphaCode = "USD",
            CurrencyId = 100249,
            CurrencyName = "Dollars"
        });

        // Exchange Rate
        dynamic exchangeRateRecord = CreateRow(tableName: "Reference.ExchangeRate", values: new
        {
            ExchangeRateId = 10,
            FromCurrencyId = currencyRecordEur.CurrencyId,
            ToCurrencyId = currencyRecord2.CurrencyId,
            EffectiveDate = "2024-02-04",
            ExchangeRateTypeId = (int)ExchangeRateType.AverageRate,
            IsDeleted = false,
            ExchangeRate = 1.5
        });

        dynamic negotiationRecord = CreateRow(tableName: "PS.Negotiation", values: new
        {
            NegotiationKey = $"SUBC|{submissionContainerId}",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            SubmissionName = "Submission"
        });

        dynamic negotiationMarketRecord = CreateRow(tableName: "PS.NegotiationMarket", values: new
        {
            NegotiationId = negotiationRecord.NegotiationId,
            NegotiationMarketKey = $"SUBCONMKT|{submissionContainerMarketId}",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
        });

        dynamic marketResponseRecord = CreateRow(tableName: "PS.MarketResponse", values: new
        {
            NegotiationMarketId = negotiationMarketRecord.NegotiationMarketId,
            MarketResponseKey = $"MKTRES|{marketResponseId}",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            ResponseDate = "2024-02-08 06:34:49.4500000",
        });

        dynamic marketQuoteResponseRecord = CreateRow(tableName: "PS.MarketQuoteResponse", values: new
        {
            MarketResponseId = marketResponseRecord.MarketResponseId,
            SignedLineRate = 2.000000,
            PremiumCurrencyId = 2,
            Premium = 100.0000,
        });

        dynamic results = ExecuteStoredProcedureWithResultRow(storedProcedureName: "rpt.Load_rpt_MarketInteractionResponse");
        Assert.Equal(expected: 1, actual: results.InsertedCount);
        Assert.Equal(expected: 0, actual: results.UpdatedCount);
        Assert.Equal(expected: 0, actual: results.DeletedCount);
        Assert.Equal(expected: 0, actual: results.RejectedCount);

        //This should create one row
        CheckSprocExecutionLog(sprocName: "rpt.Load_rpt_MarketInteractionResponse", insertedCount: 1);

        dynamic row = GetResultRow(tableName: "rpt.MarketInteractionResponse");
        Assert.NotNull(row);
        Assert.Equal(expected: (decimal)((marketQuoteResponseRecord.SignedLineRate / 100) * marketQuoteResponseRecord.Premium), actual: row.SignedPremium);
        Assert.Equal(expected: (decimal)(((marketQuoteResponseRecord.SignedLineRate / 100) * marketQuoteResponseRecord.Premium) * exchangeRateRecord.ExchangeRate), actual: row.SignedPremium_USD);
    }

    [Fact]
    public void LoadMarketInteractionExpiringSignedPremiumUSDTest()
    {
        var submissionContainerId = 20;
        var submissionContainerMarketId = 10;
        var marketResponseId = 40;
        var PlacementId = 123456;
        var CarrierId = 1;

        // Currency
        dynamic currencyRecordEur = CreateRow(tableName: "Reference.Currency", values: new
        {
            CurrencyAlphaCode = "EUR",
            CurrencyId = 2,
            CurrencyName = "Euro"
        });

        // Currency
        dynamic currencyRecord2 = CreateRow(tableName: "Reference.Currency", values: new
        {
            CurrencyAlphaCode = "USD",
            CurrencyId = 100249,
            CurrencyName = "Dollars"
        });

        // Exchange Rate
        dynamic exchangeRateRecord = CreateRow(tableName: "Reference.ExchangeRate", values: new
        {
            ExchangeRateId = 10,
            FromCurrencyId = currencyRecordEur.CurrencyId,
            ToCurrencyId = currencyRecord2.CurrencyId,
            EffectiveDate = "2024-02-04",
            ExchangeRateTypeId = (int)ExchangeRateType.AverageRate,
            IsDeleted = false,
            ExchangeRate = 1.5
        });

        dynamic negotiationRecord = CreateRow(tableName: "PS.Negotiation", values: new
        {
            NegotiationKey = $"SUBC|{submissionContainerId}",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            PlacementId = PlacementId,
            SubmissionName = "Submission"
        });

        dynamic negotiationMarketRecord = CreateRow(tableName: "PS.NegotiationMarket", values: new
        {
            NegotiationId = negotiationRecord.NegotiationId,
            NegotiationMarketKey = $"SUBCONMKT|{submissionContainerMarketId}",
            CarrierId = CarrierId,
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
        });

        dynamic riskProfileRecord = CreateRow(tableName: "PS.RiskProfile", values: new
        {
            LineOfBusiness = "LOB",
            IsDeleted = 0
        });

        dynamic layerTypeRecord = CreateRow(tableName: "ref.LayerType", values: new
        {
            LayerType = "Primary"
        });

        dynamic marketResponseRecord = CreateRow(tableName: "PS.MarketResponse", values: new
        {
            NegotiationMarketId = negotiationMarketRecord.NegotiationMarketId,
            MarketResponseKey = $"MKTRES|{marketResponseId}",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            ResponseDate = "2024-02-08 06:34:49.4500000",
            LayerTypeId = layerTypeRecord.LayerTypeId
        });

        dynamic marketResponseBasisRecord = CreateRow(tableName: "PS.MarketResponseBasis", values: new
        {
            MarketResponseBasisKey = $"MKTRES|{marketResponseId}",
            RiskProfileId = riskProfileRecord.RiskProfileId,
            MarketResponseId = marketResponseRecord.MarketResponseId,
            IsDeleted = 0
        });

        dynamic marketQuoteResponseRecord = CreateRow(tableName: "PS.MarketQuoteResponse", values: new
        {
            MarketResponseId = marketResponseRecord.MarketResponseId,
            SignedLineRate = 2.000000,
            PremiumCurrencyId = 2,
            Premium = 100.0000
        });

        //Expiring signed line data
        dynamic expNegotiationTypeRecord = CreateRow(tableName: "ref.NegotiationType", values: new
        {
            NegotiationType = "Expiring"
        });

        dynamic expNegotiationRecord = CreateRow(tableName: "PS.Negotiation", values: new
        {
            NegotiationKey = $"EXPRESP|{submissionContainerId}",
            NegotiationTypeKey = expNegotiationTypeRecord.NegotiationTypeId,
            PlacementId = PlacementId,
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            SubmissionName = "Submission"
        });

        dynamic expNegotiationMarketRecord = CreateRow(tableName: "PS.NegotiationMarket", values: new
        {
            NegotiationId = expNegotiationRecord.NegotiationId,
            NegotiationMarketKey = $"EXPRESPMKT|{submissionContainerMarketId}",
            CarrierId = CarrierId,
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform
        });

        dynamic expMarketResponseRecord = CreateRow(tableName: "PS.MarketResponse", values: new
        {
            NegotiationMarketId = expNegotiationMarketRecord.NegotiationMarketId,
            MarketResponseKey = $"EXPRESP|{marketResponseId}",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            ResponseDate = "2023-02-08 06:34:49.4500000",
            LayerTypeId = layerTypeRecord.LayerTypeId
        });

        dynamic expMarketResponseBasisRecord = CreateRow(tableName: "PS.MarketResponseBasis", values: new
        {
            MarketResponseBasisKey = $"EXPRESP|{marketResponseId}",
            RiskProfileId = riskProfileRecord.RiskProfileId,
            MarketResponseId = expMarketResponseRecord.MarketResponseId,
            IsDeleted = 0
        });

        dynamic expMarketQuoteResponseRecord = CreateRow(tableName: "PS.MarketQuoteResponse", values: new
        {
            MarketResponseId = expMarketResponseRecord.MarketResponseId,
            SourceMarketQuoteResponseKey = "MQR|1",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            SignedLineRate = 2.000000,
            PremiumCurrencyId = 2,
            Premium = 100.0000,
            PremiumRate = 2.000000,
            IsDeleted = 0,
            IsOverride = 0
        });

        dynamic results = ExecuteStoredProcedureWithResultRow(storedProcedureName: "rpt.Load_rpt_MarketInteractionResponse");
        Assert.Equal(expected: 1, actual: results.InsertedCount);
        Assert.Equal(expected: 0, actual: results.UpdatedCount);
        Assert.Equal(expected: 0, actual: results.DeletedCount);
        Assert.Equal(expected: 0, actual: results.RejectedCount);

        //This should create one row
        CheckSprocExecutionLog(sprocName: "rpt.Load_rpt_MarketInteractionResponse", insertedCount: 1);

        dynamic row = GetResultRow(tableName: "rpt.MarketInteractionResponse");
        Assert.NotNull(row);
        Assert.Equal(expected: (decimal)((marketQuoteResponseRecord.SignedLineRate / 100) * marketQuoteResponseRecord.Premium), actual: row.SignedPremium);
        Assert.Equal(expected: (decimal)(((marketQuoteResponseRecord.SignedLineRate / 100) * marketQuoteResponseRecord.Premium) * exchangeRateRecord.ExchangeRate), actual: row.SignedPremium_USD);
        Assert.Equal(expected: (decimal)expMarketQuoteResponseRecord.SignedLineRate, actual: row.ExpiringSignedLineRate);
        Assert.Equal(expected: (decimal)((expMarketQuoteResponseRecord.SignedLineRate / 100) * expMarketQuoteResponseRecord.Premium), actual: row.ExpiringSignedPremium);
        Assert.Equal(expected: (decimal)(((expMarketQuoteResponseRecord.SignedLineRate / 100) * expMarketQuoteResponseRecord.Premium) * exchangeRateRecord.ExchangeRate), actual: row.ExpiringSignedPremium_USD);
    }

    [Fact]
    public void Load_rpt_MarketInteractionResponseMarketSecurityCarrierTest()
    {
        var submissionContainerId = 20;
        var submissionContainerMarketId = 10;
        var marketResponseId = 40;
        var carrierId = 23;

        dynamic responseTypeRecord,
            placementSystemTableRecordCR,
            placementSystemTableRecordSCM,
            placementSystemTableRecordPC,
            placementSystemTableRecordMR,
            outcomeStatusRecordAcc,
            outcomeStatusRecordNotAcc,
            outcomeReasonRecord,
            layerTypeRecord,
            currencyRecordEur,
            facilityRecord,
            facilitySectionRecord,
            facilitySectionCarrierRecord,
            productHierarchyRecord,
            productHierarchyRecord2,
            placementStatusRecord;
        CreateReferenceRecords(carrierId, out responseTypeRecord, out placementSystemTableRecordCR, out placementSystemTableRecordSCM, out placementSystemTableRecordPC, out placementSystemTableRecordMR, out outcomeStatusRecordAcc, out outcomeStatusRecordNotAcc, out outcomeReasonRecord, out layerTypeRecord, out currencyRecordEur, out facilityRecord, out facilitySectionRecord, out facilitySectionCarrierRecord, out productHierarchyRecord, out productHierarchyRecord2, out placementStatusRecord);

        dynamic placementRecord = CreateRow(tableName: "dbo.Placement", values: new
        {
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            PlacementName = "Test Placement",
            PlacementStatusId = placementStatusRecord.PlacementStatusId,
            PlacementSystemId = 1022232,
            IsDeleted = false
        });

        //RiskProfile for the specification
        dynamic riskProfileRecord = CreateRow(tableName: "PS.RiskProfile", values: new
        {
            PlacementId = placementRecord.PlacementId,
            ProductId = productHierarchyRecord2.ProductId,
            ClassId = 812,
            RiskProfileKey = "ELEM|12345",
            LineOfBusiness = "RiskProfileLOB",
            ClassOfBusiness = "RiskProfileCOB",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform
        });

        //RiskProfile for the specification
        dynamic riskProfileRecord2 = CreateRow(tableName: "PS.RiskProfile", values: new
        {
            PlacementId = placementRecord.PlacementId,
            ProductId = productHierarchyRecord.ProductId,
            ODSProductId = 24074,
            ClassId = riskProfileRecord.ClassId,
            RiskProfileKey = "SPEC|1",
            LineOfBusiness = "RiskProfileLOB",
            ClassOfBusiness = "RiskProfileCOB",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform
        });

        dynamic productRecord = CreateRow(tableName: "dbo.Product", values: new
        {
            ProductId = riskProfileRecord2.ODSProductId,
            ProductKey = productHierarchyRecord.ProductKey,
            DataSourceInstanceId = productHierarchyRecord.DataSourceInstanceId,
            IsDeleted = false
        });

        dynamic negotiationRecord = CreateRow(tableName: "PS.Negotiation", values: new
        {
            NegotiationKey = $"SUBC|{submissionContainerId}",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            SubmissionName = "Submission"
        });

        dynamic negotiationMarketRecord = CreateRow(tableName: "PS.NegotiationMarket", values: new
        {
            NegotiationId = negotiationRecord.NegotiationId,
            NegotiationMarketKey = $"SUBCONMKT|{submissionContainerMarketId}",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
        });

        dynamic SubmissionRecord = CreateRow(tableName: "BP.Submission", values: new
        {
            SubmissionContainerId = submissionContainerId,
            Sent = "2023-11-11"
        });

        dynamic contractRiskRecord = CreateRow(tableName: "PS.ContractRiskProfile", values: new
        {
            ContractRiskId = 1,
            ContractId = 2,
            RiskProfileId = riskProfileRecord.RiskProfileId,
            IsDeleted = false
        });

        dynamic contractRiskRecord2 = CreateRow(tableName: "PS.ContractRiskProfile", values: new
        {
            ContractRiskId = 2,
            ContractId = 3,
            RiskProfileId = riskProfileRecord.RiskProfileId,
            IsDeleted = false
        });

        dynamic specificationRecord2 = CreateRow(tableName: "dbo.Specification", values: new
        {
            SpecificationId = 1,
            PlacementId = placementRecord.PlacementId,
            Label = "Old Specification",
            IsDeleted = false
        });

        dynamic marketResponseRecord = CreateRow(tableName: "PS.MarketResponse", values: new
        {
            NegotiationMarketId = negotiationMarketRecord.NegotiationMarketId,
            MarketResponseKey = $"MKTRES|{marketResponseId}",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            ResponseTypeId = responseTypeRecord.ResponseTypeId,
            LayerTypeId = layerTypeRecord.LayerTypeId,
            LimitCurrencyId = currencyRecordEur.CurrencyId,
            AttachmentPointCurrencyId = currencyRecordEur.CurrencyId,
            ResponseDate = "2023-11-21",
            Limit = 1000,
            Comments = "Comments",
            AttachmentPoint = 100000,
            UnderwriterName = "Joe Bloggs",
            FacilitySectionId = facilitySectionRecord.FacilitySectionId
        });

        dynamic marketQuoteResponseRecord = CreateRow(tableName: "PS.MarketQuoteResponse", values: new
        {
            MarketResponseId = marketResponseRecord.MarketResponseId,
            OutcomeReasonId = outcomeReasonRecord.OutcomeReasonId,
            OutcomeStatusId = 1,
            PremiumCurrencyId = currencyRecordEur.CurrencyId,
            IsDeleted = false,
            QuotedToLead = 1,
            Premium = 10,
            PremiumRate = 10000,
            OfferedLine = 1000,
            OfferedLineRate = 100,
            CommissionRate = 15,
            Subjectivity = "Subjectivity",
        });

        dynamic marketResponseBasisRecord = CreateRow(tableName: "PS.MarketResponseBasis", values: new
        {
            MarketResponseId = marketResponseRecord.MarketResponseId,
            MarketResponseBasisKey = "MRB|2",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            RiskProfileId = riskProfileRecord.RiskProfileId,
            IsDeleted = false
        });

        dynamic marketResponseBasisRecord2 = CreateRow(tableName: "PS.MarketResponseBasis", values: new
        {
            MarketResponseId = marketResponseRecord.MarketResponseId,
            MarketResponseBasisKey = "MRB|1",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            RiskProfileId = riskProfileRecord2.RiskProfileId,
            IsDeleted = false
        });

        dynamic marketSecurityRecord = CreateRow(tableName: "dbo.MarketResponseSecurity", values: new
        {
            MarketResponseId = marketResponseId,
            CarrierId = carrierId,
            Split = 10,
            IsFacilityLead = true
        });

        dynamic results = ExecuteStoredProcedureWithResultRow(storedProcedureName: "rpt.Load_rpt_MarketInteractionResponse");
        Assert.Equal(expected: 2, actual: results.InsertedCount);
        Assert.Equal(expected: 0, actual: results.UpdatedCount);
        Assert.Equal(expected: 0, actual: results.DeletedCount);
        Assert.Equal(expected: 0, actual: results.RejectedCount);

        //This should create two rows, one for SubmissionMarket and one for MarketSecurity
        CheckSprocExecutionLog(sprocName: "rpt.Load_rpt_MarketInteractionResponse", insertedCount: 2);

        dynamic row = GetResultRow(tableName: "rpt.MarketInteractionResponse", whereClause: $"MarketSecurityCarrierId = {carrierId}");
        //Get the actual record generated for ref.Facility to get the calculated PSFacilityPolicyId
        dynamic refFacility = GetResultRow(tableName: "ref.Facility");
        Assert.Equal(expected: "MarketSecurity", actual: row.MarketCategory);
        Assert.Equal(expected: marketResponseId, actual: row.CarrierResponseId);
        Assert.Equal(expected: $"MKTRES|{marketResponseId}", actual: row.MarketResponseKey);
        Assert.Equal(expected: submissionContainerMarketId, actual: row.MarketSelectionId);
        Assert.Equal(expected: $"MKTSEC|{submissionContainerMarketId}", actual: row.NegotiationMarketKey);
        Assert.Equal(expected: $"{placementSystemTableRecordSCM.TableId}|{submissionContainerMarketId}", actual: row.MarketSelectionKey);
        Assert.Equal(expected: negotiationRecord.SubmissionName, actual: row.InteractionName);
        Assert.Equal(expected: DateTime.Parse(SubmissionRecord.Sent), actual: row.SubmittedDate);
        Assert.Equal(expected: DateTime.Parse(marketResponseRecord.ResponseDate), actual: row.ResponseDate);
        Assert.Equal(expected: 10, actual: row.ResponseTimeDays);
        Assert.Equal(expected: outcomeReasonRecord.OutcomeReason, actual: row.OutcomeReason);
        Assert.Equal(expected: "Suitability of insurer", actual: row.OutcomeReasonGroupName);
        Assert.Equal(expected: outcomeStatusRecordAcc.OutcomeStatus, actual: row.OutcomeStatus);
        Assert.Equal(expected: productHierarchyRecord.ProductKey, actual: row.ProductKey);
        Assert.Equal(expected: riskProfileRecord2.ProductId, actual: row.ProductId);
        Assert.Equal(expected: riskProfileRecord2.ClassId, actual: row.ClassId);
        Assert.Equal(expected: layerTypeRecord.LayerType, actual: row.LayerType);
        Assert.Equal(expected: true, actual: row.QuotedToLead);
        Assert.Equal(expected: currencyRecordEur.CurrencyAlphaCode, actual: row.Premium_Currency);
        Assert.Equal(expected: marketQuoteResponseRecord.Premium, actual: row.Premium);
        Assert.Equal(expected: 10, actual: row.Premium_USD);
        Assert.Equal(expected: 100, actual: row.OfferedLine); //Sec.Split / 100 * ms.OfferedLine
        Assert.Equal(expected: 100, actual: row.OfferedLine_USD);
        Assert.Equal(expected: 10, actual: row.OfferedLineRate); //Sec.Split / 100 * ms.OfferedLineRate
        Assert.Equal(expected: marketQuoteResponseRecord.PremiumRate, actual: row.PremiumRate);
        Assert.Equal(expected: marketQuoteResponseRecord.CommissionRate, actual: row.CommissionRate);
        Assert.Equal(expected: currencyRecordEur.CurrencyAlphaCode, actual: row.Limit_Currency);
        Assert.Equal(expected: marketResponseRecord.Limit, actual: row.Limit);
        Assert.Equal(expected: 1000, actual: row.Limit_USD);
        Assert.Equal(expected: currencyRecordEur.CurrencyAlphaCode, actual: row.AttachmentPoint_Currency);
        Assert.Equal(expected: marketResponseRecord.Comments, actual: row.Comments);
        Assert.Equal(expected: marketQuoteResponseRecord.Subjectivity, actual: row.Subjectivity);
        Assert.Equal(expected: 20231121, actual: row.ResponseDateKey);
        Assert.Equal(expected: $"{placementSystemTableRecordMR.TableId}|{marketResponseId}", actual: row.ResponseKey);
        Assert.Equal(expected: 1, actual: row.WrittenPremium);
        Assert.Equal(expected: 1, actual: row.WrittenPremium_USD);
        Assert.Equal(expected: true, actual: row.MultiRisk);
        Assert.Equal(expected: marketSecurityRecord.Split, actual: row.Split);
        Assert.Equal(expected: $"FacilityMember-{refFacility.PSFacilityPolicyId}-{facilitySectionCarrierRecord.FacilitySectionId}-{facilitySectionCarrierRecord.CarrierId}", actual: row.MarketSecurityCarrierKey);
        Assert.Equal(expected: marketSecurityRecord.CarrierId, actual: row.MarketSecurityCarrierId);

        dynamic row2 = GetResultRow(tableName: "rpt.MarketInteractionResponse", whereClause: "MarketSecurityCarrierId IS NULL");
        Assert.Equal(expected: "SubmissionMarket", actual: row2.MarketCategory);
        Assert.Equal(expected: marketResponseId, actual: row2.CarrierResponseId);
        Assert.Equal(expected: $"MKTRES|{marketResponseId}", actual: row2.MarketResponseKey);
        Assert.Equal(expected: submissionContainerMarketId, actual: row2.MarketSelectionId);
        Assert.Equal(expected: $"SUBCONMKT|{submissionContainerMarketId}", actual: row2.NegotiationMarketKey);
        Assert.Equal(expected: $"{placementSystemTableRecordSCM.TableId}|{submissionContainerMarketId}", actual: row2.MarketSelectionKey);
        Assert.Equal(expected: negotiationRecord.SubmissionName, actual: row2.InteractionName);
        Assert.Equal(expected: DateTime.Parse(SubmissionRecord.Sent), actual: row2.SubmittedDate);
        Assert.Equal(expected: DateTime.Parse(marketResponseRecord.ResponseDate), actual: row2.ResponseDate);
        Assert.Equal(expected: 10, actual: row2.ResponseTimeDays);
        Assert.Equal(expected: outcomeReasonRecord.OutcomeReason, actual: row2.OutcomeReason);
        Assert.Equal(expected: "Suitability of insurer", actual: row2.OutcomeReasonGroupName);
        Assert.Equal(expected: outcomeStatusRecordAcc.OutcomeStatus, actual: row2.OutcomeStatus);
        Assert.Equal(expected: productHierarchyRecord.ProductKey, actual: row2.ProductKey);
        Assert.Equal(expected: riskProfileRecord2.ProductId, actual: row2.ProductId);
        Assert.Equal(expected: riskProfileRecord2.ClassId, actual: row2.ClassId);
        Assert.Equal(expected: layerTypeRecord.LayerType, actual: row2.LayerType);
        Assert.Equal(expected: true, actual: row2.QuotedToLead);
        Assert.Equal(expected: currencyRecordEur.CurrencyAlphaCode, actual: row2.Premium_Currency);
        Assert.Equal(expected: marketQuoteResponseRecord.Premium, actual: row2.Premium);
        Assert.Equal(expected: 10, actual: row2.Premium_USD);
        Assert.Equal(expected: marketQuoteResponseRecord.OfferedLine, actual: row2.OfferedLine);
        Assert.Equal(expected: 1000, actual: row2.OfferedLine_USD);
        Assert.Equal(expected: marketQuoteResponseRecord.OfferedLineRate, actual: row2.OfferedLineRate);
        Assert.Equal(expected: marketQuoteResponseRecord.PremiumRate, actual: row2.PremiumRate);
        Assert.Equal(expected: marketQuoteResponseRecord.CommissionRate, actual: row2.CommissionRate);
        Assert.Equal(expected: currencyRecordEur.CurrencyAlphaCode, actual: row2.Limit_Currency);
        Assert.Equal(expected: marketResponseRecord.Limit, actual: row2.Limit);
        Assert.Equal(expected: 1000, actual: row2.Limit_USD);
        Assert.Equal(expected: currencyRecordEur.CurrencyAlphaCode, actual: row2.AttachmentPoint_Currency);
        Assert.Equal(expected: marketResponseRecord.Comments, actual: row2.Comments);
        Assert.Equal(expected: marketQuoteResponseRecord.Subjectivity, actual: row2.Subjectivity);
        Assert.Equal(expected: 20231121, actual: row2.ResponseDateKey);
        Assert.Equal(expected: $"{placementSystemTableRecordMR.TableId}|{marketResponseId}", actual: row2.ResponseKey);
        Assert.Equal(expected: 10, actual: row2.WrittenPremium);
        Assert.Equal(expected: 10, actual: row2.WrittenPremium_USD);
        Assert.Equal(expected: true, actual: row2.MultiRisk);
        Assert.Equal(expected: marketQuoteResponseRecord.OfferedLineRate, actual: row2.Split);
    }


    [Fact]
    public void Load_rpt_MarketInteractionResponseMarketSecurityPanelMemberTest()
    {
        var submissionContainerId = 20;
        var submissionContainerMarketId = 10;
        var marketResponseId = 40;
        var carrierId = 23;

        dynamic responseTypeRecord,
            placementSystemTableRecordCR,
            placementSystemTableRecordSCM,
            placementSystemTableRecordPC,
            placementSystemTableRecordMR,
            outcomeStatusRecordAcc,
            outcomeStatusRecordNotAcc,
            outcomeReasonRecord,
            layerTypeRecord,
            currencyRecordEur,
            facilityRecord,
            facilitySectionRecord,
            facilitySectionCarrierRecord,
            productHierarchyRecord,
            productHierarchyRecord2,
            placementStatusRecord;
        CreateReferenceRecords(carrierId, out responseTypeRecord, out placementSystemTableRecordCR, out placementSystemTableRecordSCM, out placementSystemTableRecordPC, out placementSystemTableRecordMR, out outcomeStatusRecordAcc, out outcomeStatusRecordNotAcc, out outcomeReasonRecord, out layerTypeRecord, out currencyRecordEur, out facilityRecord, out facilitySectionRecord, out facilitySectionCarrierRecord, out productHierarchyRecord, out productHierarchyRecord2, out placementStatusRecord);

        dynamic placementRecord = CreateRow(tableName: "dbo.Placement", values: new
        {
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            PlacementName = "Test Placement",
            PlacementStatusId = placementStatusRecord.PlacementStatusId,
            PlacementSystemId = 1022232,
            IsDeleted = false
        });

        //RiskProfile for the specification
        dynamic riskProfileRecord = CreateRow(tableName: "PS.RiskProfile", values: new
        {
            PlacementId = placementRecord.PlacementId,
            ProductId = productHierarchyRecord2.ProductId,
            ClassId = 812,
            RiskProfileKey = "ELEM|12345",
            LineOfBusiness = "RiskProfileLOB",
            ClassOfBusiness = "RiskProfileCOB",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform
        });

        //RiskProfile for the specification
        dynamic riskProfileRecord2 = CreateRow(tableName: "PS.RiskProfile", values: new
        {
            PlacementId = placementRecord.PlacementId,
            ProductId = productHierarchyRecord.ProductId,
            ODSProductId = 24074,
            ClassId = riskProfileRecord.ClassId,
            RiskProfileKey = "SPEC|1",
            LineOfBusiness = "RiskProfileLOB",
            ClassOfBusiness = "RiskProfileCOB",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform
        });

        dynamic productRecord = CreateRow(tableName: "dbo.Product", values: new
        {
            ProductId = riskProfileRecord2.ODSProductId,
            ProductKey = productHierarchyRecord.ProductKey,
            DataSourceInstanceId = productHierarchyRecord.DataSourceInstanceId,
            IsDeleted = false
        });

        dynamic negotiationRecord = CreateRow(tableName: "PS.Negotiation", values: new
        {
            NegotiationKey = $"SUBC|{submissionContainerId}",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            SubmissionName = "Submission"
        });

        dynamic panelRecord = CreateRow(tableName: "ref.Panel", values: new {
            PanelSK = 1,
            PanelId = 1,
            PanelKey = 1,
            PanelName = "A",
            IsDeprecated = false
        });

        dynamic panelMemberRecord = CreateRow(tableName: "ref.PanelMember", values: new { 
            PanelMemberSK = 1,
            PanelMemberId = 1,
            PanelMemberKey = 1,
            PanelMemberName = "Panel Member 1",
            PanelSK = panelRecord.PanelSK,
            IsDeprecated = false
        });

        dynamic negotiationMarketRecord = CreateRow(tableName: "PS.NegotiationMarket", values: new
        {
            NegotiationId = negotiationRecord.NegotiationId,
            NegotiationMarketKey = $"SUBCONMKT|{submissionContainerMarketId}",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            PanelMemberId = panelMemberRecord.PanelMemberId,
            PanelMemberName = panelMemberRecord.PanelMemberName,
        });

        dynamic SubmissionRecord = CreateRow(tableName: "BP.Submission", values: new
        {
            SubmissionContainerId = submissionContainerId,
            Sent = "2023-11-11"
        });

        dynamic contractRiskRecord = CreateRow(tableName: "PS.ContractRiskProfile", values: new
        {
            ContractRiskId = 1,
            ContractId = 2,
            RiskProfileId = riskProfileRecord.RiskProfileId,
            IsDeleted = false
        });

        dynamic contractRiskRecord2 = CreateRow(tableName: "PS.ContractRiskProfile", values: new
        {
            ContractRiskId = 2,
            ContractId = 3,
            RiskProfileId = riskProfileRecord.RiskProfileId,
            IsDeleted = false
        });

        dynamic specificationRecord2 = CreateRow(tableName: "dbo.Specification", values: new
        {
            SpecificationId = 1,
            PlacementId = placementRecord.PlacementId,
            Label = "Old Specification",
            IsDeleted = false
        });

        dynamic marketResponseRecord = CreateRow(tableName: "PS.MarketResponse", values: new
        {
            NegotiationMarketId = negotiationMarketRecord.NegotiationMarketId,
            MarketResponseKey = $"MKTRES|{marketResponseId}",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            ResponseTypeId = responseTypeRecord.ResponseTypeId,
            LayerTypeId = layerTypeRecord.LayerTypeId,
            LimitCurrencyId = currencyRecordEur.CurrencyId,
            AttachmentPointCurrencyId = currencyRecordEur.CurrencyId,
            ResponseDate = "2023-11-21",
            Limit = 1000,
            Comments = "Comments",
            AttachmentPoint = 100000,
            UnderwriterName = "Joe Bloggs"
        });

        dynamic marketQuoteResponseRecord = CreateRow(tableName: "PS.MarketQuoteResponse", values: new
        {
            MarketResponseId = marketResponseRecord.MarketResponseId,
            OutcomeReasonId = outcomeReasonRecord.OutcomeReasonId,
            OutcomeStatusId = 1,
            PremiumCurrencyId = currencyRecordEur.CurrencyId,
            IsDeleted = false,
            QuotedToLead = 1,
            Premium = 10,
            PremiumRate = 10000,
            OfferedLine = 1000,
            OfferedLineRate = 100,
            CommissionRate = 15,
            Subjectivity = "Subjectivity",
        });

        dynamic marketResponseBasisRecord = CreateRow(tableName: "PS.MarketResponseBasis", values: new
        {
            MarketResponseId = marketResponseRecord.MarketResponseId,
            MarketResponseBasisKey = "MRB|2",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            RiskProfileId = riskProfileRecord.RiskProfileId,
            IsDeleted = false
        });

        dynamic marketResponseBasisRecord2 = CreateRow(tableName: "PS.MarketResponseBasis", values: new
        {
            MarketResponseId = marketResponseRecord.MarketResponseId,
            MarketResponseBasisKey = "MRB|1",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            RiskProfileId = riskProfileRecord2.RiskProfileId,
            IsDeleted = false
        });

        dynamic marketSecurityRecord = CreateRow(tableName: "dbo.MarketResponseSecurity", values: new
        {
            MarketResponseId = marketResponseId,
            CarrierId = carrierId,
            Split = 10,
            IsFacilityLead = true
        });

        dynamic results = ExecuteStoredProcedureWithResultRow(storedProcedureName: "rpt.Load_rpt_MarketInteractionResponse");
        Assert.Equal(expected: 2, actual: results.InsertedCount);
        Assert.Equal(expected: 0, actual: results.UpdatedCount);
        Assert.Equal(expected: 0, actual: results.DeletedCount);
        Assert.Equal(expected: 0, actual: results.RejectedCount);

        //This should create two rows, one for SubmissionMarket and one for MarketSecurity
        CheckSprocExecutionLog(sprocName: "rpt.Load_rpt_MarketInteractionResponse", insertedCount: 2);

        dynamic row = GetResultRow(tableName: "rpt.MarketInteractionResponse", whereClause: $"MarketSecurityCarrierId = {carrierId}");
        Assert.Equal(expected: "MarketSecurity", actual: row.MarketCategory);
        Assert.Equal(expected: marketResponseId, actual: row.CarrierResponseId);
        Assert.Equal(expected: $"MKTRES|{marketResponseId}", actual: row.MarketResponseKey);
        Assert.Equal(expected: submissionContainerMarketId, actual: row.MarketSelectionId);
        Assert.Equal(expected: $"MKTSEC|{submissionContainerMarketId}", actual: row.NegotiationMarketKey);
        Assert.Equal(expected: $"{placementSystemTableRecordSCM.TableId}|{submissionContainerMarketId}", actual: row.MarketSelectionKey);
        Assert.Equal(expected: negotiationRecord.SubmissionName, actual: row.InteractionName);
        Assert.Equal(expected: DateTime.Parse(SubmissionRecord.Sent), actual: row.SubmittedDate);
        Assert.Equal(expected: DateTime.Parse(marketResponseRecord.ResponseDate), actual: row.ResponseDate);
        Assert.Equal(expected: 10, actual: row.ResponseTimeDays);
        Assert.Equal(expected: outcomeReasonRecord.OutcomeReason, actual: row.OutcomeReason);
        Assert.Equal(expected: "Suitability of insurer", actual: row.OutcomeReasonGroupName);
        Assert.Equal(expected: outcomeStatusRecordAcc.OutcomeStatus, actual: row.OutcomeStatus);
        Assert.Equal(expected: productHierarchyRecord.ProductKey, actual: row.ProductKey);
        Assert.Equal(expected: riskProfileRecord2.ProductId, actual: row.ProductId);
        Assert.Equal(expected: riskProfileRecord2.ClassId, actual: row.ClassId);
        Assert.Equal(expected: layerTypeRecord.LayerType, actual: row.LayerType);
        Assert.Equal(expected: true, actual: row.QuotedToLead);
        Assert.Equal(expected: currencyRecordEur.CurrencyAlphaCode, actual: row.Premium_Currency);
        Assert.Equal(expected: marketQuoteResponseRecord.Premium, actual: row.Premium);
        Assert.Equal(expected: 10, actual: row.Premium_USD);
        Assert.Equal(expected: 100, actual: row.OfferedLine); //Sec.Split / 100 * ms.OfferedLine
        Assert.Equal(expected: 100, actual: row.OfferedLine_USD);
        Assert.Equal(expected: 10, actual: row.OfferedLineRate); //Sec.Split / 100 * ms.OfferedLineRate
        Assert.Equal(expected: marketQuoteResponseRecord.PremiumRate, actual: row.PremiumRate);
        Assert.Equal(expected: marketQuoteResponseRecord.CommissionRate, actual: row.CommissionRate);
        Assert.Equal(expected: currencyRecordEur.CurrencyAlphaCode, actual: row.Limit_Currency);
        Assert.Equal(expected: marketResponseRecord.Limit, actual: row.Limit);
        Assert.Equal(expected: 1000, actual: row.Limit_USD);
        Assert.Equal(expected: currencyRecordEur.CurrencyAlphaCode, actual: row.AttachmentPoint_Currency);
        Assert.Equal(expected: marketResponseRecord.Comments, actual: row.Comments);
        Assert.Equal(expected: marketQuoteResponseRecord.Subjectivity, actual: row.Subjectivity);
        Assert.Equal(expected: 20231121, actual: row.ResponseDateKey);
        Assert.Equal(expected: $"{placementSystemTableRecordMR.TableId}|{marketResponseId}", actual: row.ResponseKey);
        Assert.Equal(expected: 1, actual: row.WrittenPremium);
        Assert.Equal(expected: 1, actual: row.WrittenPremium_USD);
        Assert.Equal(expected: true, actual: row.MultiRisk);
        Assert.Equal(expected: marketSecurityRecord.Split, actual: row.Split);
        Assert.Equal(expected: $"Carrier-{marketSecurityRecord.CarrierId}", actual: row.MarketSecurityCarrierKey);
        Assert.Equal(expected: marketSecurityRecord.CarrierId, actual: row.MarketSecurityCarrierId);

        dynamic row2 = GetResultRow(tableName: "rpt.MarketInteractionResponse", whereClause: "MarketSecurityCarrierId IS NULL");
        Assert.Equal(expected: "SubmissionMarket", actual: row2.MarketCategory);
        Assert.Equal(expected: marketResponseId, actual: row2.CarrierResponseId);
        Assert.Equal(expected: $"MKTRES|{marketResponseId}", actual: row2.MarketResponseKey);
        Assert.Equal(expected: submissionContainerMarketId, actual: row2.MarketSelectionId);
        Assert.Equal(expected: $"SUBCONMKT|{submissionContainerMarketId}", actual: row2.NegotiationMarketKey);
        Assert.Equal(expected: $"{placementSystemTableRecordSCM.TableId}|{submissionContainerMarketId}", actual: row2.MarketSelectionKey);
        Assert.Equal(expected: negotiationRecord.SubmissionName, actual: row2.InteractionName);
        Assert.Equal(expected: DateTime.Parse(SubmissionRecord.Sent), actual: row2.SubmittedDate);
        Assert.Equal(expected: DateTime.Parse(marketResponseRecord.ResponseDate), actual: row2.ResponseDate);
        Assert.Equal(expected: 10, actual: row2.ResponseTimeDays);
        Assert.Equal(expected: outcomeReasonRecord.OutcomeReason, actual: row2.OutcomeReason);
        Assert.Equal(expected: "Suitability of insurer", actual: row2.OutcomeReasonGroupName);
        Assert.Equal(expected: outcomeStatusRecordAcc.OutcomeStatus, actual: row2.OutcomeStatus);
        Assert.Equal(expected: productHierarchyRecord.ProductKey, actual: row2.ProductKey);
        Assert.Equal(expected: riskProfileRecord2.ProductId, actual: row2.ProductId);
        Assert.Equal(expected: riskProfileRecord2.ClassId, actual: row2.ClassId);
        Assert.Equal(expected: layerTypeRecord.LayerType, actual: row2.LayerType);
        Assert.Equal(expected: true, actual: row2.QuotedToLead);
        Assert.Equal(expected: currencyRecordEur.CurrencyAlphaCode, actual: row2.Premium_Currency);
        Assert.Equal(expected: marketQuoteResponseRecord.Premium, actual: row2.Premium);
        Assert.Equal(expected: 10, actual: row2.Premium_USD);
        Assert.Equal(expected: marketQuoteResponseRecord.OfferedLine, actual: row2.OfferedLine);
        Assert.Equal(expected: 1000, actual: row2.OfferedLine_USD);
        Assert.Equal(expected: marketQuoteResponseRecord.OfferedLineRate, actual: row2.OfferedLineRate);
        Assert.Equal(expected: marketQuoteResponseRecord.PremiumRate, actual: row2.PremiumRate);
        Assert.Equal(expected: marketQuoteResponseRecord.CommissionRate, actual: row2.CommissionRate);
        Assert.Equal(expected: currencyRecordEur.CurrencyAlphaCode, actual: row2.Limit_Currency);
        Assert.Equal(expected: marketResponseRecord.Limit, actual: row2.Limit);
        Assert.Equal(expected: 1000, actual: row2.Limit_USD);
        Assert.Equal(expected: currencyRecordEur.CurrencyAlphaCode, actual: row2.AttachmentPoint_Currency);
        Assert.Equal(expected: marketResponseRecord.Comments, actual: row2.Comments);
        Assert.Equal(expected: marketQuoteResponseRecord.Subjectivity, actual: row2.Subjectivity);
        Assert.Equal(expected: 20231121, actual: row2.ResponseDateKey);
        Assert.Equal(expected: $"{placementSystemTableRecordMR.TableId}|{marketResponseId}", actual: row2.ResponseKey);
        Assert.Equal(expected: 10, actual: row2.WrittenPremium);
        Assert.Equal(expected: 10, actual: row2.WrittenPremium_USD);
        Assert.Equal(expected: true, actual: row2.MultiRisk);
        Assert.Equal(expected: marketQuoteResponseRecord.OfferedLineRate, actual: row2.Split);
    }

    [Fact]
    public void Load_rpt_MarketInteractionResponseFacilitySectionCarrierTests()
    {
        var submissionContainerId = 20;
        var submissionContainerMarketId = 10;
        var marketResponseId = 40;
        var carrierId = 23;

        dynamic responseTypeRecord,
            placementSystemTableRecordCR,
            placementSystemTableRecordSCM,
            placementSystemTableRecordPC,
            placementSystemTableRecordMR,
            outcomeStatusRecordAcc,
            outcomeStatusRecordNotAcc,
            outcomeReasonRecord,
            layerTypeRecord,
            currencyRecordEur,
            facilityRecord,
            facilitySectionRecord,
            facilitySectionCarrierRecord,
            productHierarchyRecord,
            productHierarchyRecord2,
            placementStatusRecord;
        CreateReferenceRecords(carrierId, out responseTypeRecord, out placementSystemTableRecordCR, out placementSystemTableRecordSCM, out placementSystemTableRecordPC, out placementSystemTableRecordMR, out outcomeStatusRecordAcc, out outcomeStatusRecordNotAcc, out outcomeReasonRecord, out layerTypeRecord, out currencyRecordEur, out facilityRecord, out facilitySectionRecord, out facilitySectionCarrierRecord, out productHierarchyRecord, out productHierarchyRecord2, out placementStatusRecord);

        dynamic facilitySectionCarrierRecord2 = CreateRow(tableName: "ref.FacilitySectionCarrier", values: new
        {
            FacilitySectionId = facilitySectionRecord.FacilitySectionId,
            CarrierId = facilitySectionCarrierRecord.CarrierId,
            IsDeprecated = 0,
            SourceUpdatedDate = "2024-03-21",
            IsLead = 1,
            FacilitySectionCarrierKey = 2 + "|" + facilitySectionCarrierRecord.CarrierId + "|" + 1
        });

        dynamic facilitySectionCarrierRecord3 = CreateRow(tableName: "ref.FacilitySectionCarrier", values: new
        {
            FacilitySectionId = facilitySectionRecord.FacilitySectionId,
            CarrierId = facilitySectionCarrierRecord.CarrierId,
            IsDeprecated = 1,
            SourceUpdatedDate = "2024-03-22",
            IsLead = 1,
            FacilitySectionCarrierKey = 3 + "|" + facilitySectionCarrierRecord.CarrierId + "|" + 1
        });

        dynamic negotiationRecord = CreateRow(tableName: "PS.Negotiation", values: new
        {
            NegotiationKey = $"SUBC|{submissionContainerId}",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            SubmissionName = "Submission"
        });

        dynamic negotiationMarketRecord = CreateRow(tableName: "PS.NegotiationMarket", values: new
        {
            NegotiationId = negotiationRecord.NegotiationId,
            NegotiationMarketKey = $"SUBCONMKT|{submissionContainerMarketId}",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
        });

        dynamic marketResponseRecord = CreateRow(tableName: "PS.MarketResponse", values: new
        {
            NegotiationMarketId = negotiationMarketRecord.NegotiationMarketId,
            MarketResponseKey = $"MKTRES|{marketResponseId}",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            ResponseTypeId = responseTypeRecord.ResponseTypeId,
            LayerTypeId = layerTypeRecord.LayerTypeId,
            LimitCurrencyId = currencyRecordEur.CurrencyId,
            AttachmentPointCurrencyId = currencyRecordEur.CurrencyId,
            ResponseDate = "2023-11-21",
            Limit = 1000,
            Comments = "Comments",
            AttachmentPoint = 100000,
            UnderwriterName = "Joe Bloggs",
            FacilitySectionId = facilitySectionRecord.FacilitySectionId
        });

        dynamic marketQuoteResponseRecord = CreateRow(tableName: "PS.MarketQuoteResponse", values: new
        {
            MarketResponseId = marketResponseRecord.MarketResponseId,
            OutcomeReasonId = outcomeReasonRecord.OutcomeReasonId,
            OutcomeStatusId = 1,
            PremiumCurrencyId = currencyRecordEur.CurrencyId,
            IsDeleted = false,
            QuotedToLead = 1,
            Premium = 10,
            PremiumRate = 10000,
            OfferedLine = 1000,
            OfferedLineRate = 100,
            CommissionRate = 15,
            Subjectivity = "Subjectivity",
        });

        dynamic marketSecurityRecord = CreateRow(tableName: "dbo.MarketResponseSecurity", values: new
        {
            MarketResponseId = marketResponseId,
            CarrierId = facilitySectionCarrierRecord.CarrierId,
            Split = 10,
            IsFacilityLead = 1,
            MarketResponseSecurityId = 1
        });

        dynamic results = ExecuteStoredProcedureWithResultRow(storedProcedureName: "rpt.Load_rpt_MarketInteractionResponse");
        Assert.Equal(expected: 2, actual: results.InsertedCount);
        Assert.Equal(expected: 0, actual: results.UpdatedCount);
        Assert.Equal(expected: 0, actual: results.DeletedCount);
        Assert.Equal(expected: 0, actual: results.RejectedCount);

        //This should create two rows, one for SubmissionMarket and one for MarketSecurity
        CheckSprocExecutionLog(sprocName: "rpt.Load_rpt_MarketInteractionResponse", insertedCount: 2);

        dynamic row = GetResultRow(tableName: "rpt.MarketInteractionResponse", whereClause: "MarketSecurityCarrierId IS NOT NULL");
        //Get the actual record generated for ref.Facility to get the calculated PSFacilityPolicyId
        dynamic refFacility = GetResultRow(tableName: "ref.Facility");
        Assert.Equal(expected: "MarketSecurity", actual: row.MarketCategory);
        Assert.Equal(expected: "FacilityMember-" + refFacility.PSFacilityPolicyId + "-" + facilitySectionRecord.FacilitySectionId + "-" + facilitySectionCarrierRecord2.CarrierId, actual: row.MarketSecurityCarrierKey);
        Assert.Equal(expected: marketSecurityRecord.CarrierId, actual: row.MarketSecurityCarrierId);
        Assert.Equal(expected: marketSecurityRecord.MarketResponseSecurityId, actual: row.MarketResponseSecurityId);
    }

    [Fact]
    public void Load_rpt_MarketInteractionResponseUnderwriterEmailAddedTest()
    {
        var submissionContainerId = 20;
        var submissionContainerMarketId = 10;
        var marketResponseId = 40;
        var carrierId = 23;

        dynamic responseTypeRecord,
            placementSystemTableRecordCR,
            placementSystemTableRecordSCM,
            placementSystemTableRecordPC,
            placementSystemTableRecordMR,
            outcomeStatusRecordAcc,
            outcomeStatusRecordNotAcc,
            outcomeReasonRecord,
            layerTypeRecord,
            currencyRecordEur,
            facilityRecord,
            facilitySectionRecord,
            facilitySectionCarrierRecord,
            productHierarchyRecord,
            productHierarchyRecord2,
            placementStatusRecord;
        CreateReferenceRecords(carrierId, out responseTypeRecord, out placementSystemTableRecordCR, out placementSystemTableRecordSCM, out placementSystemTableRecordPC, out placementSystemTableRecordMR, out outcomeStatusRecordAcc, out outcomeStatusRecordNotAcc, out outcomeReasonRecord, out layerTypeRecord, out currencyRecordEur, out facilityRecord, out facilitySectionRecord, out facilitySectionCarrierRecord, out productHierarchyRecord, out productHierarchyRecord2, out placementStatusRecord);

        dynamic negotiationRecord = CreateRow(tableName: "PS.Negotiation", values: new
        {
            NegotiationKey = $"SUBC|{submissionContainerId}",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            SubmissionName = "Submission"
        });

        dynamic negotiationMarketRecord = CreateRow(tableName: "PS.NegotiationMarket", values: new
        {
            NegotiationId = negotiationRecord.NegotiationId,
            NegotiationMarketKey = $"SUBCONMKT|{submissionContainerMarketId}",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
        });

        dynamic marketResponseRecord = CreateRow(tableName: "PS.MarketResponse", values: new
        {
            NegotiationMarketId = negotiationMarketRecord.NegotiationMarketId,
            MarketResponseKey = $"MKTRES|{marketResponseId}",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            ResponseTypeId = responseTypeRecord.ResponseTypeId,
            LayerTypeId = layerTypeRecord.LayerTypeId,
            LimitCurrencyId = currencyRecordEur.CurrencyId,
            AttachmentPointCurrencyId = currencyRecordEur.CurrencyId,
            ResponseDate = "2023-11-21",
            Limit = 1000,
            Comments = "Comments",
            AttachmentPoint = 100000,
            UnderwriterName = "Joe Bloggs",
            UnderwriterEmail = "<EMAIL>",
            FacilitySectionId = facilitySectionRecord.FacilitySectionId
        });

        dynamic marketSecurityRecord = CreateRow(tableName: "dbo.MarketResponseSecurity", values: new
        {
            MarketResponseId = marketResponseId,
            CarrierId = facilitySectionCarrierRecord.CarrierId,
            Split = 10,
            IsFacilityLead = 1,
            MarketResponseSecurityId = 1
        });

        ExecuteStoredProcedureWithoutResult(storedProcedureName: @"rpt.Load_rpt_MarketInteractionResponse");

        //This should create two rows, one for SubmissionMarket and one for MarketSecurity
        CheckSprocExecutionLog(sprocName: "rpt.Load_rpt_MarketInteractionResponse", insertedCount: 2);

        dynamic row = GetResultRow(tableName: "rpt.MarketInteractionResponse", whereClause: "MarketSecurityCarrierId IS NOT NULL");
        Assert.Equal(expected: "MarketSecurity", actual: row.MarketCategory);
        Assert.Equal(expected: marketResponseRecord.UnderwriterEmail, actual: row.UnderwriterEmail);
    }

    [Fact]
    public void Load_rpt_MarketInteractionResponseBoundPositionPolicyCountTest()
    {
        var submissionContainerId = 20;
        var submissionContainerMarketId = 10;
        var marketResponseId = 40;
        var carrierId = 23;

        dynamic responseTypeRecord,
            placementSystemTableRecordCR,
            placementSystemTableRecordSCM,
            placementSystemTableRecordPC,
            placementSystemTableRecordMR,
            outcomeStatusRecordAcc,
            outcomeStatusRecordNotAcc,
            outcomeReasonRecord,
            layerTypeRecord,
            currencyRecordEur,
            facilityRecord,
            facilitySectionRecord,
            facilitySectionCarrierRecord,
            productHierarchyRecord,
            productHierarchyRecord2,
            placementStatusRecord;
        CreateReferenceRecords(carrierId, out responseTypeRecord, out placementSystemTableRecordCR, out placementSystemTableRecordSCM, out placementSystemTableRecordPC, out placementSystemTableRecordMR, out outcomeStatusRecordAcc, out outcomeStatusRecordNotAcc, out outcomeReasonRecord, out layerTypeRecord, out currencyRecordEur, out facilityRecord, out facilitySectionRecord, out facilitySectionCarrierRecord, out productHierarchyRecord, out productHierarchyRecord2, out placementStatusRecord);

        dynamic negotiationRecord = CreateRow(tableName: "PS.Negotiation", values: new
        {
            NegotiationKey = $"SUBC|{submissionContainerId}",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            SubmissionName = "Submission"
        });

        dynamic negotiationMarketRecord = CreateRow(tableName: "PS.NegotiationMarket", values: new
        {
            NegotiationId = negotiationRecord.NegotiationId,
            NegotiationMarketKey = $"SUBCONMKT|{submissionContainerMarketId}",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
        });

        dynamic marketResponseRecord = CreateRow(tableName: "PS.MarketResponse", values: new
        {
            NegotiationMarketId = negotiationMarketRecord.NegotiationMarketId,
            MarketResponseKey = $"MKTRES|{marketResponseId}",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            ResponseTypeId = responseTypeRecord.ResponseTypeId,
            LayerTypeId = layerTypeRecord.LayerTypeId,
            LimitCurrencyId = currencyRecordEur.CurrencyId,
            AttachmentPointCurrencyId = currencyRecordEur.CurrencyId,
            ResponseDate = "2023-11-21",
            Limit = 1000,
            Comments = "Comments",
            AttachmentPoint = 100000,
            UnderwriterName = "Joe Bloggs",
            UnderwriterEmail = "<EMAIL>",
            FacilitySectionId = facilitySectionRecord.FacilitySectionId
        });

        dynamic marketSecurityRecord = CreateRow(tableName: "dbo.MarketResponseSecurity", values: new
        {
            MarketResponseId = marketResponseId,
            CarrierId = facilitySectionCarrierRecord.CarrierId,
            Split = 10,
            IsFacilityLead = 1,
            MarketResponseSecurityId = 1
        });

        dynamic bpMarketResponseElement = CreateRow(tableName: "BP.MarketResponseElement", values: new
        {
            MarketResponseId = marketResponseId,
            ResponseManagementElementId = 1,
            ElementBranchId = 1
        });

        dynamic currentBPBoundPosition = CreateRow(tableName: "BP.BoundPosition", values: new
        {
            Id = 1,
            ResponseManagementElementId = bpMarketResponseElement.ResponseManagementElementId,
            ElementBranchId = bpMarketResponseElement.ElementBranchId,
            BoundPositionTypeId = 1
        });

        dynamic expiringBPBoundPosition = CreateRow(tableName: "BP.BoundPosition", values: new
        {
            Id = 2,
            ResponseManagementElementId = bpMarketResponseElement.ResponseManagementElementId,
            ElementBranchId = bpMarketResponseElement.ElementBranchId,
            BoundPositionTypeId = 2
        });

        dynamic currentBPBoundPositionPlacementPolicy = CreateRow(tableName: "BP.BoundPositionPlacementPolicy", values: new
        {
            Id = 34,
            BoundPositionId = currentBPBoundPosition.Id,
            PlacementPolicyId = 12657
        });

        dynamic expiringBPBoundPositionPlacementPolicy = CreateRow(tableName: "BP.BoundPositionPlacementPolicy", values: new
        {
            Id = 38,
            BoundPositionId = expiringBPBoundPosition.Id,
            PlacementPolicyId = 44578
        });

        dynamic currentdboPolicy = CreateRow(tableName: "dbo.Policy", values: new
        {
            PolicyReference = "12345A25"
        });

        dynamic expiringdboPolicy = CreateRow(tableName: "dbo.Policy", values: new
        {
            PolicyReference = "12345A24"
        });

        dynamic currentdboPlacementPolicy = CreateRow(tableName: "dbo.PlacementPolicy", values: new
        {
            SourcePlacementPolicyId = currentBPBoundPositionPlacementPolicy.PlacementPolicyId,
            PolicyId = currentdboPolicy.PolicyId
        });

        dynamic expiringdboPlacementPolicy = CreateRow(tableName: "dbo.PlacementPolicy", values: new
        {
            SourcePlacementPolicyId = expiringBPBoundPositionPlacementPolicy.PlacementPolicyId,
            PolicyId = expiringdboPolicy.PolicyId
        });

        ExecuteStoredProcedureWithoutResult(storedProcedureName: @"rpt.Load_rpt_MarketInteractionResponse");

        //This should create two rows, one for SubmissionMarket and one for MarketSecurity
        CheckSprocExecutionLog(sprocName: "rpt.Load_rpt_MarketInteractionResponse", insertedCount: 2);

        dynamic row = GetResultRow(tableName: "rpt.MarketInteractionResponse", whereClause: "MarketSecurityCarrierId IS NOT NULL");
        Assert.Equal(expected: "MarketSecurity", actual: row.MarketCategory);
        Assert.Equal(expected: 1, actual: row.CurrentPolicyCount);
        Assert.Equal(expected: currentdboPolicy.PolicyReference, actual: row.CurrentPolicyList);
        Assert.Equal(expected: 1, actual: row.ExpiringPolicyCount);
        Assert.Equal(expected: expiringdboPolicy.PolicyReference, actual: row.ExpiringPolicyList);
    }

    [Fact]
    public void Load_rpt_MarketInteractionResponseBoundPositionLongStringTest()
    {
        var submissionContainerId = 20;
        var submissionContainerMarketId = 10;
        var marketResponseId = 40;
        var carrierId = 23;

        dynamic responseTypeRecord,
            placementSystemTableRecordCR,
            placementSystemTableRecordSCM,
            placementSystemTableRecordPC,
            placementSystemTableRecordMR,
            outcomeStatusRecordAcc,
            outcomeStatusRecordNotAcc,
            outcomeReasonRecord,
            layerTypeRecord,
            currencyRecordEur,
            facilityRecord,
            facilitySectionRecord,
            facilitySectionCarrierRecord,
            productHierarchyRecord,
            productHierarchyRecord2,
            placementStatusRecord;
        CreateReferenceRecords(carrierId, out responseTypeRecord, out placementSystemTableRecordCR, out placementSystemTableRecordSCM, out placementSystemTableRecordPC, out placementSystemTableRecordMR, out outcomeStatusRecordAcc, out outcomeStatusRecordNotAcc, out outcomeReasonRecord, out layerTypeRecord, out currencyRecordEur, out facilityRecord, out facilitySectionRecord, out facilitySectionCarrierRecord, out productHierarchyRecord, out productHierarchyRecord2, out placementStatusRecord);

        dynamic negotiationRecord = CreateRow(tableName: "PS.Negotiation", values: new
        {
            NegotiationKey = $"SUBC|{submissionContainerId}",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            SubmissionName = "Submission"
        });

        dynamic negotiationMarketRecord = CreateRow(tableName: "PS.NegotiationMarket", values: new
        {
            NegotiationId = negotiationRecord.NegotiationId,
            NegotiationMarketKey = $"SUBCONMKT|{submissionContainerMarketId}",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
        });

        dynamic marketResponseRecord = CreateRow(tableName: "PS.MarketResponse", values: new
        {
            NegotiationMarketId = negotiationMarketRecord.NegotiationMarketId,
            MarketResponseKey = $"MKTRES|{marketResponseId}",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            ResponseTypeId = responseTypeRecord.ResponseTypeId,
            LayerTypeId = layerTypeRecord.LayerTypeId,
            LimitCurrencyId = currencyRecordEur.CurrencyId,
            AttachmentPointCurrencyId = currencyRecordEur.CurrencyId,
            ResponseDate = "2023-11-21",
            Limit = 1000,
            Comments = "Comments",
            AttachmentPoint = 100000,
            UnderwriterName = "Joe Bloggs",
            UnderwriterEmail = "<EMAIL>",
            FacilitySectionId = facilitySectionRecord.FacilitySectionId
        });

        dynamic marketSecurityRecord = CreateRow(tableName: "dbo.MarketResponseSecurity", values: new
        {
            MarketResponseId = marketResponseId,
            CarrierId = facilitySectionCarrierRecord.CarrierId,
            Split = 10,
            IsFacilityLead = 1,
            MarketResponseSecurityId = 1
        });

        dynamic bpMarketResponseElement = CreateRow(tableName: "BP.MarketResponseElement", values: new
        {
            MarketResponseId = marketResponseId,
            ResponseManagementElementId = 1,
            ElementBranchId = 1
        });

        dynamic currentBPBoundPosition, dboPolicy;

        dboPolicy = CreateRow(tableName: "dbo.Policy", values: new
        {
            PolicyReference = "12345A25"
        });

        dynamic placementPolicyRecord = CreateRow(tableName: "dbo.PlacementPolicy", values: new
        {
            SourcePlacementPolicyId = 12345,
            PolicyId = dboPolicy.PolicyId
        });

        for(int i = 0; i < 100; i++)
        {
            currentBPBoundPosition = CreateRow(tableName: "BP.BoundPosition", values: new
            {
                Id = i,
                ResponseManagementElementId = bpMarketResponseElement.ResponseManagementElementId,
                ElementBranchId = bpMarketResponseElement.ElementBranchId,
                BoundPositionTypeId = 1
            });

            CreateRow(tableName: "BP.BoundPositionPlacementPolicy", values: new
            {
                Id = i,
                BoundPositionId = currentBPBoundPosition.Id,
                PlacementPolicyId = placementPolicyRecord.SourcePlacementPolicyId,
            });
        }

        dynamic expiringBPBoundPosition;

        for(int i = 0; i < 100; i++)
        {
            expiringBPBoundPosition = CreateRow(tableName: "BP.BoundPosition", values: new
            {
                Id = 100000 + i,
                ResponseManagementElementId = bpMarketResponseElement.ResponseManagementElementId,
                ElementBranchId = bpMarketResponseElement.ElementBranchId,
                BoundPositionTypeId = 2
            });

            CreateRow(tableName: "BP.BoundPositionPlacementPolicy", values: new
            {
                Id = 100000 + i,
                BoundPositionId = expiringBPBoundPosition.Id,
                PlacementPolicyId = placementPolicyRecord.SourcePlacementPolicyId,
            });
        }
        ExecuteStoredProcedureWithoutResult(storedProcedureName: @"rpt.Load_rpt_MarketInteractionResponse");

        //This should create two rows, one for SubmissionMarket and one for MarketSecurity
        CheckSprocExecutionLog(sprocName: "rpt.Load_rpt_MarketInteractionResponse", insertedCount: 2);

        dynamic row = GetResultRow(tableName: "rpt.MarketInteractionResponse", whereClause: "MarketSecurityCarrierId IS NOT NULL");
        Assert.Equal(expected: "MarketSecurity", actual: row.MarketCategory);
        Assert.Equal(expected: 100, actual: row.CurrentPolicyCount);
        Assert.Equal(expected: 1000, actual: row.CurrentPolicyList.Length);
        Assert.Equal(expected: 100, actual: row.ExpiringPolicyCount);
        Assert.Equal(expected: 1000, actual: row.ExpiringPolicyList.Length);
    }

    [Fact]
    public void Load_rpt_MarketInteractionResponseLeadCarrier()
    {
        var submissionContainerId = 20;
        var submissionContainerMarketId = 10;
        var submissionContainerMarketIdLead = 22;
        var marketResponseId = 40;
        var leadMarketResponseId = 100;
        var carrierId = 23;
        var marketKey = 80;
        var leadMarketKey = 70;
        var parentId = DBNull.Value;

        dynamic responseTypeRecord,
           placementSystemTableRecordCR,
           placementSystemTableRecordSCM,
           placementSystemTableRecordPC,
           placementSystemTableRecordMR,
           outcomeStatusRecordAcc,
           outcomeStatusRecordNotAcc,
           outcomeReasonRecord,
           layerTypeRecord,
           currencyRecordEur,
           facilityRecord,
           facilitySectionRecord,
           facilitySectionCarrierRecord,
           productHierarchyRecord,
           productHierarchyRecord2,
           placementStatusRecord;
        CreateReferenceRecords(carrierId, out responseTypeRecord, out placementSystemTableRecordCR, out placementSystemTableRecordSCM, out placementSystemTableRecordPC, out placementSystemTableRecordMR, out outcomeStatusRecordAcc, out outcomeStatusRecordNotAcc, out outcomeReasonRecord, out layerTypeRecord, out currencyRecordEur, out facilityRecord, out facilitySectionRecord, out facilitySectionCarrierRecord, out productHierarchyRecord, out productHierarchyRecord2, out placementStatusRecord);

        dynamic placementRecord = CreateRow(tableName: "dbo.Placement", values: new
        {
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            PlacementName = "Test Placement",
            PlacementStatusId = placementStatusRecord.PlacementStatusId,
            PlacementSystemId = 1022232,
            IsDeleted = false
        });

        //RiskProfile for the specification
        dynamic riskProfileRecord = CreateRow(tableName: "PS.RiskProfile", values: new
        {
            RiskProfileId = 7,
            PlacementId = placementRecord.PlacementId,
            ProductId = productHierarchyRecord.ProductId,
            ClassId = 811,
            RiskProfileKey = "ELEM|12345",
            LineOfBusiness = "RiskProfileLOB",
            ClassOfBusiness = "RiskProfileCOB",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform
        });

        //RiskProfile for the specification
        dynamic riskProfileRecord2 = CreateRow(tableName: "PS.RiskProfile", values: new
        {
            RiskProfileId = 8,
            PlacementId = placementRecord.PlacementId,
            ProductId = productHierarchyRecord2.ProductId,
            ClassId = 812,
            RiskProfileKey = "SPEC|1",
            LineOfBusiness = "RiskProfileLOB",
            ClassOfBusiness = "RiskProfileCOB",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform
        });

        dynamic negotiationRecord = CreateRow(tableName: "PS.Negotiation", values: new
        {
            NegotiationKey = $"SUBC|{submissionContainerId}",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            SubmissionName = "Submission"
        });

        dynamic negotiationMarketRecordLead = CreateRow(tableName: "PS.NegotiationMarket", values: new
        {
            NegotiationId = negotiationRecord.NegotiationId,
            NegotiationMarketKey = $"SUBCONMKT|{submissionContainerMarketIdLead}",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            MarketKey = leadMarketKey
        });

        dynamic negotiationMarketRecord = CreateRow(tableName: "PS.NegotiationMarket", values: new
        {
            NegotiationId = negotiationRecord.NegotiationId,
            NegotiationMarketKey = $"SUBCONMKT|{submissionContainerMarketId}",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            MarketKey = marketKey
        });

        dynamic SubmissionRecord = CreateRow(tableName: "BP.Submission", values: new
        {
            SubmissionContainerId = submissionContainerId,
            Sent = "2023-11-11"
        });

        dynamic marketResponseRecordLeader = CreateRow(tableName: "PS.MarketResponse", values: new
        {
            NegotiationMarketId = negotiationMarketRecordLead.NegotiationMarketId,
            MarketResponseId = leadMarketResponseId,
            MarketResponseKey = $"MKTRES|{leadMarketResponseId}",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            ResponseTypeId = responseTypeRecord.ResponseTypeId
            
        });

        dynamic marketResponseRecord = CreateRow(tableName: "PS.MarketResponse", values: new
        {
            NegotiationMarketId = negotiationMarketRecord.NegotiationMarketId,
            MarketResponseKey = $"MKTRES|{marketResponseId}",
            ParentId = leadMarketResponseId,
            ParentKey = $"MKTRES|{leadMarketResponseId}",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            ResponseTypeId = responseTypeRecord.ResponseTypeId
        });


        dynamic followTypeRecord = CreateRow(tableName: "ref.FollowType", values: new
        {
            FollowType = "Follow"
        });

        dynamic followTypeRecordLead = CreateRow(tableName: "ref.FollowType", values: new
        {
            FollowType = "Lead"
        });

        dynamic marketQuoteResponseRecordLeader = CreateRow(tableName: "PS.MarketQuoteResponse", values: new
        {
            MarketResponseId = marketResponseRecordLeader.MarketResponseId,
            SourceMarketQuoteResponseKey = "MQR|1",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            OutcomeReasonId = outcomeReasonRecord.OutcomeReasonId,
            OutcomeStatusId = 1,
            IsDeleted = false,
            QuotedToLead = 1,
            SignedLineRate = 70,
            FollowTypeId = followTypeRecordLead.FollowTypeId
        });

        dynamic marketQuoteResponseRecord = CreateRow(tableName: "PS.MarketQuoteResponse", values: new
        {
            MarketResponseId = marketResponseRecord.MarketResponseId,
            OutcomeReasonId = outcomeReasonRecord.OutcomeReasonId,
            OutcomeStatusId = 1,
            IsDeleted = false,
            QuotedToLead = 0,
            SignedLineRate = 30,
            FollowTypeId = followTypeRecord.FollowTypeId
        });

        dynamic marketRecord = CreateRow(tableName: "rpt.Market", values: new
        {
            MarketKey = marketKey,
            MarketName = new String('A', 550)
        });

        dynamic marketRecordLead = CreateRow(tableName: "rpt.Market", values: new
        {
            MarketKey = leadMarketKey,
            MarketName = new String('A', 550)
        });


        dynamic results = ExecuteStoredProcedureWithResultRow(storedProcedureName: "rpt.Load_rpt_MarketInteractionResponse");
        Assert.Equal(expected: 2, actual: results.InsertedCount);
        Assert.Equal(expected: 0, actual: results.UpdatedCount);
        Assert.Equal(expected: 0, actual: results.DeletedCount);
        Assert.Equal(expected: 0, actual: results.RejectedCount);

        //This should create two rows, one for SubmissionMarket and one for MarketSecurity
        CheckSprocExecutionLog(sprocName: "rpt.Load_rpt_MarketInteractionResponse", insertedCount: 2);

        dynamic row = GetResultRow(tableName: "rpt.MarketInteractionResponse", whereClause: $"ParentResponseKey = 'MKTRES|{leadMarketResponseId}'");
        Assert.Equal(expected: "SubmissionMarket", actual: row.MarketCategory);
        Assert.Equal(expected: marketResponseId, actual: row.CarrierResponseId);
        Assert.Equal(expected: $"MKTRES|{marketResponseId}", actual: row.MarketResponseKey);
        Assert.Equal(expected: $"MKTRES|{leadMarketResponseId}", actual: row.ParentResponseKey);
        Assert.Equal(expected: marketRecordLead.MarketName, actual: row.LeadCarrier);
        Assert.Equal(expected: marketQuoteResponseRecordLeader.SignedLineRate, actual: row.LeadSignedLineRate);
    }

    [Fact]
    public void Load_rpt_MarketInteractionResponseLeadCarrierMarketSecurity()
    {
        var submissionContainerId = 20;
        var submissionContainerMarketId = 10;
        var submissionContainerMarketIdLead = 22;
        var marketResponseId = 1234567891;
        var leadMarketResponseId = 1234567890;
        var carrierId = 40;
        var leadcarrierId = 50;
        var marketKey = 80;
        var leadMarketKey = 70;
        var parentId = DBNull.Value;

        dynamic responseTypeRecord,
           placementSystemTableRecordCR,
           placementSystemTableRecordSCM,
           placementSystemTableRecordPC,
           placementSystemTableRecordMR,
           outcomeStatusRecordAcc,
           outcomeStatusRecordNotAcc,
           outcomeReasonRecord,
           layerTypeRecord,
           currencyRecordEur,
           facilityRecord,
           facilitySectionRecord,
           facilitySectionCarrierRecord,
           productHierarchyRecord,
           productHierarchyRecord2,
           placementStatusRecord;

        CreateReferenceRecords(carrierId, out responseTypeRecord, out placementSystemTableRecordCR, out placementSystemTableRecordSCM, out placementSystemTableRecordPC, out placementSystemTableRecordMR, out outcomeStatusRecordAcc, out outcomeStatusRecordNotAcc, out outcomeReasonRecord, out layerTypeRecord, out currencyRecordEur, out facilityRecord, out facilitySectionRecord, out facilitySectionCarrierRecord, out productHierarchyRecord, out productHierarchyRecord2, out placementStatusRecord);

        dynamic placementRecord = CreateRow(tableName: "dbo.Placement", values: new
        {
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            PlacementName = "Test Placement",
            PlacementStatusId = placementStatusRecord.PlacementStatusId,
            PlacementSystemId = 1022232,
            IsDeleted = false
        });

        //RiskProfile for the specification
        dynamic riskProfileRecord = CreateRow(tableName: "PS.RiskProfile", values: new
        {
            RiskProfileId = 7,
            PlacementId = placementRecord.PlacementId,
            ProductId = productHierarchyRecord.ProductId,
            ClassId = 811,
            RiskProfileKey = "ELEM|12345",
            LineOfBusiness = "RiskProfileLOB",
            ClassOfBusiness = "RiskProfileCOB",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform
        });

        //RiskProfile for the specification
        dynamic riskProfileRecord2 = CreateRow(tableName: "PS.RiskProfile", values: new
        {
            RiskProfileId = 8,
            PlacementId = placementRecord.PlacementId,
            ProductId = productHierarchyRecord2.ProductId,
            ClassId = 812,
            RiskProfileKey = "SPEC|1",
            LineOfBusiness = "RiskProfileLOB",
            ClassOfBusiness = "RiskProfileCOB",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform
        });

        dynamic negotiationRecord = CreateRow(tableName: "PS.Negotiation", values: new
        {
            NegotiationKey = $"SUBC|{submissionContainerId}",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            SubmissionName = "Submission"
        });

        dynamic negotiationMarketRecordLead = CreateRow(tableName: "PS.NegotiationMarket", values: new
        {
            NegotiationId = negotiationRecord.NegotiationId,
            NegotiationMarketKey = $"SUBCONMKT|{submissionContainerMarketIdLead}",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            MarketKey = leadMarketKey
        });

        dynamic negotiationMarketRecord = CreateRow(tableName: "PS.NegotiationMarket", values: new
        {
            NegotiationId = negotiationRecord.NegotiationId,
            NegotiationMarketKey = $"SUBCONMKT|{submissionContainerMarketId}",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            MarketKey = marketKey
        });

        dynamic SubmissionRecord = CreateRow(tableName: "BP.Submission", values: new
        {
            SubmissionContainerId = submissionContainerId,
            Sent = "2023-11-11"
        });

        dynamic marketResponseRecordLeader = CreateRow(tableName: "PS.MarketResponse", values: new
        {
            NegotiationMarketId = negotiationMarketRecordLead.NegotiationMarketId,
            MarketResponseId = leadMarketResponseId,
            MarketResponseKey = "MKTRES|1234567890",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            ResponseTypeId = responseTypeRecord.ResponseTypeId,
            FacilitySectionId = facilitySectionRecord.FacilitySectionId

        });

        dynamic marketResponseRecord = CreateRow(tableName: "PS.MarketResponse", values: new
        {
            NegotiationMarketId = negotiationMarketRecord.NegotiationMarketId,
            MarketResponseId = marketResponseId,
            MarketResponseKey = "MKTRES|1234567891",
            ParentId = leadMarketResponseId,
            ParentKey = "MKTRES|1234567890",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            ResponseTypeId = responseTypeRecord.ResponseTypeId,
            FacilitySectionId = facilitySectionRecord.FacilitySectionId
        });

        dynamic followTypeRecord = CreateRow(tableName: "ref.FollowType", values: new
        {
            FollowType = "Follow"
        });

        dynamic followTypeRecordLead = CreateRow(tableName: "ref.FollowType", values: new
        {
            FollowType = "Lead"
        });

        dynamic marketQuoteResponseRecordLeader = CreateRow(tableName: "PS.MarketQuoteResponse", values: new
        {
            MarketResponseId = marketResponseRecordLeader.MarketResponseId,
            SourceMarketQuoteResponseKey = "MQR|1",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            OutcomeReasonId = outcomeReasonRecord.OutcomeReasonId,
            OutcomeStatusId = 1,
            IsDeleted = false,
            IsOverride = false,
            QuotedToLead = 1,
            SignedLineRate = 70,
            FollowTypeId = followTypeRecordLead.FollowTypeId
        });

        dynamic marketQuoteResponseRecord = CreateRow(tableName: "PS.MarketQuoteResponse", values: new
        {
            MarketResponseId = marketResponseRecord.MarketResponseId,
            SourceMarketQuoteResponseKey = "MQR|2",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            OutcomeReasonId = outcomeReasonRecord.OutcomeReasonId,
            OutcomeStatusId = 1,
            IsDeleted = false,
            IsOverride = false,
            QuotedToLead = 0,
            SignedLineRate = 30,
            FollowTypeId = followTypeRecord.FollowTypeId
        });

        dynamic marketResponseBasisRecordLeader = CreateRow(tableName: "PS.MarketResponseBasis", values: new
        {
            MarketResponseId = marketResponseRecordLeader.MarketResponseId,
            MarketResponseBasisKey = "MKTRES|1234567890",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            RiskProfileId = riskProfileRecord.RiskProfileId,
            IsDeleted = false,
            SpecificationId = 1

        });

        dynamic marketResponseBasisRecord = CreateRow(tableName: "PS.MarketResponseBasis", values: new
        {
            MarketResponseId = marketResponseRecord.MarketResponseId,
            MarketResponseBasisKey = "MKTRES|1234567891",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            RiskProfileId = riskProfileRecord2.RiskProfileId,
            IsDeleted = false,
            SpecificationId = 2
        });

        dynamic marketSecurityRecordleader = CreateRow(tableName: "dbo.MarketResponseSecurity", values: new
        {
            MarketResponseSecurityId = 1,
            MarketResponseId = leadMarketResponseId,
            CarrierId = leadcarrierId,
            Split = 10,
            IsFacilityLead = true
        });

        dynamic marketSecurityRecord = CreateRow(tableName: "dbo.MarketResponseSecurity", values: new
        {
            MarketResponseSecurityId = 2,
            MarketResponseId = marketResponseId,
            CarrierId = carrierId,
            Split = 10,
            IsFacilityLead = false
        });

        dynamic marketRecord = CreateRow(tableName: "rpt.Market", values: new
        {
            MarketKey = marketKey,
            MarketName = new String('A', 550)
        });

        dynamic marketRecordLead = CreateRow(tableName: "rpt.Market", values: new
        {
            MarketKey = leadMarketKey,
            MarketName = new String('A', 550)
        });


        dynamic results = ExecuteStoredProcedureWithResultRow(storedProcedureName: "rpt.Load_rpt_MarketInteractionResponse");
        Assert.Equal(expected: 4, actual: results.InsertedCount);
        Assert.Equal(expected: 0, actual: results.UpdatedCount);
        Assert.Equal(expected: 0, actual: results.DeletedCount);
        Assert.Equal(expected: 0, actual: results.RejectedCount);

        //This should create two rows, one for SubmissionMarket and one for MarketSecurity
        CheckSprocExecutionLog(sprocName: "rpt.Load_rpt_MarketInteractionResponse", insertedCount: 4);

        var Results = GetResultRows(tableName: "rpt.MarketInteractionResponse");

        dynamic row = GetResultRow(tableName: "rpt.MarketInteractionResponse", whereClause: $"ParentResponseKey = 'MKTRES|{leadMarketResponseId}' AND MarketCategory = 'MarketSecurity'");
        //Assert.Equal(expected: "Market Security", actual: row.MarketCategory);
        Assert.Equal(expected: marketResponseId, actual: row.CarrierResponseId);
        Assert.Equal(expected: $"MKTRES|{marketResponseId}", actual: row.MarketResponseKey);
        Assert.Equal(expected: $"MKTRES|{leadMarketResponseId}", actual: row.ParentResponseKey);
        Assert.Equal(expected: marketRecordLead.MarketName, actual: row.LeadCarrier);
        Assert.Equal(expected: marketQuoteResponseRecordLeader.SignedLineRate, actual: row.LeadSignedLineRate);
    }

    [Fact]
    //This test has been created to validate the MarketSecurityCarrierKey created in rpt.MarketInteractionResponse matches the MarketKey created in rpt.Market
    public void Load_rpt_MarketInteractionResponseMarketSecurityCarrierKeyTest()
    {
        var submissionContainerId = 20;
        var submissionContainerMarketId = 10;
        var marketResponseId = 40;
        var carrierId = 23;

        dynamic responseTypeRecord,
           placementSystemTableRecordCR,
           placementSystemTableRecordSCM,
           placementSystemTableRecordPC,
           placementSystemTableRecordMR,
           outcomeStatusRecordAcc,
           outcomeStatusRecordNotAcc,
           outcomeReasonRecord,
           layerTypeRecord,
           currencyRecordEur,
           facilityRecord,
           facilitySectionRecord,
           facilitySectionCarrierRecord,
           productHierarchyRecord,
           productHierarchyRecord2,
           placementStatusRecord;
        CreateReferenceRecords(carrierId, out responseTypeRecord, out placementSystemTableRecordCR, out placementSystemTableRecordSCM, out placementSystemTableRecordPC, out placementSystemTableRecordMR, out outcomeStatusRecordAcc, out outcomeStatusRecordNotAcc, out outcomeReasonRecord, out layerTypeRecord, out currencyRecordEur, out facilityRecord, out facilitySectionRecord, out facilitySectionCarrierRecord, out productHierarchyRecord, out productHierarchyRecord2, out placementStatusRecord);

        dynamic placementRecord = CreateRow(tableName: "dbo.Placement", values: new
        {
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            PlacementName = "Test Placement",
            PlacementStatusId = placementStatusRecord.PlacementStatusId,
            PlacementSystemId = 1022232,
            IsDeleted = false
        });

        dynamic carrierHierarchyExtendedRecord = CreateRow(tableName: "PS.CarrierHierarchyExtended", values: new
        {
            CarrierId = carrierId
        });

        dynamic carrierRecord = CreateRow(tableName: "dbo.Carrier", values: new
        {
            CarrierTypeId = 5,
            SourceCarrierId = carrierId
        });

        dynamic negotiationRecord = CreateRow(tableName: "PS.Negotiation", values: new
        {
            NegotiationKey = $"SUBC|{submissionContainerId}",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            SubmissionName = "Submission"
        });

        dynamic negotiationMarketRecord = CreateRow(tableName: "PS.NegotiationMarket", values: new
        {
            NegotiationId = negotiationRecord.NegotiationId,
            NegotiationMarketKey = $"SUBCONMKT|{submissionContainerMarketId}",
            FacilityId = facilityRecord.FacilityId,
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
        });

        dynamic marketResponseRecord = CreateRow(tableName: "PS.MarketResponse", values: new
        {
            NegotiationMarketId = negotiationMarketRecord.NegotiationMarketId,
            MarketResponseKey = $"MKTRES|{marketResponseId}",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            ResponseTypeId = responseTypeRecord.ResponseTypeId,
            LayerTypeId = layerTypeRecord.LayerTypeId,
            LimitCurrencyId = currencyRecordEur.CurrencyId,
            AttachmentPointCurrencyId = currencyRecordEur.CurrencyId,
            ResponseDate = "2023-11-21",
            ResponseCreatedDate = DateTime.Parse("2023-11-21"),
            Limit = 1000,
            Comments = "Comments",
            AttachmentPoint = 100000,
            UnderwriterName = "Joe Bloggs",
            FacilitySectionId = facilitySectionRecord.FacilitySectionId,
            IsInvalid = true
        });

        dynamic marketSecurityRecord = CreateRow(tableName: "dbo.MarketResponseSecurity", values: new
        {
            MarketResponseId = marketResponseId,
            CarrierId = carrierId,
            Split = 10,
            IsFacilityLead = 1
        });

        dynamic results = ExecuteStoredProcedureWithResultRow(storedProcedureName: "rpt.Load_rpt_MarketInteractionResponse");
        Assert.Equal(expected: 2, actual: results.InsertedCount);
        Assert.Equal(expected: 0, actual: results.UpdatedCount);
        Assert.Equal(expected: 0, actual: results.DeletedCount);
        Assert.Equal(expected: 0, actual: results.RejectedCount);

        ExecuteStoredProcedureWithoutResult(storedProcedureName: "rpt.Load_rpt_Market");
        //Get the actual record generated for rpt.Market to get the calculated Market Key
        dynamic rptMarket = GetResultRow(tableName: "rpt.Market", whereClause: "MarketType = 'Facility Member'");

        //This should create two rows, one for SubmissionMarket and one for MarketSecurity
        CheckSprocExecutionLog(sprocName: "rpt.Load_rpt_MarketInteractionResponse", insertedCount: 2);

        //This test compares the MarketSecurityCarrierKey created in the Market Interaction Response load to the MarketKey created in rpt.Market to ensure they align
        dynamic row = GetResultRow(tableName: "rpt.MarketInteractionResponse", whereClause: "MarketCategory = 'MarketSecurity'");
        Assert.Equal(expected: "MarketSecurity", actual: row.MarketCategory);
        Assert.Equal(expected: rptMarket.MarketKey, actual: row.MarketSecurityCarrierKey);
    }

    [Fact]
    public void CheckDependenciesTest()
    {
        CheckToSeeIfInterdependenciesExistForStoredProcedure(storedProcedureName: @"rpt.Load_rpt_MarketInteractionResponse", excludedTables: new List<string> { "ref.NegotiationType","dbo.Policy" });
    }

    #region private shared functions
    /// <summary>
    /// Code that can be used by all.
    /// </summary>
    /// <param name="carrierId"></param>
    /// <param name="responseTypeRecord"></param>
    /// <param name="placementSystemTableRecordCR"></param>
    /// <param name="placementSystemTableRecordSCM"></param>
    /// <param name="placementSystemTableRecordPC"></param>
    /// <param name="placementSystemTableRecordMR"></param>
    /// <param name="outcomeStatusRecordAcc"></param>
    /// <param name="outcomeStatusRecordNotAcc"></param>
    /// <param name="outcomeReasonRecord"></param>
    /// <param name="layerTypeRecord"></param>
    /// <param name="currencyRecordEur"></param>
    /// <param name="facilityRecord"></param>
    /// <param name="facilitySectionRecord"></param>
    /// <param name="facilitySectionCarrierRecord"></param>
    /// <param name="productHierarchyRecord"></param>
    /// <param name="productHierarchyRecord2"></param>
    /// <param name="placementStatusRecord"></param>
    private void CreateReferenceRecords(int carrierId, out dynamic responseTypeRecord, out dynamic placementSystemTableRecordCR, out dynamic placementSystemTableRecordSCM, out dynamic placementSystemTableRecordPC, out dynamic placementSystemTableRecordMR, out dynamic outcomeStatusRecordAcc, out dynamic outcomeStatusRecordNotAcc, out dynamic outcomeReasonRecord, out dynamic layerTypeRecord, out dynamic currencyRecordEur, out dynamic facilityRecord, out dynamic facilitySectionRecord, out dynamic facilitySectionCarrierRecord, out dynamic productHierarchyRecord, out dynamic productHierarchyRecord2, out dynamic placementStatusRecord)
    {
        responseTypeRecord = CreateRow(tableName: "ref.ResponseType", values: new
        {
            ResponseTypeId = 1,
            ResponseType = "Offered"
        });

        placementSystemTableRecordCR = CreateRow(tableName: "dbo.PlacementSystemTable", values: new
        {
            TableId = 90,
            SchemaName = "app",
            TableName = "CarrierResponse"
        });

        placementSystemTableRecordSCM = CreateRow(tableName: "dbo.PlacementSystemTable", values: new
        {
            TableId = 92,
            SchemaName = "app",
            TableName = "SubmissionContainerMarket"
        });
        placementSystemTableRecordPC = CreateRow(tableName: "dbo.PlacementSystemTable", values: new
        {
            TableId = 91,
            SchemaName = "app",
            TableName = "PlacementCarrier"
        });

        placementSystemTableRecordMR = CreateRow(tableName: "dbo.PlacementSystemTable", values: new
        {
            TableId = 93,
            SchemaName = "app",
            TableName = "MarketResponse"
        });

        // OutcomeType
        outcomeStatusRecordAcc = CreateRow(tableName: "ref.OutcomeStatus", values: new
        {
            OutcomeStatusId = 1,
            OutcomeStatus = "Accepted"
        });

        // OutcomeType
        outcomeStatusRecordNotAcc = CreateRow(tableName: "ref.OutcomeStatus", values: new
        {
            OutcomeStatusId = 2,
            OutcomeStatus = "Not Accepted"
        });

        // OutcomeReason
        outcomeReasonRecord = CreateRow(tableName: "ref.OutcomeReason", values: new
        {
            OutcomeReasonId = 2,
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            OutcomeStatusId = outcomeStatusRecordAcc.OutcomeStatusId,
            OutcomeReason = "Financial strength of Carrier",
            ReasonGroupId = 1
        });

        // layerType
        layerTypeRecord = CreateRow(tableName: "ref.LayerType", values: new
        {
            LayerTypeId = 1,
            LayerType = "Primary"
        });

        // Currency
        currencyRecordEur = CreateRow(tableName: "Reference.Currency", values: new
        {
            CurrencyAlphaCode = "EUR",
            CurrencyId = 2,
            CurrencyName = "Euro"
        });

        // Currency
        dynamic currencyRecordUSD = CreateRow(tableName: "Reference.Currency", values: new
        {
            CurrencyAlphaCode = "USD",
            CurrencyId = 1,
            CurrencyName = "Dollars"
        });

        // Exchange Rate
        dynamic exchangeRateRecord = CreateRow(tableName: "Reference.ExchangeRate", values: new
        {
            ExchangeRateId = 10,
            FromCurrencyId = currencyRecordEur.CurrencyId,
            ToCurrencyId = currencyRecordUSD.CurrencyId,
            EffectiveDate = "2023-11-01",
            ExchangeRateTypeId = (int)ExchangeRateType.AverageRate,
            IsDeleted = false,
            ExchangeRate = 1.0
        });
        CreateRow(tableName: "Reference.ExchangeRate", values: new
        {
            ExchangeRateId = 40,
            FromCurrencyId = currencyRecordUSD.CurrencyId,
            ToCurrencyId = currencyRecordEur.CurrencyId,
            EffectiveDate = "2024-02-29",
            ExchangeRateTypeId = (int)ExchangeRateType.AverageRate,
            IsDeleted = 0,
            ExchangeRate = 40.3
        });

        facilityRecord = CreateRow(tableName: "ref.Facility", values: new
        {
            FacilityKey = "999"
        });

        // FacilitySectionKey seems to be the same as PolicySectionId
        facilitySectionRecord = CreateRow(tableName: "ref.FacilitySection", values: new
        {
            FacilityId = facilityRecord.FacilityId,
            FacilitySectionKey = "23",
            DataSourceInstanceId = (int)DataSourceInstance.Eclipse
        });

        facilitySectionCarrierRecord = CreateRow(tableName: "ref.FacilitySectionCarrier", values: new
        {
            FacilitySectionId = facilitySectionRecord.FacilitySectionId,
            CarrierId = carrierId
        });

        productHierarchyRecord = CreateRow(tableName: "rpt.ProductHierarchy", values: new
        {
            ProductId = 81,
            ProductKey = "classOfBusiness_accidentAndHealth|lineOfBusiness_life",
            ProductName = "SpecRiskProfileProduct",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform
        });

        productHierarchyRecord2 = CreateRow(tableName: "rpt.ProductHierarchy", values: new
        {
            ProductId = 82,
            ProductKey = "classOfBusiness_accidentAndHealth|lineOfBusiness_medical",
            ProductName = "ContractRiskProfileProduct",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform
        });

        placementStatusRecord = CreateRow(tableName: "ref.PlacementStatus", values: new
        {
            PlacementStatus = "Not Started",
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            PlacementStatusKey = "1"
        });
    }
    #endregion

    #region Constructors
    /// <summary>
    /// Constructor - do not change.
    /// </summary>
    /// <param name="fixture"></param>
    public Load_rpt_MarketInteractionResponseTests(DatabaseFixture fixture, ITestOutputHelper output) : base(fixture, output)
    {
    }
    #endregion
}
