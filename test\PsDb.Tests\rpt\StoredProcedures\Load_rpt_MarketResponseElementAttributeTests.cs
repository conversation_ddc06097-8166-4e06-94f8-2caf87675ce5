﻿using Microsoft.Extensions.Primitives;
using Newtonsoft.Json.Linq;
using System.Reflection;

namespace PsDb.Tests.rpt.StoredProcedures;

public class Load_rpt_MarketResponseElementAttributeTests : PlacementStoreTestBase
{
    dynamic _PSmreaRecord;
    dynamic _EtRecord;
    dynamic _PSmrRecord;
    int carrierresponseid = 123;

    [Fact]
    public void Load_rpt_MarketResponseElementAttributeNoDataTest()
    {
        NoDataStoredProcedureTest(storedProcedureTestMethod: MethodBase.GetCurrentMethod());
    }

    [Fact]
    public void LoadMarketResponseElementAttributeMinimalDataTest()
    {

        dynamic MarketResponseElementRecord = SetupData("Aggregate Limit", "responseCapture_metric_limit_generalAggregate", "Amount", 100, "$100");

        ExecuteStoredProcedureWithoutResult(storedProcedureName: "rpt.Load_rpt_MarketResponseElementAttribute");

        //This should create only one row
        CheckSprocExecutionLog(sprocName: "rpt.Load_rpt_MarketResponseElementAttribute", insertedCount: 1);

        dynamic row = GetResultRow("select * from rpt.MarketResponseElementAttribute");

        Assert.NotNull(row);
        Assert.Equal(expected: carrierresponseid, actual: row.CarrierResponseId);
        Assert.Equal(expected: _PSmreaRecord.Value, actual: row.AggregateLimitAmount);

        dynamic vwRow = GetResultRow("select * from rpt.vw_as_MarketResponseElementAttribute");

        Assert.NotNull(row);
        Assert.Equal(expected: carrierresponseid, actual: vwRow.CarrierResponseId);
        Assert.Equal(expected: _PSmreaRecord.Value, actual: vwRow.AggregateLimitAmount);
    }

    [Fact]
    public void LoadMarketResponseElementAttributeTextLengthTest()
    {
        //Create record with Text string over 2000 characters
        dynamic MarketResponseElementRecord = SetupData("Policy Limit", "responseCapture_metric_limit_policyLimit", "Text", new string('t', 3000), new string('t', 3000));

        ExecuteStoredProcedureWithoutResult(storedProcedureName: "rpt.Load_rpt_MarketResponseElementAttribute");

        //This should create only one row
        CheckSprocExecutionLog(sprocName: "rpt.Load_rpt_MarketResponseElementAttribute", insertedCount: 1);

        dynamic row = GetResultRow("select * from rpt.MarketResponseElementAttribute");

        Assert.NotNull(row);
        Assert.Equal(expected: carrierresponseid, actual: row.CarrierResponseId);
        Assert.Equal(expected: Convert.ToString(_PSmreaRecord.Value).Substring(0, 1999), actual: row.PolicyLimitText);

        dynamic vwRow = GetResultRow("select * from rpt.vw_as_MarketResponseElementAttribute");

        Assert.NotNull(row);
        Assert.Equal(expected: carrierresponseid, actual: vwRow.CarrierResponseId);
        Assert.Equal(expected: Convert.ToString(_PSmreaRecord.Value).Substring(0, 1999), actual: vwRow.PolicyLimitText);
    }

    [Theory]
    [InlineData("Aggregate Limit", "responseCapture_metric_limit_generalAggregate", "Amount", "AggregateLimitAmount", 100, "$100")]
    [InlineData("Auto Physical Damage - Collision", "responseCapture_metric_limit_automotive_physicalDamageCollision", "Amount", "AutoPhysicalDamageCollisionAmount", 200, "$200")]
    [InlineData("Auto Physical Damage - Comprehensive", "responseCapture_metric_limit_automotive_physicalDamageComprehensive", "Amount", "AutoPhysicalDamageComprehensiveAmount", 102, "$102")]
    [InlineData("Bodily Injury by Accident - Each Accident", "responseCapture_metric_limit_employersLiability_bodilyInjuryAccident", "Amount", "BodilyInjurybyAccidentEachAccidentAmount", 103, "$103")]
    [InlineData("Bodily Injury by Disease - Each Employee", "responseCapture_metric_limit_employersLiability_bodilyInjuryDiseaseEmployee", "Amount", "BodilyInjurybyDiseaseEachEmployeeAmount", 104, "$104")]
    [InlineData("Bodily Injury by Disease - Policy Limit", "responseCapture_metric_limit_employersLiability_bodilyInjuryDiseasePolicyLimit", "Amount", "BodilyInjurybyDiseasePolicyLimitAmount", 105, "$105")]
    [InlineData("Damage to Premises Rented to You", "responseCapture_metric_limit_generalLiability_damageToPremises", "Amount", "DamagetoPremisesRentedtoYouAmount", 106, "$106")]
    [InlineData("Deductible", "responseCapture_metric_deductible", "Amount", "DeductibleAmount", 107, "$107")]
    [InlineData("Each Occurrence Limit", "responseCapture_metric_limit_perOcurrence", "Amount", "EachOccurrenceLimitAmount", 108, "$108")]
    [InlineData("Employee Benefits Liability Deductible / Retention Amount", "responseCapture_metric_deductible_employeeBenefitsLiab", "Amount", "EmployeeBenefitsLiabilityDeductibleRetentionAmountAmount", 109, "$109")]
    [InlineData("Estimated Claims Handling Cost", "responseCapture_metric_deductible_CollateralClaimsHandling_EstClaimsHandleCost", "Amount", "EstimatedClaimsHandlingCostAmount", 110, "$110")]
    [InlineData("General Aggregate", "responseCapture_metric_limit_generalLiability_aggregate", "Amount", "GeneralAggregateAmount", 111, "$111")]
    [InlineData("Liability", "responseCapture_metric_deductible_liability", "Amount", "LiabilityAmount", 112, "$112")]
    [InlineData("Med Pay", "responseCapture_metric_limit_medPay", "Amount", "MedPayAmount", 113, "$113")]
    [InlineData("Personal & Advertising Injury - Per Person or Organization", "responseCapture_metric_limit_generalLiability_personalAndAdvertising", "Amount", "PersonalAdvertisingInjuryPerPersonorOrganizationAmount", 114, "$114")]
    [InlineData("Policy Limit", "responseCapture_metric_limit_policyLimit", "Amount", "PolicyLimitAmount", 115, "$115")]
    [InlineData("Products & Completed Ops Aggregate Limit", "responseCapture_metric_limit_pncOpsAggregate", "Amount", "ProductsCompletedOpsAggregateLimitAmount", 116, "$116")]
    [InlineData("Revenue", "exposure_exposuremetric_Revenue", "Amount", "RevenueAmount", 117, "$117")]
    [InlineData("Total Collateral Requirement", "responseCapture_metric_deductible_CollateralClaimsHandling_TotalCollateralReq", "Amount", "TotalCollateralRequirementAmount", 118, "$118")]
    [InlineData("Total Estimated Payroll ", "exposure_exposuremetric_TotalEstimatedPayroll", "Amount", "TotalEstimatedPayrollAmount", 119, "$119")]
    [InlineData("Total Outstanding Actuarial Liability - Carrier", "responseCapture_metric_deductible_CollateralClaimsHandling_TotalOSAcutariaLiab_Carrier", "Amount", "TotalOutstandingActuarialLiabilityCarrierAmount", 120, "$120")]
    [InlineData("Total Value", "exposure_exposuremetric_TotalValue", "Amount", "TotalValueAmount", 121, "$121")]
    [InlineData("TRIA Premium", "responseCapture_metric_premium_tria", "Amount", "TerrorismRiskInsuranceActPremiumAmount", 122, "$122")]
    [InlineData("Workers Compensation", "responseCapture_metric_limit_workersCompensation", "Amount", "WorkersCompensationAmount", 123, "$123")]
    [InlineData("Surplus Lines Tax", "responseCapture_metric_premium_suplusLinesTax", "Amount", "SurplusLinesTaxAmount", 124, "$124")]
    [InlineData("Total Excess Limits", "responseCapture_metric_totalExcessLimits", "Amount", "TotalExcessLimitsAmount", 123, "$123")]

    public void AmountTest(string elementtype, string elementtypekey, string elementattributetype, string columnName, dynamic value, string displayvalue)
    {
        dynamic MarketResponseElementRecord = SetupData(elementtype, elementtypekey, elementattributetype, value, displayvalue);
        dynamic results = ExecuteStoredProcedureWithResultRow(storedProcedureName: "rpt.Load_rpt_MarketResponseElementAttribute");
        Assert.Equal(expected: 1, actual: results.InsertedCount);
        Assert.Equal(expected: 0, actual: results.UpdatedCount);
        Assert.Equal(expected: 0, actual: results.DeletedCount);
        Assert.Equal(expected: 0, actual: results.RejectedCount);

        //This should create only one row
        CheckSprocExecutionLog(sprocName: "rpt.Load_rpt_MarketResponseElementAttribute", insertedCount: 1);

        dynamic row = GetResultRow($"select {columnName} AS result from rpt.MarketResponseElementAttribute");

        Assert.NotNull(row);
        Assert.Equal(expected: _PSmreaRecord.Value, actual: row.result);
    }

    [Theory]
    [InlineData("Auto Liability Effective Date", "responseCapture_metric_autoLiability_effectiveDate", "Date", "AutoLiabilityEffectiveDate", "2024-05-02 00:00:00.00")]
    [InlineData("Auto Liability Expiration Date", "responseCapture_metric_autoLiability_expirationDate", "Date", "AutoLiabilityExpirationDate", "2024-05-02 00:00:00.00")]
    [InlineData("Employers Liability Effective Date", "responseCapture_metric_employersLiability_effectiveDate", "Date", "EmployersLiabilityEffectiveDate", "2024-05-02 00:00:00.00")]
    [InlineData("Employers Liability Expiration Date", "responseCapture_metric_employersLiability_expirationDate", "Date", "EmployersLiabilityExpirationDate", "2024-05-02 00:00:00.00")]
    [InlineData("General Liability Effective Date", "responseCapture_metric_generalLiability_effectiveDate", "Date", "GeneralLiabilityEffectiveDate", "2024-05-02 00:00:00.00")]
    [InlineData("General Liability Expiration Date", "responseCapture_metric_generalLiability_expirationDate", "Date", "GeneralLiabilityExpirationDate", "2024-05-02 00:00:00.00")]
    [InlineData("Policy Effective Date", "responseCapture_metric_limit_policyEffectiveDate", "Date", "PolicyEffectiveDate", "2024-05-02 00:00:00.00")]
    [InlineData("Policy Expiration Date", "responseCapture_metric_limit_policyExpiryDate", "Date", "PolicyExpirationDate", "2024-05-02 00:00:00.00")]
    [InlineData("Professional Liability Effective Date", "responseCapture_metric_professionalLiability_effectiveDate", "Date", "ProfessionalLiabilityEffectiveDate", "2024-05-02 00:00:00.00")]
    [InlineData("Professional Liability Expiration Date", "responseCapture_metric_professionalLiability_expirationDate", "Date", "ProfessionalLiabilityExpirationDate", "2024-05-02 00:00:00.00")]
    [InlineData("Quoted Expiration Date", "responseCapture_metric_quotedExpiration", "Date", "QuotedExpirationDate", "2024-05-02 00:00:00.00")]
    [InlineData("Quote Expiry Date", "responseCapture_metric_quoteExpiryDate", "Date", "QuoteExpiryDate", "2024-05-02 00:00:00.00")]
    public void DateTest(string elementtype, string elementtypekey, string elementattributetype, string columnName, dynamic value)
    {
        dynamic MarketResponseElementRecord = SetupData(elementtype, elementtypekey, elementattributetype, value, null);
        dynamic results = ExecuteStoredProcedureWithResultRow(storedProcedureName: "rpt.Load_rpt_MarketResponseElementAttribute");
        Assert.Equal(expected: 1, actual: results.InsertedCount);
        Assert.Equal(expected: 0, actual: results.UpdatedCount);
        Assert.Equal(expected: 0, actual: results.DeletedCount);
        Assert.Equal(expected: 0, actual: results.RejectedCount);

        //This should create only one row
        CheckSprocExecutionLog(sprocName: "rpt.Load_rpt_MarketResponseElementAttribute", insertedCount: 1);

        dynamic row = GetResultRow($"select {columnName} AS result from rpt.MarketResponseElementAttribute");

        Assert.NotNull(row);
        Assert.Equal(expected: DateTime.Parse(_PSmreaRecord.Value), actual: row.result);
    }

    [Theory]
    [InlineData("Basis of Cover", "responseCapture_metric_basisOfCover", "Basis", "BasisofCoverBasis", 1, "Occurrence")]
    [InlineData("Basis of Cover", "responseCapture_metric_basisOfCover", "Basis", "BasisofCoverBasis", 2, "Claims Made")]
    [InlineData("Basis of Cover", "responseCapture_metric_basisOfCover", "Basis", "BasisofCoverBasis", 3, "Claims Made and Reported")]
    [InlineData("Basis of Cover", "responseCapture_metric_basisOfCover", "Basis", "BasisofCoverBasis", 4, "I/O")]
    [InlineData("Basis of Cover", "responseCapture_metric_basisOfCover", "Basis", "BasisofCoverBasis", 5, "Dual Trigger")]
    [InlineData("Basis of Cover", "responseCapture_metric_basisOfCover", "Basis", "BasisofCoverBasis", 6, "Loss Discovered")]
    [InlineData("Basis of Cover", "responseCapture_metric_basisOfCover", "Basis", "BasisofCoverBasis", 7, "Loss Sustained")]
    public void BasisOfCoverTest(string elementtype, string elementtypekey, string elementattributetype, string columnName, dynamic value, string basisofcover)
    {
        dynamic MarketResponseElementRecord = SetupData(elementtype, elementtypekey, elementattributetype, value, null);
        dynamic basisOfCoverRecord = CreateRow(tableName: "ref.BasisOfCover", values: new
        {
            BasisOfCoverId = value,
            BasisOfCover = basisofcover
        });

        dynamic results = ExecuteStoredProcedureWithResultRow(storedProcedureName: "rpt.Load_rpt_MarketResponseElementAttribute");
        Assert.Equal(expected: 1, actual: results.InsertedCount);
        Assert.Equal(expected: 0, actual: results.UpdatedCount);
        Assert.Equal(expected: 0, actual: results.DeletedCount);
        Assert.Equal(expected: 0, actual: results.RejectedCount);

        //This should create only one row
        CheckSprocExecutionLog(sprocName: "rpt.Load_rpt_MarketResponseElementAttribute", insertedCount: 1);

        dynamic row = GetResultRow($"select {columnName} AS result from rpt.MarketResponseElementAttribute");

        Assert.NotNull(row);
        Assert.Equal(expected: basisOfCoverRecord.BasisOfCover, actual: row.result);
    }

    [Theory]
    [InlineData("Aggregate Limit", "responseCapture_metric_limit_generalAggregate", "Basis", "AggregateLimitBasis", 1136, "Annual Aggregate")]
    [InlineData("Auto Physical Damage - Collision", "responseCapture_metric_limit_automotive_physicalDamageCollision", "Basis", "AutoPhysicalDamageCollisionBasis", 1141, "Per Occurrence")]
    [InlineData("Auto Physical Damage - Comprehensive", "responseCapture_metric_limit_automotive_physicalDamageComprehensive", "Basis", "AutoPhysicalDamageComprehensiveBasis", 1072, "Each and every loss")]
    [InlineData("Bodily Injury by Accident - Each Accident", "responseCapture_metric_limit_employersLiability_bodilyInjuryAccident", "Basis", "BodilyInjurybyAccidentEachAccidentBasis", 53, "Each and every")]
    [InlineData("Bodily Injury by Disease - Each Employee", "responseCapture_metric_limit_employersLiability_bodilyInjuryDiseaseEmployee", "Basis", "BodilyInjurybyDiseaseEachEmployeeBasis", 1144, "Included")]
    [InlineData("Bodily Injury by Disease - Policy Limit", "responseCapture_metric_limit_employersLiability_bodilyInjuryDiseasePolicyLimit", "Basis", "BodilyInjurybyDiseasePolicyLimitBasis", 1088, "In Aggregate")]
    [InlineData("Damage to Premises Rented to You", "responseCapture_metric_limit_generalLiability_damageToPremises", "Basis", "DamagetoPremisesRentedtoYouBasis", 1022, "Any One Fire")]
    [InlineData("Deductible", "responseCapture_metric_deductible", "Basis", "DeductibleBasis", 1005, "Aircraft")]
    [InlineData("Each Occurrence Limit", "responseCapture_metric_limit_perOcurrence", "Basis", "EachOccurrenceLimitBasis", 1008, "Any One Accident")]
    [InlineData("Employee Benefits Liability Deductible / Retention Amount", "responseCapture_metric_deductible_employeeBenefitsLiab", "Basis", "EmployeeBenefitsLiabilityDeductibleRetentionAmountBasis", 1016, "Any One Claim")]
    [InlineData("General Aggregate", "responseCapture_metric_limit_generalLiability_aggregate", "Basis", "GeneralAggregateBasis", 1059, "Claim")]
    [InlineData("Liability", "responseCapture_metric_deductible_liability", "Basis", "LiabilityBasis", 1097, "Loss")]
    [InlineData("Med Pay", "responseCapture_metric_limit_medPay", "Basis", "MedPayBasis", 1009, "Any One Accident Or Occurrence")]
    [InlineData("Personal & Advertising Injury - Per Person or Organization", "responseCapture_metric_limit_generalLiability_personalAndAdvertising", "Basis", "PersonalAdvertisingInjuryPerPersonorOrganizationBasis", 1104, "Person")]
    [InlineData("Policy Limit", "responseCapture_metric_limit_policyLimit", "Basis", "PolicyLimitBasis", "102", "Month")]
    [InlineData("Products & Completed Ops Aggregate Limit", "responseCapture_metric_limit_pncOpsAggregate", "Basis", "ProductsCompletedOpsAggregateLimitBasis", 1096, "Location")]
    [InlineData("Workers Compensation", "responseCapture_metric_limit_workersCompensation", "Basis", "WorkersCompensationBasis", 1002, "Accident")]
    public void BasisTest(string elementtype, string elementtypekey, string elementattributetype, string columnName, dynamic value, string coveragebasis)
    {
        dynamic MarketResponseElementRecord = SetupData(elementtype, elementtypekey, elementattributetype, value, null);
        dynamic coverageBasisRecord = CreateRow(tableName: "ref.CoverageBasis", values: new
        {
            CoverageBasisId = value,
            CoverageBasis = coveragebasis
        });

        dynamic results = ExecuteStoredProcedureWithResultRow(storedProcedureName: "rpt.Load_rpt_MarketResponseElementAttribute");
        Assert.Equal(expected: 1, actual: results.InsertedCount);
        Assert.Equal(expected: 0, actual: results.UpdatedCount);
        Assert.Equal(expected: 0, actual: results.DeletedCount);
        Assert.Equal(expected: 0, actual: results.RejectedCount);

        //This should create only one row
        CheckSprocExecutionLog(sprocName: "rpt.Load_rpt_MarketResponseElementAttribute", insertedCount: 1);

        dynamic row = GetResultRow($"select {columnName} AS result from rpt.MarketResponseElementAttribute");

        Assert.NotNull(row);
        Assert.Equal(expected: coverageBasisRecord.CoverageBasis, actual: row.result);
    }

    [Theory]
    [InlineData("Aggregate Limit", "responseCapture_metric_limit_generalAggregate", "Currency", "AggregateLimitCurrency", "100017", "AUD")]
    [InlineData("Auto Physical Damage - Collision", "responseCapture_metric_limit_automotive_physicalDamageCollision", "Currency", "AutoPhysicalDamageCollisionCurrency", "100053", "CAD")]
    [InlineData("Auto Physical Damage - Comprehensive", "responseCapture_metric_limit_automotive_physicalDamageComprehensive", "Currency", "AutoPhysicalDamageComprehensiveCurrency", "100217", "SGD")]
    [InlineData("Bodily Injury by Accident - Each Accident", "responseCapture_metric_limit_employersLiability_bodilyInjuryAccident", "Currency", "BodilyInjurybyAccidentEachAccidentCurrency", "100249", "USD")]
    [InlineData("Bodily Injury by Disease - Each Employee", "responseCapture_metric_limit_employersLiability_bodilyInjuryDiseaseEmployee", "Currency", "BodilyInjurybyDiseaseEachEmployeeCurrency", "100216", "SEK")]
    [InlineData("Bodily Injury by Disease - Policy Limit", "responseCapture_metric_limit_employersLiability_bodilyInjuryDiseasePolicyLimit", "Currency", "BodilyInjurybyDiseasePolicyLimitCurrency", "100090", "EUR")]
    [InlineData("Damage to Premises Rented to You", "responseCapture_metric_limit_generalLiability_damageToPremises", "Currency", "DamagetoPremisesRentedtoYouCurrency", "100095", "GBP")]
    [InlineData("Deductible", "responseCapture_metric_deductible", "Currency", "DeductibleCurrency", "100185", "NOK")]
    [InlineData("Each Occurrence Limit", "responseCapture_metric_limit_perOcurrence", "Currency", "EachOccurrenceLimitCurrency", "100197", "PLN")]
    [InlineData("Employee Benefits Liability Deductible / Retention Amount", "responseCapture_metric_deductible_employeeBenefitsLiab", "Currency", "EmployeeBenefitsLiabilityDeductibleRetentionAmountCurrency", "100290", "ZAR")]
    [InlineData("Estimated Claims Handling Cost", "responseCapture_metric_deductible_CollateralClaimsHandling_EstClaimsHandleCost", "Currency", "EstimatedClaimsHandlingCostCurrency", "100077", "DKK")]
    [InlineData("General Aggregate", "responseCapture_metric_limit_generalLiability_aggregate", "Currency", "GeneralAggregateCurrency", "100043", "BRL")]
    [InlineData("Liability", "responseCapture_metric_deductible_liability", "Currency", "LiabilityCurrency", "100017", "AUD")]
    [InlineData("Med Pay", "responseCapture_metric_limit_medPay", "Currency", "MedPayCurrency", "100053", "CAD")]
    [InlineData("Personal & Advertising Injury - Per Person or Organization", "responseCapture_metric_limit_generalLiability_personalAndAdvertising", "Currency", "PersonalAdvertisingInjuryPerPersonorOrganizationCurrency", "100217", "SGD")]
    [InlineData("Policy Effective Date", "responseCapture_metric_limit_policyEffectiveDate", "Currency", "PolicyEffectiveDateCurrency", "100249", "USD")]
    [InlineData("Policy Expiration Date", "responseCapture_metric_limit_policyExpiryDate", "Currency", "PolicyExpirationDateCurrency", "100216", "SEK")]
    [InlineData("Policy Limit", "responseCapture_metric_limit_policyLimit", "Currency", "PolicyLimitCurrency", "100090", "EUR")]
    [InlineData("Products & Completed Ops Aggregate Limit", "responseCapture_metric_limit_pncOpsAggregate", "Currency", "ProductsCompletedOpsAggregateLimitCurrency", "100095", "GBP")]
    [InlineData("Revenue", "exposure_exposuremetric_Revenue", "Currency", "RevenueCurrency", "100185", "NOK")]
    [InlineData("Total Collateral Requirement", "responseCapture_metric_deductible_CollateralClaimsHandling_TotalCollateralReq", "Currency", "TotalCollateralRequirementCurrency", "100197", "PLN")]
    [InlineData("Total Estimated Payroll ", "exposure_exposuremetric_TotalEstimatedPayroll", "Currency", "TotalEstimatedPayrollCurrency", "100290", "ZAR")]
    [InlineData("Total Outstanding Actuarial Liability - Carrier", "responseCapture_metric_deductible_CollateralClaimsHandling_TotalOSAcutariaLiab_Carrier", "Currency", "TotalOutstandingActuarialLiabilityCarrierCurrency", "100077", "DKK")]
    [InlineData("Total Value", "exposure_exposuremetric_TotalValue", "Currency", "TotalValueCurrency", "100043", "BRL")]
    [InlineData("TRIA Premium", "responseCapture_metric_premium_tria", "Currency", "TerrorismRiskInsuranceActPremiumCurrency", "100095", "GBP")]
    [InlineData("Workers Compensation", "responseCapture_metric_limit_workersCompensation", "Currency", "WorkersCompensationCurrency", "100095", "GBP")]
    [InlineData("Surplus Lines Tax", "responseCapture_metric_premium_suplusLinesTax", "Currency", "SurplusLinesTaxCurrency", "100095", "GBP")]
    [InlineData("Total Excess Limits", "responseCapture_metric_totalExcessLimits", "Currency", "TotalExcessLimitsCurrency", "100095", "GBP")]
    public void CurrencyTest(string elementtype, string elementtypekey, string elementattributetype, string columnName, dynamic value, string currencycode)
    {
        dynamic MarketResponseElementRecord = SetupData(elementtype, elementtypekey, elementattributetype, value, null);
        dynamic currencyRecord = CreateRow(tableName: "Reference.Currency", values: new
        {
            CurrencyID = value,
            CurrencyAlphaCode = currencycode
        });

        dynamic results = ExecuteStoredProcedureWithResultRow(storedProcedureName: "rpt.Load_rpt_MarketResponseElementAttribute");
        Assert.Equal(expected: 1, actual: results.InsertedCount);
        Assert.Equal(expected: 0, actual: results.UpdatedCount);
        Assert.Equal(expected: 0, actual: results.DeletedCount);
        Assert.Equal(expected: 0, actual: results.RejectedCount);

        //This should create only one row
        CheckSprocExecutionLog(sprocName: "rpt.Load_rpt_MarketResponseElementAttribute", insertedCount: 1);

        dynamic row = GetResultRow($"select {columnName} AS result from rpt.MarketResponseElementAttribute");

        Assert.NotNull(row);
        Assert.Equal(expected: currencyRecord.CurrencyAlphaCode, actual: row.result);
    }

    [Theory]
    [InlineData("Deductible", "responseCapture_metric_deductible", "Percentage", "DeductiblePercentage", 1, "1%")]
    [InlineData("Liability", "responseCapture_metric_deductible_liability", "Percentage", "LiabilityPercentage", 2, "2%")]
    [InlineData("Auto Physical Damage - Comprehensive", "responseCapture_metric_limit_automotive_physicalDamageComprehensive", "Percentage", "AutoPhysicalDamageComprehensivePercentage", 3, "3%")]
    [InlineData("Bodily Injury by Accident - Each Accident", "responseCapture_metric_limit_employersLiability_bodilyInjuryAccident", "Percentage", "BodilyInjurybyAccidentEachAccidentPercentage", 4, "4%")]
    [InlineData("Bodily Injury by Disease - Each Employee", "responseCapture_metric_limit_employersLiability_bodilyInjuryDiseaseEmployee", "Percentage", "BodilyInjurybyDiseaseEachEmployeePercentage", 5, "5%")]
    [InlineData("Bodily Injury by Disease - Policy Limit", "responseCapture_metric_limit_employersLiability_bodilyInjuryDiseasePolicyLimit", "Percentage", "BodilyInjurybyDiseasePolicyLimitPercentage", 6, "6%")]
    [InlineData("Aggregate Limit", "responseCapture_metric_limit_generalAggregate", "Percentage", "AggregateLimitPercentage", 7, "7%")]
    [InlineData("Each Occurrence Limit", "responseCapture_metric_limit_perOcurrence", "Percentage", "EachOccurrenceLimitPercentage", 8, "8%")]
    [InlineData("Products & Completed Ops Aggregate Limit", "responseCapture_metric_limit_pncOpsAggregate", "Percentage", "ProductsCompletedOpsAggregateLimitPercentage", 9, "9%")]
    [InlineData("Policy Limit", "responseCapture_metric_limit_policyLimit", "Percentage", "PolicyLimitPercentage", 10, "10%")]
    [InlineData("Total Excess Limits", "responseCapture_metric_totalExcessLimits", "Percentage", "TotalExcessLimitsPercentage", 10, "10%")]
    public void DecimalTest(string elementtype, string elementtypekey, string elementattributetype, string columnName, dynamic value, string displayvalue)
    {
        dynamic MarketResponseElementRecord = SetupData(elementtype, elementtypekey, elementattributetype, value, displayvalue);
        dynamic results = ExecuteStoredProcedureWithResultRow(storedProcedureName: "rpt.Load_rpt_MarketResponseElementAttribute");
        Assert.Equal(expected: 1, actual: results.InsertedCount);
        Assert.Equal(expected: 0, actual: results.UpdatedCount);
        Assert.Equal(expected: 0, actual: results.DeletedCount);
        Assert.Equal(expected: 0, actual: results.RejectedCount);

        //This should create only one row
        CheckSprocExecutionLog(sprocName: "rpt.Load_rpt_MarketResponseElementAttribute", insertedCount: 1);

        dynamic row = GetResultRow($"select {columnName} AS result from rpt.MarketResponseElementAttribute");

        Assert.NotNull(row);
        Assert.Equal(expected: Convert.ToDecimal(_PSmreaRecord.Value) / 100, actual: row.result);
    }

    [Theory]
    [InlineData("Structure", "responseCapture_metric_programStructure", "Program Structure", "StructureProgramStructure", "1", "Deductible")]
    [InlineData("Structure", "responseCapture_metric_programStructure", "Program Structure", "StructureProgramStructure", "2", "Guaranteed Cost")]
    [InlineData("Structure", "responseCapture_metric_programStructure", "Program Structure", "StructureProgramStructure", "3", "Self-insured Retention")]
    [InlineData("Structure", "responseCapture_metric_programStructure", "Program Structure", "StructureProgramStructure", "4", "Retrospective")]
    public void ProgramStructureTest(string elementtype, string elementtypekey, string elementattributetype, string columnName, dynamic value, string displayvalue)
    {
        dynamic programStructureTypeRecord = CreateRow(tableName: "ref.ProgramStructureType", values: new
        {
            ProgramStructureTypeKey = value,
            ProgramStructureType = displayvalue
        });
        dynamic MarketResponseElementRecord = SetupData(elementtype, elementtypekey, elementattributetype, value, displayvalue);
        dynamic results = ExecuteStoredProcedureWithResultRow(storedProcedureName: "rpt.Load_rpt_MarketResponseElementAttribute");
        Assert.Equal(expected: 1, actual: results.InsertedCount);
        Assert.Equal(expected: 0, actual: results.UpdatedCount);
        Assert.Equal(expected: 0, actual: results.DeletedCount);
        Assert.Equal(expected: 0, actual: results.RejectedCount);

        //This should create only one row
        CheckSprocExecutionLog(sprocName: "rpt.Load_rpt_MarketResponseElementAttribute", insertedCount: 1);

        dynamic row = GetResultRow($"select {columnName} AS result from rpt.MarketResponseElementAttribute");

        Assert.NotNull(row);
        Assert.Equal(expected: programStructureTypeRecord.ProgramStructureType, actual: row.result);
    }

    [Theory]
    [InlineData("Aggregate Limit", "responseCapture_metric_limit_generalAggregate", "Text", "AggregateLimitText", "Carrier Form", "Carrier Form")]
    [InlineData("Auto Physical Damage - Collision", "responseCapture_metric_limit_automotive_physicalDamageCollision", "Text", "AutoPhysicalDamageCollisionText", "None", "None")]
    [InlineData("Auto Physical Damage - Comprehensive", "responseCapture_metric_limit_automotive_physicalDamageComprehensive", "Text", "AutoPhysicalDamageComprehensiveText", "none", "none")]
    [InlineData("Bodily Injury by Accident - Each Accident", "responseCapture_metric_limit_employersLiability_bodilyInjuryAccident", "Text", "BodilyInjurybyAccidentEachAccidentText", "Per Claim ; BI PD", "Per Claim ; BI PD")]
    [InlineData("Bodily Injury by Disease - Each Employee", "responseCapture_metric_limit_employersLiability_bodilyInjuryDiseaseEmployee", "Text", "BodilyInjurybyDiseaseEachEmployeeText", "n/a", "n/a")]
    [InlineData("Bodily Injury by Disease - Policy Limit", "responseCapture_metric_limit_employersLiability_bodilyInjuryDiseasePolicyLimit", "Text", "BodilyInjurybyDiseasePolicyLimitText", "Both BI PD", "Both BI PD")]
    [InlineData("Damage to Premises Rented to You", "responseCapture_metric_limit_generalLiability_damageToPremises", "Text", "DamagetoPremisesRentedtoYouText", "Incl in WC", "Incl in WC")]
    [InlineData("Deductible", "responseCapture_metric_deductible", "Text", "DeductibleText", "Incl in WC", "Incl in WC")]
    [InlineData("Each Occurrence Limit", "responseCapture_metric_limit_perOcurrence", "Text", "EachOccurrenceLimitText", "Carrier Form", "Carrier Form")]
    [InlineData("Employee Benefits Liability Deductible / Retention Amount", "responseCapture_metric_deductible_employeeBenefitsLiab", "Text", "EmployeeBenefitsLiabilityDeductibleRetentionAmountText", "2% subject to $25,000 minimum and no maximum", "2% subject to $25,000 minimum and no maximum")]
    [InlineData("Estimated Claims Handling Cost", "responseCapture_metric_deductible_CollateralClaimsHandling_EstClaimsHandleCost", "Text", "EstimatedClaimsHandlingCostText", "EB T0 01 02 19", "EB T0 01 02 19")]
    [InlineData("General Aggregate", "responseCapture_metric_limit_generalLiability_aggregate", "Text", "GeneralAggregateText", "None", "None")]
    [InlineData("Liability", "responseCapture_metric_deductible_liability", "Text", "LiabilityText", "Special", "Special")]
    [InlineData("Med Pay", "responseCapture_metric_limit_medPay", "Text", "MedPayText", "Included", "Included")]
    [InlineData("Personal & Advertising Injury - Per Person or Organization", "responseCapture_metric_limit_generalLiability_personalAndAdvertising", "Text", "PersonalAdvertisingInjuryPerPersonorOrganizationText", "Included", "Included")]
    [InlineData("Policy Effective Date", "responseCapture_metric_limit_policyEffectiveDate", "Text", "PolicyEffectiveDateText", "Manuscript", "Manuscript")]
    [InlineData("Policy Expiration Date", "responseCapture_metric_limit_policyExpiryDate", "Text", "PolicyExpirationDateText", "All Risk", "All Risk")]
    [InlineData("Policy Form", "responseCapture_metric_policyForm", "Text", "PolicyFormText", "Each Claim", "Each Claim")]
    [InlineData("Policy Limit", "responseCapture_metric_limit_policyLimit", "Text", "PolicyLimitText", "included", "included")]
    [InlineData("Policy Trigger", "responseCapture_metric_policyTrigger", "Text", "PolicyTriggerText", "7091", "7091")]
    [InlineData("Products & Completed Ops Aggregate Limit", "responseCapture_metric_limit_pncOpsAggregate", "Text", "ProductsCompletedOpsAggregateLimitText", "Manuscript", "Manuscript")]
    [InlineData("Total Collateral Requirement", "responseCapture_metric_deductible_CollateralClaimsHandling_TotalCollateralReq", "Text", "TotalCollateralRequirementText", "n/a", "n/a")]
    [InlineData("Total Outstanding Actuarial Liability - Carrier", "responseCapture_metric_deductible_CollateralClaimsHandling_TotalOSAcutariaLiab_Carrier", "Text", "TotalOutstandingActuarialLiabilityCarrierText", "3% of the limit of insurance on the item(s) involved in the loss with a $2,500 minimum", "3% of the limit of insurance on the item(s) involved in the loss with a $2,500 minimum")]
    [InlineData("Workers Compensation", "responseCapture_metric_limit_workersCompensation", "Text", "WorkersCompensationText", "Zurich EDGE II", "Zurich EDGE II")]
    [InlineData("Total Excess Limits", "responseCapture_metric_totalExcessLimits", "Text", "TotalExcessLimitsText", "Excess PL; Excess GL is $4M ", "Excess PL; Excess GL is $4M ")]
    public void TextTest(string elementtype, string elementtypekey, string elementattributetype, string columnName, dynamic value, string displayvalue)
    {
        dynamic MarketResponseElementRecord = SetupData(elementtype, elementtypekey, elementattributetype, value, displayvalue);
        dynamic results = ExecuteStoredProcedureWithResultRow(storedProcedureName: "rpt.Load_rpt_MarketResponseElementAttribute");
        Assert.Equal(expected: 1, actual: results.InsertedCount);
        Assert.Equal(expected: 0, actual: results.UpdatedCount);
        Assert.Equal(expected: 0, actual: results.DeletedCount);
        Assert.Equal(expected: 0, actual: results.RejectedCount);

        //This should create only one row
        CheckSprocExecutionLog(sprocName: "rpt.Load_rpt_MarketResponseElementAttribute", insertedCount: 1);

        dynamic row = GetResultRow($"select {columnName} AS result from rpt.MarketResponseElementAttribute");

        Assert.NotNull(row);
        Assert.Equal(expected: _PSmreaRecord.Value, actual: row.result);
    }

    [Theory]
    [InlineData("Policy Trigger", "responseCapture_metric_policyTrigger", "Policy Trigger", "PolicyTrigger", "1", "All Perils")]
    [InlineData("Third Party Administrator", "responseCapture_metric_deductible_CollateralClaimsHandling_TPAdmin", "Third Party Administrator", "ThirdPartyAdministrator", "15", "Helmsman")]
    public void DisplayValueTest(string elementtype, string elementtypekey, string elementattributetype, string columnName, dynamic value, string displayvalue)
    {
        dynamic MarketResponseElementRecord = SetupData(elementtype, elementtypekey, elementattributetype, value, displayvalue);
        dynamic results = ExecuteStoredProcedureWithResultRow(storedProcedureName: "rpt.Load_rpt_MarketResponseElementAttribute");
        Assert.Equal(expected: 1, actual: results.InsertedCount);
        Assert.Equal(expected: 0, actual: results.UpdatedCount);
        Assert.Equal(expected: 0, actual: results.DeletedCount);
        Assert.Equal(expected: 0, actual: results.RejectedCount);

        //This should create only one row
        CheckSprocExecutionLog(sprocName: "rpt.Load_rpt_MarketResponseElementAttribute", insertedCount: 1);

        dynamic row = GetResultRow($"select {columnName} AS result from rpt.MarketResponseElementAttribute");

        Assert.NotNull(row);
        Assert.Equal(expected: _PSmreaRecord.DisplayValue, actual: row.result);
    }

    [Theory]
    [InlineData("Bodily Injury by Disease - Each Employee", "responseCapture_metric_limit_employersLiability_bodilyInjuryDiseaseEmployee", "Number of Units", "BodilyInjuryByDiseaseEachEmployeeNumberOfUnits", 125, "125")]
    [InlineData("Deductible", "responseCapture_metric_deductible", "Number of Units", "DeductibleNumberOfUnits", 7, "7")]
    [InlineData("Each Occurrence Limit", "responseCapture_metric_limit_perOcurrence", "Number of Units", "EachOccurrenceLimitNumberOfUnits", 23, "23")]
    [InlineData("Liability", "responseCapture_metric_deductible_liability", "Number of Units", "LiabilityNumberOfUnits", 21, "21")]
    [InlineData("Number of Extra Heavy Trucks ", "exposure_exposuremetric_NumberOfExtraHeavyTrucks", "Number of Items", "NumberOfExtraHeavyTrucks", 1, null)]
    [InlineData("Number of Heavy Trucks ", "exposure_exposuremetric_NumberOfHeavyTrucks", "Number of Items", "NumberOfHeavyTrucks", 8, null)]
    [InlineData("Number of Light Trucks ", "exposure_exposuremetric_NumberOfLightTrucks", "Number of Items", "NumberOfLightTrucks", 2, null)]
    [InlineData("Number of Power Units", "exposure_exposuremetric_PowerUnitCount", "Number of Items", "NumberOfPowerUnits", 67, "67")]
    [InlineData("Number of Private Personal Transport Vehicles ", "exposure_exposuremetric_NumberOfPrivatePersonalTransportVehicles", "Number of Items", "NumberOfPrivatePersonalTransportVehicles", 9, null)]
    [InlineData("Number of Trailers", "exposure_exposuremetric_TrailerCount", "Number of Items", "NumberOfTrailers", 66, "66")]
    [InlineData("Policy Limit", "responseCapture_metric_limit_policyLimit", "Number of Units", "PolicyLimitNumberOfUnits", 40, "40")]
    [InlineData("Workers Compensation", "responseCapture_metric_limit_workersCompensation", "Number of Units", "WorkersCompensationNumberOfUnits", 39, "39")]
    public void NumberOfTest(string elementtype, string elementtypekey, string elementattributetype, string columnName, dynamic value, string? displayvalue)
    {
        dynamic MarketResponseElementRecord = SetupData(elementtype, elementtypekey, elementattributetype, value, displayvalue);
        dynamic results = ExecuteStoredProcedureWithResultRow(storedProcedureName: "rpt.Load_rpt_MarketResponseElementAttribute");
        Assert.Equal(expected: 1, actual: results.InsertedCount);
        Assert.Equal(expected: 0, actual: results.UpdatedCount);
        Assert.Equal(expected: 0, actual: results.DeletedCount);
        Assert.Equal(expected: 0, actual: results.RejectedCount);

        //This should create only one row
        CheckSprocExecutionLog(sprocName: "rpt.Load_rpt_MarketResponseElementAttribute", insertedCount: 1);

        dynamic row = GetResultRow($"select {columnName} AS result from rpt.MarketResponseElementAttribute");

        Assert.NotNull(row);
        Assert.Equal(expected: _PSmreaRecord.Value, actual: row.result);
    }

    [Theory]
    [InlineData("Treatment of ALAE ", "responseCapture_metric_treatmentAlae ", "ALAE Option", "TreatmentOfAllocatedLossAdjustmentExpenses", "8", "Random Text", "Inside Retention / Inside Limit")]
    [InlineData("Claims Handling Structure", "responseCapture_metric_deductible_CollateralClaimsHandling_ClaimsHandllingStructure", "Claims Handling Structure", "ClaimsHandlingStructure", "56", "Random Text", "Bundled")]
    [InlineData("Form of Collateral", "responseCapture_metric_deductible_CollateralClaimsHandling_FormOfCollateral", "Form of Collateral", "FormOfCollateral", "50", "Random Text", "Letter of Credit")]
    [InlineData("TRIA Premium", "responseCapture_metric_premium_tria", "Included / Excluded", "TerrorismRiskInsuranceActPremiumIncludedExcluded", "33", "Random Text", "Included in Premium")]
    [InlineData("Aggregate Limit", "responseCapture_metric_limit_generalAggregate", "Inclusion Type", "AggregateLimitInclusionType", "45", "Random Text", "Excluded")]
    [InlineData("Auto Physical Damage - Collision", "responseCapture_metric_limit_automotive_physicalDamageCollision", "Inclusion Type", "AutoPhysicalDamageCollisionInclusionType", "58", "Random Text", "Included")]
    [InlineData("Auto Physical Damage - Comprehensive", "responseCapture_metric_limit_automotive_physicalDamageComprehensive", "Inclusion Type", "AutoPhysicalDamageComprehensiveInclusionType", "45", "Random Text", "Excluded")]
    [InlineData("Bodily Injury by Accident - Each Accident", "responseCapture_metric_limit_employersLiability_bodilyInjuryAccident", "Inclusion Type", "BodilyInjurybyAccidentEachAccidentInclusionType", "58", "Random Text", "Included")]
    [InlineData("Bodily Injury by Disease - Each Employee", "responseCapture_metric_limit_employersLiability_bodilyInjuryDiseaseEmployee", "Inclusion Type", "BodilyInjurybyDiseaseEachEmployeeInclusionType", "45", "Random Text", "Excluded")]
    [InlineData("Bodily Injury by Disease - Policy Limit", "responseCapture_metric_limit_employersLiability_bodilyInjuryDiseasePolicyLimit", "Inclusion Type", "BodilyInjurybyDiseasePolicyLimitInclusionType", "58", "Random Text", "Included")]
    [InlineData("Damage to Premises Rented to You", "responseCapture_metric_limit_generalLiability_damageToPremises", "Inclusion Type", "DamagetoPremisesRentedtoYouInclusionType", "45", "Random Text", "Excluded")]
    [InlineData("Each Occurrence Limit", "responseCapture_metric_limit_perOcurrence", "Inclusion Type", "EachOccurrenceLimitInclusionType", "58", "Random Text", "Included")]
    [InlineData("General Aggregate", "responseCapture_metric_limit_generalLiability_aggregate", "Inclusion Type", "GeneralAggregateInclusionType", "45", "Random Text", "Excluded")]
    [InlineData("Med Pay", "responseCapture_metric_limit_medPay", "Inclusion Type", "MedPayInclusionType", "58", "Random Text", "Included")]
    [InlineData("Personal & Advertising Injury - Per Person or Organization", "responseCapture_metric_limit_generalLiability_personalAndAdvertising", "Inclusion Type", "PersonalAdvertisingInjuryPerPersonorOrganizationInclusionType", "45", "Random Text", "Excluded")]
    [InlineData("Policy Limit", "responseCapture_metric_limit_policyLimit", "Inclusion Type", "PolicyLimitInclusionType", "58", "Random Text", "Included")]
    [InlineData("Products & Completed Ops Aggregate Limit", "responseCapture_metric_limit_pncOpsAggregate", "Inclusion Type", "ProductsCompletedOpsAggregateLimitInclusionType", "45", "Random Text", "Excluded")]
    [InlineData("Aggregate Limit", "responseCapture_metric_limit_generalAggregate", "Not Applicable", "AggregateLimitNotApplicable", "47", "Random Text", "Not Applicable")]
    [InlineData("Other States Coverage", "responseCapture_metric_limit_otherStatesCoverage", "Other States Coverage", "OtherStatesCoverage", "46", "Random Text", "All other states except monopolistic")]
    [InlineData("Total Collateral Requirement", "responseCapture_metric_deductible_CollateralClaimsHandling_TotalCollateralReq", "Per State Requirements", "TotalCollateralRequirementPerStateRequirements", "59", "Random Text", "Per State Requirements")]
    [InlineData("Policy Form", "responseCapture_metric_policyForm", "Reporting Form", "PolicyFormReportingForm", "64", "Random Text", "None")]
    [InlineData("Workers Compensation", "responseCapture_metric_limit_workersCompensation", "Statutory / Rejected", "WorkersCompensationStatutoryRejected", "1", "Random Text", "Statutory")]
    [InlineData("Auto Physical Damage - Collision", "responseCapture_metric_limit_automotive_physicalDamageCollision", "Valuation", "AutoPhysicalDamageCollisionValuation", "26", "Random Text", "ACV")]
    [InlineData("Auto Physical Damage - Comprehensive", "responseCapture_metric_limit_automotive_physicalDamageComprehensive", "Valuation", "AutoPhysicalDamageComprehensiveValuation", "28", "Random Text", "ACV or Repair Cost, whichever is less")]
    [InlineData("Surplus Lines Tax", "responseCapture_metric_premium_suplusLinesTax", "Surplus Lines Tax", "SurplusLinesTax", "41", "Random Text", "Admitted")]
    public void AllOtherTypeTest(string elementtype, string elementtypekey, string elementattributetype, string columnName, dynamic value, string displayvalue, string elementattributereference)
    {
        dynamic EAFORecord = CreateRow(tableName: "ref.ElementAttributeReferenceOption", values: new
        {
            ElementAttributeReferenceOptionKey = value,
            ElementAttributeReference = elementattributereference
        });
        dynamic MarketResponseElementRecord = SetupData(elementtype, elementtypekey, elementattributetype, value, displayvalue);
        dynamic results = ExecuteStoredProcedureWithResultRow(storedProcedureName: "rpt.Load_rpt_MarketResponseElementAttribute");
        Assert.Equal(expected: 1, actual: results.InsertedCount);
        Assert.Equal(expected: 0, actual: results.UpdatedCount);
        Assert.Equal(expected: 0, actual: results.DeletedCount);
        Assert.Equal(expected: 0, actual: results.RejectedCount);

        //This should create only one row
        CheckSprocExecutionLog(sprocName: "rpt.Load_rpt_MarketResponseElementAttribute", insertedCount: 1);

        dynamic row = GetResultRow($"select {columnName} AS result from rpt.MarketResponseElementAttribute");

        Assert.NotNull(row);
        Assert.Equal(expected: EAFORecord.ElementAttributeReference, actual: row.result);
    }

    #region Private Methods
    private dynamic SetupData(string elementtype, string elementtypekey, string elementattributetype, dynamic value, string displayvalue)
    {
        _EtRecord = CreateRow(tableName: "ref.ElementType", values: new
        {
            ElementTypeID = 1,
            ElementType = elementtype,
            ElementTypeKey = elementtypekey,
            IsDeleted = 0
        });

        _PSmreaRecord = CreateRow(tableName: "PS.MarketResponseElementAttribute", values: new
        {
            ElementAttributeTypeId = 369,
            MarketResponseElementKey = "123|456|789",
            ElementAttributeType = elementattributetype,
            Value = value,
            DisplayValue = displayvalue,
            IsDeleted = 0
        });

        _PSmrRecord = CreateRow(tableName: "PS.MarketResponse", values: new
        {
            MarketResponseId = 2345,
            MarketResponseKey = "MKTRES|" + carrierresponseid,
            ResponseDate = "2024-02-25 06:34:49.4500000",
            IsDeleted = 0
        });

        dynamic MarketResponseElementRecord = CreateRow(tableName: "PS.MarketResponseElement", values: new
        {
            MarketResponseId = _PSmrRecord.MarketResponseId,
            ElementID = 1,
            ElementTypeID = _EtRecord.ElementTypeId,
            ElementType = _EtRecord.ElementType,
            MarketResponseElementKey = _PSmreaRecord.MarketResponseElementKey,
            IsDeleted = false
        });

        return MarketResponseElementRecord;
    }
    #endregion

    #region Constructor

    /// <summary>
    /// Constructor - Do not change.
    /// </summary>
    /// <param name="fixture"></param>
    public Load_rpt_MarketResponseElementAttributeTests(DatabaseFixture fixture) : base(fixture)
    {
    }

    #endregion
}