﻿using System.Reflection;
using System.Runtime.InteropServices;

namespace PsDb.Tests.rpt.StoredProcedures;

public class Load_rpt_PlacementSecurityTests : PlacementStoreTestBase
{
    readonly string storedProcedureName = "rpt.Load_rpt_PlacementSecurity";

    [Fact]
    public void Load_rpt_PlacementSecurityNoDataTest()
    {
        NoDataStoredProcedureTest(storedProcedureTestMethod: MethodBase.GetCurrentMethod());
    }

    [Fact]
    public void PLacementTeamMemberTest()
    {
        dynamic placementRecord = CreateRow(tableName: "dbo.Placement", values: new { 
            DataSourceInstanceId = (int) DataSourceInstance.BrokingPlatform,
            PlacementSystemId = 1,
        });

        dynamic placementTeamMemberRecord = CreateRow(tableName: "dbo.PlacementTeamMember", values: new
        {
            PlacementId = placementRecord.PlacementId,
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
        });

        dynamic spResult = ExecuteStoredProcedureWithResultRow(storedProcedureName: storedProcedureName);
        Assert.Equal(expected: 1, actual: spResult.InsertedCount);
        Assert.Equal(expected: 0, actual: spResult.UpdatedCount);
        Assert.Equal(expected: 0, actual: spResult.DeletedCount);
        Assert.Equal(expected: 0, actual: spResult.RejectedCount);

        CheckSprocExecutionLog(sprocName: storedProcedureName, insertedCount: 1);

        dynamic row = GetResultRow(tableName: "rpt.PlacementSecurity");
        Assert.NotNull(row);
        Assert.Equal(expected: placementRecord.PlacementId, actual: row.PlacementId);
        Assert.Equal(expected: placementRecord.PlacementId.ToString(), actual: row.TeamId);
        Assert.Equal(expected: "Placement", actual: row.TeamType);
        Assert.Equal(expected: placementTeamMemberRecord.DataSourceInstanceId, actual: row.DataSourceInstanceId);
    }

    [Fact]
    public void ScopeTest()
    {

        dynamic teamRecord = CreateRow(tableName: "ref.Team", values: new
        {
            TeamId = 1,
            TeamKey = "1",
            OrgNode = "/1/",
            IsDeprecated = false,
        });

        dynamic placementRecord = CreateRow(tableName: "dbo.Placement", values: new { 
            DataSourceInstanceId = (int) DataSourceInstance.BrokingPlatform,
            PlacementSystemId = 12,
            IsDeleted = false,
            ServicingPlatformId = (int) DataSourceInstance.Eclipse
        });

        dynamic placementTeamRecord = CreateRow(tableName: "dbo.PlacementTeams", values: new { 
            TeamId = teamRecord.TeamId,
            IsDeleted = false,
            PlacementId = placementRecord.PlacementId,
        });

        dynamic scopeRecord = CreateRow(tableName: "dbo.Scope", values: new { 
            TeamId = teamRecord.TeamId,
            ScopeItemId = 2,
            IsDeleted = false

        });

        dynamic spResult = ExecuteStoredProcedureWithResultRow(storedProcedureName: storedProcedureName);
        Assert.Equal(expected: 1, actual: spResult.InsertedCount);
        Assert.Equal(expected: 0, actual: spResult.UpdatedCount);
        Assert.Equal(expected: 0, actual: spResult.DeletedCount);
        Assert.Equal(expected: 0, actual: spResult.RejectedCount);

        CheckSprocExecutionLog(sprocName: storedProcedureName, insertedCount: 1);

        dynamic row = GetResultRow(tableName: "rpt.PlacementSecurity");
        Assert.NotNull(row);
        Assert.Equal(expected: "Scope", actual: row.TeamType);
        Assert.Equal(expected: scopeRecord.ScopeId + "|" + scopeRecord.ScopeItemId + "|" + teamRecord.TeamId, actual: row.TeamId);
        Assert.Equal(expected: placementRecord.ServicingPlatformId, actual: row.DataSourceInstanceId);
    }

    #region Constructor

    /// <summary>
    /// Constructor - Do not change.
    /// </summary>
    /// <param name="fixture"></param>
    public Load_rpt_PlacementSecurityTests(DatabaseFixture fixture) : base(fixture)
    {
    }

    #endregion
}
