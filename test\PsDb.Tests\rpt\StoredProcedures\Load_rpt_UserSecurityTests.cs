﻿using System.Data;
using System.Reflection;
using System.Runtime.InteropServices;

namespace PsDb.Tests.rpt.StoredProcedures;

public class Load_rpt_UserSecurityTests : PlacementStoreTestBase
{
    readonly string storedProcedureName = "rpt.Load_rpt_UserSecurity";
    dynamic _teamRecord2, _teamRecord3;

    [Fact]
    public void Load_rpt_UserSecurityNoDataTest()
    {
        NoDataStoredProcedureTest(storedProcedureTestMethod: MethodBase.GetCurrentMethod());
    }

    [Fact]
    public void PlacementTeamMemberDataTest()
    {
        dynamic placementRecord = CreateRow(tableName: "dbo.Placement", values: new { 
            DataSourceInstanceId = (int) DataSourceInstance.BrokingPlatform,
            IsDeleted = false,
            PlacementSystemId = 123
        });
        dynamic placementTeamMemberRecord = CreateRow(tableName: "dbo.PlacementTeamMember", values: new { 
            DataSourceInstanceId = (int) DataSourceInstance.BrokingPlatform,
            PlacementId = placementRecord.PlacementId,
        });

        dynamic spResult = ExecuteStoredProcedureWithResultRow(storedProcedureName: storedProcedureName);
        Assert.Equal(expected: 1, actual: spResult.InsertedCount);
        Assert.Equal(expected: 0, actual: spResult.UpdatedCount);
        Assert.Equal(expected: 0, actual: spResult.DeletedCount);
        Assert.Equal(expected: 0, actual: spResult.RejectedCount);

        CheckSprocExecutionLog(sprocName: storedProcedureName, insertedCount: 1);

        dynamic row = GetResultRow(tableName: "rpt.UserSecurity");
        Assert.NotNull(row);
        Assert.Equal(expected: placementRecord.PlacementId.ToString(), actual: row.TeamId);
    }

    [Fact]
    public void ScopeDataTest()
    {
        dynamic scopeRecord = SetupScopeData(false);

        dynamic spResult = ExecuteStoredProcedureWithResultRow(storedProcedureName: storedProcedureName);
        Assert.Equal(expected: 1, actual: spResult.InsertedCount);
        Assert.Equal(expected: 0, actual: spResult.UpdatedCount);
        Assert.Equal(expected: 0, actual: spResult.DeletedCount);
        Assert.Equal(expected: 0, actual: spResult.RejectedCount);

        CheckSprocExecutionLog(sprocName: storedProcedureName, insertedCount: 1);

        dynamic row = GetResultRow(tableName: "rpt.UserSecurity");
        Assert.NotNull(row);
        Assert.Equal(expected: scopeRecord.ScopeId + "|" + scopeRecord.ScopeItemId + "|" + scopeRecord.TeamId, actual: row.TeamId);
        Assert.Equal(expected: "Scope", actual: row.TeamType);
    }

    [Fact]
    public void ScopeMultiLevelDataTest()
    {
        dynamic scopeRecord = SetupScopeData(true);

        dynamic spResult = ExecuteStoredProcedureWithResultRow(storedProcedureName: storedProcedureName);
        Assert.Equal(expected: 3, actual: spResult.InsertedCount);
        Assert.Equal(expected: 0, actual: spResult.UpdatedCount);
        Assert.Equal(expected: 0, actual: spResult.DeletedCount);
        Assert.Equal(expected: 0, actual: spResult.RejectedCount);

        CheckSprocExecutionLog(sprocName: storedProcedureName, insertedCount: 3);

        dynamic results = GetResultRows(tableName: "rpt.UserSecurity");
        Assert.NotNull(results);
        Assert.Equal(expected: 3, actual: results.Rows.Count);


        foreach(DataRow row in results.Rows)
        {
            if(row["TeamId"].ToString().Contains(_teamRecord2.TeamId.ToString()))
            {
                Assert.Equal(expected: scopeRecord.ScopeId + "|" + scopeRecord.ScopeItemId + "|" + _teamRecord2.TeamId, actual: row["TeamId"]);
                Assert.Equal(expected: "Scope", actual: row["TeamType"]);
            }
            else if(row["TeamId"].ToString().Contains(_teamRecord3.TeamId.ToString()))
            {
                Assert.Equal(expected: scopeRecord.ScopeId + "|" + scopeRecord.ScopeItemId + "|" + _teamRecord3.TeamId, actual: row["TeamId"]);
                Assert.Equal(expected: "Scope", actual: row["TeamType"]);
            }
            else
            {
                Assert.Equal(expected: scopeRecord.ScopeId + "|" + scopeRecord.ScopeItemId + "|" + scopeRecord.TeamId, actual: row["TeamId"]);
                Assert.Equal(expected: "Scope", actual: row["TeamType"]);
            }
        }
    }

    [Fact]
    public void DeleteDataTest()
    {
        CreateRow(tableName: "rpt.UserSecurity", values: new
        {
            TeamId = "1024|12345|300001",
            TeamType = "Scope",
            UserId = 1,
            RoleId = 2,
        });

        dynamic spResult = ExecuteStoredProcedureWithResultRow(storedProcedureName: storedProcedureName);
        Assert.Equal(expected: 0, actual: spResult.InsertedCount);
        Assert.Equal(expected: 0, actual: spResult.UpdatedCount);
        Assert.Equal(expected: 1, actual: spResult.DeletedCount);
        Assert.Equal(expected: 0, actual: spResult.RejectedCount);

        CheckSprocExecutionLog(sprocName: storedProcedureName, deletedCount: 1);

        dynamic row = GetResultRow(tableName: "rpt.UserSecurity");
        Assert.Null(row);
    }

    #region private methods
    private dynamic SetupScopeData(bool fullTeamStructure)
    {
        dynamic teamRecord = CreateRow(tableName: "ref.Team", values: new
        {
            TeamId = 1024,
            TeamKey = "1024",
            OrgNode = "/1024/",
            IsDeprecated = false,
        });
        if (fullTeamStructure)
        {

            _teamRecord2 = CreateRow(tableName: "ref.Team", values: new
            {
                TeamId = 1025,
                TeamKey = "1025",
                OrgNode = "/1024/1025/",
                IsDeprecated = false,
            });
            _teamRecord3 = CreateRow(tableName: "ref.Team", values: new
            {
                TeamId = 1026,
                TeamKey = "1026",
                OrgNode = "/1024/1025/1026/",
                IsDeprecated = false,
            });
        }

        dynamic scopeRecord = CreateRow(tableName: "dbo.Scope", values: new
        {
            IsDeleted = false,
            TeamId = teamRecord.TeamId,
        });

        CreateRow(tableName: "dbo.UserScope", values: new
        {
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            IsDeleted = false,
            ScopeId = scopeRecord.ScopeId,

            UserId = 1,
            RoleId = 2
        });
        return scopeRecord;
    }
    #endregion

    #region Constructor

    /// <summary>
    /// Constructor - Do not change.
    /// </summary>
    /// <param name="fixture"></param>
    public Load_rpt_UserSecurityTests(DatabaseFixture fixture) : base(fixture)
    {
    }

    #endregion
}
