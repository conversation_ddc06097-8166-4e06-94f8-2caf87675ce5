﻿using PsDb.Tests.PlacementStoreHelpers;
using System.Data;
using System.Diagnostics.CodeAnalysis;
using Xunit.Abstractions;

namespace PsDb.Tests.rpt.Views;

[ExcludeFromCodeCoverage]
[ExpectedColumn(columnName: "DataSourceInstanceID", columnType: "int", nullable: true)]
[ExpectedColumn(columnName: "TeamType", columnType: "varchar", nullable: false, length: 9)]
[ExpectedColumn(columnName: "TeamID", columnType: "nvarchar", nullable: true, length: 33)]
[ExpectedColumn(columnName: "TeamName", columnType: "nvarchar", nullable: true, length: 500)]
[ExpectedColumn(columnName: "ParentId", columnType: "int", nullable: true)]
[ExpectedColumn(columnName: "OrgLevel1", columnType: "nvarchar", nullable: true, length: 500)]
[ExpectedColumn(columnName: "OrgLevel2", columnType: "nvarchar", nullable: true, length: 500)]
[ExpectedColumn(columnName: "OrgLevel3", columnType: "nvarchar", nullable: true, length: 500)]
[ExpectedColumn(columnName: "OrgLevel4", columnType: "nvarchar", nullable: true, length: 500)]
[ExpectedColumn(columnName: "OrgLevel5", columnType: "nvarchar", nullable: true, length: 500)]
[ExpectedColumn(columnName: "OrgLevel6", columnType: "nvarchar", nullable: true, length: 500)]
[ExpectedColumn(columnName: "LevelNum", columnType: "int", nullable: true)]
[ExpectedColumn(columnName: "OrganisationRole", columnType: "varchar", nullable: false, length: 9)]
[ExpectedColumn(columnName: "Team", columnType: "nvarchar", nullable: false, length: 2009)]
[ExpectedColumn(columnName: "Segment", columnType: "nvarchar", nullable: true, length: 100)]
[ExpectedColumn(columnName: "Region", columnType: "nvarchar", nullable: true, length: 100)]
[ExpectedColumn(columnName: "SubSegment", columnType: "nvarchar", nullable: true, length: 100)]
[ExpectedColumn(columnName: "Industry", columnType: "nvarchar", nullable: true, length: 10)]
[ExpectedColumn(columnName: "PlacementStatusId", columnType: "int", nullable: true)]
public class vw_as_SecurityTeamTests : PlacementStoreLockedDefinitionViewTestBase
{
    public override void DoesNotNeedMuchToReturnARowTest()
    {
        dynamic RegionRecord = CreateRow(
            tableName: "ref.Team",
            values: new
            {
                TeamId = 1,
                TeamName = "Region",
                TeamKey = "1",
                OrgNode = "/1/",
                OrganisationRole = "Region",
                OrgLevel = 1,
                FullPath = "Region"
            });

        dynamic scopeRecord = CreateRow(
            tableName: "dbo.Scope",
            values: new
            {
                ScopeId = 100,
                TeamId = RegionRecord.TeamId,
                ScopeItemID = 11
            });

        dynamic result = GetResultRow(tableName: ViewName, whereClause: "OrganisationRole='Scope'");
        Assert.NotNull(result);
        Assert.Equal(expected: $"{scopeRecord.ScopeId}|{scopeRecord.ScopeItemId}|{RegionRecord.TeamId}", actual: result.TeamID);
    }

    [Fact]
    public void ScopeTest()
    {

        dynamic teamRecord = CreateRow(
            tableName: "ref.Team",
            values: new
            {
                TeamId = 1,
                TeamName = "Region",
                TeamKey = "1",
                OrgNode = "/1/",
                OrganisationRole = "Region",
                OrgLevel = 1,
                FullPath = "Region"
            });

        dynamic scopeRecord = CreateRow(
            tableName: "dbo.Scope",
            values: new
            {
                ScopeId=100,
                TeamId = 1,
                ScopeItemID=11
            });

        dynamic result = GetResultRow(tableName: ViewName, whereClause: "OrganisationRole='Scope'");
        Assert.NotNull(result);
        Assert.Equal(expected: $"{scopeRecord.ScopeId}|{scopeRecord.ScopeItemId}|{teamRecord.TeamId}", actual: result.TeamID);
        Assert.Equal(expected: teamRecord.TeamName, actual: result.OrgLevel1);
    }
    
    [Fact]
    public void ScopeFullTeamStructureTest()
    {

        dynamic RegionRecord = CreateRow(
            tableName: "ref.Team",
            values: new
            {
                TeamId = 1,
                TeamName = "Region",
                TeamKey = "1",
                OrgNode = "/1/",
                OrganisationRole = "Region",
                OrgLevel = 1,
                FullPath = "Region"
            });

        dynamic BURecord = CreateRow(
            tableName: "ref.Team",
            values: new
            {
                TeamId = 2,
                TeamName = "BU",
                TeamKey = "2",
                OrgNode = "/1/2/",
                OrganisationRole = "BU",
                OrgLevel = 2,
                FullPath = "Region>BU"
            });
        dynamic TeamRecord = CreateRow(
            tableName: "ref.Team",
            values: new
            {
                TeamId = 3,
                TeamName = "Team",
                TeamKey = "3",
                OrgNode = "/1/2/3/",
                OrganisationRole = "Team",
                OrgLevel = 3,
                FullPath = "Region>BU>Team"
            });

        dynamic clientRecord = CreateRow(
            tableName: "ref.Team",
            values: new
            {
                TeamId = 4,
                TeamName = "ClientTeam",
                TeamKey = "4",
                OrgNode = "/1/2/3/4/",
                OrganisationRole = "ClientTeam",
                OrgLevel = 4,
                FullPath = "Region>BU>Team>ClientTeam"
            });

        dynamic segmentRecord = CreateRow(tableName: "ref.BrokingSegment", values: new {
            BrokingSegment = "A"
        });

        dynamic brokingSubSegmentRecord = CreateRow(tableName: "ref.BrokingSubSegment", values: new
        {
            BrokingSubSegment = "B"
        });

        dynamic brokingRegionRecord = CreateRow(tableName: "ref.BrokingRegion", values: new
        {
            BrokingRegion = "B"
        });

        dynamic scopeRecord = CreateRow(
            tableName: "dbo.Scope",
            values: new
            {
                ScopeId=100,
                TeamId = clientRecord.TeamId,
                ScopeItemID=11,
                BrokingSegmentId = segmentRecord.BrokingSegmentId,
                BrokingRegionId = brokingRegionRecord.BrokingRegionId,
                BrokingSubSegmentId = brokingSubSegmentRecord.BrokingSubSegmentId
            });

        dynamic result = GetResultRow(tableName: ViewName, whereClause: "OrganisationRole='Scope'");
        Assert.NotNull(result);
        Assert.Equal(expected: $"{scopeRecord.ScopeId}|{scopeRecord.ScopeItemId}|{clientRecord.TeamId}", actual: result.TeamID);
        Assert.Equal(expected: clientRecord.TeamId, actual: result.ParentId);
        Assert.Equal(expected: RegionRecord.TeamName, actual: result.OrgLevel1);
        Assert.Equal(expected: BURecord.TeamName, actual: result.OrgLevel2);
        Assert.Equal(expected: TeamRecord.TeamName, actual: result.OrgLevel3);
        Assert.Equal(expected: clientRecord.TeamName, actual: result.OrgLevel4);
        Assert.Equal(expected: segmentRecord.BrokingSegment, actual: result.Segment);
        Assert.Equal(expected: brokingSubSegmentRecord.BrokingSubSegment, actual: result.SubSegment);
        Assert.Equal(expected: brokingRegionRecord.BrokingRegion, actual: result.Region);
    }

    /*
     * Test to validate that a scope at the top level of the hierarchy also returns the full team structure.
     */
    [Fact]
    public void ScopeFullTeamStructureTopLevelTest()
    {

        dynamic RegionRecord = CreateRow(
            tableName: "ref.Team",
            values: new
            {
                TeamId = 1,
                TeamName = "Region",
                TeamKey = "1",
                OrgNode = "/1/",
                OrganisationRole = "Region",
                OrgLevel = 1,
                FullPath = "Region"
            });

        dynamic BURecord = CreateRow(
            tableName: "ref.Team",
            values: new
            {
                TeamId = 2,
                TeamName = "BU",
                TeamKey = "2",
                OrgNode = "/1/2/",
                OrganisationRole = "BU",
                OrgLevel = 2,
                FullPath = "Region>BU"
            });
        dynamic TeamRecord = CreateRow(
            tableName: "ref.Team",
            values: new
            {
                TeamId = 3,
                TeamName = "Team",
                TeamKey = "3",
                OrgNode = "/1/2/3/",
                OrganisationRole = "Team",
                OrgLevel = 3,
                FullPath = "Region>BU>Team"
            });

        dynamic clientRecord = CreateRow(
            tableName: "ref.Team",
            values: new
            {
                TeamId = 4,
                TeamName = "ClientTeam",
                TeamKey = "4",
                OrgNode = "/1/2/3/4/",
                OrganisationRole = "ClientTeam",
                OrgLevel = 4,
                FullPath = "Region>BU>Team>ClientTeam"
            });

        dynamic segmentRecord = CreateRow(tableName: "ref.BrokingSegment", values: new
        {
            BrokingSegment = "A"
        });

        dynamic brokingSubSegmentRecord = CreateRow(tableName: "ref.BrokingSubSegment", values: new
        {
            BrokingSubSegment = "B"
        });

        dynamic brokingRegionRecord = CreateRow(tableName: "ref.BrokingRegion", values: new
        {
            BrokingRegion = "B"
        });

        dynamic scopeRecord = CreateRow(
            tableName: "dbo.Scope",
            values: new
            {
                ScopeId = 100,
                TeamId = RegionRecord.TeamId,
                ScopeItemID = 11,
                BrokingSegmentId = segmentRecord.BrokingSegmentId,
                BrokingRegionId = brokingRegionRecord.BrokingRegionId,
                BrokingSubSegmentId = brokingSubSegmentRecord.BrokingSubSegmentId
            });

        dynamic result = GetResultRows(tableName: ViewName, whereClause: $"OrganisationRole='Scope'");
        Assert.NotNull(result);
        Assert.Equal(expected: 4, actual: result.Rows.Count);

        foreach(DataRow row in result.Rows)
        {
            Assert.Equal(expected: RegionRecord.TeamName, actual: row["OrgLevel1"]);
            Assert.Equal(expected: segmentRecord.BrokingSegment, actual: row["Segment"]);
            Assert.Equal(expected: brokingSubSegmentRecord.BrokingSubSegment, actual: row["SubSegment"]);
            Assert.Equal(expected: brokingRegionRecord.BrokingRegion, actual: row["Region"]);

            var teamId = Convert.ToString(row["TeamID"]);

            if(teamId.Contains(Convert.ToString(clientRecord.TeamId)))
            {
                Assert.Equal(expected: clientRecord.TeamId, actual: row["ParentId"]);
                Assert.Equal(expected: $"{scopeRecord.ScopeId}|{scopeRecord.ScopeItemId}|{clientRecord.TeamId}", actual: row["TeamID"]);
            }
            if(teamId.Contains(Convert.ToString(BURecord.TeamId)))
            {
                Assert.Equal(expected: BURecord.TeamId, actual: row["ParentId"]);
                Assert.Equal(expected: $"{scopeRecord.ScopeId}|{scopeRecord.ScopeItemId}|{BURecord.TeamId}", actual: row["TeamID"]);
                Assert.Equal(expected: BURecord.TeamName, actual: row["OrgLevel2"]);
            }
            if(teamId.Contains(Convert.ToString(TeamRecord.TeamId)))
            {
                Assert.Equal(expected: TeamRecord.TeamId, actual: row["ParentId"]);
                Assert.Equal(expected: $"{scopeRecord.ScopeId}|{scopeRecord.ScopeItemId}|{TeamRecord.TeamId}", actual: row["TeamID"]);
                Assert.Equal(expected: BURecord.TeamName, actual: row["OrgLevel2"]);
                Assert.Equal(expected: TeamRecord.TeamName, actual: row["OrgLevel3"]);
            }
            if(teamId.Contains(Convert.ToString(clientRecord.TeamId)))
            {
                Assert.Equal(expected: clientRecord.TeamId, actual: row["ParentId"]);
                Assert.Equal(expected: $"{scopeRecord.ScopeId}|{scopeRecord.ScopeItemId}|{clientRecord.TeamId}", actual: row["TeamID"]);
                Assert.Equal(expected: BURecord.TeamName, actual: row["OrgLevel2"]);
                Assert.Equal(expected: TeamRecord.TeamName, actual: row["OrgLevel3"]);
                Assert.Equal(expected: clientRecord.TeamName, actual: row["OrgLevel4"]);
            }
        }
    }

    [Fact]
    public void PlacementTeamTest()
    {
        dynamic teamRecord = CreateRow(tableName: "ref.Team", values: new{
            TeamId = 1,
            TeamName = "Region",
            TeamKey = "1",
            OrgNode = "/1/",
            OrganisationRole = "Region",
            OrgLevel = 1,
            FullPath = "Region"
        });

        dynamic placementRecord = CreateRow(tableName: "dbo.Placement", values: new { 
            PlacementSystemId = 1,
            DataSourceInstanceId = (int) DataSourceInstance.BrokingPlatform,
            PlacementName = "AnotherPlacement",
        });

        CreateRow(tableName: "dbo.PlacementTeamMember", values: new
        {
            PlacementId = placementRecord.PlacementId,
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform
        });

        CreateRow(tableName: "dbo.PlacementTeams", values: new
        {
            PlacementId = placementRecord.PlacementId,
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            TeamId = teamRecord.TeamId,
            IsDeleted = false
        });

        dynamic result = GetResultRow(tableName: ViewName, whereClause: "OrganisationRole='Placement'");
        Assert.NotNull(result);
        Assert.Equal(expected: placementRecord.PlacementId.ToString(), actual: result.TeamID);
        Assert.Equal(expected: $"Placement:{placementRecord.PlacementName}", actual: result.TeamName);
    }

    /// <summary>
    /// Constructor
    /// </summary>
    /// <param name="fixture"></param>
    public vw_as_SecurityTeamTests(DatabaseFixture fixture, ITestOutputHelper output) : base(fixture, output)
    {
    }
}
