﻿namespace PsDb.Tests.rpt.Views;

public class vw_as_SubmissionContainerMarketTests : PlacementStoreTestBase
{
    readonly string viewName = "rpt.vw_as_SubmissionContainerMarket";
    [Fact]
    public void NoDataTest()
    {
        dynamic row = GetResultRow(tableName: viewName);
        Assert.Null(row);
    }

    [Fact]
    public void MinimalDataTest()
    {
        int submissionContainerMarketId = 1;
        int submissionContainerId = 10;

        dynamic negotiationRecord = CreateRow(tableName: "PS.Negotiation", values: new { 
            IsDeleted = false,
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            NegotiationKey = $"SUBC|{submissionContainerId}"

        });
        dynamic negotiationMarketRecord = CreateRow(tableName: "PS.NegotiationMarket", values: new {
            IsDeleted = false,
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            NegotiationMarketKey = $"SUBCONMKT|{submissionContainerMarketId}",
            NegotiationId = negotiationRecord.NegotiationId,
            MarketKey = "Panel-1"
        });

        dynamic submissionRecord = CreateRow(tableName: "BP.Submission", values: new
        {
        });

        dynamic submissionDistributionRecord = CreateRow(tableName: "dbo.SubmissionDistribution", values: new {
            SubmissionContainerMarketId = submissionContainerMarketId,
            SubmissionId = submissionRecord.Id
        });

        dynamic row = GetResultRow(tableName: viewName);
        Assert.NotNull(row);
        Assert.Equal(expected: negotiationMarketRecord.MarketKey, actual: row.CarrierID);
        Assert.Equal(expected: submissionContainerId, actual: row.SubmissionContainerId);
        Assert.Equal(expected: negotiationMarketRecord.MarketKey + '-' + submissionContainerId, actual: row.SubmissionContainerMarket);
    }

    #region Constructor

    /// <summary>
    /// Constructor - Do not change.
    /// </summary>
    /// <param name="fixture"></param>
    public vw_as_SubmissionContainerMarketTests(DatabaseFixture fixture) : base(fixture)
    {
    }

    #endregion
}