﻿namespace PsDb.Tests.rpt.Views;

public class vw_as_SubmissionDocumentTests : PlacementStoreTestBase
{
    readonly string viewName = "rpt.vw_as_SubmissionDocument";
    [Fact]
    public void NoDataTest()
    {
        dynamic row = GetResultRow(tableName: viewName);
        Assert.Null(row);
    }

    [Fact]
    public void MinimalDataTest()
    {
        int submissionContainerMarketId = 1;
        int submissionContainerId = 10;

        dynamic negotiationRecord = CreateRow(tableName: "PS.Negotiation", values: new { 
            IsDeleted = false,
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            NegotiatioNKey = $"SUBC|{submissionContainerId}"

        });
        dynamic negotiationMarketRecord = CreateRow(tableName: "PS.NegotiationMarket", values: new {
            IsDeleted = false,
            DataSourceInstanceId = (int)DataSourceInstance.BrokingPlatform,
            NegotiationMarketKey = $"SUBCONMKT|{submissionContainerMarketId}",
            NegotiationId = negotiationRecord.NegotiationId,
            PanelId = 1,
            MarketKey = "Panel-1"
        });

        dynamic submissionRecord = CreateRow(tableName: "BP.Submission", values: new
        {
        });

        dynamic submissionDistributionRecord = CreateRow(tableName: "dbo.SubmissionDistribution", values: new {
            SubmissionContainerMarketId = submissionContainerMarketId,
            SubmissionId = submissionRecord.Id
        });

        dynamic submissionMarketDocumentRecord = CreateRow(tableName: "dbo.SubmissionMarketDocuments", values: new {
            SubmissionContainerMarketId = submissionContainerMarketId,
            SubmissionId = submissionRecord.Id,
            NumberOfDocuments = 2,
            LastDocumentUpdate = DateTime.Now,
        });

        dynamic row = GetResultRow(tableName: viewName);
        Assert.NotNull(row);
        Assert.Equal(expected: submissionMarketDocumentRecord.NumberOfDocuments, actual: row.NumberOfDocuments);
        Assert.Equal(expected: submissionMarketDocumentRecord.LastDocumentUpdate.Date, actual: row.LastDocumentUpdate.Date);
        Assert.Equal(expected: negotiationMarketRecord.MarketKey, actual: row.CarrierID);
    }

    #region Constructor

    /// <summary>
    /// Constructor - Do not change.
    /// </summary>
    /// <param name="fixture"></param>
    public vw_as_SubmissionDocumentTests(DatabaseFixture fixture) : base(fixture)
    {
    }

    #endregion
}
