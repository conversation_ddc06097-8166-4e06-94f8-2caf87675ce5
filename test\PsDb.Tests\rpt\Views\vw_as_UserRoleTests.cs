﻿using PsDb.Tests.PlacementStoreHelpers;
using System.Diagnostics.CodeAnalysis;
using Xunit.Abstractions;

namespace PsDb.Tests.rpt.Views;

[ExcludeFromCodeCoverage]
[ExpectedColumn(columnName: "UserId", columnType: "int", nullable: false)]
[ExpectedColumn(columnName: "DataSourceInstanceId", columnType: "int", nullable: false)]
[ExpectedColumn(columnName: "UserPrincipalName", columnType: "nvarchar", nullable: false, length: 100)]
[ExpectedColumn(columnName: "GivenName", columnType: "nvarchar", nullable: true, length: 100)]
[ExpectedColumn(columnName: "Surname", columnType: "nvarchar", nullable: true, length: 100)]
[ExpectedColumn(columnName: "SupportedLanguageID", columnType: "int", nullable: true)]
[ExpectedColumn(columnName: "UserKey", columnType: "varchar", nullable: true, length: 14)]
[ExpectedColumn(columnName: "TeamType", columnType: "nvarchar", nullable: false, length: 50)]
[ExpectedColumn(columnName: "TeamId", columnType: "nvarchar", nullable: false, length: 30)]
[ExpectedColumn(columnName: "RoleID", columnType: "int", nullable: false)]
[ExpectedColumn(columnName: "RoleDescription", columnType: "nvarchar", nullable: false, length: 100)]
[ExpectedColumn(columnName: "RoleKey", columnType: "varchar", nullable: true, length: 14)]
[ExpectedColumn(columnName: "Enabled", columnType: "varchar", nullable: false, length: 3)]
public class vw_as_UserRoleTests : PlacementStoreLockedDefinitionViewTestBase
{
    public override void DoesNotNeedMuchToReturnARowTest()
    {
        dynamic placementSystemUserRecord = CreateRow(tableName: "dbo.PlacementSystemUser", values: new {
            UserID = 1234 
        });

        dynamic userSecurityRecord = CreateRow(tableName: "rpt.UserSecurity", values: new { 
            TeamType = "Scope", 
            RoleID = 1234, 
            UserID = placementSystemUserRecord.UserID 
        });

        dynamic servicingRoleRecord = CreateRow(tableName: "ref.ServicingRole", values: new { 
            ServicingRoleId = userSecurityRecord.RoleID 
        });

        dynamic row = GetResultRow(tableName: "rpt.vw_as_UserRole");

        Assert.NotNull(row);
        Assert.Equal(expected: placementSystemUserRecord.UserID, actual: row.UserID);
        Assert.Equal(expected: "No", actual: row.Enabled);
    }

    [Theory]
    [InlineData(true, "No")]
    [InlineData(false, "Yes")]
    public void ValidateUserRoleEnabled(bool isDeleted, string expectedOutput)
    {
        dynamic activeDirectoryRecord = CreateRow(tableName: "Reference.ActiveDirectory", values: new {
            Enabled = "True",
            IsDeleted = isDeleted
        });
        dynamic servicingRoleRecord = CreateRow(tableName: "ref.ServicingRole", values: new {
            ServicingRoleId = 134
        });
        dynamic userSecurityRecord = CreateRow(tableName: "rpt.UserSecurity", values: new {
            TeamType = "UserGroup",
            RoleID = servicingRoleRecord.ServicingRoleId,
            UserID = 1234
        });

        CreateRow(tableName: "dbo.PlacementSystemUser", values: new {
            UserID = userSecurityRecord.UserId,
            ActiveDirectoryId = activeDirectoryRecord.ActiveDirectoryId
        });
        dynamic row = GetResultRow(tableName: "rpt.vw_as_UserRole");
        Assert.Equal(expected: expectedOutput, actual: row.Enabled);

    }

    public vw_as_UserRoleTests(DatabaseFixture fixture, ITestOutputHelper output) : base(fixture, output)
    {

    }

}
